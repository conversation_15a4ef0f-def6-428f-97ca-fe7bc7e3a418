import{u as Id,m as Dd,r as i,M as za,a as Ga,c as kd,f as Ka,i as Pn,b as Ld,d as Fd,e as Bd,g as se,j as f,h as U,R as V,k as Me,l as Jt,n as ko,V as Ud,o as Wd,_ as W,$ as Nn,p as be,q as qa,s as pe,H as Vd,t as Vr,A as Ya,C as Xa,X as Hd,v as zd,B as Tn}from"./index-DSWmgocX.js";function Vt(e){const t=Id(()=>Dd(e)),{isStatic:n}=i.useContext(za);if(n){const[,r]=i.useState(e);i.useEffect(()=>t.on("change",r),[])}return t}function Gd(e,t){const n=Vt(t()),r=()=>n.set(t());return r(),Ga(()=>{const o=()=>Ka.preRender(r,!1,!0),s=e.map(a=>a.on("change",o));return()=>{s.forEach(a=>a()),kd(r)}}),n}function Kd(e,...t){const n=e.length;function r(){let o="";for(let s=0;s<n;s++){o+=e[s];const a=t[s];a&&(o+=Pn(a)?a.get():a)}return o}return Gd(t.filter(Pn),r)}function Ds(e){return typeof e=="number"?e:parseFloat(e)}function qd(e,t={}){const{isStatic:n}=i.useContext(za),r=i.useRef(null),o=Vt(Pn(e)?Ds(e.get()):e),s=i.useRef(o.get()),a=i.useRef(()=>{}),c=()=>{const u=r.current;u&&u.time===0&&u.sample(Ld.delta),l(),r.current=Fd({keyframes:[o.get(),s.current],velocity:o.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...t,onUpdate:a.current})},l=()=>{r.current&&r.current.stop()};return i.useInsertionEffect(()=>o.attach((u,d)=>n?d(u):(s.current=u,a.current=d,Ka.update(c),o.get()),l),[JSON.stringify(t)]),Ga(()=>{if(Pn(e))return e.on("change",u=>o.set(Ds(u)))},[o]),o}function Yd(e,t,n){var r;if(typeof e=="string"){let o=document;t&&(Bd(!!t.current),o=t.current),n?((r=n[e])!==null&&r!==void 0||(n[e]=o.querySelectorAll(e)),e=n[e]):e=o.querySelectorAll(e)}else e instanceof Element&&(e=[e]);return Array.from(e||[])}const Xd={some:0,all:1};function Jd(e,t,{root:n,margin:r,amount:o="some"}={}){const s=Yd(e),a=new WeakMap,c=u=>{u.forEach(d=>{const p=a.get(d.target);if(d.isIntersecting!==!!p)if(d.isIntersecting){const g=t(d);typeof g=="function"?a.set(d.target,g):l.unobserve(d.target)}else p&&(p(d),a.delete(d.target))})},l=new IntersectionObserver(c,{root:n,rootMargin:r,threshold:typeof o=="number"?o:Xd[o]});return s.forEach(u=>l.observe(u)),()=>l.disconnect()}function Zd(e,{root:t,margin:n,amount:r,once:o=!1}={}){const[s,a]=i.useState(!1);return i.useEffect(()=>{if(!e.current||o&&s)return;const c=()=>(a(!0),o?void 0:()=>a(!1)),l={root:t&&t.current||void 0,margin:n,amount:r};return Jd(e.current,c,l)},[t,e,n,o,r]),s}/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qd=se("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lo=se("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ht=se("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ef=se("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ja=se("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tf=se("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nf=se("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rf=se("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hr=se("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const of=se("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sf=se("FileImage",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["circle",{cx:"10",cy:"13",r:"2",key:"6v46hv"}],["path",{d:"m20 17-1.09-1.09a2 2 0 0 0-2.82 0L10 22",key:"17vly1"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const af=se("FileWarning",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ks=se("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cf=se("Layers",[["path",{d:"m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z",key:"8b97xw"}],["path",{d:"m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65",key:"dd6zsq"}],["path",{d:"m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65",key:"ep9fru"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lf=se("Monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ls=se("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",key:"1xcu5"}],["circle",{cx:"17.5",cy:"10.5",r:".5",key:"736e4u"}],["circle",{cx:"8.5",cy:"7.5",r:".5",key:"clrty"}],["circle",{cx:"6.5",cy:"12.5",r:".5",key:"1s4xz9"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uf=se("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const df=se("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]);function Za(e,t){return function(){return e.apply(t,arguments)}}const{toString:ff}=Object.prototype,{getPrototypeOf:Fo}=Object,{iterator:Xn,toStringTag:Qa}=Symbol,Jn=(e=>t=>{const n=ff.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),Re=e=>(e=e.toLowerCase(),t=>Jn(t)===e),Zn=e=>t=>typeof t===e,{isArray:Tt}=Array,zt=Zn("undefined");function Zt(e){return e!==null&&!zt(e)&&e.constructor!==null&&!zt(e.constructor)&&me(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const ei=Re("ArrayBuffer");function pf(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&ei(e.buffer),t}const mf=Zn("string"),me=Zn("function"),ti=Zn("number"),Qt=e=>e!==null&&typeof e=="object",hf=e=>e===!0||e===!1,Cn=e=>{if(Jn(e)!=="object")return!1;const t=Fo(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Qa in e)&&!(Xn in e)},gf=e=>{if(!Qt(e)||Zt(e))return!1;try{return Object.keys(e).length===0&&Object.getPrototypeOf(e)===Object.prototype}catch{return!1}},vf=Re("Date"),bf=Re("File"),xf=Re("Blob"),yf=Re("FileList"),wf=e=>Qt(e)&&me(e.pipe),Cf=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||me(e.append)&&((t=Jn(e))==="formdata"||t==="object"&&me(e.toString)&&e.toString()==="[object FormData]"))},Ef=Re("URLSearchParams"),[Sf,$f,Rf,Pf]=["ReadableStream","Request","Response","Headers"].map(Re),Nf=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function en(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,o;if(typeof e!="object"&&(e=[e]),Tt(e))for(r=0,o=e.length;r<o;r++)t.call(null,e[r],r,e);else{if(Zt(e))return;const s=n?Object.getOwnPropertyNames(e):Object.keys(e),a=s.length;let c;for(r=0;r<a;r++)c=s[r],t.call(null,e[c],c,e)}}function ni(e,t){if(Zt(e))return null;t=t.toLowerCase();const n=Object.keys(e);let r=n.length,o;for(;r-- >0;)if(o=n[r],t===o.toLowerCase())return o;return null}const tt=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,ri=e=>!zt(e)&&e!==tt;function zr(){const{caseless:e}=ri(this)&&this||{},t={},n=(r,o)=>{const s=e&&ni(t,o)||o;Cn(t[s])&&Cn(r)?t[s]=zr(t[s],r):Cn(r)?t[s]=zr({},r):Tt(r)?t[s]=r.slice():t[s]=r};for(let r=0,o=arguments.length;r<o;r++)arguments[r]&&en(arguments[r],n);return t}const Tf=(e,t,n,{allOwnKeys:r}={})=>(en(t,(o,s)=>{n&&me(o)?e[s]=Za(o,n):e[s]=o},{allOwnKeys:r}),e),Af=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Of=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},_f=(e,t,n,r)=>{let o,s,a;const c={};if(t=t||{},e==null)return t;do{for(o=Object.getOwnPropertyNames(e),s=o.length;s-- >0;)a=o[s],(!r||r(a,e,t))&&!c[a]&&(t[a]=e[a],c[a]=!0);e=n!==!1&&Fo(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Mf=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},jf=e=>{if(!e)return null;if(Tt(e))return e;let t=e.length;if(!ti(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},If=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Fo(Uint8Array)),Df=(e,t)=>{const r=(e&&e[Xn]).call(e);let o;for(;(o=r.next())&&!o.done;){const s=o.value;t.call(e,s[0],s[1])}},kf=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},Lf=Re("HTMLFormElement"),Ff=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,o){return r.toUpperCase()+o}),Fs=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Bf=Re("RegExp"),oi=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};en(n,(o,s)=>{let a;(a=t(o,s,e))!==!1&&(r[s]=a||o)}),Object.defineProperties(e,r)},Uf=e=>{oi(e,(t,n)=>{if(me(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(me(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Wf=(e,t)=>{const n={},r=o=>{o.forEach(s=>{n[s]=!0})};return Tt(e)?r(e):r(String(e).split(t)),n},Vf=()=>{},Hf=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function zf(e){return!!(e&&me(e.append)&&e[Qa]==="FormData"&&e[Xn])}const Gf=e=>{const t=new Array(10),n=(r,o)=>{if(Qt(r)){if(t.indexOf(r)>=0)return;if(Zt(r))return r;if(!("toJSON"in r)){t[o]=r;const s=Tt(r)?[]:{};return en(r,(a,c)=>{const l=n(a,o+1);!zt(l)&&(s[c]=l)}),t[o]=void 0,s}}return r};return n(e,0)},Kf=Re("AsyncFunction"),qf=e=>e&&(Qt(e)||me(e))&&me(e.then)&&me(e.catch),si=((e,t)=>e?setImmediate:t?((n,r)=>(tt.addEventListener("message",({source:o,data:s})=>{o===tt&&s===n&&r.length&&r.shift()()},!1),o=>{r.push(o),tt.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",me(tt.postMessage)),Yf=typeof queueMicrotask<"u"?queueMicrotask.bind(tt):typeof process<"u"&&process.nextTick||si,Xf=e=>e!=null&&me(e[Xn]),R={isArray:Tt,isArrayBuffer:ei,isBuffer:Zt,isFormData:Cf,isArrayBufferView:pf,isString:mf,isNumber:ti,isBoolean:hf,isObject:Qt,isPlainObject:Cn,isEmptyObject:gf,isReadableStream:Sf,isRequest:$f,isResponse:Rf,isHeaders:Pf,isUndefined:zt,isDate:vf,isFile:bf,isBlob:xf,isRegExp:Bf,isFunction:me,isStream:wf,isURLSearchParams:Ef,isTypedArray:If,isFileList:yf,forEach:en,merge:zr,extend:Tf,trim:Nf,stripBOM:Af,inherits:Of,toFlatObject:_f,kindOf:Jn,kindOfTest:Re,endsWith:Mf,toArray:jf,forEachEntry:Df,matchAll:kf,isHTMLForm:Lf,hasOwnProperty:Fs,hasOwnProp:Fs,reduceDescriptors:oi,freezeMethods:Uf,toObjectSet:Wf,toCamelCase:Ff,noop:Vf,toFiniteNumber:Hf,findKey:ni,global:tt,isContextDefined:ri,isSpecCompliantForm:zf,toJSONObject:Gf,isAsyncFn:Kf,isThenable:qf,setImmediate:si,asap:Yf,isIterable:Xf};function B(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}R.inherits(B,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:R.toJSONObject(this.config),code:this.code,status:this.status}}});const ai=B.prototype,ii={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{ii[e]={value:e}});Object.defineProperties(B,ii);Object.defineProperty(ai,"isAxiosError",{value:!0});B.from=(e,t,n,r,o,s)=>{const a=Object.create(ai);return R.toFlatObject(e,a,function(l){return l!==Error.prototype},c=>c!=="isAxiosError"),B.call(a,e.message,t,n,r,o),a.cause=e,a.name=e.name,s&&Object.assign(a,s),a};const Jf=null;function Gr(e){return R.isPlainObject(e)||R.isArray(e)}function ci(e){return R.endsWith(e,"[]")?e.slice(0,-2):e}function Bs(e,t,n){return e?e.concat(t).map(function(o,s){return o=ci(o),!n&&s?"["+o+"]":o}).join(n?".":""):t}function Zf(e){return R.isArray(e)&&!e.some(Gr)}const Qf=R.toFlatObject(R,{},null,function(t){return/^is[A-Z]/.test(t)});function Qn(e,t,n){if(!R.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=R.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(m,b){return!R.isUndefined(b[m])});const r=n.metaTokens,o=n.visitor||d,s=n.dots,a=n.indexes,l=(n.Blob||typeof Blob<"u"&&Blob)&&R.isSpecCompliantForm(t);if(!R.isFunction(o))throw new TypeError("visitor must be a function");function u(h){if(h===null)return"";if(R.isDate(h))return h.toISOString();if(R.isBoolean(h))return h.toString();if(!l&&R.isBlob(h))throw new B("Blob is not supported. Use a Buffer instead.");return R.isArrayBuffer(h)||R.isTypedArray(h)?l&&typeof Blob=="function"?new Blob([h]):Buffer.from(h):h}function d(h,m,b){let x=h;if(h&&!b&&typeof h=="object"){if(R.endsWith(m,"{}"))m=r?m:m.slice(0,-2),h=JSON.stringify(h);else if(R.isArray(h)&&Zf(h)||(R.isFileList(h)||R.endsWith(m,"[]"))&&(x=R.toArray(h)))return m=ci(m),x.forEach(function(w,C){!(R.isUndefined(w)||w===null)&&t.append(a===!0?Bs([m],C,s):a===null?m:m+"[]",u(w))}),!1}return Gr(h)?!0:(t.append(Bs(b,m,s),u(h)),!1)}const p=[],g=Object.assign(Qf,{defaultVisitor:d,convertValue:u,isVisitable:Gr});function v(h,m){if(!R.isUndefined(h)){if(p.indexOf(h)!==-1)throw Error("Circular reference detected in "+m.join("."));p.push(h),R.forEach(h,function(x,y){(!(R.isUndefined(x)||x===null)&&o.call(t,x,R.isString(y)?y.trim():y,m,g))===!0&&v(x,m?m.concat(y):[y])}),p.pop()}}if(!R.isObject(e))throw new TypeError("data must be an object");return v(e),t}function Us(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function Bo(e,t){this._pairs=[],e&&Qn(e,this,t)}const li=Bo.prototype;li.append=function(t,n){this._pairs.push([t,n])};li.toString=function(t){const n=t?function(r){return t.call(this,r,Us)}:Us;return this._pairs.map(function(o){return n(o[0])+"="+n(o[1])},"").join("&")};function ep(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ui(e,t,n){if(!t)return e;const r=n&&n.encode||ep;R.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let s;if(o?s=o(t,n):s=R.isURLSearchParams(t)?t.toString():new Bo(t,n).toString(r),s){const a=e.indexOf("#");a!==-1&&(e=e.slice(0,a)),e+=(e.indexOf("?")===-1?"?":"&")+s}return e}class Ws{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){R.forEach(this.handlers,function(r){r!==null&&t(r)})}}const di={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},tp=typeof URLSearchParams<"u"?URLSearchParams:Bo,np=typeof FormData<"u"?FormData:null,rp=typeof Blob<"u"?Blob:null,op={isBrowser:!0,classes:{URLSearchParams:tp,FormData:np,Blob:rp},protocols:["http","https","file","blob","url","data"]},Uo=typeof window<"u"&&typeof document<"u",Kr=typeof navigator=="object"&&navigator||void 0,sp=Uo&&(!Kr||["ReactNative","NativeScript","NS"].indexOf(Kr.product)<0),ap=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",ip=Uo&&window.location.href||"http://localhost",cp=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Uo,hasStandardBrowserEnv:sp,hasStandardBrowserWebWorkerEnv:ap,navigator:Kr,origin:ip},Symbol.toStringTag,{value:"Module"})),de={...cp,...op};function lp(e,t){return Qn(e,new de.classes.URLSearchParams,{visitor:function(n,r,o,s){return de.isNode&&R.isBuffer(n)?(this.append(r,n.toString("base64")),!1):s.defaultVisitor.apply(this,arguments)},...t})}function up(e){return R.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function dp(e){const t={},n=Object.keys(e);let r;const o=n.length;let s;for(r=0;r<o;r++)s=n[r],t[s]=e[s];return t}function fi(e){function t(n,r,o,s){let a=n[s++];if(a==="__proto__")return!0;const c=Number.isFinite(+a),l=s>=n.length;return a=!a&&R.isArray(o)?o.length:a,l?(R.hasOwnProp(o,a)?o[a]=[o[a],r]:o[a]=r,!c):((!o[a]||!R.isObject(o[a]))&&(o[a]=[]),t(n,r,o[a],s)&&R.isArray(o[a])&&(o[a]=dp(o[a])),!c)}if(R.isFormData(e)&&R.isFunction(e.entries)){const n={};return R.forEachEntry(e,(r,o)=>{t(up(r),o,n,0)}),n}return null}function fp(e,t,n){if(R.isString(e))try{return(t||JSON.parse)(e),R.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const Wo={transitional:di,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",o=r.indexOf("application/json")>-1,s=R.isObject(t);if(s&&R.isHTMLForm(t)&&(t=new FormData(t)),R.isFormData(t))return o?JSON.stringify(fi(t)):t;if(R.isArrayBuffer(t)||R.isBuffer(t)||R.isStream(t)||R.isFile(t)||R.isBlob(t)||R.isReadableStream(t))return t;if(R.isArrayBufferView(t))return t.buffer;if(R.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let c;if(s){if(r.indexOf("application/x-www-form-urlencoded")>-1)return lp(t,this.formSerializer).toString();if((c=R.isFileList(t))||r.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return Qn(c?{"files[]":t}:t,l&&new l,this.formSerializer)}}return s||o?(n.setContentType("application/json",!1),fp(t)):t}],transformResponse:[function(t){const n=this.transitional||Wo.transitional,r=n&&n.forcedJSONParsing,o=this.responseType==="json";if(R.isResponse(t)||R.isReadableStream(t))return t;if(t&&R.isString(t)&&(r&&!this.responseType||o)){const a=!(n&&n.silentJSONParsing)&&o;try{return JSON.parse(t)}catch(c){if(a)throw c.name==="SyntaxError"?B.from(c,B.ERR_BAD_RESPONSE,this,null,this.response):c}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:de.classes.FormData,Blob:de.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};R.forEach(["delete","get","head","post","put","patch"],e=>{Wo.headers[e]={}});const Vo=Wo,pp=R.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),mp=e=>{const t={};let n,r,o;return e&&e.split(`
`).forEach(function(a){o=a.indexOf(":"),n=a.substring(0,o).trim().toLowerCase(),r=a.substring(o+1).trim(),!(!n||t[n]&&pp[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},Vs=Symbol("internals");function Ft(e){return e&&String(e).trim().toLowerCase()}function En(e){return e===!1||e==null?e:R.isArray(e)?e.map(En):String(e)}function hp(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const gp=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Cr(e,t,n,r,o){if(R.isFunction(r))return r.call(this,t,n);if(o&&(t=n),!!R.isString(t)){if(R.isString(r))return t.indexOf(r)!==-1;if(R.isRegExp(r))return r.test(t)}}function vp(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function bp(e,t){const n=R.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(o,s,a){return this[r].call(this,t,o,s,a)},configurable:!0})})}class er{constructor(t){t&&this.set(t)}set(t,n,r){const o=this;function s(c,l,u){const d=Ft(l);if(!d)throw new Error("header name must be a non-empty string");const p=R.findKey(o,d);(!p||o[p]===void 0||u===!0||u===void 0&&o[p]!==!1)&&(o[p||l]=En(c))}const a=(c,l)=>R.forEach(c,(u,d)=>s(u,d,l));if(R.isPlainObject(t)||t instanceof this.constructor)a(t,n);else if(R.isString(t)&&(t=t.trim())&&!gp(t))a(mp(t),n);else if(R.isObject(t)&&R.isIterable(t)){let c={},l,u;for(const d of t){if(!R.isArray(d))throw TypeError("Object iterator must return a key-value pair");c[u=d[0]]=(l=c[u])?R.isArray(l)?[...l,d[1]]:[l,d[1]]:d[1]}a(c,n)}else t!=null&&s(n,t,r);return this}get(t,n){if(t=Ft(t),t){const r=R.findKey(this,t);if(r){const o=this[r];if(!n)return o;if(n===!0)return hp(o);if(R.isFunction(n))return n.call(this,o,r);if(R.isRegExp(n))return n.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=Ft(t),t){const r=R.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||Cr(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let o=!1;function s(a){if(a=Ft(a),a){const c=R.findKey(r,a);c&&(!n||Cr(r,r[c],c,n))&&(delete r[c],o=!0)}}return R.isArray(t)?t.forEach(s):s(t),o}clear(t){const n=Object.keys(this);let r=n.length,o=!1;for(;r--;){const s=n[r];(!t||Cr(this,this[s],s,t,!0))&&(delete this[s],o=!0)}return o}normalize(t){const n=this,r={};return R.forEach(this,(o,s)=>{const a=R.findKey(r,s);if(a){n[a]=En(o),delete n[s];return}const c=t?vp(s):String(s).trim();c!==s&&delete n[s],n[c]=En(o),r[c]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return R.forEach(this,(r,o)=>{r!=null&&r!==!1&&(n[o]=t&&R.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(o=>r.set(o)),r}static accessor(t){const r=(this[Vs]=this[Vs]={accessors:{}}).accessors,o=this.prototype;function s(a){const c=Ft(a);r[c]||(bp(o,a),r[c]=!0)}return R.isArray(t)?t.forEach(s):s(t),this}}er.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);R.reduceDescriptors(er.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});R.freezeMethods(er);const Se=er;function Er(e,t){const n=this||Vo,r=t||n,o=Se.from(r.headers);let s=r.data;return R.forEach(e,function(c){s=c.call(n,s,o.normalize(),t?t.status:void 0)}),o.normalize(),s}function pi(e){return!!(e&&e.__CANCEL__)}function At(e,t,n){B.call(this,e??"canceled",B.ERR_CANCELED,t,n),this.name="CanceledError"}R.inherits(At,B,{__CANCEL__:!0});function mi(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new B("Request failed with status code "+n.status,[B.ERR_BAD_REQUEST,B.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function xp(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function yp(e,t){e=e||10;const n=new Array(e),r=new Array(e);let o=0,s=0,a;return t=t!==void 0?t:1e3,function(l){const u=Date.now(),d=r[s];a||(a=u),n[o]=l,r[o]=u;let p=s,g=0;for(;p!==o;)g+=n[p++],p=p%e;if(o=(o+1)%e,o===s&&(s=(s+1)%e),u-a<t)return;const v=d&&u-d;return v?Math.round(g*1e3/v):void 0}}function wp(e,t){let n=0,r=1e3/t,o,s;const a=(u,d=Date.now())=>{n=d,o=null,s&&(clearTimeout(s),s=null),e(...u)};return[(...u)=>{const d=Date.now(),p=d-n;p>=r?a(u,d):(o=u,s||(s=setTimeout(()=>{s=null,a(o)},r-p)))},()=>o&&a(o)]}const An=(e,t,n=3)=>{let r=0;const o=yp(50,250);return wp(s=>{const a=s.loaded,c=s.lengthComputable?s.total:void 0,l=a-r,u=o(l),d=a<=c;r=a;const p={loaded:a,total:c,progress:c?a/c:void 0,bytes:l,rate:u||void 0,estimated:u&&c&&d?(c-a)/u:void 0,event:s,lengthComputable:c!=null,[t?"download":"upload"]:!0};e(p)},n)},Hs=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},zs=e=>(...t)=>R.asap(()=>e(...t)),Cp=de.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,de.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(de.origin),de.navigator&&/(msie|trident)/i.test(de.navigator.userAgent)):()=>!0,Ep=de.hasStandardBrowserEnv?{write(e,t,n,r,o,s){const a=[e+"="+encodeURIComponent(t)];R.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),R.isString(r)&&a.push("path="+r),R.isString(o)&&a.push("domain="+o),s===!0&&a.push("secure"),document.cookie=a.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Sp(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function $p(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function hi(e,t,n){let r=!Sp(t);return e&&(r||n==!1)?$p(e,t):t}const Gs=e=>e instanceof Se?{...e}:e;function nt(e,t){t=t||{};const n={};function r(u,d,p,g){return R.isPlainObject(u)&&R.isPlainObject(d)?R.merge.call({caseless:g},u,d):R.isPlainObject(d)?R.merge({},d):R.isArray(d)?d.slice():d}function o(u,d,p,g){if(R.isUndefined(d)){if(!R.isUndefined(u))return r(void 0,u,p,g)}else return r(u,d,p,g)}function s(u,d){if(!R.isUndefined(d))return r(void 0,d)}function a(u,d){if(R.isUndefined(d)){if(!R.isUndefined(u))return r(void 0,u)}else return r(void 0,d)}function c(u,d,p){if(p in t)return r(u,d);if(p in e)return r(void 0,u)}const l={url:s,method:s,data:s,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,withXSRFToken:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:c,headers:(u,d,p)=>o(Gs(u),Gs(d),p,!0)};return R.forEach(Object.keys({...e,...t}),function(d){const p=l[d]||o,g=p(e[d],t[d],d);R.isUndefined(g)&&p!==c||(n[d]=g)}),n}const gi=e=>{const t=nt({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:o,xsrfCookieName:s,headers:a,auth:c}=t;t.headers=a=Se.from(a),t.url=ui(hi(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&a.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):"")));let l;if(R.isFormData(n)){if(de.hasStandardBrowserEnv||de.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if((l=a.getContentType())!==!1){const[u,...d]=l?l.split(";").map(p=>p.trim()).filter(Boolean):[];a.setContentType([u||"multipart/form-data",...d].join("; "))}}if(de.hasStandardBrowserEnv&&(r&&R.isFunction(r)&&(r=r(t)),r||r!==!1&&Cp(t.url))){const u=o&&s&&Ep.read(s);u&&a.set(o,u)}return t},Rp=typeof XMLHttpRequest<"u",Pp=Rp&&function(e){return new Promise(function(n,r){const o=gi(e);let s=o.data;const a=Se.from(o.headers).normalize();let{responseType:c,onUploadProgress:l,onDownloadProgress:u}=o,d,p,g,v,h;function m(){v&&v(),h&&h(),o.cancelToken&&o.cancelToken.unsubscribe(d),o.signal&&o.signal.removeEventListener("abort",d)}let b=new XMLHttpRequest;b.open(o.method.toUpperCase(),o.url,!0),b.timeout=o.timeout;function x(){if(!b)return;const w=Se.from("getAllResponseHeaders"in b&&b.getAllResponseHeaders()),S={data:!c||c==="text"||c==="json"?b.responseText:b.response,status:b.status,statusText:b.statusText,headers:w,config:e,request:b};mi(function(E){n(E),m()},function(E){r(E),m()},S),b=null}"onloadend"in b?b.onloadend=x:b.onreadystatechange=function(){!b||b.readyState!==4||b.status===0&&!(b.responseURL&&b.responseURL.indexOf("file:")===0)||setTimeout(x)},b.onabort=function(){b&&(r(new B("Request aborted",B.ECONNABORTED,e,b)),b=null)},b.onerror=function(){r(new B("Network Error",B.ERR_NETWORK,e,b)),b=null},b.ontimeout=function(){let C=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const S=o.transitional||di;o.timeoutErrorMessage&&(C=o.timeoutErrorMessage),r(new B(C,S.clarifyTimeoutError?B.ETIMEDOUT:B.ECONNABORTED,e,b)),b=null},s===void 0&&a.setContentType(null),"setRequestHeader"in b&&R.forEach(a.toJSON(),function(C,S){b.setRequestHeader(S,C)}),R.isUndefined(o.withCredentials)||(b.withCredentials=!!o.withCredentials),c&&c!=="json"&&(b.responseType=o.responseType),u&&([g,h]=An(u,!0),b.addEventListener("progress",g)),l&&b.upload&&([p,v]=An(l),b.upload.addEventListener("progress",p),b.upload.addEventListener("loadend",v)),(o.cancelToken||o.signal)&&(d=w=>{b&&(r(!w||w.type?new At(null,e,b):w),b.abort(),b=null)},o.cancelToken&&o.cancelToken.subscribe(d),o.signal&&(o.signal.aborted?d():o.signal.addEventListener("abort",d)));const y=xp(o.url);if(y&&de.protocols.indexOf(y)===-1){r(new B("Unsupported protocol "+y+":",B.ERR_BAD_REQUEST,e));return}b.send(s||null)})},Np=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,o;const s=function(u){if(!o){o=!0,c();const d=u instanceof Error?u:this.reason;r.abort(d instanceof B?d:new At(d instanceof Error?d.message:d))}};let a=t&&setTimeout(()=>{a=null,s(new B(`timeout ${t} of ms exceeded`,B.ETIMEDOUT))},t);const c=()=>{e&&(a&&clearTimeout(a),a=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(s):u.removeEventListener("abort",s)}),e=null)};e.forEach(u=>u.addEventListener("abort",s));const{signal:l}=r;return l.unsubscribe=()=>R.asap(c),l}},Tp=Np,Ap=function*(e,t){let n=e.byteLength;if(!t||n<t){yield e;return}let r=0,o;for(;r<n;)o=r+t,yield e.slice(r,o),r=o},Op=async function*(e,t){for await(const n of _p(e))yield*Ap(n,t)},_p=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},Ks=(e,t,n,r)=>{const o=Op(e,t);let s=0,a,c=l=>{a||(a=!0,r&&r(l))};return new ReadableStream({async pull(l){try{const{done:u,value:d}=await o.next();if(u){c(),l.close();return}let p=d.byteLength;if(n){let g=s+=p;n(g)}l.enqueue(new Uint8Array(d))}catch(u){throw c(u),u}},cancel(l){return c(l),o.return()}},{highWaterMark:2})},tr=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",vi=tr&&typeof ReadableStream=="function",Mp=tr&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),bi=(e,...t)=>{try{return!!e(...t)}catch{return!1}},jp=vi&&bi(()=>{let e=!1;const t=new Request(de.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),qs=64*1024,qr=vi&&bi(()=>R.isReadableStream(new Response("").body)),On={stream:qr&&(e=>e.body)};tr&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!On[t]&&(On[t]=R.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new B(`Response type '${t}' is not supported`,B.ERR_NOT_SUPPORT,r)})})})(new Response);const Ip=async e=>{if(e==null)return 0;if(R.isBlob(e))return e.size;if(R.isSpecCompliantForm(e))return(await new Request(de.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(R.isArrayBufferView(e)||R.isArrayBuffer(e))return e.byteLength;if(R.isURLSearchParams(e)&&(e=e+""),R.isString(e))return(await Mp(e)).byteLength},Dp=async(e,t)=>{const n=R.toFiniteNumber(e.getContentLength());return n??Ip(t)},kp=tr&&(async e=>{let{url:t,method:n,data:r,signal:o,cancelToken:s,timeout:a,onDownloadProgress:c,onUploadProgress:l,responseType:u,headers:d,withCredentials:p="same-origin",fetchOptions:g}=gi(e);u=u?(u+"").toLowerCase():"text";let v=Tp([o,s&&s.toAbortSignal()],a),h;const m=v&&v.unsubscribe&&(()=>{v.unsubscribe()});let b;try{if(l&&jp&&n!=="get"&&n!=="head"&&(b=await Dp(d,r))!==0){let S=new Request(t,{method:"POST",body:r,duplex:"half"}),P;if(R.isFormData(r)&&(P=S.headers.get("content-type"))&&d.setContentType(P),S.body){const[E,$]=Hs(b,An(zs(l)));r=Ks(S.body,qs,E,$)}}R.isString(p)||(p=p?"include":"omit");const x="credentials"in Request.prototype;h=new Request(t,{...g,signal:v,method:n.toUpperCase(),headers:d.normalize().toJSON(),body:r,duplex:"half",credentials:x?p:void 0});let y=await fetch(h,g);const w=qr&&(u==="stream"||u==="response");if(qr&&(c||w&&m)){const S={};["status","statusText","headers"].forEach(N=>{S[N]=y[N]});const P=R.toFiniteNumber(y.headers.get("content-length")),[E,$]=c&&Hs(P,An(zs(c),!0))||[];y=new Response(Ks(y.body,qs,E,()=>{$&&$(),m&&m()}),S)}u=u||"text";let C=await On[R.findKey(On,u)||"text"](y,e);return!w&&m&&m(),await new Promise((S,P)=>{mi(S,P,{data:C,headers:Se.from(y.headers),status:y.status,statusText:y.statusText,config:e,request:h})})}catch(x){throw m&&m(),x&&x.name==="TypeError"&&/Load failed|fetch/i.test(x.message)?Object.assign(new B("Network Error",B.ERR_NETWORK,e,h),{cause:x.cause||x}):B.from(x,x&&x.code,e,h)}}),Yr={http:Jf,xhr:Pp,fetch:kp};R.forEach(Yr,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Ys=e=>`- ${e}`,Lp=e=>R.isFunction(e)||e===null||e===!1,xi={getAdapter:e=>{e=R.isArray(e)?e:[e];const{length:t}=e;let n,r;const o={};for(let s=0;s<t;s++){n=e[s];let a;if(r=n,!Lp(n)&&(r=Yr[(a=String(n)).toLowerCase()],r===void 0))throw new B(`Unknown adapter '${a}'`);if(r)break;o[a||"#"+s]=r}if(!r){const s=Object.entries(o).map(([c,l])=>`adapter ${c} `+(l===!1?"is not supported by the environment":"is not available in the build"));let a=t?s.length>1?`since :
`+s.map(Ys).join(`
`):" "+Ys(s[0]):"as no adapter specified";throw new B("There is no suitable adapter to dispatch the request "+a,"ERR_NOT_SUPPORT")}return r},adapters:Yr};function Sr(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new At(null,e)}function Xs(e){return Sr(e),e.headers=Se.from(e.headers),e.data=Er.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),xi.getAdapter(e.adapter||Vo.adapter)(e).then(function(r){return Sr(e),r.data=Er.call(e,e.transformResponse,r),r.headers=Se.from(r.headers),r},function(r){return pi(r)||(Sr(e),r&&r.response&&(r.response.data=Er.call(e,e.transformResponse,r.response),r.response.headers=Se.from(r.response.headers))),Promise.reject(r)})}const yi="1.11.0",nr={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{nr[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Js={};nr.transitional=function(t,n,r){function o(s,a){return"[Axios v"+yi+"] Transitional option '"+s+"'"+a+(r?". "+r:"")}return(s,a,c)=>{if(t===!1)throw new B(o(a," has been removed"+(n?" in "+n:"")),B.ERR_DEPRECATED);return n&&!Js[a]&&(Js[a]=!0,console.warn(o(a," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(s,a,c):!0}};nr.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function Fp(e,t,n){if(typeof e!="object")throw new B("options must be an object",B.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let o=r.length;for(;o-- >0;){const s=r[o],a=t[s];if(a){const c=e[s],l=c===void 0||a(c,s,e);if(l!==!0)throw new B("option "+s+" must be "+l,B.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new B("Unknown option "+s,B.ERR_BAD_OPTION)}}const Sn={assertOptions:Fp,validators:nr},Te=Sn.validators;class _n{constructor(t){this.defaults=t||{},this.interceptors={request:new Ws,response:new Ws}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const s=o.stack?o.stack.replace(/^.+\n/,""):"";try{r.stack?s&&!String(r.stack).endsWith(s.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+s):r.stack=s}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=nt(this.defaults,n);const{transitional:r,paramsSerializer:o,headers:s}=n;r!==void 0&&Sn.assertOptions(r,{silentJSONParsing:Te.transitional(Te.boolean),forcedJSONParsing:Te.transitional(Te.boolean),clarifyTimeoutError:Te.transitional(Te.boolean)},!1),o!=null&&(R.isFunction(o)?n.paramsSerializer={serialize:o}:Sn.assertOptions(o,{encode:Te.function,serialize:Te.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),Sn.assertOptions(n,{baseUrl:Te.spelling("baseURL"),withXsrfToken:Te.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let a=s&&R.merge(s.common,s[n.method]);s&&R.forEach(["delete","get","head","post","put","patch","common"],h=>{delete s[h]}),n.headers=Se.concat(a,s);const c=[];let l=!0;this.interceptors.request.forEach(function(m){typeof m.runWhen=="function"&&m.runWhen(n)===!1||(l=l&&m.synchronous,c.unshift(m.fulfilled,m.rejected))});const u=[];this.interceptors.response.forEach(function(m){u.push(m.fulfilled,m.rejected)});let d,p=0,g;if(!l){const h=[Xs.bind(this),void 0];for(h.unshift(...c),h.push(...u),g=h.length,d=Promise.resolve(n);p<g;)d=d.then(h[p++],h[p++]);return d}g=c.length;let v=n;for(p=0;p<g;){const h=c[p++],m=c[p++];try{v=h(v)}catch(b){m.call(this,b);break}}try{d=Xs.call(this,v)}catch(h){return Promise.reject(h)}for(p=0,g=u.length;p<g;)d=d.then(u[p++],u[p++]);return d}getUri(t){t=nt(this.defaults,t);const n=hi(t.baseURL,t.url,t.allowAbsoluteUrls);return ui(n,t.params,t.paramsSerializer)}}R.forEach(["delete","get","head","options"],function(t){_n.prototype[t]=function(n,r){return this.request(nt(r||{},{method:t,url:n,data:(r||{}).data}))}});R.forEach(["post","put","patch"],function(t){function n(r){return function(s,a,c){return this.request(nt(c||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:s,data:a}))}}_n.prototype[t]=n(),_n.prototype[t+"Form"]=n(!0)});const $n=_n;class Ho{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(s){n=s});const r=this;this.promise.then(o=>{if(!r._listeners)return;let s=r._listeners.length;for(;s-- >0;)r._listeners[s](o);r._listeners=null}),this.promise.then=o=>{let s;const a=new Promise(c=>{r.subscribe(c),s=c}).then(o);return a.cancel=function(){r.unsubscribe(s)},a},t(function(s,a,c){r.reason||(r.reason=new At(s,a,c),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new Ho(function(o){t=o}),cancel:t}}}const Bp=Ho;function Up(e){return function(n){return e.apply(null,n)}}function Wp(e){return R.isObject(e)&&e.isAxiosError===!0}const Xr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Xr).forEach(([e,t])=>{Xr[t]=e});const Vp=Xr;function wi(e){const t=new $n(e),n=Za($n.prototype.request,t);return R.extend(n,$n.prototype,t,{allOwnKeys:!0}),R.extend(n,t,null,{allOwnKeys:!0}),n.create=function(o){return wi(nt(e,o))},n}const ne=wi(Vo);ne.Axios=$n;ne.CanceledError=At;ne.CancelToken=Bp;ne.isCancel=pi;ne.VERSION=yi;ne.toFormData=Qn;ne.AxiosError=B;ne.Cancel=ne.CanceledError;ne.all=function(t){return Promise.all(t)};ne.spread=Up;ne.isAxiosError=Wp;ne.mergeConfig=nt;ne.AxiosHeaders=Se;ne.formToJSON=e=>fi(R.isHTMLForm(e)?new FormData(e):e);ne.getAdapter=xi.getAdapter;ne.HttpStatusCode=Vp;ne.default=ne;const Ci=i.forwardRef(({className:e,type:t,...n},r)=>f.jsx("input",{type:t,className:U("flex h-10 w-full rounded-md border-2 border-[#7b7b7b7b] bg-[var(--input-background)] px-3 py-2 text-sm text-[var(--input-text)] placeholder-[var(--paragraph)] ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus:border-[var(--input-border-color)] focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...n}));Ci.displayName="Input";function Mn(e,[t,n]){return Math.min(n,Math.max(t,e))}function oe(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function zo(e,t=[]){let n=[];function r(s,a){const c=i.createContext(a),l=n.length;n=[...n,a];function u(p){const{scope:g,children:v,...h}=p,m=(g==null?void 0:g[e][l])||c,b=i.useMemo(()=>h,Object.values(h));return f.jsx(m.Provider,{value:b,children:v})}function d(p,g){const v=(g==null?void 0:g[e][l])||c,h=i.useContext(v);if(h)return h;if(a!==void 0)return a;throw new Error(`\`${p}\` must be used within \`${s}\``)}return u.displayName=s+"Provider",[u,d]}const o=()=>{const s=n.map(a=>i.createContext(a));return function(c){const l=(c==null?void 0:c[e])||s;return i.useMemo(()=>({[`__scope${e}`]:{...c,[e]:l}}),[c,l])}};return o.scopeName=e,[r,Hp(o,...t)]}function Hp(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const a=r.reduce((c,{useScope:l,scopeName:u})=>{const p=l(s)[`__scope${u}`];return{...c,...p}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return n.scopeName=t.scopeName,n}function zp(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function Ei(...e){return t=>e.forEach(n=>zp(n,t))}function ie(...e){return i.useCallback(Ei(...e),e)}var Gt=i.forwardRef((e,t)=>{const{children:n,...r}=e,o=i.Children.toArray(n),s=o.find(Kp);if(s){const a=s.props.children,c=o.map(l=>l===s?i.Children.count(a)>1?i.Children.only(null):i.isValidElement(a)?a.props.children:null:l);return f.jsx(Jr,{...r,ref:t,children:i.isValidElement(a)?i.cloneElement(a,void 0,c):null})}return f.jsx(Jr,{...r,ref:t,children:n})});Gt.displayName="Slot";var Jr=i.forwardRef((e,t)=>{const{children:n,...r}=e;if(i.isValidElement(n)){const o=Yp(n);return i.cloneElement(n,{...qp(r,n.props),ref:t?Ei(t,o):o})}return i.Children.count(n)>1?i.Children.only(null):null});Jr.displayName="SlotClone";var Gp=({children:e})=>f.jsx(f.Fragment,{children:e});function Kp(e){return i.isValidElement(e)&&e.type===Gp}function qp(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...c)=>{s(...c),o(...c)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function Yp(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Xp(e){const t=e+"CollectionProvider",[n,r]=zo(t),[o,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=v=>{const{scope:h,children:m}=v,b=V.useRef(null),x=V.useRef(new Map).current;return f.jsx(o,{scope:h,itemMap:x,collectionRef:b,children:m})};a.displayName=t;const c=e+"CollectionSlot",l=V.forwardRef((v,h)=>{const{scope:m,children:b}=v,x=s(c,m),y=ie(h,x.collectionRef);return f.jsx(Gt,{ref:y,children:b})});l.displayName=c;const u=e+"CollectionItemSlot",d="data-radix-collection-item",p=V.forwardRef((v,h)=>{const{scope:m,children:b,...x}=v,y=V.useRef(null),w=ie(h,y),C=s(u,m);return V.useEffect(()=>(C.itemMap.set(y,{ref:y,...x}),()=>void C.itemMap.delete(y))),f.jsx(Gt,{[d]:"",ref:w,children:b})});p.displayName=u;function g(v){const h=s(e+"CollectionConsumer",v);return V.useCallback(()=>{const b=h.collectionRef.current;if(!b)return[];const x=Array.from(b.querySelectorAll(`[${d}]`));return Array.from(h.itemMap.values()).sort((C,S)=>x.indexOf(C.ref.current)-x.indexOf(S.ref.current))},[h.collectionRef,h.itemMap])}return[{Provider:a,Slot:l,ItemSlot:p},g,r]}var Jp=i.createContext(void 0);function Zp(e){const t=i.useContext(Jp);return e||t||"ltr"}var Qp=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],re=Qp.reduce((e,t)=>{const n=i.forwardRef((r,o)=>{const{asChild:s,...a}=r,c=s?Gt:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),f.jsx(c,{...a,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function em(e,t){e&&Me.flushSync(()=>e.dispatchEvent(t))}function Le(e){const t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function tm(e,t=globalThis==null?void 0:globalThis.document){const n=Le(e);i.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var nm="DismissableLayer",Zr="dismissableLayer.update",rm="dismissableLayer.pointerDownOutside",om="dismissableLayer.focusOutside",Zs,Si=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),$i=i.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:s,onInteractOutside:a,onDismiss:c,...l}=e,u=i.useContext(Si),[d,p]=i.useState(null),g=(d==null?void 0:d.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,v]=i.useState({}),h=ie(t,E=>p(E)),m=Array.from(u.layers),[b]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),x=m.indexOf(b),y=d?m.indexOf(d):-1,w=u.layersWithOutsidePointerEventsDisabled.size>0,C=y>=x,S=im(E=>{const $=E.target,N=[...u.branches].some(A=>A.contains($));!C||N||(o==null||o(E),a==null||a(E),E.defaultPrevented||c==null||c())},g),P=cm(E=>{const $=E.target;[...u.branches].some(A=>A.contains($))||(s==null||s(E),a==null||a(E),E.defaultPrevented||c==null||c())},g);return tm(E=>{y===u.layers.size-1&&(r==null||r(E),!E.defaultPrevented&&c&&(E.preventDefault(),c()))},g),i.useEffect(()=>{if(d)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(Zs=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(d)),u.layers.add(d),Qs(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(g.body.style.pointerEvents=Zs)}},[d,g,n,u]),i.useEffect(()=>()=>{d&&(u.layers.delete(d),u.layersWithOutsidePointerEventsDisabled.delete(d),Qs())},[d,u]),i.useEffect(()=>{const E=()=>v({});return document.addEventListener(Zr,E),()=>document.removeEventListener(Zr,E)},[]),f.jsx(re.div,{...l,ref:h,style:{pointerEvents:w?C?"auto":"none":void 0,...e.style},onFocusCapture:oe(e.onFocusCapture,P.onFocusCapture),onBlurCapture:oe(e.onBlurCapture,P.onBlurCapture),onPointerDownCapture:oe(e.onPointerDownCapture,S.onPointerDownCapture)})});$i.displayName=nm;var sm="DismissableLayerBranch",am=i.forwardRef((e,t)=>{const n=i.useContext(Si),r=i.useRef(null),o=ie(t,r);return i.useEffect(()=>{const s=r.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),f.jsx(re.div,{...e,ref:o})});am.displayName=sm;function im(e,t=globalThis==null?void 0:globalThis.document){const n=Le(e),r=i.useRef(!1),o=i.useRef(()=>{});return i.useEffect(()=>{const s=c=>{if(c.target&&!r.current){let l=function(){Ri(rm,n,u,{discrete:!0})};const u={originalEvent:c};c.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=l,t.addEventListener("click",o.current,{once:!0})):l()}else t.removeEventListener("click",o.current);r.current=!1},a=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(a),t.removeEventListener("pointerdown",s),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function cm(e,t=globalThis==null?void 0:globalThis.document){const n=Le(e),r=i.useRef(!1);return i.useEffect(()=>{const o=s=>{s.target&&!r.current&&Ri(om,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Qs(){const e=new CustomEvent(Zr);document.dispatchEvent(e)}function Ri(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?em(o,s):o.dispatchEvent(s)}var $r=0;function lm(){i.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??ea()),document.body.insertAdjacentElement("beforeend",e[1]??ea()),$r++,()=>{$r===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),$r--}},[])}function ea(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}var Rr="focusScope.autoFocusOnMount",Pr="focusScope.autoFocusOnUnmount",ta={bubbles:!1,cancelable:!0},um="FocusScope",Pi=i.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:s,...a}=e,[c,l]=i.useState(null),u=Le(o),d=Le(s),p=i.useRef(null),g=ie(t,m=>l(m)),v=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(r){let m=function(w){if(v.paused||!c)return;const C=w.target;c.contains(C)?p.current=C:He(p.current,{select:!0})},b=function(w){if(v.paused||!c)return;const C=w.relatedTarget;C!==null&&(c.contains(C)||He(p.current,{select:!0}))},x=function(w){if(document.activeElement===document.body)for(const S of w)S.removedNodes.length>0&&He(c)};document.addEventListener("focusin",m),document.addEventListener("focusout",b);const y=new MutationObserver(x);return c&&y.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",m),document.removeEventListener("focusout",b),y.disconnect()}}},[r,c,v.paused]),i.useEffect(()=>{if(c){ra.add(v);const m=document.activeElement;if(!c.contains(m)){const x=new CustomEvent(Rr,ta);c.addEventListener(Rr,u),c.dispatchEvent(x),x.defaultPrevented||(dm(gm(Ni(c)),{select:!0}),document.activeElement===m&&He(c))}return()=>{c.removeEventListener(Rr,u),setTimeout(()=>{const x=new CustomEvent(Pr,ta);c.addEventListener(Pr,d),c.dispatchEvent(x),x.defaultPrevented||He(m??document.body,{select:!0}),c.removeEventListener(Pr,d),ra.remove(v)},0)}}},[c,u,d,v]);const h=i.useCallback(m=>{if(!n&&!r||v.paused)return;const b=m.key==="Tab"&&!m.altKey&&!m.ctrlKey&&!m.metaKey,x=document.activeElement;if(b&&x){const y=m.currentTarget,[w,C]=fm(y);w&&C?!m.shiftKey&&x===C?(m.preventDefault(),n&&He(w,{select:!0})):m.shiftKey&&x===w&&(m.preventDefault(),n&&He(C,{select:!0})):x===y&&m.preventDefault()}},[n,r,v.paused]);return f.jsx(re.div,{tabIndex:-1,...a,ref:g,onKeyDown:h})});Pi.displayName=um;function dm(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(He(r,{select:t}),document.activeElement!==n)return}function fm(e){const t=Ni(e),n=na(t,e),r=na(t.reverse(),e);return[n,r]}function Ni(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function na(e,t){for(const n of e)if(!pm(n,{upTo:t}))return n}function pm(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function mm(e){return e instanceof HTMLInputElement&&"select"in e}function He(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&mm(e)&&t&&e.select()}}var ra=hm();function hm(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=oa(e,t),e.unshift(t)},remove(t){var n;e=oa(e,t),(n=e[0])==null||n.resume()}}}function oa(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function gm(e){return e.filter(t=>t.tagName!=="A")}var xe=globalThis!=null&&globalThis.document?i.useLayoutEffect:()=>{},vm=Jt.useId||(()=>{}),bm=0;function Go(e){const[t,n]=i.useState(vm());return xe(()=>{e||n(r=>r??String(bm++))},[e]),e||(t?`radix-${t}`:"")}const xm=["top","right","bottom","left"],Xe=Math.min,ge=Math.max,jn=Math.round,cn=Math.floor,Je=e=>({x:e,y:e}),ym={left:"right",right:"left",bottom:"top",top:"bottom"},wm={start:"end",end:"start"};function Qr(e,t,n){return ge(e,Xe(t,n))}function Fe(e,t){return typeof e=="function"?e(t):e}function Be(e){return e.split("-")[0]}function Ot(e){return e.split("-")[1]}function Ko(e){return e==="x"?"y":"x"}function qo(e){return e==="y"?"height":"width"}function _t(e){return["top","bottom"].includes(Be(e))?"y":"x"}function Yo(e){return Ko(_t(e))}function Cm(e,t,n){n===void 0&&(n=!1);const r=Ot(e),o=Yo(e),s=qo(o);let a=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(a=In(a)),[a,In(a)]}function Em(e){const t=In(e);return[eo(e),t,eo(t)]}function eo(e){return e.replace(/start|end/g,t=>wm[t])}function Sm(e,t,n){const r=["left","right"],o=["right","left"],s=["top","bottom"],a=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?s:a;default:return[]}}function $m(e,t,n,r){const o=Ot(e);let s=Sm(Be(e),n==="start",r);return o&&(s=s.map(a=>a+"-"+o),t&&(s=s.concat(s.map(eo)))),s}function In(e){return e.replace(/left|right|bottom|top/g,t=>ym[t])}function Rm(e){return{top:0,right:0,bottom:0,left:0,...e}}function Ti(e){return typeof e!="number"?Rm(e):{top:e,right:e,bottom:e,left:e}}function Dn(e){return{...e,top:e.y,left:e.x,right:e.x+e.width,bottom:e.y+e.height}}function sa(e,t,n){let{reference:r,floating:o}=e;const s=_t(t),a=Yo(t),c=qo(a),l=Be(t),u=s==="y",d=r.x+r.width/2-o.width/2,p=r.y+r.height/2-o.height/2,g=r[c]/2-o[c]/2;let v;switch(l){case"top":v={x:d,y:r.y-o.height};break;case"bottom":v={x:d,y:r.y+r.height};break;case"right":v={x:r.x+r.width,y:p};break;case"left":v={x:r.x-o.width,y:p};break;default:v={x:r.x,y:r.y}}switch(Ot(t)){case"start":v[a]-=g*(n&&u?-1:1);break;case"end":v[a]+=g*(n&&u?-1:1);break}return v}const Pm=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:s=[],platform:a}=n,c=s.filter(Boolean),l=await(a.isRTL==null?void 0:a.isRTL(t));let u=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:p}=sa(u,r,l),g=r,v={},h=0;for(let m=0;m<c.length;m++){const{name:b,fn:x}=c[m],{x:y,y:w,data:C,reset:S}=await x({x:d,y:p,initialPlacement:r,placement:g,strategy:o,middlewareData:v,rects:u,platform:a,elements:{reference:e,floating:t}});d=y??d,p=w??p,v={...v,[b]:{...v[b],...C}},S&&h<=50&&(h++,typeof S=="object"&&(S.placement&&(g=S.placement),S.rects&&(u=S.rects===!0?await a.getElementRects({reference:e,floating:t,strategy:o}):S.rects),{x:d,y:p}=sa(u,g,l)),m=-1)}return{x:d,y:p,placement:g,strategy:o,middlewareData:v}};async function Kt(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:s,rects:a,elements:c,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:d="viewport",elementContext:p="floating",altBoundary:g=!1,padding:v=0}=Fe(t,e),h=Ti(v),b=c[g?p==="floating"?"reference":"floating":p],x=Dn(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(b)))==null||n?b:b.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(c.floating)),boundary:u,rootBoundary:d,strategy:l})),y=p==="floating"?{...a.floating,x:r,y:o}:a.reference,w=await(s.getOffsetParent==null?void 0:s.getOffsetParent(c.floating)),C=await(s.isElement==null?void 0:s.isElement(w))?await(s.getScale==null?void 0:s.getScale(w))||{x:1,y:1}:{x:1,y:1},S=Dn(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:y,offsetParent:w,strategy:l}):y);return{top:(x.top-S.top+h.top)/C.y,bottom:(S.bottom-x.bottom+h.bottom)/C.y,left:(x.left-S.left+h.left)/C.x,right:(S.right-x.right+h.right)/C.x}}const Nm=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:s,platform:a,elements:c,middlewareData:l}=t,{element:u,padding:d=0}=Fe(e,t)||{};if(u==null)return{};const p=Ti(d),g={x:n,y:r},v=Yo(o),h=qo(v),m=await a.getDimensions(u),b=v==="y",x=b?"top":"left",y=b?"bottom":"right",w=b?"clientHeight":"clientWidth",C=s.reference[h]+s.reference[v]-g[v]-s.floating[h],S=g[v]-s.reference[v],P=await(a.getOffsetParent==null?void 0:a.getOffsetParent(u));let E=P?P[w]:0;(!E||!await(a.isElement==null?void 0:a.isElement(P)))&&(E=c.floating[w]||s.floating[h]);const $=C/2-S/2,N=E/2-m[h]/2-1,A=Xe(p[x],N),O=Xe(p[y],N),_=A,L=E-m[h]-O,D=E/2-m[h]/2+$,M=Qr(_,D,L),k=!l.arrow&&Ot(o)!=null&&D!==M&&s.reference[h]/2-(D<_?A:O)-m[h]/2<0,j=k?D<_?D-_:D-L:0;return{[v]:g[v]+j,data:{[v]:M,centerOffset:D-M-j,...k&&{alignmentOffset:j}},reset:k}}}),Tm=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:s,rects:a,initialPlacement:c,platform:l,elements:u}=t,{mainAxis:d=!0,crossAxis:p=!0,fallbackPlacements:g,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:h="none",flipAlignment:m=!0,...b}=Fe(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const x=Be(o),y=Be(c)===c,w=await(l.isRTL==null?void 0:l.isRTL(u.floating)),C=g||(y||!m?[In(c)]:Em(c));!g&&h!=="none"&&C.push(...$m(c,m,h,w));const S=[c,...C],P=await Kt(t,b),E=[];let $=((r=s.flip)==null?void 0:r.overflows)||[];if(d&&E.push(P[x]),p){const _=Cm(o,a,w);E.push(P[_[0]],P[_[1]])}if($=[...$,{placement:o,overflows:E}],!E.every(_=>_<=0)){var N,A;const _=(((N=s.flip)==null?void 0:N.index)||0)+1,L=S[_];if(L)return{data:{index:_,overflows:$},reset:{placement:L}};let D=(A=$.filter(M=>M.overflows[0]<=0).sort((M,k)=>M.overflows[1]-k.overflows[1])[0])==null?void 0:A.placement;if(!D)switch(v){case"bestFit":{var O;const M=(O=$.map(k=>[k.placement,k.overflows.filter(j=>j>0).reduce((j,F)=>j+F,0)]).sort((k,j)=>k[1]-j[1])[0])==null?void 0:O[0];M&&(D=M);break}case"initialPlacement":D=c;break}if(o!==D)return{reset:{placement:D}}}return{}}}};function aa(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ia(e){return xm.some(t=>e[t]>=0)}const Am=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=Fe(e,t);switch(r){case"referenceHidden":{const s=await Kt(t,{...o,elementContext:"reference"}),a=aa(s,n.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:ia(a)}}}case"escaped":{const s=await Kt(t,{...o,altBoundary:!0}),a=aa(s,n.floating);return{data:{escapedOffsets:a,escaped:ia(a)}}}default:return{}}}}};async function Om(e,t){const{placement:n,platform:r,elements:o}=e,s=await(r.isRTL==null?void 0:r.isRTL(o.floating)),a=Be(n),c=Ot(n),l=_t(n)==="y",u=["left","top"].includes(a)?-1:1,d=s&&l?-1:1,p=Fe(t,e);let{mainAxis:g,crossAxis:v,alignmentAxis:h}=typeof p=="number"?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...p};return c&&typeof h=="number"&&(v=c==="end"?h*-1:h),l?{x:v*d,y:g*u}:{x:g*u,y:v*d}}const Xo=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:s,placement:a,middlewareData:c}=t,l=await Om(t,e);return a===((n=c.offset)==null?void 0:n.placement)&&(r=c.arrow)!=null&&r.alignmentOffset?{}:{x:o+l.x,y:s+l.y,data:{...l,placement:a}}}}},_m=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:s=!0,crossAxis:a=!1,limiter:c={fn:b=>{let{x,y}=b;return{x,y}}},...l}=Fe(e,t),u={x:n,y:r},d=await Kt(t,l),p=_t(Be(o)),g=Ko(p);let v=u[g],h=u[p];if(s){const b=g==="y"?"top":"left",x=g==="y"?"bottom":"right",y=v+d[b],w=v-d[x];v=Qr(y,v,w)}if(a){const b=p==="y"?"top":"left",x=p==="y"?"bottom":"right",y=h+d[b],w=h-d[x];h=Qr(y,h,w)}const m=c.fn({...t,[g]:v,[p]:h});return{...m,data:{x:m.x-n,y:m.y-r}}}}},Mm=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:s,middlewareData:a}=t,{offset:c=0,mainAxis:l=!0,crossAxis:u=!0}=Fe(e,t),d={x:n,y:r},p=_t(o),g=Ko(p);let v=d[g],h=d[p];const m=Fe(c,t),b=typeof m=="number"?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(l){const w=g==="y"?"height":"width",C=s.reference[g]-s.floating[w]+b.mainAxis,S=s.reference[g]+s.reference[w]-b.mainAxis;v<C?v=C:v>S&&(v=S)}if(u){var x,y;const w=g==="y"?"width":"height",C=["top","left"].includes(Be(o)),S=s.reference[p]-s.floating[w]+(C&&((x=a.offset)==null?void 0:x[p])||0)+(C?0:b.crossAxis),P=s.reference[p]+s.reference[w]+(C?0:((y=a.offset)==null?void 0:y[p])||0)-(C?b.crossAxis:0);h<S?h=S:h>P&&(h=P)}return{[g]:v,[p]:h}}}},jm=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){const{placement:n,rects:r,platform:o,elements:s}=t,{apply:a=()=>{},...c}=Fe(e,t),l=await Kt(t,c),u=Be(n),d=Ot(n),p=_t(n)==="y",{width:g,height:v}=r.floating;let h,m;u==="top"||u==="bottom"?(h=u,m=d===(await(o.isRTL==null?void 0:o.isRTL(s.floating))?"start":"end")?"left":"right"):(m=u,h=d==="end"?"top":"bottom");const b=v-l[h],x=g-l[m],y=!t.middlewareData.shift;let w=b,C=x;if(p){const P=g-l.left-l.right;C=d||y?Xe(x,P):P}else{const P=v-l.top-l.bottom;w=d||y?Xe(b,P):P}if(y&&!d){const P=ge(l.left,0),E=ge(l.right,0),$=ge(l.top,0),N=ge(l.bottom,0);p?C=g-2*(P!==0||E!==0?P+E:ge(l.left,l.right)):w=v-2*($!==0||N!==0?$+N:ge(l.top,l.bottom))}await a({...t,availableWidth:C,availableHeight:w});const S=await o.getDimensions(s.floating);return g!==S.width||v!==S.height?{reset:{rects:!0}}:{}}}};function Ze(e){return Ai(e)?(e.nodeName||"").toLowerCase():"#document"}function ve(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function We(e){var t;return(t=(Ai(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Ai(e){return e instanceof Node||e instanceof ve(e).Node}function Ue(e){return e instanceof Element||e instanceof ve(e).Element}function Oe(e){return e instanceof HTMLElement||e instanceof ve(e).HTMLElement}function ca(e){return typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof ve(e).ShadowRoot}function tn(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=ye(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function Im(e){return["table","td","th"].includes(Ze(e))}function Jo(e){const t=Zo(),n=ye(e);return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function Dm(e){let t=$t(e);for(;Oe(t)&&!rr(t);){if(Jo(t))return t;t=$t(t)}return null}function Zo(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function rr(e){return["html","body","#document"].includes(Ze(e))}function ye(e){return ve(e).getComputedStyle(e)}function or(e){return Ue(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function $t(e){if(Ze(e)==="html")return e;const t=e.assignedSlot||e.parentNode||ca(e)&&e.host||We(e);return ca(t)?t.host:t}function Oi(e){const t=$t(e);return rr(t)?e.ownerDocument?e.ownerDocument.body:e.body:Oe(t)&&tn(t)?t:Oi(t)}function qt(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Oi(e),s=o===((r=e.ownerDocument)==null?void 0:r.body),a=ve(o);return s?t.concat(a,a.visualViewport||[],tn(o)?o:[],a.frameElement&&n?qt(a.frameElement):[]):t.concat(o,qt(o,[],n))}function _i(e){const t=ye(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=Oe(e),s=o?e.offsetWidth:n,a=o?e.offsetHeight:r,c=jn(n)!==s||jn(r)!==a;return c&&(n=s,r=a),{width:n,height:r,$:c}}function Qo(e){return Ue(e)?e:e.contextElement}function yt(e){const t=Qo(e);if(!Oe(t))return Je(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:s}=_i(t);let a=(s?jn(n.width):n.width)/r,c=(s?jn(n.height):n.height)/o;return(!a||!Number.isFinite(a))&&(a=1),(!c||!Number.isFinite(c))&&(c=1),{x:a,y:c}}const km=Je(0);function Mi(e){const t=ve(e);return!Zo()||!t.visualViewport?km:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Lm(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==ve(e)?!1:t}function rt(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),s=Qo(e);let a=Je(1);t&&(r?Ue(r)&&(a=yt(r)):a=yt(e));const c=Lm(s,n,r)?Mi(s):Je(0);let l=(o.left+c.x)/a.x,u=(o.top+c.y)/a.y,d=o.width/a.x,p=o.height/a.y;if(s){const g=ve(s),v=r&&Ue(r)?ve(r):r;let h=g,m=h.frameElement;for(;m&&r&&v!==h;){const b=yt(m),x=m.getBoundingClientRect(),y=ye(m),w=x.left+(m.clientLeft+parseFloat(y.paddingLeft))*b.x,C=x.top+(m.clientTop+parseFloat(y.paddingTop))*b.y;l*=b.x,u*=b.y,d*=b.x,p*=b.y,l+=w,u+=C,h=ve(m),m=h.frameElement}}return Dn({width:d,height:p,x:l,y:u})}const Fm=[":popover-open",":modal"];function ji(e){return Fm.some(t=>{try{return e.matches(t)}catch{return!1}})}function Bm(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const s=o==="fixed",a=We(r),c=t?ji(t.floating):!1;if(r===a||c&&s)return n;let l={scrollLeft:0,scrollTop:0},u=Je(1);const d=Je(0),p=Oe(r);if((p||!p&&!s)&&((Ze(r)!=="body"||tn(a))&&(l=or(r)),Oe(r))){const g=rt(r);u=yt(r),d.x=g.x+r.clientLeft,d.y=g.y+r.clientTop}return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-l.scrollLeft*u.x+d.x,y:n.y*u.y-l.scrollTop*u.y+d.y}}function Um(e){return Array.from(e.getClientRects())}function Ii(e){return rt(We(e)).left+or(e).scrollLeft}function Wm(e){const t=We(e),n=or(e),r=e.ownerDocument.body,o=ge(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),s=ge(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let a=-n.scrollLeft+Ii(e);const c=-n.scrollTop;return ye(r).direction==="rtl"&&(a+=ge(t.clientWidth,r.clientWidth)-o),{width:o,height:s,x:a,y:c}}function Vm(e,t){const n=ve(e),r=We(e),o=n.visualViewport;let s=r.clientWidth,a=r.clientHeight,c=0,l=0;if(o){s=o.width,a=o.height;const u=Zo();(!u||u&&t==="fixed")&&(c=o.offsetLeft,l=o.offsetTop)}return{width:s,height:a,x:c,y:l}}function Hm(e,t){const n=rt(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,s=Oe(e)?yt(e):Je(1),a=e.clientWidth*s.x,c=e.clientHeight*s.y,l=o*s.x,u=r*s.y;return{width:a,height:c,x:l,y:u}}function la(e,t,n){let r;if(t==="viewport")r=Vm(e,n);else if(t==="document")r=Wm(We(e));else if(Ue(t))r=Hm(t,n);else{const o=Mi(e);r={...t,x:t.x-o.x,y:t.y-o.y}}return Dn(r)}function Di(e,t){const n=$t(e);return n===t||!Ue(n)||rr(n)?!1:ye(n).position==="fixed"||Di(n,t)}function zm(e,t){const n=t.get(e);if(n)return n;let r=qt(e,[],!1).filter(c=>Ue(c)&&Ze(c)!=="body"),o=null;const s=ye(e).position==="fixed";let a=s?$t(e):e;for(;Ue(a)&&!rr(a);){const c=ye(a),l=Jo(a);!l&&c.position==="fixed"&&(o=null),(s?!l&&!o:!l&&c.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||tn(a)&&!l&&Di(e,a))?r=r.filter(d=>d!==a):o=c,a=$t(a)}return t.set(e,r),r}function Gm(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const a=[...n==="clippingAncestors"?zm(t,this._c):[].concat(n),r],c=a[0],l=a.reduce((u,d)=>{const p=la(t,d,o);return u.top=ge(p.top,u.top),u.right=Xe(p.right,u.right),u.bottom=Xe(p.bottom,u.bottom),u.left=ge(p.left,u.left),u},la(t,c,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function Km(e){const{width:t,height:n}=_i(e);return{width:t,height:n}}function qm(e,t,n){const r=Oe(t),o=We(t),s=n==="fixed",a=rt(e,!0,s,t);let c={scrollLeft:0,scrollTop:0};const l=Je(0);if(r||!r&&!s)if((Ze(t)!=="body"||tn(o))&&(c=or(t)),r){const p=rt(t,!0,s,t);l.x=p.x+t.clientLeft,l.y=p.y+t.clientTop}else o&&(l.x=Ii(o));const u=a.left+c.scrollLeft-l.x,d=a.top+c.scrollTop-l.y;return{x:u,y:d,width:a.width,height:a.height}}function ua(e,t){return!Oe(e)||ye(e).position==="fixed"?null:t?t(e):e.offsetParent}function ki(e,t){const n=ve(e);if(!Oe(e)||ji(e))return n;let r=ua(e,t);for(;r&&Im(r)&&ye(r).position==="static";)r=ua(r,t);return r&&(Ze(r)==="html"||Ze(r)==="body"&&ye(r).position==="static"&&!Jo(r))?n:r||Dm(e)||n}const Ym=async function(e){const t=this.getOffsetParent||ki,n=this.getDimensions;return{reference:qm(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,...await n(e.floating)}}};function Xm(e){return ye(e).direction==="rtl"}const Jm={convertOffsetParentRelativeRectToViewportRelativeRect:Bm,getDocumentElement:We,getClippingRect:Gm,getOffsetParent:ki,getElementRects:Ym,getClientRects:Um,getDimensions:Km,getScale:yt,isElement:Ue,isRTL:Xm};function Zm(e,t){let n=null,r;const o=We(e);function s(){var c;clearTimeout(r),(c=n)==null||c.disconnect(),n=null}function a(c,l){c===void 0&&(c=!1),l===void 0&&(l=1),s();const{left:u,top:d,width:p,height:g}=e.getBoundingClientRect();if(c||t(),!p||!g)return;const v=cn(d),h=cn(o.clientWidth-(u+p)),m=cn(o.clientHeight-(d+g)),b=cn(u),y={rootMargin:-v+"px "+-h+"px "+-m+"px "+-b+"px",threshold:ge(0,Xe(1,l))||1};let w=!0;function C(S){const P=S[0].intersectionRatio;if(P!==l){if(!w)return a();P?a(!1,P):r=setTimeout(()=>{a(!1,1e-7)},100)}w=!1}try{n=new IntersectionObserver(C,{...y,root:o.ownerDocument})}catch{n=new IntersectionObserver(C,y)}n.observe(e)}return a(!0),s}function es(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:s=!0,elementResize:a=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:l=!1}=r,u=Qo(e),d=o||s?[...u?qt(u):[],...qt(t)]:[];d.forEach(x=>{o&&x.addEventListener("scroll",n,{passive:!0}),s&&x.addEventListener("resize",n)});const p=u&&c?Zm(u,n):null;let g=-1,v=null;a&&(v=new ResizeObserver(x=>{let[y]=x;y&&y.target===u&&v&&(v.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var w;(w=v)==null||w.observe(t)})),n()}),u&&!l&&v.observe(u),v.observe(t));let h,m=l?rt(e):null;l&&b();function b(){const x=rt(e);m&&(x.x!==m.x||x.y!==m.y||x.width!==m.width||x.height!==m.height)&&n(),m=x,h=requestAnimationFrame(b)}return n(),()=>{var x;d.forEach(y=>{o&&y.removeEventListener("scroll",n),s&&y.removeEventListener("resize",n)}),p==null||p(),(x=v)==null||x.disconnect(),v=null,l&&cancelAnimationFrame(h)}}const ts=_m,ns=Tm,rs=jm,os=Am,da=Nm,ss=Mm,Qm=(e,t,n)=>{const r=new Map,o={platform:Jm,...n},s={...o.platform,_c:r};return Pm(e,t,{...o,platform:s})},as=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?da({element:r.current,padding:o}).fn(n):{}:r?da({element:r,padding:o}).fn(n):{}}}};var Rn=typeof document<"u"?i.useLayoutEffect:i.useEffect;function kn(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!kn(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const s=o[r];if(!(s==="_owner"&&e.$$typeof)&&!kn(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function Li(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function fa(e,t){const n=Li(e);return Math.round(t*n)/n}function pa(e){const t=i.useRef(e);return Rn(()=>{t.current=e}),t}function is(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:s,floating:a}={},transform:c=!0,whileElementsMounted:l,open:u}=e,[d,p]=i.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[g,v]=i.useState(r);kn(g,r)||v(r);const[h,m]=i.useState(null),[b,x]=i.useState(null),y=i.useCallback(j=>{j!==P.current&&(P.current=j,m(j))},[]),w=i.useCallback(j=>{j!==E.current&&(E.current=j,x(j))},[]),C=s||h,S=a||b,P=i.useRef(null),E=i.useRef(null),$=i.useRef(d),N=l!=null,A=pa(l),O=pa(o),_=i.useCallback(()=>{if(!P.current||!E.current)return;const j={placement:t,strategy:n,middleware:g};O.current&&(j.platform=O.current),Qm(P.current,E.current,j).then(F=>{const T={...F,isPositioned:!0};L.current&&!kn($.current,T)&&($.current=T,Me.flushSync(()=>{p(T)}))})},[g,t,n,O]);Rn(()=>{u===!1&&$.current.isPositioned&&($.current.isPositioned=!1,p(j=>({...j,isPositioned:!1})))},[u]);const L=i.useRef(!1);Rn(()=>(L.current=!0,()=>{L.current=!1}),[]),Rn(()=>{if(C&&(P.current=C),S&&(E.current=S),C&&S){if(A.current)return A.current(C,S,_);_()}},[C,S,_,A,N]);const D=i.useMemo(()=>({reference:P,floating:E,setReference:y,setFloating:w}),[y,w]),M=i.useMemo(()=>({reference:C,floating:S}),[C,S]),k=i.useMemo(()=>{const j={position:n,left:0,top:0};if(!M.floating)return j;const F=fa(M.floating,d.x),T=fa(M.floating,d.y);return c?{...j,transform:"translate("+F+"px, "+T+"px)",...Li(M.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:F,top:T}},[n,c,M.floating,d.x,d.y]);return i.useMemo(()=>({...d,update:_,refs:D,elements:M,floatingStyles:k}),[d,_,D,M,k])}var eh="Arrow",Fi=i.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...s}=e;return f.jsx(re.svg,{...s,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:f.jsx("polygon",{points:"0,0 30,0 15,10"})})});Fi.displayName=eh;var th=Fi;function nh(e){const[t,n]=i.useState(void 0);return xe(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const s=o[0];let a,c;if("borderBoxSize"in s){const l=s.borderBoxSize,u=Array.isArray(l)?l[0]:l;a=u.inlineSize,c=u.blockSize}else a=e.offsetWidth,c=e.offsetHeight;n({width:a,height:c})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var cs="Popper",[Bi,Ui]=zo(cs),[rh,Wi]=Bi(cs),Vi=e=>{const{__scopePopper:t,children:n}=e,[r,o]=i.useState(null);return f.jsx(rh,{scope:t,anchor:r,onAnchorChange:o,children:n})};Vi.displayName=cs;var Hi="PopperAnchor",zi=i.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,s=Wi(Hi,n),a=i.useRef(null),c=ie(t,a);return i.useEffect(()=>{s.onAnchorChange((r==null?void 0:r.current)||a.current)}),r?null:f.jsx(re.div,{...o,ref:c})});zi.displayName=Hi;var ls="PopperContent",[oh,sh]=Bi(ls),Gi=i.forwardRef((e,t)=>{var I,K,H,z,q,Y;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:s="center",alignOffset:a=0,arrowPadding:c=0,avoidCollisions:l=!0,collisionBoundary:u=[],collisionPadding:d=0,sticky:p="partial",hideWhenDetached:g=!1,updatePositionStrategy:v="optimized",onPlaced:h,...m}=e,b=Wi(ls,n),[x,y]=i.useState(null),w=ie(t,te=>y(te)),[C,S]=i.useState(null),P=nh(C),E=(P==null?void 0:P.width)??0,$=(P==null?void 0:P.height)??0,N=r+(s!=="center"?"-"+s:""),A=typeof d=="number"?d:{top:0,right:0,bottom:0,left:0,...d},O=Array.isArray(u)?u:[u],_=O.length>0,L={padding:A,boundary:O.filter(ih),altBoundary:_},{refs:D,floatingStyles:M,placement:k,isPositioned:j,middlewareData:F}=is({strategy:"fixed",placement:N,whileElementsMounted:(...te)=>es(...te,{animationFrame:v==="always"}),elements:{reference:b.anchor},middleware:[Xo({mainAxis:o+$,alignmentAxis:a}),l&&ts({mainAxis:!0,crossAxis:!1,limiter:p==="partial"?ss():void 0,...L}),l&&ns({...L}),rs({...L,apply:({elements:te,rects:fe,availableWidth:he,availableHeight:Ne})=>{const{width:je,height:Lt}=fe.reference,Ee=te.floating.style;Ee.setProperty("--radix-popper-available-width",`${he}px`),Ee.setProperty("--radix-popper-available-height",`${Ne}px`),Ee.setProperty("--radix-popper-anchor-width",`${je}px`),Ee.setProperty("--radix-popper-anchor-height",`${Lt}px`)}}),C&&as({element:C,padding:c}),ch({arrowWidth:E,arrowHeight:$}),g&&os({strategy:"referenceHidden",...L})]}),[T,J]=Yi(k),X=Le(h);xe(()=>{j&&(X==null||X())},[j,X]);const ae=(I=F.arrow)==null?void 0:I.x,ce=(K=F.arrow)==null?void 0:K.y,le=((H=F.arrow)==null?void 0:H.centerOffset)!==0,[ue,ee]=i.useState();return xe(()=>{x&&ee(window.getComputedStyle(x).zIndex)},[x]),f.jsx("div",{ref:D.setFloating,"data-radix-popper-content-wrapper":"",style:{...M,transform:j?M.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ue,"--radix-popper-transform-origin":[(z=F.transformOrigin)==null?void 0:z.x,(q=F.transformOrigin)==null?void 0:q.y].join(" "),...((Y=F.hide)==null?void 0:Y.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:f.jsx(oh,{scope:n,placedSide:T,onArrowChange:S,arrowX:ae,arrowY:ce,shouldHideArrow:le,children:f.jsx(re.div,{"data-side":T,"data-align":J,...m,ref:w,style:{...m.style,animation:j?void 0:"none"}})})})});Gi.displayName=ls;var Ki="PopperArrow",ah={top:"bottom",right:"left",bottom:"top",left:"right"},qi=i.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,s=sh(Ki,r),a=ah[s.placedSide];return f.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:f.jsx(th,{...o,ref:n,style:{...o.style,display:"block"}})})});qi.displayName=Ki;function ih(e){return e!==null}var ch=e=>({name:"transformOrigin",options:e,fn(t){var b,x,y;const{placement:n,rects:r,middlewareData:o}=t,a=((b=o.arrow)==null?void 0:b.centerOffset)!==0,c=a?0:e.arrowWidth,l=a?0:e.arrowHeight,[u,d]=Yi(n),p={start:"0%",center:"50%",end:"100%"}[d],g=(((x=o.arrow)==null?void 0:x.x)??0)+c/2,v=(((y=o.arrow)==null?void 0:y.y)??0)+l/2;let h="",m="";return u==="bottom"?(h=a?p:`${g}px`,m=`${-l}px`):u==="top"?(h=a?p:`${g}px`,m=`${r.floating.height+l}px`):u==="right"?(h=`${-l}px`,m=a?p:`${v}px`):u==="left"&&(h=`${r.floating.width+l}px`,m=a?p:`${v}px`),{data:{x:h,y:m}}}});function Yi(e){const[t,n="center"]=e.split("-");return[t,n]}var lh=Vi,uh=zi,dh=Gi,fh=qi,ph="Portal",Xi=i.forwardRef((e,t)=>{var c;const{container:n,...r}=e,[o,s]=i.useState(!1);xe(()=>s(!0),[]);const a=n||o&&((c=globalThis==null?void 0:globalThis.document)==null?void 0:c.body);return a?ko.createPortal(f.jsx(re.div,{...r,ref:t}),a):null});Xi.displayName=ph;function ma({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=mh({defaultProp:t,onChange:n}),s=e!==void 0,a=s?e:r,c=Le(n),l=i.useCallback(u=>{if(s){const p=typeof u=="function"?u(e):u;p!==e&&c(p)}else o(u)},[s,e,o,c]);return[a,l]}function mh({defaultProp:e,onChange:t}){const n=i.useState(e),[r]=n,o=i.useRef(r),s=Le(t);return i.useEffect(()=>{o.current!==r&&(s(r),o.current=r)},[r,o,s]),n}function Ji(e){const t=i.useRef({value:e,previous:e});return i.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var hh=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},ft=new WeakMap,ln=new WeakMap,un={},Nr=0,Zi=function(e){return e&&(e.host||Zi(e.parentNode))},gh=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=Zi(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},vh=function(e,t,n,r){var o=gh(t,Array.isArray(e)?e:[e]);un[n]||(un[n]=new WeakMap);var s=un[n],a=[],c=new Set,l=new Set(o),u=function(p){!p||c.has(p)||(c.add(p),u(p.parentNode))};o.forEach(u);var d=function(p){!p||l.has(p)||Array.prototype.forEach.call(p.children,function(g){if(c.has(g))d(g);else{var v=g.getAttribute(r),h=v!==null&&v!=="false",m=(ft.get(g)||0)+1,b=(s.get(g)||0)+1;ft.set(g,m),s.set(g,b),a.push(g),m===1&&h&&ln.set(g,!0),b===1&&g.setAttribute(n,"true"),h||g.setAttribute(r,"true")}})};return d(t),c.clear(),Nr++,function(){a.forEach(function(p){var g=ft.get(p)-1,v=s.get(p)-1;ft.set(p,g),s.set(p,v),g||(ln.has(p)||p.removeAttribute(r),ln.delete(p)),v||p.removeAttribute(n)}),Nr--,Nr||(ft=new WeakMap,ft=new WeakMap,ln=new WeakMap,un={})}},us=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||hh(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),vh(r,o,n,"aria-hidden")):function(){return null}},Q=function(){return Q=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(t[s]=n[s])}return t},Q.apply(this,arguments)};function sr(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function ds(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,s;r<o;r++)(s||!(r in t))&&(s||(s=Array.prototype.slice.call(t,0,r)),s[r]=t[r]);return e.concat(s||Array.prototype.slice.call(t))}var wt="right-scroll-bar-position",Ct="width-before-scroll-bar",bh="with-scroll-bars-hidden",xh="--removed-body-scroll-bar-size";function Tr(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function yh(e,t){var n=i.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var wh=typeof window<"u"?i.useLayoutEffect:i.useEffect,ha=new WeakMap;function fs(e,t){var n=yh(t||null,function(r){return e.forEach(function(o){return Tr(o,r)})});return wh(function(){var r=ha.get(n);if(r){var o=new Set(r),s=new Set(e),a=n.current;o.forEach(function(c){s.has(c)||Tr(c,null)}),s.forEach(function(c){o.has(c)||Tr(c,a)})}ha.set(n,e)},[e]),n}function Ch(e){return e}function Eh(e,t){t===void 0&&(t=Ch);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(s){var a=t(s,r);return n.push(a),function(){n=n.filter(function(c){return c!==a})}},assignSyncMedium:function(s){for(r=!0;n.length;){var a=n;n=[],a.forEach(s)}n={push:function(c){return s(c)},filter:function(){return n}}},assignMedium:function(s){r=!0;var a=[];if(n.length){var c=n;n=[],c.forEach(s),a=n}var l=function(){var d=a;a=[],d.forEach(s)},u=function(){return Promise.resolve().then(l)};u(),n={push:function(d){a.push(d),u()},filter:function(d){return a=a.filter(d),n}}}};return o}function ps(e){e===void 0&&(e={});var t=Eh(null);return t.options=Q({async:!0,ssr:!1},e),t}var Qi=function(e){var t=e.sideCar,n=sr(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return i.createElement(r,Q({},n))};Qi.isSideCarExport=!0;function ms(e,t){return e.useMedium(t),Qi}var ec=ps(),Ar=function(){},ar=i.forwardRef(function(e,t){var n=i.useRef(null),r=i.useState({onScrollCapture:Ar,onWheelCapture:Ar,onTouchMoveCapture:Ar}),o=r[0],s=r[1],a=e.forwardProps,c=e.children,l=e.className,u=e.removeScrollBar,d=e.enabled,p=e.shards,g=e.sideCar,v=e.noIsolation,h=e.inert,m=e.allowPinchZoom,b=e.as,x=b===void 0?"div":b,y=e.gapMode,w=sr(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=g,S=fs([n,t]),P=Q(Q({},w),o);return i.createElement(i.Fragment,null,d&&i.createElement(C,{sideCar:ec,removeScrollBar:u,shards:p,noIsolation:v,inert:h,setCallbacks:s,allowPinchZoom:!!m,lockRef:n,gapMode:y}),a?i.cloneElement(i.Children.only(c),Q(Q({},P),{ref:S})):i.createElement(x,Q({},P,{className:l,ref:S}),c))});ar.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};ar.classNames={fullWidth:Ct,zeroRight:wt};var Sh=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function $h(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Sh();return t&&e.setAttribute("nonce",t),e}function Rh(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Ph(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var Nh=function(){var e=0,t=null;return{add:function(n){e==0&&(t=$h())&&(Rh(t,n),Ph(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Th=function(){var e=Nh();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},ir=function(){var e=Th(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},Ah={left:0,top:0,right:0,gap:0},Or=function(e){return parseInt(e||"",10)||0},Oh=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[Or(n),Or(r),Or(o)]},_h=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return Ah;var t=Oh(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},Mh=ir(),Et="data-scroll-locked",jh=function(e,t,n,r){var o=e.left,s=e.top,a=e.right,c=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(bh,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(c,"px ").concat(r,`;
  }
  body[`).concat(Et,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(s,`px;
    padding-right: `).concat(a,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(c,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(wt,` {
    right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(Ct,` {
    margin-right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(wt," .").concat(wt,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(Ct," .").concat(Ct,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(Et,`] {
    `).concat(xh,": ").concat(c,`px;
  }
`)},ga=function(){var e=parseInt(document.body.getAttribute(Et)||"0",10);return isFinite(e)?e:0},Ih=function(){i.useEffect(function(){return document.body.setAttribute(Et,(ga()+1).toString()),function(){var e=ga()-1;e<=0?document.body.removeAttribute(Et):document.body.setAttribute(Et,e.toString())}},[])},hs=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;Ih();var s=i.useMemo(function(){return _h(o)},[o]);return i.createElement(Mh,{styles:jh(s,!t,o,n?"":"!important")})},to=!1;if(typeof window<"u")try{var dn=Object.defineProperty({},"passive",{get:function(){return to=!0,!0}});window.addEventListener("test",dn,dn),window.removeEventListener("test",dn,dn)}catch{to=!1}var pt=to?{passive:!1}:!1,Dh=function(e){return e.tagName==="TEXTAREA"},tc=function(e,t){var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Dh(e)&&n[t]==="visible")},kh=function(e){return tc(e,"overflowY")},Lh=function(e){return tc(e,"overflowX")},va=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=nc(e,r);if(o){var s=rc(e,r),a=s[1],c=s[2];if(a>c)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Fh=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},Bh=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},nc=function(e,t){return e==="v"?kh(t):Lh(t)},rc=function(e,t){return e==="v"?Fh(t):Bh(t)},Uh=function(e,t){return e==="h"&&t==="rtl"?-1:1},Wh=function(e,t,n,r,o){var s=Uh(e,window.getComputedStyle(t).direction),a=s*r,c=n.target,l=t.contains(c),u=!1,d=a>0,p=0,g=0;do{var v=rc(e,c),h=v[0],m=v[1],b=v[2],x=m-b-s*h;(h||x)&&nc(e,c)&&(p+=x,g+=h),c instanceof ShadowRoot?c=c.host:c=c.parentNode}while(!l&&c!==document.body||l&&(t.contains(c)||t===c));return(d&&(o&&Math.abs(p)<1||!o&&a>p)||!d&&(o&&Math.abs(g)<1||!o&&-a>g))&&(u=!0),u},fn=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ba=function(e){return[e.deltaX,e.deltaY]},xa=function(e){return e&&"current"in e?e.current:e},Vh=function(e,t){return e[0]===t[0]&&e[1]===t[1]},Hh=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},zh=0,mt=[];function Gh(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),o=i.useState(zh++)[0],s=i.useState(ir)[0],a=i.useRef(e);i.useEffect(function(){a.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var m=ds([e.lockRef.current],(e.shards||[]).map(xa),!0).filter(Boolean);return m.forEach(function(b){return b.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),m.forEach(function(b){return b.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=i.useCallback(function(m,b){if("touches"in m&&m.touches.length===2)return!a.current.allowPinchZoom;var x=fn(m),y=n.current,w="deltaX"in m?m.deltaX:y[0]-x[0],C="deltaY"in m?m.deltaY:y[1]-x[1],S,P=m.target,E=Math.abs(w)>Math.abs(C)?"h":"v";if("touches"in m&&E==="h"&&P.type==="range")return!1;var $=va(E,P);if(!$)return!0;if($?S=E:(S=E==="v"?"h":"v",$=va(E,P)),!$)return!1;if(!r.current&&"changedTouches"in m&&(w||C)&&(r.current=S),!S)return!0;var N=r.current||S;return Wh(N,b,m,N==="h"?w:C,!0)},[]),l=i.useCallback(function(m){var b=m;if(!(!mt.length||mt[mt.length-1]!==s)){var x="deltaY"in b?ba(b):fn(b),y=t.current.filter(function(S){return S.name===b.type&&(S.target===b.target||b.target===S.shadowParent)&&Vh(S.delta,x)})[0];if(y&&y.should){b.cancelable&&b.preventDefault();return}if(!y){var w=(a.current.shards||[]).map(xa).filter(Boolean).filter(function(S){return S.contains(b.target)}),C=w.length>0?c(b,w[0]):!a.current.noIsolation;C&&b.cancelable&&b.preventDefault()}}},[]),u=i.useCallback(function(m,b,x,y){var w={name:m,delta:b,target:x,should:y,shadowParent:Kh(x)};t.current.push(w),setTimeout(function(){t.current=t.current.filter(function(C){return C!==w})},1)},[]),d=i.useCallback(function(m){n.current=fn(m),r.current=void 0},[]),p=i.useCallback(function(m){u(m.type,ba(m),m.target,c(m,e.lockRef.current))},[]),g=i.useCallback(function(m){u(m.type,fn(m),m.target,c(m,e.lockRef.current))},[]);i.useEffect(function(){return mt.push(s),e.setCallbacks({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:g}),document.addEventListener("wheel",l,pt),document.addEventListener("touchmove",l,pt),document.addEventListener("touchstart",d,pt),function(){mt=mt.filter(function(m){return m!==s}),document.removeEventListener("wheel",l,pt),document.removeEventListener("touchmove",l,pt),document.removeEventListener("touchstart",d,pt)}},[]);var v=e.removeScrollBar,h=e.inert;return i.createElement(i.Fragment,null,h?i.createElement(s,{styles:Hh(o)}):null,v?i.createElement(hs,{gapMode:e.gapMode}):null)}function Kh(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const qh=ms(ec,Gh);var oc=i.forwardRef(function(e,t){return i.createElement(ar,Q({},e,{ref:t,sideCar:qh}))});oc.classNames=ar.classNames;const Yh=oc;var Xh=[" ","Enter","ArrowUp","ArrowDown"],Jh=[" ","Enter"],nn="Select",[cr,lr,Zh]=Xp(nn),[Mt,sC]=zo(nn,[Zh,Ui]),ur=Ui(),[Qh,Qe]=Mt(nn),[eg,tg]=Mt(nn),sc=e=>{const{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:s,value:a,defaultValue:c,onValueChange:l,dir:u,name:d,autoComplete:p,disabled:g,required:v}=e,h=ur(t),[m,b]=i.useState(null),[x,y]=i.useState(null),[w,C]=i.useState(!1),S=Zp(u),[P=!1,E]=ma({prop:r,defaultProp:o,onChange:s}),[$,N]=ma({prop:a,defaultProp:c,onChange:l}),A=i.useRef(null),O=m?!!m.closest("form"):!0,[_,L]=i.useState(new Set),D=Array.from(_).map(M=>M.props.value).join(";");return f.jsx(lh,{...h,children:f.jsxs(Qh,{required:v,scope:t,trigger:m,onTriggerChange:b,valueNode:x,onValueNodeChange:y,valueNodeHasChildren:w,onValueNodeHasChildrenChange:C,contentId:Go(),value:$,onValueChange:N,open:P,onOpenChange:E,dir:S,triggerPointerDownPosRef:A,disabled:g,children:[f.jsx(cr.Provider,{scope:t,children:f.jsx(eg,{scope:e.__scopeSelect,onNativeOptionAdd:i.useCallback(M=>{L(k=>new Set(k).add(M))},[]),onNativeOptionRemove:i.useCallback(M=>{L(k=>{const j=new Set(k);return j.delete(M),j})},[]),children:n})}),O?f.jsxs(Ac,{"aria-hidden":!0,required:v,tabIndex:-1,name:d,autoComplete:p,value:$,onChange:M=>N(M.target.value),disabled:g,children:[$===void 0?f.jsx("option",{value:""}):null,Array.from(_)]},D):null]})})};sc.displayName=nn;var ac="SelectTrigger",ic=i.forwardRef((e,t)=>{const{__scopeSelect:n,disabled:r=!1,...o}=e,s=ur(n),a=Qe(ac,n),c=a.disabled||r,l=ie(t,a.onTriggerChange),u=lr(n),[d,p,g]=Oc(h=>{const m=u().filter(y=>!y.disabled),b=m.find(y=>y.value===a.value),x=_c(m,h,b);x!==void 0&&a.onValueChange(x.value)}),v=()=>{c||(a.onOpenChange(!0),g())};return f.jsx(uh,{asChild:!0,...s,children:f.jsx(re.button,{type:"button",role:"combobox","aria-controls":a.contentId,"aria-expanded":a.open,"aria-required":a.required,"aria-autocomplete":"none",dir:a.dir,"data-state":a.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":Tc(a.value)?"":void 0,...o,ref:l,onClick:oe(o.onClick,h=>{h.currentTarget.focus()}),onPointerDown:oe(o.onPointerDown,h=>{const m=h.target;m.hasPointerCapture(h.pointerId)&&m.releasePointerCapture(h.pointerId),h.button===0&&h.ctrlKey===!1&&(v(),a.triggerPointerDownPosRef.current={x:Math.round(h.pageX),y:Math.round(h.pageY)},h.preventDefault())}),onKeyDown:oe(o.onKeyDown,h=>{const m=d.current!=="";!(h.ctrlKey||h.altKey||h.metaKey)&&h.key.length===1&&p(h.key),!(m&&h.key===" ")&&Xh.includes(h.key)&&(v(),h.preventDefault())})})})});ic.displayName=ac;var cc="SelectValue",lc=i.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:o,children:s,placeholder:a="",...c}=e,l=Qe(cc,n),{onValueNodeHasChildrenChange:u}=l,d=s!==void 0,p=ie(t,l.onValueNodeChange);return xe(()=>{u(d)},[u,d]),f.jsx(re.span,{...c,ref:p,style:{pointerEvents:"none"},children:Tc(l.value)?f.jsx(f.Fragment,{children:a}):s})});lc.displayName=cc;var ng="SelectIcon",uc=i.forwardRef((e,t)=>{const{__scopeSelect:n,children:r,...o}=e;return f.jsx(re.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});uc.displayName=ng;var rg="SelectPortal",dc=e=>f.jsx(Xi,{asChild:!0,...e});dc.displayName=rg;var ot="SelectContent",fc=i.forwardRef((e,t)=>{const n=Qe(ot,e.__scopeSelect),[r,o]=i.useState();if(xe(()=>{o(new DocumentFragment)},[]),!n.open){const s=r;return s?Me.createPortal(f.jsx(pc,{scope:e.__scopeSelect,children:f.jsx(cr.Slot,{scope:e.__scopeSelect,children:f.jsx("div",{children:e.children})})}),s):null}return f.jsx(mc,{...e,ref:t})});fc.displayName=ot;var Ie=10,[pc,et]=Mt(ot),og="SelectContentImpl",mc=i.forwardRef((e,t)=>{const{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:s,onPointerDownOutside:a,side:c,sideOffset:l,align:u,alignOffset:d,arrowPadding:p,collisionBoundary:g,collisionPadding:v,sticky:h,hideWhenDetached:m,avoidCollisions:b,...x}=e,y=Qe(ot,n),[w,C]=i.useState(null),[S,P]=i.useState(null),E=ie(t,I=>C(I)),[$,N]=i.useState(null),[A,O]=i.useState(null),_=lr(n),[L,D]=i.useState(!1),M=i.useRef(!1);i.useEffect(()=>{if(w)return us(w)},[w]),lm();const k=i.useCallback(I=>{const[K,...H]=_().map(Y=>Y.ref.current),[z]=H.slice(-1),q=document.activeElement;for(const Y of I)if(Y===q||(Y==null||Y.scrollIntoView({block:"nearest"}),Y===K&&S&&(S.scrollTop=0),Y===z&&S&&(S.scrollTop=S.scrollHeight),Y==null||Y.focus(),document.activeElement!==q))return},[_,S]),j=i.useCallback(()=>k([$,w]),[k,$,w]);i.useEffect(()=>{L&&j()},[L,j]);const{onOpenChange:F,triggerPointerDownPosRef:T}=y;i.useEffect(()=>{if(w){let I={x:0,y:0};const K=z=>{var q,Y;I={x:Math.abs(Math.round(z.pageX)-(((q=T.current)==null?void 0:q.x)??0)),y:Math.abs(Math.round(z.pageY)-(((Y=T.current)==null?void 0:Y.y)??0))}},H=z=>{I.x<=10&&I.y<=10?z.preventDefault():w.contains(z.target)||F(!1),document.removeEventListener("pointermove",K),T.current=null};return T.current!==null&&(document.addEventListener("pointermove",K),document.addEventListener("pointerup",H,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",K),document.removeEventListener("pointerup",H,{capture:!0})}}},[w,F,T]),i.useEffect(()=>{const I=()=>F(!1);return window.addEventListener("blur",I),window.addEventListener("resize",I),()=>{window.removeEventListener("blur",I),window.removeEventListener("resize",I)}},[F]);const[J,X]=Oc(I=>{const K=_().filter(q=>!q.disabled),H=K.find(q=>q.ref.current===document.activeElement),z=_c(K,I,H);z&&setTimeout(()=>z.ref.current.focus())}),ae=i.useCallback((I,K,H)=>{const z=!M.current&&!H;(y.value!==void 0&&y.value===K||z)&&(N(I),z&&(M.current=!0))},[y.value]),ce=i.useCallback(()=>w==null?void 0:w.focus(),[w]),le=i.useCallback((I,K,H)=>{const z=!M.current&&!H;(y.value!==void 0&&y.value===K||z)&&O(I)},[y.value]),ue=r==="popper"?no:hc,ee=ue===no?{side:c,sideOffset:l,align:u,alignOffset:d,arrowPadding:p,collisionBoundary:g,collisionPadding:v,sticky:h,hideWhenDetached:m,avoidCollisions:b}:{};return f.jsx(pc,{scope:n,content:w,viewport:S,onViewportChange:P,itemRefCallback:ae,selectedItem:$,onItemLeave:ce,itemTextRefCallback:le,focusSelectedItem:j,selectedItemText:A,position:r,isPositioned:L,searchRef:J,children:f.jsx(Yh,{as:Gt,allowPinchZoom:!0,children:f.jsx(Pi,{asChild:!0,trapped:y.open,onMountAutoFocus:I=>{I.preventDefault()},onUnmountAutoFocus:oe(o,I=>{var K;(K=y.trigger)==null||K.focus({preventScroll:!0}),I.preventDefault()}),children:f.jsx($i,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:s,onPointerDownOutside:a,onFocusOutside:I=>I.preventDefault(),onDismiss:()=>y.onOpenChange(!1),children:f.jsx(ue,{role:"listbox",id:y.contentId,"data-state":y.open?"open":"closed",dir:y.dir,onContextMenu:I=>I.preventDefault(),...x,...ee,onPlaced:()=>D(!0),ref:E,style:{display:"flex",flexDirection:"column",outline:"none",...x.style},onKeyDown:oe(x.onKeyDown,I=>{const K=I.ctrlKey||I.altKey||I.metaKey;if(I.key==="Tab"&&I.preventDefault(),!K&&I.key.length===1&&X(I.key),["ArrowUp","ArrowDown","Home","End"].includes(I.key)){let z=_().filter(q=>!q.disabled).map(q=>q.ref.current);if(["ArrowUp","End"].includes(I.key)&&(z=z.slice().reverse()),["ArrowUp","ArrowDown"].includes(I.key)){const q=I.target,Y=z.indexOf(q);z=z.slice(Y+1)}setTimeout(()=>k(z)),I.preventDefault()}})})})})})})});mc.displayName=og;var sg="SelectItemAlignedPosition",hc=i.forwardRef((e,t)=>{const{__scopeSelect:n,onPlaced:r,...o}=e,s=Qe(ot,n),a=et(ot,n),[c,l]=i.useState(null),[u,d]=i.useState(null),p=ie(t,E=>d(E)),g=lr(n),v=i.useRef(!1),h=i.useRef(!0),{viewport:m,selectedItem:b,selectedItemText:x,focusSelectedItem:y}=a,w=i.useCallback(()=>{if(s.trigger&&s.valueNode&&c&&u&&m&&b&&x){const E=s.trigger.getBoundingClientRect(),$=u.getBoundingClientRect(),N=s.valueNode.getBoundingClientRect(),A=x.getBoundingClientRect();if(s.dir!=="rtl"){const q=A.left-$.left,Y=N.left-q,te=E.left-Y,fe=E.width+te,he=Math.max(fe,$.width),Ne=window.innerWidth-Ie,je=Mn(Y,[Ie,Ne-he]);c.style.minWidth=fe+"px",c.style.left=je+"px"}else{const q=$.right-A.right,Y=window.innerWidth-N.right-q,te=window.innerWidth-E.right-Y,fe=E.width+te,he=Math.max(fe,$.width),Ne=window.innerWidth-Ie,je=Mn(Y,[Ie,Ne-he]);c.style.minWidth=fe+"px",c.style.right=je+"px"}const O=g(),_=window.innerHeight-Ie*2,L=m.scrollHeight,D=window.getComputedStyle(u),M=parseInt(D.borderTopWidth,10),k=parseInt(D.paddingTop,10),j=parseInt(D.borderBottomWidth,10),F=parseInt(D.paddingBottom,10),T=M+k+L+F+j,J=Math.min(b.offsetHeight*5,T),X=window.getComputedStyle(m),ae=parseInt(X.paddingTop,10),ce=parseInt(X.paddingBottom,10),le=E.top+E.height/2-Ie,ue=_-le,ee=b.offsetHeight/2,I=b.offsetTop+ee,K=M+k+I,H=T-K;if(K<=le){const q=b===O[O.length-1].ref.current;c.style.bottom="0px";const Y=u.clientHeight-m.offsetTop-m.offsetHeight,te=Math.max(ue,ee+(q?ce:0)+Y+j),fe=K+te;c.style.height=fe+"px"}else{const q=b===O[0].ref.current;c.style.top="0px";const te=Math.max(le,M+m.offsetTop+(q?ae:0)+ee)+H;c.style.height=te+"px",m.scrollTop=K-le+m.offsetTop}c.style.margin=`${Ie}px 0`,c.style.minHeight=J+"px",c.style.maxHeight=_+"px",r==null||r(),requestAnimationFrame(()=>v.current=!0)}},[g,s.trigger,s.valueNode,c,u,m,b,x,s.dir,r]);xe(()=>w(),[w]);const[C,S]=i.useState();xe(()=>{u&&S(window.getComputedStyle(u).zIndex)},[u]);const P=i.useCallback(E=>{E&&h.current===!0&&(w(),y==null||y(),h.current=!1)},[w,y]);return f.jsx(ig,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:v,onScrollButtonChange:P,children:f.jsx("div",{ref:l,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:C},children:f.jsx(re.div,{...o,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});hc.displayName=sg;var ag="SelectPopperPosition",no=i.forwardRef((e,t)=>{const{__scopeSelect:n,align:r="start",collisionPadding:o=Ie,...s}=e,a=ur(n);return f.jsx(dh,{...a,...s,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...s.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});no.displayName=ag;var[ig,gs]=Mt(ot,{}),ro="SelectViewport",gc=i.forwardRef((e,t)=>{const{__scopeSelect:n,nonce:r,...o}=e,s=et(ro,n),a=gs(ro,n),c=ie(t,s.onViewportChange),l=i.useRef(0);return f.jsxs(f.Fragment,{children:[f.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),f.jsx(cr.Slot,{scope:n,children:f.jsx(re.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:c,style:{position:"relative",flex:1,overflow:"auto",...o.style},onScroll:oe(o.onScroll,u=>{const d=u.currentTarget,{contentWrapper:p,shouldExpandOnScrollRef:g}=a;if(g!=null&&g.current&&p){const v=Math.abs(l.current-d.scrollTop);if(v>0){const h=window.innerHeight-Ie*2,m=parseFloat(p.style.minHeight),b=parseFloat(p.style.height),x=Math.max(m,b);if(x<h){const y=x+v,w=Math.min(h,y),C=y-w;p.style.height=w+"px",p.style.bottom==="0px"&&(d.scrollTop=C>0?C:0,p.style.justifyContent="flex-end")}}}l.current=d.scrollTop})})})]})});gc.displayName=ro;var vc="SelectGroup",[cg,lg]=Mt(vc),ug=i.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=Go();return f.jsx(cg,{scope:n,id:o,children:f.jsx(re.div,{role:"group","aria-labelledby":o,...r,ref:t})})});ug.displayName=vc;var bc="SelectLabel",xc=i.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=lg(bc,n);return f.jsx(re.div,{id:o.id,...r,ref:t})});xc.displayName=bc;var Ln="SelectItem",[dg,yc]=Mt(Ln),wc=i.forwardRef((e,t)=>{const{__scopeSelect:n,value:r,disabled:o=!1,textValue:s,...a}=e,c=Qe(Ln,n),l=et(Ln,n),u=c.value===r,[d,p]=i.useState(s??""),[g,v]=i.useState(!1),h=ie(t,x=>{var y;return(y=l.itemRefCallback)==null?void 0:y.call(l,x,r,o)}),m=Go(),b=()=>{o||(c.onValueChange(r),c.onOpenChange(!1))};if(r==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return f.jsx(dg,{scope:n,value:r,disabled:o,textId:m,isSelected:u,onItemTextChange:i.useCallback(x=>{p(y=>y||((x==null?void 0:x.textContent)??"").trim())},[]),children:f.jsx(cr.ItemSlot,{scope:n,value:r,disabled:o,textValue:d,children:f.jsx(re.div,{role:"option","aria-labelledby":m,"data-highlighted":g?"":void 0,"aria-selected":u&&g,"data-state":u?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...a,ref:h,onFocus:oe(a.onFocus,()=>v(!0)),onBlur:oe(a.onBlur,()=>v(!1)),onPointerUp:oe(a.onPointerUp,b),onPointerMove:oe(a.onPointerMove,x=>{var y;o?(y=l.onItemLeave)==null||y.call(l):x.currentTarget.focus({preventScroll:!0})}),onPointerLeave:oe(a.onPointerLeave,x=>{var y;x.currentTarget===document.activeElement&&((y=l.onItemLeave)==null||y.call(l))}),onKeyDown:oe(a.onKeyDown,x=>{var w;((w=l.searchRef)==null?void 0:w.current)!==""&&x.key===" "||(Jh.includes(x.key)&&b(),x.key===" "&&x.preventDefault())})})})})});wc.displayName=Ln;var Bt="SelectItemText",Cc=i.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:o,...s}=e,a=Qe(Bt,n),c=et(Bt,n),l=yc(Bt,n),u=tg(Bt,n),[d,p]=i.useState(null),g=ie(t,x=>p(x),l.onItemTextChange,x=>{var y;return(y=c.itemTextRefCallback)==null?void 0:y.call(c,x,l.value,l.disabled)}),v=d==null?void 0:d.textContent,h=i.useMemo(()=>f.jsx("option",{value:l.value,disabled:l.disabled,children:v},l.value),[l.disabled,l.value,v]),{onNativeOptionAdd:m,onNativeOptionRemove:b}=u;return xe(()=>(m(h),()=>b(h)),[m,b,h]),f.jsxs(f.Fragment,{children:[f.jsx(re.span,{id:l.textId,...s,ref:g}),l.isSelected&&a.valueNode&&!a.valueNodeHasChildren?Me.createPortal(s.children,a.valueNode):null]})});Cc.displayName=Bt;var Ec="SelectItemIndicator",Sc=i.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return yc(Ec,n).isSelected?f.jsx(re.span,{"aria-hidden":!0,...r,ref:t}):null});Sc.displayName=Ec;var oo="SelectScrollUpButton",$c=i.forwardRef((e,t)=>{const n=et(oo,e.__scopeSelect),r=gs(oo,e.__scopeSelect),[o,s]=i.useState(!1),a=ie(t,r.onScrollButtonChange);return xe(()=>{if(n.viewport&&n.isPositioned){let c=function(){const u=l.scrollTop>0;s(u)};const l=n.viewport;return c(),l.addEventListener("scroll",c),()=>l.removeEventListener("scroll",c)}},[n.viewport,n.isPositioned]),o?f.jsx(Pc,{...e,ref:a,onAutoScroll:()=>{const{viewport:c,selectedItem:l}=n;c&&l&&(c.scrollTop=c.scrollTop-l.offsetHeight)}}):null});$c.displayName=oo;var so="SelectScrollDownButton",Rc=i.forwardRef((e,t)=>{const n=et(so,e.__scopeSelect),r=gs(so,e.__scopeSelect),[o,s]=i.useState(!1),a=ie(t,r.onScrollButtonChange);return xe(()=>{if(n.viewport&&n.isPositioned){let c=function(){const u=l.scrollHeight-l.clientHeight,d=Math.ceil(l.scrollTop)<u;s(d)};const l=n.viewport;return c(),l.addEventListener("scroll",c),()=>l.removeEventListener("scroll",c)}},[n.viewport,n.isPositioned]),o?f.jsx(Pc,{...e,ref:a,onAutoScroll:()=>{const{viewport:c,selectedItem:l}=n;c&&l&&(c.scrollTop=c.scrollTop+l.offsetHeight)}}):null});Rc.displayName=so;var Pc=i.forwardRef((e,t)=>{const{__scopeSelect:n,onAutoScroll:r,...o}=e,s=et("SelectScrollButton",n),a=i.useRef(null),c=lr(n),l=i.useCallback(()=>{a.current!==null&&(window.clearInterval(a.current),a.current=null)},[]);return i.useEffect(()=>()=>l(),[l]),xe(()=>{var d;const u=c().find(p=>p.ref.current===document.activeElement);(d=u==null?void 0:u.ref.current)==null||d.scrollIntoView({block:"nearest"})},[c]),f.jsx(re.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:oe(o.onPointerDown,()=>{a.current===null&&(a.current=window.setInterval(r,50))}),onPointerMove:oe(o.onPointerMove,()=>{var u;(u=s.onItemLeave)==null||u.call(s),a.current===null&&(a.current=window.setInterval(r,50))}),onPointerLeave:oe(o.onPointerLeave,()=>{l()})})}),fg="SelectSeparator",Nc=i.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return f.jsx(re.div,{"aria-hidden":!0,...r,ref:t})});Nc.displayName=fg;var ao="SelectArrow",pg=i.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=ur(n),s=Qe(ao,n),a=et(ao,n);return s.open&&a.position==="popper"?f.jsx(fh,{...o,...r,ref:t}):null});pg.displayName=ao;function Tc(e){return e===""||e===void 0}var Ac=i.forwardRef((e,t)=>{const{value:n,...r}=e,o=i.useRef(null),s=ie(t,o),a=Ji(n);return i.useEffect(()=>{const c=o.current,l=window.HTMLSelectElement.prototype,d=Object.getOwnPropertyDescriptor(l,"value").set;if(a!==n&&d){const p=new Event("change",{bubbles:!0});d.call(c,n),c.dispatchEvent(p)}},[a,n]),f.jsx(Ud,{asChild:!0,children:f.jsx("select",{...r,ref:s,defaultValue:n})})});Ac.displayName="BubbleSelect";function Oc(e){const t=Le(e),n=i.useRef(""),r=i.useRef(0),o=i.useCallback(a=>{const c=n.current+a;t(c),function l(u){n.current=u,window.clearTimeout(r.current),u!==""&&(r.current=window.setTimeout(()=>l(""),1e3))}(c)},[t]),s=i.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return i.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,s]}function _c(e,t,n){const o=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let a=mg(e,Math.max(s,0));o.length===1&&(a=a.filter(u=>u!==n));const l=a.find(u=>u.textValue.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}function mg(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var hg=sc,Mc=ic,gg=lc,vg=uc,bg=dc,jc=fc,xg=gc,Ic=xc,Dc=wc,yg=Cc,wg=Sc,kc=$c,Lc=Rc,Fc=Nc;const io=hg,co=gg,Fn=i.forwardRef(({className:e,children:t,...n},r)=>f.jsxs(Mc,{ref:r,className:U("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...n,children:[t,f.jsx(vg,{asChild:!0,children:f.jsx(Ht,{className:"h-4 w-4 opacity-50"})})]}));Fn.displayName=Mc.displayName;const Bc=i.forwardRef(({className:e,...t},n)=>f.jsx(kc,{ref:n,className:U("flex cursor-default items-center justify-center py-1",e),...t,children:f.jsx(tf,{className:"h-4 w-4"})}));Bc.displayName=kc.displayName;const Uc=i.forwardRef(({className:e,...t},n)=>f.jsx(Lc,{ref:n,className:U("flex cursor-default items-center justify-center py-1",e),...t,children:f.jsx(Ht,{className:"h-4 w-4"})}));Uc.displayName=Lc.displayName;const Bn=i.forwardRef(({className:e,children:t,position:n="popper",...r},o)=>f.jsx(bg,{children:f.jsxs(jc,{ref:o,className:U("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-muted text-muted-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...r,children:[f.jsx(Bc,{}),f.jsx(xg,{className:U("p-1",n==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),f.jsx(Uc,{})]})}));Bn.displayName=jc.displayName;const Cg=i.forwardRef(({className:e,...t},n)=>f.jsx(Ic,{ref:n,className:U("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t}));Cg.displayName=Ic.displayName;const St=i.forwardRef(({className:e,children:t,...n},r)=>f.jsxs(Dc,{ref:r,className:U("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[f.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:f.jsx(wg,{children:f.jsx(Lo,{className:"h-4 w-4"})})}),f.jsx(yg,{children:t})]}));St.displayName=Dc.displayName;const Eg=i.forwardRef(({className:e,...t},n)=>f.jsx(Fc,{ref:n,className:U("-mx-1 my-1 h-px bg-muted",e),...t}));Eg.displayName=Fc.displayName;function De(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Sg(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function Wc(...e){return t=>e.forEach(n=>Sg(n,t))}function ct(...e){return i.useCallback(Wc(...e),e)}function Vc(e,t=[]){let n=[];function r(s,a){const c=i.createContext(a),l=n.length;n=[...n,a];function u(p){const{scope:g,children:v,...h}=p,m=(g==null?void 0:g[e][l])||c,b=i.useMemo(()=>h,Object.values(h));return f.jsx(m.Provider,{value:b,children:v})}function d(p,g){const v=(g==null?void 0:g[e][l])||c,h=i.useContext(v);if(h)return h;if(a!==void 0)return a;throw new Error(`\`${p}\` must be used within \`${s}\``)}return u.displayName=s+"Provider",[u,d]}const o=()=>{const s=n.map(a=>i.createContext(a));return function(c){const l=(c==null?void 0:c[e])||s;return i.useMemo(()=>({[`__scope${e}`]:{...c,[e]:l}}),[c,l])}};return o.scopeName=e,[r,$g(o,...t)]}function $g(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const a=r.reduce((c,{useScope:l,scopeName:u})=>{const p=l(s)[`__scope${u}`];return{...c,...p}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return n.scopeName=t.scopeName,n}var Hc=i.forwardRef((e,t)=>{const{children:n,...r}=e,o=i.Children.toArray(n),s=o.find(Rg);if(s){const a=s.props.children,c=o.map(l=>l===s?i.Children.count(a)>1?i.Children.only(null):i.isValidElement(a)?a.props.children:null:l);return f.jsx(lo,{...r,ref:t,children:i.isValidElement(a)?i.cloneElement(a,void 0,c):null})}return f.jsx(lo,{...r,ref:t,children:n})});Hc.displayName="Slot";var lo=i.forwardRef((e,t)=>{const{children:n,...r}=e;if(i.isValidElement(n)){const o=Ng(n);return i.cloneElement(n,{...Pg(r,n.props),ref:t?Wc(t,o):o})}return i.Children.count(n)>1?i.Children.only(null):null});lo.displayName="SlotClone";var zc=({children:e})=>f.jsx(f.Fragment,{children:e});function Rg(e){return i.isValidElement(e)&&e.type===zc}function Pg(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...c)=>{s(...c),o(...c)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function Ng(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Tg=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],jt=Tg.reduce((e,t)=>{const n=i.forwardRef((r,o)=>{const{asChild:s,...a}=r,c=s?Hc:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),f.jsx(c,{...a,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function Ag(e,t){e&&Me.flushSync(()=>e.dispatchEvent(t))}function It(e){const t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function Og(e,t=globalThis==null?void 0:globalThis.document){const n=It(e);i.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var _g="DismissableLayer",uo="dismissableLayer.update",Mg="dismissableLayer.pointerDownOutside",jg="dismissableLayer.focusOutside",ya,Gc=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Kc=i.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:s,onInteractOutside:a,onDismiss:c,...l}=e,u=i.useContext(Gc),[d,p]=i.useState(null),g=(d==null?void 0:d.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,v]=i.useState({}),h=ct(t,E=>p(E)),m=Array.from(u.layers),[b]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),x=m.indexOf(b),y=d?m.indexOf(d):-1,w=u.layersWithOutsidePointerEventsDisabled.size>0,C=y>=x,S=kg(E=>{const $=E.target,N=[...u.branches].some(A=>A.contains($));!C||N||(o==null||o(E),a==null||a(E),E.defaultPrevented||c==null||c())},g),P=Lg(E=>{const $=E.target;[...u.branches].some(A=>A.contains($))||(s==null||s(E),a==null||a(E),E.defaultPrevented||c==null||c())},g);return Og(E=>{y===u.layers.size-1&&(r==null||r(E),!E.defaultPrevented&&c&&(E.preventDefault(),c()))},g),i.useEffect(()=>{if(d)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(ya=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(d)),u.layers.add(d),wa(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(g.body.style.pointerEvents=ya)}},[d,g,n,u]),i.useEffect(()=>()=>{d&&(u.layers.delete(d),u.layersWithOutsidePointerEventsDisabled.delete(d),wa())},[d,u]),i.useEffect(()=>{const E=()=>v({});return document.addEventListener(uo,E),()=>document.removeEventListener(uo,E)},[]),f.jsx(jt.div,{...l,ref:h,style:{pointerEvents:w?C?"auto":"none":void 0,...e.style},onFocusCapture:De(e.onFocusCapture,P.onFocusCapture),onBlurCapture:De(e.onBlurCapture,P.onBlurCapture),onPointerDownCapture:De(e.onPointerDownCapture,S.onPointerDownCapture)})});Kc.displayName=_g;var Ig="DismissableLayerBranch",Dg=i.forwardRef((e,t)=>{const n=i.useContext(Gc),r=i.useRef(null),o=ct(t,r);return i.useEffect(()=>{const s=r.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),f.jsx(jt.div,{...e,ref:o})});Dg.displayName=Ig;function kg(e,t=globalThis==null?void 0:globalThis.document){const n=It(e),r=i.useRef(!1),o=i.useRef(()=>{});return i.useEffect(()=>{const s=c=>{if(c.target&&!r.current){let l=function(){qc(Mg,n,u,{discrete:!0})};const u={originalEvent:c};c.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=l,t.addEventListener("click",o.current,{once:!0})):l()}else t.removeEventListener("click",o.current);r.current=!1},a=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(a),t.removeEventListener("pointerdown",s),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Lg(e,t=globalThis==null?void 0:globalThis.document){const n=It(e),r=i.useRef(!1);return i.useEffect(()=>{const o=s=>{s.target&&!r.current&&qc(jg,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function wa(){const e=new CustomEvent(uo);document.dispatchEvent(e)}function qc(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Ag(o,s):o.dispatchEvent(s)}var Rt=globalThis!=null&&globalThis.document?i.useLayoutEffect:()=>{},Fg=Jt.useId||(()=>{}),Bg=0;function Ug(e){const[t,n]=i.useState(Fg());return Rt(()=>{e||n(r=>r??String(Bg++))},[e]),e||(t?`radix-${t}`:"")}var Wg="Arrow",Yc=i.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...s}=e;return f.jsx(jt.svg,{...s,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:f.jsx("polygon",{points:"0,0 30,0 15,10"})})});Yc.displayName=Wg;var Vg=Yc;function Hg(e){const[t,n]=i.useState(void 0);return Rt(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const s=o[0];let a,c;if("borderBoxSize"in s){const l=s.borderBoxSize,u=Array.isArray(l)?l[0]:l;a=u.inlineSize,c=u.blockSize}else a=e.offsetWidth,c=e.offsetHeight;n({width:a,height:c})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var vs="Popper",[Xc,Jc]=Vc(vs),[zg,Zc]=Xc(vs),Qc=e=>{const{__scopePopper:t,children:n}=e,[r,o]=i.useState(null);return f.jsx(zg,{scope:t,anchor:r,onAnchorChange:o,children:n})};Qc.displayName=vs;var el="PopperAnchor",tl=i.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,s=Zc(el,n),a=i.useRef(null),c=ct(t,a);return i.useEffect(()=>{s.onAnchorChange((r==null?void 0:r.current)||a.current)}),r?null:f.jsx(jt.div,{...o,ref:c})});tl.displayName=el;var bs="PopperContent",[Gg,Kg]=Xc(bs),nl=i.forwardRef((e,t)=>{var I,K,H,z,q,Y;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:s="center",alignOffset:a=0,arrowPadding:c=0,avoidCollisions:l=!0,collisionBoundary:u=[],collisionPadding:d=0,sticky:p="partial",hideWhenDetached:g=!1,updatePositionStrategy:v="optimized",onPlaced:h,...m}=e,b=Zc(bs,n),[x,y]=i.useState(null),w=ct(t,te=>y(te)),[C,S]=i.useState(null),P=Hg(C),E=(P==null?void 0:P.width)??0,$=(P==null?void 0:P.height)??0,N=r+(s!=="center"?"-"+s:""),A=typeof d=="number"?d:{top:0,right:0,bottom:0,left:0,...d},O=Array.isArray(u)?u:[u],_=O.length>0,L={padding:A,boundary:O.filter(Yg),altBoundary:_},{refs:D,floatingStyles:M,placement:k,isPositioned:j,middlewareData:F}=is({strategy:"fixed",placement:N,whileElementsMounted:(...te)=>es(...te,{animationFrame:v==="always"}),elements:{reference:b.anchor},middleware:[Xo({mainAxis:o+$,alignmentAxis:a}),l&&ts({mainAxis:!0,crossAxis:!1,limiter:p==="partial"?ss():void 0,...L}),l&&ns({...L}),rs({...L,apply:({elements:te,rects:fe,availableWidth:he,availableHeight:Ne})=>{const{width:je,height:Lt}=fe.reference,Ee=te.floating.style;Ee.setProperty("--radix-popper-available-width",`${he}px`),Ee.setProperty("--radix-popper-available-height",`${Ne}px`),Ee.setProperty("--radix-popper-anchor-width",`${je}px`),Ee.setProperty("--radix-popper-anchor-height",`${Lt}px`)}}),C&&as({element:C,padding:c}),Xg({arrowWidth:E,arrowHeight:$}),g&&os({strategy:"referenceHidden",...L})]}),[T,J]=sl(k),X=It(h);Rt(()=>{j&&(X==null||X())},[j,X]);const ae=(I=F.arrow)==null?void 0:I.x,ce=(K=F.arrow)==null?void 0:K.y,le=((H=F.arrow)==null?void 0:H.centerOffset)!==0,[ue,ee]=i.useState();return Rt(()=>{x&&ee(window.getComputedStyle(x).zIndex)},[x]),f.jsx("div",{ref:D.setFloating,"data-radix-popper-content-wrapper":"",style:{...M,transform:j?M.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ue,"--radix-popper-transform-origin":[(z=F.transformOrigin)==null?void 0:z.x,(q=F.transformOrigin)==null?void 0:q.y].join(" "),...((Y=F.hide)==null?void 0:Y.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:f.jsx(Gg,{scope:n,placedSide:T,onArrowChange:S,arrowX:ae,arrowY:ce,shouldHideArrow:le,children:f.jsx(jt.div,{"data-side":T,"data-align":J,...m,ref:w,style:{...m.style,animation:j?void 0:"none"}})})})});nl.displayName=bs;var rl="PopperArrow",qg={top:"bottom",right:"left",bottom:"top",left:"right"},ol=i.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,s=Kg(rl,r),a=qg[s.placedSide];return f.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:f.jsx(Vg,{...o,ref:n,style:{...o.style,display:"block"}})})});ol.displayName=rl;function Yg(e){return e!==null}var Xg=e=>({name:"transformOrigin",options:e,fn(t){var b,x,y;const{placement:n,rects:r,middlewareData:o}=t,a=((b=o.arrow)==null?void 0:b.centerOffset)!==0,c=a?0:e.arrowWidth,l=a?0:e.arrowHeight,[u,d]=sl(n),p={start:"0%",center:"50%",end:"100%"}[d],g=(((x=o.arrow)==null?void 0:x.x)??0)+c/2,v=(((y=o.arrow)==null?void 0:y.y)??0)+l/2;let h="",m="";return u==="bottom"?(h=a?p:`${g}px`,m=`${-l}px`):u==="top"?(h=a?p:`${g}px`,m=`${r.floating.height+l}px`):u==="right"?(h=`${-l}px`,m=a?p:`${v}px`):u==="left"&&(h=`${r.floating.width+l}px`,m=a?p:`${v}px`),{data:{x:h,y:m}}}});function sl(e){const[t,n="center"]=e.split("-");return[t,n]}var Jg=Qc,Zg=tl,Qg=nl,ev=ol;function tv(e,t){return i.useReducer((n,r)=>t[n][r]??n,e)}var al=e=>{const{present:t,children:n}=e,r=nv(t),o=typeof n=="function"?n({present:r.isPresent}):i.Children.only(n),s=ct(r.ref,rv(o));return typeof n=="function"||r.isPresent?i.cloneElement(o,{ref:s}):null};al.displayName="Presence";function nv(e){const[t,n]=i.useState(),r=i.useRef({}),o=i.useRef(e),s=i.useRef("none"),a=e?"mounted":"unmounted",[c,l]=tv(a,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return i.useEffect(()=>{const u=pn(r.current);s.current=c==="mounted"?u:"none"},[c]),Rt(()=>{const u=r.current,d=o.current;if(d!==e){const g=s.current,v=pn(u);e?l("MOUNT"):v==="none"||(u==null?void 0:u.display)==="none"?l("UNMOUNT"):l(d&&g!==v?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),Rt(()=>{if(t){const u=p=>{const v=pn(r.current).includes(p.animationName);p.target===t&&v&&Me.flushSync(()=>l("ANIMATION_END"))},d=p=>{p.target===t&&(s.current=pn(r.current))};return t.addEventListener("animationstart",d),t.addEventListener("animationcancel",u),t.addEventListener("animationend",u),()=>{t.removeEventListener("animationstart",d),t.removeEventListener("animationcancel",u),t.removeEventListener("animationend",u)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:i.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function pn(e){return(e==null?void 0:e.animationName)||"none"}function rv(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function ov({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=sv({defaultProp:t,onChange:n}),s=e!==void 0,a=s?e:r,c=It(n),l=i.useCallback(u=>{if(s){const p=typeof u=="function"?u(e):u;p!==e&&c(p)}else o(u)},[s,e,o,c]);return[a,l]}function sv({defaultProp:e,onChange:t}){const n=i.useState(e),[r]=n,o=i.useRef(r),s=It(t);return i.useEffect(()=>{o.current!==r&&(s(r),o.current=r)},[r,o,s]),n}var[dr,aC]=Vc("Tooltip",[Jc]),fr=Jc(),il="TooltipProvider",av=700,fo="tooltip.open",[iv,xs]=dr(il),cl=e=>{const{__scopeTooltip:t,delayDuration:n=av,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:s}=e,[a,c]=i.useState(!0),l=i.useRef(!1),u=i.useRef(0);return i.useEffect(()=>{const d=u.current;return()=>window.clearTimeout(d)},[]),f.jsx(iv,{scope:t,isOpenDelayed:a,delayDuration:n,onOpen:i.useCallback(()=>{window.clearTimeout(u.current),c(!1)},[]),onClose:i.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>c(!0),r)},[r]),isPointerInTransitRef:l,onPointerInTransitChange:i.useCallback(d=>{l.current=d},[]),disableHoverableContent:o,children:s})};cl.displayName=il;var pr="Tooltip",[cv,mr]=dr(pr),ll=e=>{const{__scopeTooltip:t,children:n,open:r,defaultOpen:o=!1,onOpenChange:s,disableHoverableContent:a,delayDuration:c}=e,l=xs(pr,e.__scopeTooltip),u=fr(t),[d,p]=i.useState(null),g=Ug(),v=i.useRef(0),h=a??l.disableHoverableContent,m=c??l.delayDuration,b=i.useRef(!1),[x=!1,y]=ov({prop:r,defaultProp:o,onChange:E=>{E?(l.onOpen(),document.dispatchEvent(new CustomEvent(fo))):l.onClose(),s==null||s(E)}}),w=i.useMemo(()=>x?b.current?"delayed-open":"instant-open":"closed",[x]),C=i.useCallback(()=>{window.clearTimeout(v.current),b.current=!1,y(!0)},[y]),S=i.useCallback(()=>{window.clearTimeout(v.current),y(!1)},[y]),P=i.useCallback(()=>{window.clearTimeout(v.current),v.current=window.setTimeout(()=>{b.current=!0,y(!0)},m)},[m,y]);return i.useEffect(()=>()=>window.clearTimeout(v.current),[]),f.jsx(Jg,{...u,children:f.jsx(cv,{scope:t,contentId:g,open:x,stateAttribute:w,trigger:d,onTriggerChange:p,onTriggerEnter:i.useCallback(()=>{l.isOpenDelayed?P():C()},[l.isOpenDelayed,P,C]),onTriggerLeave:i.useCallback(()=>{h?S():window.clearTimeout(v.current)},[S,h]),onOpen:C,onClose:S,disableHoverableContent:h,children:n})})};ll.displayName=pr;var po="TooltipTrigger",ul=i.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=mr(po,n),s=xs(po,n),a=fr(n),c=i.useRef(null),l=ct(t,c,o.onTriggerChange),u=i.useRef(!1),d=i.useRef(!1),p=i.useCallback(()=>u.current=!1,[]);return i.useEffect(()=>()=>document.removeEventListener("pointerup",p),[p]),f.jsx(Zg,{asChild:!0,...a,children:f.jsx(jt.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:l,onPointerMove:De(e.onPointerMove,g=>{g.pointerType!=="touch"&&!d.current&&!s.isPointerInTransitRef.current&&(o.onTriggerEnter(),d.current=!0)}),onPointerLeave:De(e.onPointerLeave,()=>{o.onTriggerLeave(),d.current=!1}),onPointerDown:De(e.onPointerDown,()=>{u.current=!0,document.addEventListener("pointerup",p,{once:!0})}),onFocus:De(e.onFocus,()=>{u.current||o.onOpen()}),onBlur:De(e.onBlur,o.onClose),onClick:De(e.onClick,o.onClose)})})});ul.displayName=po;var lv="TooltipPortal",[iC,uv]=dr(lv,{forceMount:void 0}),Pt="TooltipContent",dl=i.forwardRef((e,t)=>{const n=uv(Pt,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...s}=e,a=mr(Pt,e.__scopeTooltip);return f.jsx(al,{present:r||a.open,children:a.disableHoverableContent?f.jsx(fl,{side:o,...s,ref:t}):f.jsx(dv,{side:o,...s,ref:t})})}),dv=i.forwardRef((e,t)=>{const n=mr(Pt,e.__scopeTooltip),r=xs(Pt,e.__scopeTooltip),o=i.useRef(null),s=ct(t,o),[a,c]=i.useState(null),{trigger:l,onClose:u}=n,d=o.current,{onPointerInTransitChange:p}=r,g=i.useCallback(()=>{c(null),p(!1)},[p]),v=i.useCallback((h,m)=>{const b=h.currentTarget,x={x:h.clientX,y:h.clientY},y=hv(x,b.getBoundingClientRect()),w=gv(x,y),C=vv(m.getBoundingClientRect()),S=xv([...w,...C]);c(S),p(!0)},[p]);return i.useEffect(()=>()=>g(),[g]),i.useEffect(()=>{if(l&&d){const h=b=>v(b,d),m=b=>v(b,l);return l.addEventListener("pointerleave",h),d.addEventListener("pointerleave",m),()=>{l.removeEventListener("pointerleave",h),d.removeEventListener("pointerleave",m)}}},[l,d,v,g]),i.useEffect(()=>{if(a){const h=m=>{const b=m.target,x={x:m.clientX,y:m.clientY},y=(l==null?void 0:l.contains(b))||(d==null?void 0:d.contains(b)),w=!bv(x,a);y?g():w&&(g(),u())};return document.addEventListener("pointermove",h),()=>document.removeEventListener("pointermove",h)}},[l,d,a,u,g]),f.jsx(fl,{...e,ref:s})}),[fv,pv]=dr(pr,{isInside:!1}),fl=i.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:s,onPointerDownOutside:a,...c}=e,l=mr(Pt,n),u=fr(n),{onClose:d}=l;return i.useEffect(()=>(document.addEventListener(fo,d),()=>document.removeEventListener(fo,d)),[d]),i.useEffect(()=>{if(l.trigger){const p=g=>{const v=g.target;v!=null&&v.contains(l.trigger)&&d()};return window.addEventListener("scroll",p,{capture:!0}),()=>window.removeEventListener("scroll",p,{capture:!0})}},[l.trigger,d]),f.jsx(Kc,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:s,onPointerDownOutside:a,onFocusOutside:p=>p.preventDefault(),onDismiss:d,children:f.jsxs(Qg,{"data-state":l.stateAttribute,...u,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[f.jsx(zc,{children:r}),f.jsx(fv,{scope:n,isInside:!0,children:f.jsx(Wd,{id:l.contentId,role:"tooltip",children:o||r})})]})})});dl.displayName=Pt;var pl="TooltipArrow",mv=i.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=fr(n);return pv(pl,n).isInside?null:f.jsx(ev,{...o,...r,ref:t})});mv.displayName=pl;function hv(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),s=Math.abs(t.left-e.x);switch(Math.min(n,r,o,s)){case s:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function gv(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function vv(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function bv(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,a=t.length-1;s<t.length;a=s++){const c=t[s].x,l=t[s].y,u=t[a].x,d=t[a].y;l>r!=d>r&&n<(u-c)*(r-l)/(d-l)+c&&(o=!o)}return o}function xv(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),yv(t)}function yv(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const s=t[t.length-1],a=t[t.length-2];if((s.x-a.x)*(o.y-a.y)>=(s.y-a.y)*(o.x-a.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const s=n[n.length-1],a=n[n.length-2];if((s.x-a.x)*(o.y-a.y)>=(s.y-a.y)*(o.x-a.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var wv=cl,Cv=ll,Ev=ul,ml=dl;const Sv=wv,mo=Cv,ho=Ev,Un=i.forwardRef(({className:e,sideOffset:t=4,...n},r)=>f.jsx(ml,{ref:r,sideOffset:t,className:U("z-50 overflow-hidden rounded-md border border-[#ffffff36] bg-muted px-3 py-1.5 text-sm text-muted-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n}));Un.displayName=ml.displayName;const $v=i.createContext(void 0),Rv={setTheme:e=>{},themes:[]},Pv=()=>{var e;return(e=i.useContext($v))!==null&&e!==void 0?e:Rv};function G(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function rn(e,t=[]){let n=[];function r(s,a){const c=i.createContext(a),l=n.length;n=[...n,a];function u(p){const{scope:g,children:v,...h}=p,m=(g==null?void 0:g[e][l])||c,b=i.useMemo(()=>h,Object.values(h));return i.createElement(m.Provider,{value:b},v)}function d(p,g){const v=(g==null?void 0:g[e][l])||c,h=i.useContext(v);if(h)return h;if(a!==void 0)return a;throw new Error(`\`${p}\` must be used within \`${s}\``)}return u.displayName=s+"Provider",[u,d]}const o=()=>{const s=n.map(a=>i.createContext(a));return function(c){const l=(c==null?void 0:c[e])||s;return i.useMemo(()=>({[`__scope${e}`]:{...c,[e]:l}}),[c,l])}};return o.scopeName=e,[r,Nv(o,...t)]}function Nv(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const a=r.reduce((c,{useScope:l,scopeName:u})=>{const p=l(s)[`__scope${u}`];return{...c,...p}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return n.scopeName=t.scopeName,n}function _e(e){const t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...n)=>{var r;return(r=t.current)===null||r===void 0?void 0:r.call(t,...n)},[])}function hl({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=Tv({defaultProp:t,onChange:n}),s=e!==void 0,a=s?e:r,c=_e(n),l=i.useCallback(u=>{if(s){const p=typeof u=="function"?u(e):u;p!==e&&c(p)}else o(u)},[s,e,o,c]);return[a,l]}function Tv({defaultProp:e,onChange:t}){const n=i.useState(e),[r]=n,o=i.useRef(r),s=_e(t);return i.useEffect(()=>{o.current!==r&&(s(r),o.current=r)},[r,o,s]),n}const Av=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],we=Av.reduce((e,t)=>{const n=i.forwardRef((r,o)=>{const{asChild:s,...a}=r,c=s?Nn:t;return i.useEffect(()=>{window[Symbol.for("radix-ui")]=!0},[]),i.createElement(c,W({},a,{ref:o}))});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function gl(e,t){e&&Me.flushSync(()=>e.dispatchEvent(t))}function vl(e){const t=e+"CollectionProvider",[n,r]=rn(t),[o,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=v=>{const{scope:h,children:m}=v,b=V.useRef(null),x=V.useRef(new Map).current;return V.createElement(o,{scope:h,itemMap:x,collectionRef:b},m)},c=e+"CollectionSlot",l=V.forwardRef((v,h)=>{const{scope:m,children:b}=v,x=s(c,m),y=be(h,x.collectionRef);return V.createElement(Nn,{ref:y},b)}),u=e+"CollectionItemSlot",d="data-radix-collection-item",p=V.forwardRef((v,h)=>{const{scope:m,children:b,...x}=v,y=V.useRef(null),w=be(h,y),C=s(u,m);return V.useEffect(()=>(C.itemMap.set(y,{ref:y,...x}),()=>void C.itemMap.delete(y))),V.createElement(Nn,{[d]:"",ref:w},b)});function g(v){const h=s(e+"CollectionConsumer",v);return V.useCallback(()=>{const b=h.collectionRef.current;if(!b)return[];const x=Array.from(b.querySelectorAll(`[${d}]`));return Array.from(h.itemMap.values()).sort((C,S)=>x.indexOf(C.ref.current)-x.indexOf(S.ref.current))},[h.collectionRef,h.itemMap])}return[{Provider:a,Slot:l,ItemSlot:p},g,r]}const Ov=i.createContext(void 0);function bl(e){const t=i.useContext(Ov);return e||t||"ltr"}function _v(e,t=globalThis==null?void 0:globalThis.document){const n=_e(e);i.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r),()=>t.removeEventListener("keydown",r)},[n,t])}const go="dismissableLayer.update",Mv="dismissableLayer.pointerDownOutside",jv="dismissableLayer.focusOutside";let Ca;const Iv=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Dv=i.forwardRef((e,t)=>{var n;const{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:o,onPointerDownOutside:s,onFocusOutside:a,onInteractOutside:c,onDismiss:l,...u}=e,d=i.useContext(Iv),[p,g]=i.useState(null),v=(n=p==null?void 0:p.ownerDocument)!==null&&n!==void 0?n:globalThis==null?void 0:globalThis.document,[,h]=i.useState({}),m=be(t,$=>g($)),b=Array.from(d.layers),[x]=[...d.layersWithOutsidePointerEventsDisabled].slice(-1),y=b.indexOf(x),w=p?b.indexOf(p):-1,C=d.layersWithOutsidePointerEventsDisabled.size>0,S=w>=y,P=kv($=>{const N=$.target,A=[...d.branches].some(O=>O.contains(N));!S||A||(s==null||s($),c==null||c($),$.defaultPrevented||l==null||l())},v),E=Lv($=>{const N=$.target;[...d.branches].some(O=>O.contains(N))||(a==null||a($),c==null||c($),$.defaultPrevented||l==null||l())},v);return _v($=>{w===d.layers.size-1&&(o==null||o($),!$.defaultPrevented&&l&&($.preventDefault(),l()))},v),i.useEffect(()=>{if(p)return r&&(d.layersWithOutsidePointerEventsDisabled.size===0&&(Ca=v.body.style.pointerEvents,v.body.style.pointerEvents="none"),d.layersWithOutsidePointerEventsDisabled.add(p)),d.layers.add(p),Ea(),()=>{r&&d.layersWithOutsidePointerEventsDisabled.size===1&&(v.body.style.pointerEvents=Ca)}},[p,v,r,d]),i.useEffect(()=>()=>{p&&(d.layers.delete(p),d.layersWithOutsidePointerEventsDisabled.delete(p),Ea())},[p,d]),i.useEffect(()=>{const $=()=>h({});return document.addEventListener(go,$),()=>document.removeEventListener(go,$)},[]),i.createElement(we.div,W({},u,{ref:m,style:{pointerEvents:C?S?"auto":"none":void 0,...e.style},onFocusCapture:G(e.onFocusCapture,E.onFocusCapture),onBlurCapture:G(e.onBlurCapture,E.onBlurCapture),onPointerDownCapture:G(e.onPointerDownCapture,P.onPointerDownCapture)}))});function kv(e,t=globalThis==null?void 0:globalThis.document){const n=_e(e),r=i.useRef(!1),o=i.useRef(()=>{});return i.useEffect(()=>{const s=c=>{if(c.target&&!r.current){let u=function(){xl(Mv,n,l,{discrete:!0})};const l={originalEvent:c};c.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=u,t.addEventListener("click",o.current,{once:!0})):u()}else t.removeEventListener("click",o.current);r.current=!1},a=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(a),t.removeEventListener("pointerdown",s),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Lv(e,t=globalThis==null?void 0:globalThis.document){const n=_e(e),r=i.useRef(!1);return i.useEffect(()=>{const o=s=>{s.target&&!r.current&&xl(jv,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Ea(){const e=new CustomEvent(go);document.dispatchEvent(e)}function xl(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?gl(o,s):o.dispatchEvent(s)}let _r=0;function Fv(){i.useEffect(()=>{var e,t;const n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",(e=n[0])!==null&&e!==void 0?e:Sa()),document.body.insertAdjacentElement("beforeend",(t=n[1])!==null&&t!==void 0?t:Sa()),_r++,()=>{_r===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(r=>r.remove()),_r--}},[])}function Sa(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}const Mr="focusScope.autoFocusOnMount",jr="focusScope.autoFocusOnUnmount",$a={bubbles:!1,cancelable:!0},Bv=i.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:s,...a}=e,[c,l]=i.useState(null),u=_e(o),d=_e(s),p=i.useRef(null),g=be(t,m=>l(m)),v=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(r){let m=function(w){if(v.paused||!c)return;const C=w.target;c.contains(C)?p.current=C:ze(p.current,{select:!0})},b=function(w){if(v.paused||!c)return;const C=w.relatedTarget;C!==null&&(c.contains(C)||ze(p.current,{select:!0}))},x=function(w){if(document.activeElement===document.body)for(const S of w)S.removedNodes.length>0&&ze(c)};document.addEventListener("focusin",m),document.addEventListener("focusout",b);const y=new MutationObserver(x);return c&&y.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",m),document.removeEventListener("focusout",b),y.disconnect()}}},[r,c,v.paused]),i.useEffect(()=>{if(c){Pa.add(v);const m=document.activeElement;if(!c.contains(m)){const x=new CustomEvent(Mr,$a);c.addEventListener(Mr,u),c.dispatchEvent(x),x.defaultPrevented||(Uv(Gv(yl(c)),{select:!0}),document.activeElement===m&&ze(c))}return()=>{c.removeEventListener(Mr,u),setTimeout(()=>{const x=new CustomEvent(jr,$a);c.addEventListener(jr,d),c.dispatchEvent(x),x.defaultPrevented||ze(m??document.body,{select:!0}),c.removeEventListener(jr,d),Pa.remove(v)},0)}}},[c,u,d,v]);const h=i.useCallback(m=>{if(!n&&!r||v.paused)return;const b=m.key==="Tab"&&!m.altKey&&!m.ctrlKey&&!m.metaKey,x=document.activeElement;if(b&&x){const y=m.currentTarget,[w,C]=Wv(y);w&&C?!m.shiftKey&&x===C?(m.preventDefault(),n&&ze(w,{select:!0})):m.shiftKey&&x===w&&(m.preventDefault(),n&&ze(C,{select:!0})):x===y&&m.preventDefault()}},[n,r,v.paused]);return i.createElement(we.div,W({tabIndex:-1},a,{ref:g,onKeyDown:h}))});function Uv(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(ze(r,{select:t}),document.activeElement!==n)return}function Wv(e){const t=yl(e),n=Ra(t,e),r=Ra(t.reverse(),e);return[n,r]}function yl(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Ra(e,t){for(const n of e)if(!Vv(n,{upTo:t}))return n}function Vv(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Hv(e){return e instanceof HTMLInputElement&&"select"in e}function ze(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&Hv(e)&&t&&e.select()}}const Pa=zv();function zv(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=Na(e,t),e.unshift(t)},remove(t){var n;e=Na(e,t),(n=e[0])===null||n===void 0||n.resume()}}}function Na(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function Gv(e){return e.filter(t=>t.tagName!=="A")}const Nt=globalThis!=null&&globalThis.document?i.useLayoutEffect:()=>{},Kv=Jt.useId||(()=>{});let qv=0;function vo(e){const[t,n]=i.useState(Kv());return Nt(()=>{e||n(r=>r??String(qv++))},[e]),e||(t?`radix-${t}`:"")}function Yv(e){const[t,n]=i.useState(void 0);return Nt(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const s=o[0];let a,c;if("borderBoxSize"in s){const l=s.borderBoxSize,u=Array.isArray(l)?l[0]:l;a=u.inlineSize,c=u.blockSize}else a=e.offsetWidth,c=e.offsetHeight;n({width:a,height:c})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}const wl="Popper",[Cl,El]=rn(wl),[Xv,Sl]=Cl(wl),Jv=e=>{const{__scopePopper:t,children:n}=e,[r,o]=i.useState(null);return i.createElement(Xv,{scope:t,anchor:r,onAnchorChange:o},n)},Zv="PopperAnchor",Qv=i.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,s=Sl(Zv,n),a=i.useRef(null),c=be(t,a);return i.useEffect(()=>{s.onAnchorChange((r==null?void 0:r.current)||a.current)}),r?null:i.createElement(we.div,W({},o,{ref:c}))}),$l="PopperContent",[eb,cC]=Cl($l),tb=i.forwardRef((e,t)=>{var n,r,o,s,a,c,l,u;const{__scopePopper:d,side:p="bottom",sideOffset:g=0,align:v="center",alignOffset:h=0,arrowPadding:m=0,avoidCollisions:b=!0,collisionBoundary:x=[],collisionPadding:y=0,sticky:w="partial",hideWhenDetached:C=!1,updatePositionStrategy:S="optimized",onPlaced:P,...E}=e,$=Sl($l,d),[N,A]=i.useState(null),O=be(t,he=>A(he)),[_,L]=i.useState(null),D=Yv(_),M=(n=D==null?void 0:D.width)!==null&&n!==void 0?n:0,k=(r=D==null?void 0:D.height)!==null&&r!==void 0?r:0,j=p+(v!=="center"?"-"+v:""),F=typeof y=="number"?y:{top:0,right:0,bottom:0,left:0,...y},T=Array.isArray(x)?x:[x],J=T.length>0,X={padding:F,boundary:T.filter(nb),altBoundary:J},{refs:ae,floatingStyles:ce,placement:le,isPositioned:ue,middlewareData:ee}=is({strategy:"fixed",placement:j,whileElementsMounted:(...he)=>es(...he,{animationFrame:S==="always"}),elements:{reference:$.anchor},middleware:[Xo({mainAxis:g+k,alignmentAxis:h}),b&&ts({mainAxis:!0,crossAxis:!1,limiter:w==="partial"?ss():void 0,...X}),b&&ns({...X}),rs({...X,apply:({elements:he,rects:Ne,availableWidth:je,availableHeight:Lt})=>{const{width:Ee,height:jd}=Ne.reference,an=he.floating.style;an.setProperty("--radix-popper-available-width",`${je}px`),an.setProperty("--radix-popper-available-height",`${Lt}px`),an.setProperty("--radix-popper-anchor-width",`${Ee}px`),an.setProperty("--radix-popper-anchor-height",`${jd}px`)}}),_&&as({element:_,padding:m}),rb({arrowWidth:M,arrowHeight:k}),C&&os({strategy:"referenceHidden",...X})]}),[I,K]=Rl(le),H=_e(P);Nt(()=>{ue&&(H==null||H())},[ue,H]);const z=(o=ee.arrow)===null||o===void 0?void 0:o.x,q=(s=ee.arrow)===null||s===void 0?void 0:s.y,Y=((a=ee.arrow)===null||a===void 0?void 0:a.centerOffset)!==0,[te,fe]=i.useState();return Nt(()=>{N&&fe(window.getComputedStyle(N).zIndex)},[N]),i.createElement("div",{ref:ae.setFloating,"data-radix-popper-content-wrapper":"",style:{...ce,transform:ue?ce.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:te,"--radix-popper-transform-origin":[(c=ee.transformOrigin)===null||c===void 0?void 0:c.x,(l=ee.transformOrigin)===null||l===void 0?void 0:l.y].join(" ")},dir:e.dir},i.createElement(eb,{scope:d,placedSide:I,onArrowChange:L,arrowX:z,arrowY:q,shouldHideArrow:Y},i.createElement(we.div,W({"data-side":I,"data-align":K},E,{ref:O,style:{...E.style,animation:ue?void 0:"none",opacity:(u=ee.hide)!==null&&u!==void 0&&u.referenceHidden?0:void 0}}))))});function nb(e){return e!==null}const rb=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,s,a;const{placement:c,rects:l,middlewareData:u}=t,p=((n=u.arrow)===null||n===void 0?void 0:n.centerOffset)!==0,g=p?0:e.arrowWidth,v=p?0:e.arrowHeight,[h,m]=Rl(c),b={start:"0%",center:"50%",end:"100%"}[m],x=((r=(o=u.arrow)===null||o===void 0?void 0:o.x)!==null&&r!==void 0?r:0)+g/2,y=((s=(a=u.arrow)===null||a===void 0?void 0:a.y)!==null&&s!==void 0?s:0)+v/2;let w="",C="";return h==="bottom"?(w=p?b:`${x}px`,C=`${-v}px`):h==="top"?(w=p?b:`${x}px`,C=`${l.floating.height+v}px`):h==="right"?(w=`${-v}px`,C=p?b:`${y}px`):h==="left"&&(w=`${l.floating.width+v}px`,C=p?b:`${y}px`),{data:{x:w,y:C}}}});function Rl(e){const[t,n="center"]=e.split("-");return[t,n]}const ob=Jv,sb=Qv,ab=tb,ib=i.forwardRef((e,t)=>{var n;const{container:r=globalThis==null||(n=globalThis.document)===null||n===void 0?void 0:n.body,...o}=e;return r?ko.createPortal(i.createElement(we.div,W({},o,{ref:t})),r):null});function cb(e,t){return i.useReducer((n,r)=>{const o=t[n][r];return o??n},e)}const on=e=>{const{present:t,children:n}=e,r=lb(t),o=typeof n=="function"?n({present:r.isPresent}):i.Children.only(n),s=be(r.ref,o.ref);return typeof n=="function"||r.isPresent?i.cloneElement(o,{ref:s}):null};on.displayName="Presence";function lb(e){const[t,n]=i.useState(),r=i.useRef({}),o=i.useRef(e),s=i.useRef("none"),a=e?"mounted":"unmounted",[c,l]=cb(a,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return i.useEffect(()=>{const u=mn(r.current);s.current=c==="mounted"?u:"none"},[c]),Nt(()=>{const u=r.current,d=o.current;if(d!==e){const g=s.current,v=mn(u);e?l("MOUNT"):v==="none"||(u==null?void 0:u.display)==="none"?l("UNMOUNT"):l(d&&g!==v?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),Nt(()=>{if(t){const u=p=>{const v=mn(r.current).includes(p.animationName);p.target===t&&v&&Me.flushSync(()=>l("ANIMATION_END"))},d=p=>{p.target===t&&(s.current=mn(r.current))};return t.addEventListener("animationstart",d),t.addEventListener("animationcancel",u),t.addEventListener("animationend",u),()=>{t.removeEventListener("animationstart",d),t.removeEventListener("animationcancel",u),t.removeEventListener("animationend",u)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:i.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function mn(e){return(e==null?void 0:e.animationName)||"none"}const Ir="rovingFocusGroup.onEntryFocus",ub={bubbles:!1,cancelable:!0},ys="RovingFocusGroup",[bo,Pl,db]=vl(ys),[fb,Nl]=rn(ys,[db]),[pb,mb]=fb(ys),hb=i.forwardRef((e,t)=>i.createElement(bo.Provider,{scope:e.__scopeRovingFocusGroup},i.createElement(bo.Slot,{scope:e.__scopeRovingFocusGroup},i.createElement(gb,W({},e,{ref:t}))))),gb=i.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:s,currentTabStopId:a,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:l,onEntryFocus:u,...d}=e,p=i.useRef(null),g=be(t,p),v=bl(s),[h=null,m]=hl({prop:a,defaultProp:c,onChange:l}),[b,x]=i.useState(!1),y=_e(u),w=Pl(n),C=i.useRef(!1),[S,P]=i.useState(0);return i.useEffect(()=>{const E=p.current;if(E)return E.addEventListener(Ir,y),()=>E.removeEventListener(Ir,y)},[y]),i.createElement(pb,{scope:n,orientation:r,dir:v,loop:o,currentTabStopId:h,onItemFocus:i.useCallback(E=>m(E),[m]),onItemShiftTab:i.useCallback(()=>x(!0),[]),onFocusableItemAdd:i.useCallback(()=>P(E=>E+1),[]),onFocusableItemRemove:i.useCallback(()=>P(E=>E-1),[])},i.createElement(we.div,W({tabIndex:b||S===0?-1:0,"data-orientation":r},d,{ref:g,style:{outline:"none",...e.style},onMouseDown:G(e.onMouseDown,()=>{C.current=!0}),onFocus:G(e.onFocus,E=>{const $=!C.current;if(E.target===E.currentTarget&&$&&!b){const N=new CustomEvent(Ir,ub);if(E.currentTarget.dispatchEvent(N),!N.defaultPrevented){const A=w().filter(M=>M.focusable),O=A.find(M=>M.active),_=A.find(M=>M.id===h),D=[O,_,...A].filter(Boolean).map(M=>M.ref.current);Tl(D)}}C.current=!1}),onBlur:G(e.onBlur,()=>x(!1))})))}),vb="RovingFocusGroupItem",bb=i.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:s,...a}=e,c=vo(),l=s||c,u=mb(vb,n),d=u.currentTabStopId===l,p=Pl(n),{onFocusableItemAdd:g,onFocusableItemRemove:v}=u;return i.useEffect(()=>{if(r)return g(),()=>v()},[r,g,v]),i.createElement(bo.ItemSlot,{scope:n,id:l,focusable:r,active:o},i.createElement(we.span,W({tabIndex:d?0:-1,"data-orientation":u.orientation},a,{ref:t,onMouseDown:G(e.onMouseDown,h=>{r?u.onItemFocus(l):h.preventDefault()}),onFocus:G(e.onFocus,()=>u.onItemFocus(l)),onKeyDown:G(e.onKeyDown,h=>{if(h.key==="Tab"&&h.shiftKey){u.onItemShiftTab();return}if(h.target!==h.currentTarget)return;const m=wb(h,u.orientation,u.dir);if(m!==void 0){h.preventDefault();let x=p().filter(y=>y.focusable).map(y=>y.ref.current);if(m==="last")x.reverse();else if(m==="prev"||m==="next"){m==="prev"&&x.reverse();const y=x.indexOf(h.currentTarget);x=u.loop?Cb(x,y+1):x.slice(y+1)}setTimeout(()=>Tl(x))}})})))}),xb={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function yb(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function wb(e,t,n){const r=yb(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return xb[r]}function Tl(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function Cb(e,t){return e.map((n,r)=>e[(t+r)%e.length])}const Eb=hb,Sb=bb;var Al=ps(),Dr=function(){},hr=i.forwardRef(function(e,t){var n=i.useRef(null),r=i.useState({onScrollCapture:Dr,onWheelCapture:Dr,onTouchMoveCapture:Dr}),o=r[0],s=r[1],a=e.forwardProps,c=e.children,l=e.className,u=e.removeScrollBar,d=e.enabled,p=e.shards,g=e.sideCar,v=e.noIsolation,h=e.inert,m=e.allowPinchZoom,b=e.as,x=b===void 0?"div":b,y=sr(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as"]),w=g,C=fs([n,t]),S=Q(Q({},y),o);return i.createElement(i.Fragment,null,d&&i.createElement(w,{sideCar:Al,removeScrollBar:u,shards:p,noIsolation:v,inert:h,setCallbacks:s,allowPinchZoom:!!m,lockRef:n}),a?i.cloneElement(i.Children.only(c),Q(Q({},S),{ref:C})):i.createElement(x,Q({},S,{className:l,ref:C}),c))});hr.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};hr.classNames={fullWidth:Ct,zeroRight:wt};var xo=!1;if(typeof window<"u")try{var hn=Object.defineProperty({},"passive",{get:function(){return xo=!0,!0}});window.addEventListener("test",hn,hn),window.removeEventListener("test",hn,hn)}catch{xo=!1}var ht=xo?{passive:!1}:!1,$b=function(e){return e.tagName==="TEXTAREA"},Ol=function(e,t){var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!$b(e)&&n[t]==="visible")},Rb=function(e){return Ol(e,"overflowY")},Pb=function(e){return Ol(e,"overflowX")},Ta=function(e,t){var n=t;do{typeof ShadowRoot<"u"&&n instanceof ShadowRoot&&(n=n.host);var r=_l(e,n);if(r){var o=Ml(e,n),s=o[1],a=o[2];if(s>a)return!0}n=n.parentNode}while(n&&n!==document.body);return!1},Nb=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},Tb=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},_l=function(e,t){return e==="v"?Rb(t):Pb(t)},Ml=function(e,t){return e==="v"?Nb(t):Tb(t)},Ab=function(e,t){return e==="h"&&t==="rtl"?-1:1},Ob=function(e,t,n,r,o){var s=Ab(e,window.getComputedStyle(t).direction),a=s*r,c=n.target,l=t.contains(c),u=!1,d=a>0,p=0,g=0;do{var v=Ml(e,c),h=v[0],m=v[1],b=v[2],x=m-b-s*h;(h||x)&&_l(e,c)&&(p+=x,g+=h),c=c.parentNode}while(!l&&c!==document.body||l&&(t.contains(c)||t===c));return(d&&(o&&p===0||!o&&a>p)||!d&&(o&&g===0||!o&&-a>g))&&(u=!0),u},gn=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Aa=function(e){return[e.deltaX,e.deltaY]},Oa=function(e){return e&&"current"in e?e.current:e},_b=function(e,t){return e[0]===t[0]&&e[1]===t[1]},Mb=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},jb=0,gt=[];function Ib(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),o=i.useState(jb++)[0],s=i.useState(function(){return ir()})[0],a=i.useRef(e);i.useEffect(function(){a.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var m=ds([e.lockRef.current],(e.shards||[]).map(Oa),!0).filter(Boolean);return m.forEach(function(b){return b.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),m.forEach(function(b){return b.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=i.useCallback(function(m,b){if("touches"in m&&m.touches.length===2)return!a.current.allowPinchZoom;var x=gn(m),y=n.current,w="deltaX"in m?m.deltaX:y[0]-x[0],C="deltaY"in m?m.deltaY:y[1]-x[1],S,P=m.target,E=Math.abs(w)>Math.abs(C)?"h":"v";if("touches"in m&&E==="h"&&P.type==="range")return!1;var $=Ta(E,P);if(!$)return!0;if($?S=E:(S=E==="v"?"h":"v",$=Ta(E,P)),!$)return!1;if(!r.current&&"changedTouches"in m&&(w||C)&&(r.current=S),!S)return!0;var N=r.current||S;return Ob(N,b,m,N==="h"?w:C,!0)},[]),l=i.useCallback(function(m){var b=m;if(!(!gt.length||gt[gt.length-1]!==s)){var x="deltaY"in b?Aa(b):gn(b),y=t.current.filter(function(S){return S.name===b.type&&S.target===b.target&&_b(S.delta,x)})[0];if(y&&y.should){b.cancelable&&b.preventDefault();return}if(!y){var w=(a.current.shards||[]).map(Oa).filter(Boolean).filter(function(S){return S.contains(b.target)}),C=w.length>0?c(b,w[0]):!a.current.noIsolation;C&&b.cancelable&&b.preventDefault()}}},[]),u=i.useCallback(function(m,b,x,y){var w={name:m,delta:b,target:x,should:y};t.current.push(w),setTimeout(function(){t.current=t.current.filter(function(C){return C!==w})},1)},[]),d=i.useCallback(function(m){n.current=gn(m),r.current=void 0},[]),p=i.useCallback(function(m){u(m.type,Aa(m),m.target,c(m,e.lockRef.current))},[]),g=i.useCallback(function(m){u(m.type,gn(m),m.target,c(m,e.lockRef.current))},[]);i.useEffect(function(){return gt.push(s),e.setCallbacks({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:g}),document.addEventListener("wheel",l,ht),document.addEventListener("touchmove",l,ht),document.addEventListener("touchstart",d,ht),function(){gt=gt.filter(function(m){return m!==s}),document.removeEventListener("wheel",l,ht),document.removeEventListener("touchmove",l,ht),document.removeEventListener("touchstart",d,ht)}},[]);var v=e.removeScrollBar,h=e.inert;return i.createElement(i.Fragment,null,h?i.createElement(s,{styles:Mb(o)}):null,v?i.createElement(hs,{gapMode:"margin"}):null)}const Db=ms(Al,Ib);var jl=i.forwardRef(function(e,t){return i.createElement(hr,Q({},e,{ref:t,sideCar:Db}))});jl.classNames=hr.classNames;const kb=jl,yo=["Enter"," "],Lb=["ArrowDown","PageUp","Home"],Il=["ArrowUp","PageDown","End"],Fb=[...Lb,...Il],Bb={ltr:[...yo,"ArrowRight"],rtl:[...yo,"ArrowLeft"]},Ub={ltr:["ArrowLeft"],rtl:["ArrowRight"]},gr="Menu",[Yt,Wb,Vb]=vl(gr),[lt,Dl]=rn(gr,[Vb,El,Nl]),ws=El(),kl=Nl(),[Hb,ut]=lt(gr),[zb,sn]=lt(gr),Gb=e=>{const{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:s,modal:a=!0}=e,c=ws(t),[l,u]=i.useState(null),d=i.useRef(!1),p=_e(s),g=bl(o);return i.useEffect(()=>{const v=()=>{d.current=!0,document.addEventListener("pointerdown",h,{capture:!0,once:!0}),document.addEventListener("pointermove",h,{capture:!0,once:!0})},h=()=>d.current=!1;return document.addEventListener("keydown",v,{capture:!0}),()=>{document.removeEventListener("keydown",v,{capture:!0}),document.removeEventListener("pointerdown",h,{capture:!0}),document.removeEventListener("pointermove",h,{capture:!0})}},[]),i.createElement(ob,c,i.createElement(Hb,{scope:t,open:n,onOpenChange:p,content:l,onContentChange:u},i.createElement(zb,{scope:t,onClose:i.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:d,dir:g,modal:a},r)))},Ll=i.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=ws(n);return i.createElement(sb,W({},o,r,{ref:t}))}),Fl="MenuPortal",[Kb,Bl]=lt(Fl,{forceMount:void 0}),qb=e=>{const{__scopeMenu:t,forceMount:n,children:r,container:o}=e,s=ut(Fl,t);return i.createElement(Kb,{scope:t,forceMount:n},i.createElement(on,{present:n||s.open},i.createElement(ib,{asChild:!0,container:o},r)))},$e="MenuContent",[Yb,Cs]=lt($e),Xb=i.forwardRef((e,t)=>{const n=Bl($e,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,s=ut($e,e.__scopeMenu),a=sn($e,e.__scopeMenu);return i.createElement(Yt.Provider,{scope:e.__scopeMenu},i.createElement(on,{present:r||s.open},i.createElement(Yt.Slot,{scope:e.__scopeMenu},a.modal?i.createElement(Jb,W({},o,{ref:t})):i.createElement(Zb,W({},o,{ref:t})))))}),Jb=i.forwardRef((e,t)=>{const n=ut($e,e.__scopeMenu),r=i.useRef(null),o=be(t,r);return i.useEffect(()=>{const s=r.current;if(s)return us(s)},[]),i.createElement(Es,W({},e,{ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:G(e.onFocusOutside,s=>s.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)}))}),Zb=i.forwardRef((e,t)=>{const n=ut($e,e.__scopeMenu);return i.createElement(Es,W({},e,{ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)}))}),Es=i.forwardRef((e,t)=>{const{__scopeMenu:n,loop:r=!1,trapFocus:o,onOpenAutoFocus:s,onCloseAutoFocus:a,disableOutsidePointerEvents:c,onEntryFocus:l,onEscapeKeyDown:u,onPointerDownOutside:d,onFocusOutside:p,onInteractOutside:g,onDismiss:v,disableOutsideScroll:h,...m}=e,b=ut($e,n),x=sn($e,n),y=ws(n),w=kl(n),C=Wb(n),[S,P]=i.useState(null),E=i.useRef(null),$=be(t,E,b.onContentChange),N=i.useRef(0),A=i.useRef(""),O=i.useRef(0),_=i.useRef(null),L=i.useRef("right"),D=i.useRef(0),M=h?kb:i.Fragment,k=h?{as:Nn,allowPinchZoom:!0}:void 0,j=T=>{var J,X;const ae=A.current+T,ce=C().filter(H=>!H.disabled),le=document.activeElement,ue=(J=ce.find(H=>H.ref.current===le))===null||J===void 0?void 0:J.textValue,ee=ce.map(H=>H.textValue),I=mx(ee,ae,ue),K=(X=ce.find(H=>H.textValue===I))===null||X===void 0?void 0:X.ref.current;(function H(z){A.current=z,window.clearTimeout(N.current),z!==""&&(N.current=window.setTimeout(()=>H(""),1e3))})(ae),K&&setTimeout(()=>K.focus())};i.useEffect(()=>()=>window.clearTimeout(N.current),[]),Fv();const F=i.useCallback(T=>{var J,X;return L.current===((J=_.current)===null||J===void 0?void 0:J.side)&&gx(T,(X=_.current)===null||X===void 0?void 0:X.area)},[]);return i.createElement(Yb,{scope:n,searchRef:A,onItemEnter:i.useCallback(T=>{F(T)&&T.preventDefault()},[F]),onItemLeave:i.useCallback(T=>{var J;F(T)||((J=E.current)===null||J===void 0||J.focus(),P(null))},[F]),onTriggerLeave:i.useCallback(T=>{F(T)&&T.preventDefault()},[F]),pointerGraceTimerRef:O,onPointerGraceIntentChange:i.useCallback(T=>{_.current=T},[])},i.createElement(M,k,i.createElement(Bv,{asChild:!0,trapped:o,onMountAutoFocus:G(s,T=>{var J;T.preventDefault(),(J=E.current)===null||J===void 0||J.focus()}),onUnmountAutoFocus:a},i.createElement(Dv,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:u,onPointerDownOutside:d,onFocusOutside:p,onInteractOutside:g,onDismiss:v},i.createElement(Eb,W({asChild:!0},w,{dir:x.dir,orientation:"vertical",loop:r,currentTabStopId:S,onCurrentTabStopIdChange:P,onEntryFocus:G(l,T=>{x.isUsingKeyboardRef.current||T.preventDefault()})}),i.createElement(ab,W({role:"menu","aria-orientation":"vertical","data-state":zl(b.open),"data-radix-menu-content":"",dir:x.dir},y,m,{ref:$,style:{outline:"none",...m.style},onKeyDown:G(m.onKeyDown,T=>{const X=T.target.closest("[data-radix-menu-content]")===T.currentTarget,ae=T.ctrlKey||T.altKey||T.metaKey,ce=T.key.length===1;X&&(T.key==="Tab"&&T.preventDefault(),!ae&&ce&&j(T.key));const le=E.current;if(T.target!==le||!Fb.includes(T.key))return;T.preventDefault();const ee=C().filter(I=>!I.disabled).map(I=>I.ref.current);Il.includes(T.key)&&ee.reverse(),fx(ee)}),onBlur:G(e.onBlur,T=>{T.currentTarget.contains(T.target)||(window.clearTimeout(N.current),A.current="")}),onPointerMove:G(e.onPointerMove,Xt(T=>{const J=T.target,X=D.current!==T.clientX;if(T.currentTarget.contains(J)&&X){const ae=T.clientX>D.current?"right":"left";L.current=ae,D.current=T.clientX}}))})))))))}),Qb=i.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return i.createElement(we.div,W({},r,{ref:t}))}),wo="MenuItem",_a="menu.itemSelect",Ss=i.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:r,...o}=e,s=i.useRef(null),a=sn(wo,e.__scopeMenu),c=Cs(wo,e.__scopeMenu),l=be(t,s),u=i.useRef(!1),d=()=>{const p=s.current;if(!n&&p){const g=new CustomEvent(_a,{bubbles:!0,cancelable:!0});p.addEventListener(_a,v=>r==null?void 0:r(v),{once:!0}),gl(p,g),g.defaultPrevented?u.current=!1:a.onClose()}};return i.createElement(Ul,W({},o,{ref:l,disabled:n,onClick:G(e.onClick,d),onPointerDown:p=>{var g;(g=e.onPointerDown)===null||g===void 0||g.call(e,p),u.current=!0},onPointerUp:G(e.onPointerUp,p=>{var g;u.current||(g=p.currentTarget)===null||g===void 0||g.click()}),onKeyDown:G(e.onKeyDown,p=>{const g=c.searchRef.current!=="";n||g&&p.key===" "||yo.includes(p.key)&&(p.currentTarget.click(),p.preventDefault())})}))}),Ul=i.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:r=!1,textValue:o,...s}=e,a=Cs(wo,n),c=kl(n),l=i.useRef(null),u=be(t,l),[d,p]=i.useState(!1),[g,v]=i.useState("");return i.useEffect(()=>{const h=l.current;if(h){var m;v(((m=h.textContent)!==null&&m!==void 0?m:"").trim())}},[s.children]),i.createElement(Yt.ItemSlot,{scope:n,disabled:r,textValue:o??g},i.createElement(Sb,W({asChild:!0},c,{focusable:!r}),i.createElement(we.div,W({role:"menuitem","data-highlighted":d?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0},s,{ref:u,onPointerMove:G(e.onPointerMove,Xt(h=>{r?a.onItemLeave(h):(a.onItemEnter(h),h.defaultPrevented||h.currentTarget.focus())})),onPointerLeave:G(e.onPointerLeave,Xt(h=>a.onItemLeave(h))),onFocus:G(e.onFocus,()=>p(!0)),onBlur:G(e.onBlur,()=>p(!1))}))))}),ex=i.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:r,...o}=e;return i.createElement(Vl,{scope:e.__scopeMenu,checked:n},i.createElement(Ss,W({role:"menuitemcheckbox","aria-checked":Wn(n)?"mixed":n},o,{ref:t,"data-state":$s(n),onSelect:G(o.onSelect,()=>r==null?void 0:r(Wn(n)?!0:!n),{checkForDefaultPrevented:!1})})))}),tx="MenuRadioGroup",[lC,nx]=lt(tx,{value:void 0,onValueChange:()=>{}}),rx="MenuRadioItem",ox=i.forwardRef((e,t)=>{const{value:n,...r}=e,o=nx(rx,e.__scopeMenu),s=n===o.value;return i.createElement(Vl,{scope:e.__scopeMenu,checked:s},i.createElement(Ss,W({role:"menuitemradio","aria-checked":s},r,{ref:t,"data-state":$s(s),onSelect:G(r.onSelect,()=>{var a;return(a=o.onValueChange)===null||a===void 0?void 0:a.call(o,n)},{checkForDefaultPrevented:!1})})))}),Wl="MenuItemIndicator",[Vl,sx]=lt(Wl,{checked:!1}),ax=i.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:r,...o}=e,s=sx(Wl,n);return i.createElement(on,{present:r||Wn(s.checked)||s.checked===!0},i.createElement(we.span,W({},o,{ref:t,"data-state":$s(s.checked)})))}),ix=i.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return i.createElement(we.div,W({role:"separator","aria-orientation":"horizontal"},r,{ref:t}))}),cx="MenuSub",[uC,Hl]=lt(cx),vn="MenuSubTrigger",lx=i.forwardRef((e,t)=>{const n=ut(vn,e.__scopeMenu),r=sn(vn,e.__scopeMenu),o=Hl(vn,e.__scopeMenu),s=Cs(vn,e.__scopeMenu),a=i.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:l}=s,u={__scopeMenu:e.__scopeMenu},d=i.useCallback(()=>{a.current&&window.clearTimeout(a.current),a.current=null},[]);return i.useEffect(()=>d,[d]),i.useEffect(()=>{const p=c.current;return()=>{window.clearTimeout(p),l(null)}},[c,l]),i.createElement(Ll,W({asChild:!0},u),i.createElement(Ul,W({id:o.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":o.contentId,"data-state":zl(n.open)},e,{ref:qa(t,o.onTriggerChange),onClick:p=>{var g;(g=e.onClick)===null||g===void 0||g.call(e,p),!(e.disabled||p.defaultPrevented)&&(p.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:G(e.onPointerMove,Xt(p=>{s.onItemEnter(p),!p.defaultPrevented&&!e.disabled&&!n.open&&!a.current&&(s.onPointerGraceIntentChange(null),a.current=window.setTimeout(()=>{n.onOpenChange(!0),d()},100))})),onPointerLeave:G(e.onPointerLeave,Xt(p=>{var g;d();const v=(g=n.content)===null||g===void 0?void 0:g.getBoundingClientRect();if(v){var h;const m=(h=n.content)===null||h===void 0?void 0:h.dataset.side,b=m==="right",x=b?-5:5,y=v[b?"left":"right"],w=v[b?"right":"left"];s.onPointerGraceIntentChange({area:[{x:p.clientX+x,y:p.clientY},{x:y,y:v.top},{x:w,y:v.top},{x:w,y:v.bottom},{x:y,y:v.bottom}],side:m}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>s.onPointerGraceIntentChange(null),300)}else{if(s.onTriggerLeave(p),p.defaultPrevented)return;s.onPointerGraceIntentChange(null)}})),onKeyDown:G(e.onKeyDown,p=>{const g=s.searchRef.current!=="";if(!(e.disabled||g&&p.key===" ")&&Bb[r.dir].includes(p.key)){var v;n.onOpenChange(!0),(v=n.content)===null||v===void 0||v.focus(),p.preventDefault()}})})))}),ux="MenuSubContent",dx=i.forwardRef((e,t)=>{const n=Bl($e,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,s=ut($e,e.__scopeMenu),a=sn($e,e.__scopeMenu),c=Hl(ux,e.__scopeMenu),l=i.useRef(null),u=be(t,l);return i.createElement(Yt.Provider,{scope:e.__scopeMenu},i.createElement(on,{present:r||s.open},i.createElement(Yt.Slot,{scope:e.__scopeMenu},i.createElement(Es,W({id:c.contentId,"aria-labelledby":c.triggerId},o,{ref:u,align:"start",side:a.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:d=>{var p;a.isUsingKeyboardRef.current&&((p=l.current)===null||p===void 0||p.focus()),d.preventDefault()},onCloseAutoFocus:d=>d.preventDefault(),onFocusOutside:G(e.onFocusOutside,d=>{d.target!==c.trigger&&s.onOpenChange(!1)}),onEscapeKeyDown:G(e.onEscapeKeyDown,d=>{a.onClose(),d.preventDefault()}),onKeyDown:G(e.onKeyDown,d=>{const p=d.currentTarget.contains(d.target),g=Ub[a.dir].includes(d.key);if(p&&g){var v;s.onOpenChange(!1),(v=c.trigger)===null||v===void 0||v.focus(),d.preventDefault()}})})))))});function zl(e){return e?"open":"closed"}function Wn(e){return e==="indeterminate"}function $s(e){return Wn(e)?"indeterminate":e?"checked":"unchecked"}function fx(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function px(e,t){return e.map((n,r)=>e[(t+r)%e.length])}function mx(e,t,n){const o=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let a=px(e,Math.max(s,0));o.length===1&&(a=a.filter(u=>u!==n));const l=a.find(u=>u.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}function hx(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,a=t.length-1;s<t.length;a=s++){const c=t[s].x,l=t[s].y,u=t[a].x,d=t[a].y;l>r!=d>r&&n<(u-c)*(r-l)/(d-l)+c&&(o=!o)}return o}function gx(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return hx(n,t)}function Xt(e){return t=>t.pointerType==="mouse"?e(t):void 0}const vx=Gb,bx=Ll,xx=qb,yx=Xb,wx=Qb,Cx=Ss,Ex=ex,Sx=ox,$x=ax,Rx=ix,Px=lx,Nx=dx,Gl="DropdownMenu",[Tx,dC]=rn(Gl,[Dl]),Ce=Dl(),[Ax,Kl]=Tx(Gl),Ox=e=>{const{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:s,onOpenChange:a,modal:c=!0}=e,l=Ce(t),u=i.useRef(null),[d=!1,p]=hl({prop:o,defaultProp:s,onChange:a});return i.createElement(Ax,{scope:t,triggerId:vo(),triggerRef:u,contentId:vo(),open:d,onOpenChange:p,onOpenToggle:i.useCallback(()=>p(g=>!g),[p]),modal:c},i.createElement(vx,W({},l,{open:d,onOpenChange:p,dir:r,modal:c}),n))},_x="DropdownMenuTrigger",Mx=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,s=Kl(_x,n),a=Ce(n);return i.createElement(bx,W({asChild:!0},a),i.createElement(we.button,W({type:"button",id:s.triggerId,"aria-haspopup":"menu","aria-expanded":s.open,"aria-controls":s.open?s.contentId:void 0,"data-state":s.open?"open":"closed","data-disabled":r?"":void 0,disabled:r},o,{ref:qa(t,s.triggerRef),onPointerDown:G(e.onPointerDown,c=>{!r&&c.button===0&&c.ctrlKey===!1&&(s.onOpenToggle(),s.open||c.preventDefault())}),onKeyDown:G(e.onKeyDown,c=>{r||(["Enter"," "].includes(c.key)&&s.onOpenToggle(),c.key==="ArrowDown"&&s.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(c.key)&&c.preventDefault())})})))}),jx=e=>{const{__scopeDropdownMenu:t,...n}=e,r=Ce(t);return i.createElement(xx,W({},r,n))},Ix="DropdownMenuContent",Dx=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Kl(Ix,n),s=Ce(n),a=i.useRef(!1);return i.createElement(yx,W({id:o.contentId,"aria-labelledby":o.triggerId},s,r,{ref:t,onCloseAutoFocus:G(e.onCloseAutoFocus,c=>{var l;a.current||(l=o.triggerRef.current)===null||l===void 0||l.focus(),a.current=!1,c.preventDefault()}),onInteractOutside:G(e.onInteractOutside,c=>{const l=c.detail.originalEvent,u=l.button===0&&l.ctrlKey===!0,d=l.button===2||u;(!o.modal||d)&&(a.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}}))}),kx=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ce(n);return i.createElement(wx,W({},o,r,{ref:t}))}),Lx=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ce(n);return i.createElement(Cx,W({},o,r,{ref:t}))}),Fx=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ce(n);return i.createElement(Ex,W({},o,r,{ref:t}))}),Bx=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ce(n);return i.createElement(Sx,W({},o,r,{ref:t}))}),Ux=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ce(n);return i.createElement($x,W({},o,r,{ref:t}))}),Wx=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ce(n);return i.createElement(Rx,W({},o,r,{ref:t}))}),Vx=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ce(n);return i.createElement(Px,W({},o,r,{ref:t}))}),Hx=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Ce(n);return i.createElement(Nx,W({},o,r,{ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}}))}),zx=Ox,Gx=Mx,Kx=jx,ql=Dx,Yl=kx,Xl=Lx,Jl=Fx,Zl=Bx,Ql=Ux,eu=Wx,tu=Vx,nu=Hx,Co=zx,Eo=Gx,qx=i.forwardRef(({className:e,inset:t,children:n,...r},o)=>f.jsxs(tu,{ref:o,className:U("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-card data-[state=open]:bg-card",t&&"pl-8",e),...r,children:[n,f.jsx(Ja,{className:"ml-auto h-4 w-4"})]}));qx.displayName=tu.displayName;const Yx=i.forwardRef(({className:e,...t},n)=>f.jsx(nu,{ref:n,className:U("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-muted p-1 text-muted-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t}));Yx.displayName=nu.displayName;const Vn=i.forwardRef(({className:e,sideOffset:t=4,...n},r)=>f.jsx(Kx,{children:f.jsx(ql,{ref:r,sideOffset:t,className:U("z-50 min-w-[8rem] overflow-hidden rounded-md bg-[var(--card-background)] p-1 text-[var(--headline)] shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n})}));Vn.displayName=ql.displayName;const Hn=i.forwardRef(({className:e,inset:t,...n},r)=>f.jsx(Xl,{ref:r,className:U("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-card focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t&&"pl-8",e),...n}));Hn.displayName=Xl.displayName;const Xx=i.forwardRef(({className:e,children:t,checked:n,...r},o)=>f.jsxs(Jl,{ref:o,className:U("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-card focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:n,...r,children:[f.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:f.jsx(Ql,{children:f.jsx(Lo,{className:"h-4 w-4"})})}),t]}));Xx.displayName=Jl.displayName;const Jx=i.forwardRef(({className:e,children:t,...n},r)=>f.jsxs(Zl,{ref:r,className:U("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-card focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[f.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:f.jsx(Ql,{children:f.jsx(nf,{className:"h-2 w-2 fill-current"})})}),t]}));Jx.displayName=Zl.displayName;const Zx=i.forwardRef(({className:e,inset:t,...n},r)=>f.jsx(Yl,{ref:r,className:U("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...n}));Zx.displayName=Yl.displayName;const Qx=i.forwardRef(({className:e,...t},n)=>f.jsx(eu,{ref:n,className:U("-mx-1 my-1 h-px bg-muted",e),...t}));Qx.displayName=eu.displayName;function ey({children:e,className:t,gradientSize:n=200,gradientColor:r="border",gradientOpacity:o=.8}){const s=i.useRef(null),a=Vt(-n),c=Vt(-n),l=i.useCallback(p=>{if(s.current){const{left:g,top:v}=s.current.getBoundingClientRect(),h=p.clientX,m=p.clientY;a.set(h-g),c.set(m-v)}},[a,c]),u=i.useCallback(p=>{p.relatedTarget||(document.removeEventListener("mousemove",l),a.set(-n),c.set(-n))},[l,a,n,c]),d=i.useCallback(()=>{document.addEventListener("mousemove",l),a.set(-n),c.set(-n)},[l,a,n,c]);return i.useEffect(()=>(document.addEventListener("mousemove",l),document.addEventListener("mouseout",u),document.addEventListener("mouseenter",d),()=>{document.removeEventListener("mousemove",l),document.removeEventListener("mouseout",u),document.removeEventListener("mouseenter",d)}),[d,l,u]),i.useEffect(()=>{a.set(-n),c.set(-n)},[n,a,c]),f.jsxs("div",{"data-aos":"fade-up",ref:s,className:U("group relative flex size-full overflow-hidden rounded-xl border border-border bg-card text-black dark:bg-neutral-900 dark:text-white",t),children:[f.jsx("div",{className:"relative z-10 w-full",children:e}),f.jsx(pe.div,{className:"pointer-events-none absolute -inset-px rounded-xl opacity-0 transition-opacity duration-300 group-hover:opacity-100",style:{background:Kd`
            radial-gradient(${n}px circle at ${a}px ${c}px, ${r}, transparent 100%)
          `,opacity:o}})]})}const ty=({style:e,gradientType:t,gradient:n})=>{const r=n.colors[0];return f.jsxs("header",{className:"relative w-full",children:[f.jsx("div",{className:"absolute inset-0 opacity-50 blur-[42px]",style:{background:`radial-gradient(circle, ${r} 0%, transparent 30%)`,transform:"translate(-25%, -25%) scale(1.5)"}}),f.jsx(pe.div,{className:"relative left-0 right-0 m-auto mt-6 flex h-48 w-48 items-center justify-center overflow-hidden rounded-full",style:t==="background"?{...e}:{},transition:{duration:.3},children:t==="text"?f.jsx(pe.span,{className:"overflow-hidden text-nowrap p-2 text-2xl font-bold",style:e,initial:{opacity:0},animate:{opacity:1},transition:{duration:.3},children:n.name}):f.jsx(pe.span,{className:"text-2xl font-bold text-white",initial:{opacity:0},animate:{opacity:1},transition:{duration:.3}})})]})},ny=({name:e,isFavorite:t,onFavoriteToggle:n,onExport:r})=>f.jsxs("div",{className:"flex w-full items-center justify-between",children:[f.jsx("h3",{className:"text-lg font-semibold text-primary",children:e}),f.jsxs("div",{className:"flex items-center gap-1",children:[r&&f.jsxs(mo,{children:[f.jsx(ho,{asChild:!0,children:f.jsx(pe.button,{className:"rounded-full p-2 transition-colors hover:bg-muted",onClick:r,whileHover:{scale:1.1},whileTap:{scale:.9},children:f.jsx(Hr,{className:"h-4 w-4 text-gray-400 hover:text-primary"})})}),f.jsx(Un,{children:f.jsx("p",{children:"Export gradient"})})]}),f.jsxs(mo,{children:[f.jsx(ho,{asChild:!0,children:f.jsx(pe.button,{className:"rounded-full p-2 transition-colors hover:bg-muted",onClick:()=>n(e),whileHover:{scale:1.1},whileTap:{scale:.9},children:f.jsx(Vd,{className:U("h-5 w-5",t?"fill-current text-red-400":"text-gray-400 hover:text-red-400")})})}),f.jsx(Un,{children:f.jsx("p",{children:t?"Remove from favorites":"Add to favorites"})})]})]})]}),ry=({colors:e,getColorInFormat:t,copyToClipboard:n})=>f.jsx("div",{className:"flex flex-wrap gap-2",children:e.map((r,o)=>f.jsxs(mo,{children:[f.jsx(ho,{asChild:!0,children:f.jsx(pe.div,{className:"hoverd h-6 w-6 cursor-pointer rounded-full border border-border",style:{backgroundColor:r},onClick:()=>n(t(r),"colors"),whileHover:{scale:1.2},whileTap:{scale:.9}})}),f.jsx(Un,{children:f.jsxs("p",{className:"text-muted-foreground",children:["Click to copy: ",t(r)]})})]},o))});function xt(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function oy(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function ru(...e){return t=>e.forEach(n=>oy(n,t))}function st(...e){return i.useCallback(ru(...e),e)}function sy(e,t=[]){let n=[];function r(s,a){const c=i.createContext(a),l=n.length;n=[...n,a];const u=p=>{var x;const{scope:g,children:v,...h}=p,m=((x=g==null?void 0:g[e])==null?void 0:x[l])||c,b=i.useMemo(()=>h,Object.values(h));return f.jsx(m.Provider,{value:b,children:v})};u.displayName=s+"Provider";function d(p,g){var m;const v=((m=g==null?void 0:g[e])==null?void 0:m[l])||c,h=i.useContext(v);if(h)return h;if(a!==void 0)return a;throw new Error(`\`${p}\` must be used within \`${s}\``)}return[u,d]}const o=()=>{const s=n.map(a=>i.createContext(a));return function(c){const l=(c==null?void 0:c[e])||s;return i.useMemo(()=>({[`__scope${e}`]:{...c,[e]:l}}),[c,l])}};return o.scopeName=e,[r,ay(o,...t)]}function ay(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const a=r.reduce((c,{useScope:l,scopeName:u})=>{const p=l(s)[`__scope${u}`];return{...c,...p}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return n.scopeName=t.scopeName,n}function ou(e){const t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function iy({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=cy({defaultProp:t,onChange:n}),s=e!==void 0,a=s?e:r,c=ou(n),l=i.useCallback(u=>{if(s){const p=typeof u=="function"?u(e):u;p!==e&&c(p)}else o(u)},[s,e,o,c]);return[a,l]}function cy({defaultProp:e,onChange:t}){const n=i.useState(e),[r]=n,o=i.useRef(r),s=ou(t);return i.useEffect(()=>{o.current!==r&&(s(r),o.current=r)},[r,o,s]),n}var ly=i.createContext(void 0);function uy(e){const t=i.useContext(ly);return e||t||"ltr"}var dy=globalThis!=null&&globalThis.document?i.useLayoutEffect:()=>{};function fy(e){const[t,n]=i.useState(void 0);return dy(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const s=o[0];let a,c;if("borderBoxSize"in s){const l=s.borderBoxSize,u=Array.isArray(l)?l[0]:l;a=u.inlineSize,c=u.blockSize}else a=e.offsetWidth,c=e.offsetHeight;n({width:a,height:c})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var zn=i.forwardRef((e,t)=>{const{children:n,...r}=e,o=i.Children.toArray(n),s=o.find(my);if(s){const a=s.props.children,c=o.map(l=>l===s?i.Children.count(a)>1?i.Children.only(null):i.isValidElement(a)?a.props.children:null:l);return f.jsx(So,{...r,ref:t,children:i.isValidElement(a)?i.cloneElement(a,void 0,c):null})}return f.jsx(So,{...r,ref:t,children:n})});zn.displayName="Slot";var So=i.forwardRef((e,t)=>{const{children:n,...r}=e;if(i.isValidElement(n)){const o=gy(n);return i.cloneElement(n,{...hy(r,n.props),ref:t?ru(t,o):o})}return i.Children.count(n)>1?i.Children.only(null):null});So.displayName="SlotClone";var py=({children:e})=>f.jsx(f.Fragment,{children:e});function my(e){return i.isValidElement(e)&&e.type===py}function hy(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...c)=>{s(...c),o(...c)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function gy(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var vy=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],vr=vy.reduce((e,t)=>{const n=i.forwardRef((r,o)=>{const{asChild:s,...a}=r,c=s?zn:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),f.jsx(c,{...a,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function by(e,t=[]){let n=[];function r(s,a){const c=i.createContext(a),l=n.length;n=[...n,a];function u(p){const{scope:g,children:v,...h}=p,m=(g==null?void 0:g[e][l])||c,b=i.useMemo(()=>h,Object.values(h));return f.jsx(m.Provider,{value:b,children:v})}function d(p,g){const v=(g==null?void 0:g[e][l])||c,h=i.useContext(v);if(h)return h;if(a!==void 0)return a;throw new Error(`\`${p}\` must be used within \`${s}\``)}return u.displayName=s+"Provider",[u,d]}const o=()=>{const s=n.map(a=>i.createContext(a));return function(c){const l=(c==null?void 0:c[e])||s;return i.useMemo(()=>({[`__scope${e}`]:{...c,[e]:l}}),[c,l])}};return o.scopeName=e,[r,xy(o,...t)]}function xy(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const a=r.reduce((c,{useScope:l,scopeName:u})=>{const p=l(s)[`__scope${u}`];return{...c,...p}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return n.scopeName=t.scopeName,n}function yy(e){const t=e+"CollectionProvider",[n,r]=by(t),[o,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=v=>{const{scope:h,children:m}=v,b=V.useRef(null),x=V.useRef(new Map).current;return f.jsx(o,{scope:h,itemMap:x,collectionRef:b,children:m})};a.displayName=t;const c=e+"CollectionSlot",l=V.forwardRef((v,h)=>{const{scope:m,children:b}=v,x=s(c,m),y=st(h,x.collectionRef);return f.jsx(zn,{ref:y,children:b})});l.displayName=c;const u=e+"CollectionItemSlot",d="data-radix-collection-item",p=V.forwardRef((v,h)=>{const{scope:m,children:b,...x}=v,y=V.useRef(null),w=st(h,y),C=s(u,m);return V.useEffect(()=>(C.itemMap.set(y,{ref:y,...x}),()=>void C.itemMap.delete(y))),f.jsx(zn,{[d]:"",ref:w,children:b})});p.displayName=u;function g(v){const h=s(e+"CollectionConsumer",v);return V.useCallback(()=>{const b=h.collectionRef.current;if(!b)return[];const x=Array.from(b.querySelectorAll(`[${d}]`));return Array.from(h.itemMap.values()).sort((C,S)=>x.indexOf(C.ref.current)-x.indexOf(S.ref.current))},[h.collectionRef,h.itemMap])}return[{Provider:a,Slot:l,ItemSlot:p},g,r]}var su=["PageUp","PageDown"],au=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],iu={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},Dt="Slider",[$o,wy,Cy]=yy(Dt),[cu,fC]=sy(Dt,[Cy]),[Ey,br]=cu(Dt),lu=i.forwardRef((e,t)=>{const{name:n,min:r=0,max:o=100,step:s=1,orientation:a="horizontal",disabled:c=!1,minStepsBetweenThumbs:l=0,defaultValue:u=[r],value:d,onValueChange:p=()=>{},onValueCommit:g=()=>{},inverted:v=!1,form:h,...m}=e,b=i.useRef(new Set),x=i.useRef(0),w=a==="horizontal"?Sy:$y,[C=[],S]=iy({prop:d,defaultProp:u,onChange:O=>{var L;(L=[...b.current][x.current])==null||L.focus(),p(O)}}),P=i.useRef(C);function E(O){const _=Ay(C,O);A(O,_)}function $(O){A(O,x.current)}function N(){const O=P.current[x.current];C[x.current]!==O&&g(C)}function A(O,_,{commit:L}={commit:!1}){const D=jy(s),M=Iy(Math.round((O-r)/s)*s+r,D),k=Mn(M,[r,o]);S((j=[])=>{const F=Ny(j,k,_);if(My(F,l*s)){x.current=F.indexOf(k);const T=String(F)!==String(j);return T&&L&&g(F),T?F:j}else return j})}return f.jsx(Ey,{scope:e.__scopeSlider,name:n,disabled:c,min:r,max:o,valueIndexToChangeRef:x,thumbs:b.current,values:C,orientation:a,form:h,children:f.jsx($o.Provider,{scope:e.__scopeSlider,children:f.jsx($o.Slot,{scope:e.__scopeSlider,children:f.jsx(w,{"aria-disabled":c,"data-disabled":c?"":void 0,...m,ref:t,onPointerDown:xt(m.onPointerDown,()=>{c||(P.current=C)}),min:r,max:o,inverted:v,onSlideStart:c?void 0:E,onSlideMove:c?void 0:$,onSlideEnd:c?void 0:N,onHomeKeyDown:()=>!c&&A(r,0,{commit:!0}),onEndKeyDown:()=>!c&&A(o,C.length-1,{commit:!0}),onStepKeyDown:({event:O,direction:_})=>{if(!c){const M=su.includes(O.key)||O.shiftKey&&au.includes(O.key)?10:1,k=x.current,j=C[k],F=s*M*_;A(j+F,k,{commit:!0})}}})})})})});lu.displayName=Dt;var[uu,du]=cu(Dt,{startEdge:"left",endEdge:"right",size:"width",direction:1}),Sy=i.forwardRef((e,t)=>{const{min:n,max:r,dir:o,inverted:s,onSlideStart:a,onSlideMove:c,onSlideEnd:l,onStepKeyDown:u,...d}=e,[p,g]=i.useState(null),v=st(t,w=>g(w)),h=i.useRef(),m=uy(o),b=m==="ltr",x=b&&!s||!b&&s;function y(w){const C=h.current||p.getBoundingClientRect(),S=[0,C.width],E=Rs(S,x?[n,r]:[r,n]);return h.current=C,E(w-C.left)}return f.jsx(uu,{scope:e.__scopeSlider,startEdge:x?"left":"right",endEdge:x?"right":"left",direction:x?1:-1,size:"width",children:f.jsx(fu,{dir:m,"data-orientation":"horizontal",...d,ref:v,style:{...d.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:w=>{const C=y(w.clientX);a==null||a(C)},onSlideMove:w=>{const C=y(w.clientX);c==null||c(C)},onSlideEnd:()=>{h.current=void 0,l==null||l()},onStepKeyDown:w=>{const S=iu[x?"from-left":"from-right"].includes(w.key);u==null||u({event:w,direction:S?-1:1})}})})}),$y=i.forwardRef((e,t)=>{const{min:n,max:r,inverted:o,onSlideStart:s,onSlideMove:a,onSlideEnd:c,onStepKeyDown:l,...u}=e,d=i.useRef(null),p=st(t,d),g=i.useRef(),v=!o;function h(m){const b=g.current||d.current.getBoundingClientRect(),x=[0,b.height],w=Rs(x,v?[r,n]:[n,r]);return g.current=b,w(m-b.top)}return f.jsx(uu,{scope:e.__scopeSlider,startEdge:v?"bottom":"top",endEdge:v?"top":"bottom",size:"height",direction:v?1:-1,children:f.jsx(fu,{"data-orientation":"vertical",...u,ref:p,style:{...u.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:m=>{const b=h(m.clientY);s==null||s(b)},onSlideMove:m=>{const b=h(m.clientY);a==null||a(b)},onSlideEnd:()=>{g.current=void 0,c==null||c()},onStepKeyDown:m=>{const x=iu[v?"from-bottom":"from-top"].includes(m.key);l==null||l({event:m,direction:x?-1:1})}})})}),fu=i.forwardRef((e,t)=>{const{__scopeSlider:n,onSlideStart:r,onSlideMove:o,onSlideEnd:s,onHomeKeyDown:a,onEndKeyDown:c,onStepKeyDown:l,...u}=e,d=br(Dt,n);return f.jsx(vr.span,{...u,ref:t,onKeyDown:xt(e.onKeyDown,p=>{p.key==="Home"?(a(p),p.preventDefault()):p.key==="End"?(c(p),p.preventDefault()):su.concat(au).includes(p.key)&&(l(p),p.preventDefault())}),onPointerDown:xt(e.onPointerDown,p=>{const g=p.target;g.setPointerCapture(p.pointerId),p.preventDefault(),d.thumbs.has(g)?g.focus():r(p)}),onPointerMove:xt(e.onPointerMove,p=>{p.target.hasPointerCapture(p.pointerId)&&o(p)}),onPointerUp:xt(e.onPointerUp,p=>{const g=p.target;g.hasPointerCapture(p.pointerId)&&(g.releasePointerCapture(p.pointerId),s(p))})})}),pu="SliderTrack",mu=i.forwardRef((e,t)=>{const{__scopeSlider:n,...r}=e,o=br(pu,n);return f.jsx(vr.span,{"data-disabled":o.disabled?"":void 0,"data-orientation":o.orientation,...r,ref:t})});mu.displayName=pu;var Ro="SliderRange",hu=i.forwardRef((e,t)=>{const{__scopeSlider:n,...r}=e,o=br(Ro,n),s=du(Ro,n),a=i.useRef(null),c=st(t,a),l=o.values.length,u=o.values.map(g=>vu(g,o.min,o.max)),d=l>1?Math.min(...u):0,p=100-Math.max(...u);return f.jsx(vr.span,{"data-orientation":o.orientation,"data-disabled":o.disabled?"":void 0,...r,ref:c,style:{...e.style,[s.startEdge]:d+"%",[s.endEdge]:p+"%"}})});hu.displayName=Ro;var Po="SliderThumb",gu=i.forwardRef((e,t)=>{const n=wy(e.__scopeSlider),[r,o]=i.useState(null),s=st(t,c=>o(c)),a=i.useMemo(()=>r?n().findIndex(c=>c.ref.current===r):-1,[n,r]);return f.jsx(Ry,{...e,ref:s,index:a})}),Ry=i.forwardRef((e,t)=>{const{__scopeSlider:n,index:r,name:o,...s}=e,a=br(Po,n),c=du(Po,n),[l,u]=i.useState(null),d=st(t,y=>u(y)),p=l?a.form||!!l.closest("form"):!0,g=fy(l),v=a.values[r],h=v===void 0?0:vu(v,a.min,a.max),m=Ty(r,a.values.length),b=g==null?void 0:g[c.size],x=b?Oy(b,h,c.direction):0;return i.useEffect(()=>{if(l)return a.thumbs.add(l),()=>{a.thumbs.delete(l)}},[l,a.thumbs]),f.jsxs("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[c.startEdge]:`calc(${h}% + ${x}px)`},children:[f.jsx($o.ItemSlot,{scope:e.__scopeSlider,children:f.jsx(vr.span,{role:"slider","aria-label":e["aria-label"]||m,"aria-valuemin":a.min,"aria-valuenow":v,"aria-valuemax":a.max,"aria-orientation":a.orientation,"data-orientation":a.orientation,"data-disabled":a.disabled?"":void 0,tabIndex:a.disabled?void 0:0,...s,ref:d,style:v===void 0?{display:"none"}:e.style,onFocus:xt(e.onFocus,()=>{a.valueIndexToChangeRef.current=r})})}),p&&f.jsx(Py,{name:o??(a.name?a.name+(a.values.length>1?"[]":""):void 0),form:a.form,value:v},r)]})});gu.displayName=Po;var Py=e=>{const{value:t,...n}=e,r=i.useRef(null),o=Ji(t);return i.useEffect(()=>{const s=r.current,a=window.HTMLInputElement.prototype,l=Object.getOwnPropertyDescriptor(a,"value").set;if(o!==t&&l){const u=new Event("input",{bubbles:!0});l.call(s,t),s.dispatchEvent(u)}},[o,t]),f.jsx("input",{style:{display:"none"},...n,ref:r,defaultValue:t})};function Ny(e=[],t,n){const r=[...e];return r[n]=t,r.sort((o,s)=>o-s)}function vu(e,t,n){const s=100/(n-t)*(e-t);return Mn(s,[0,100])}function Ty(e,t){return t>2?`Value ${e+1} of ${t}`:t===2?["Minimum","Maximum"][e]:void 0}function Ay(e,t){if(e.length===1)return 0;const n=e.map(o=>Math.abs(o-t)),r=Math.min(...n);return n.indexOf(r)}function Oy(e,t,n){const r=e/2,s=Rs([0,50],[0,r]);return(r-s(t)*n)*n}function _y(e){return e.slice(0,-1).map((t,n)=>e[n+1]-t)}function My(e,t){if(t>0){const n=_y(e);return Math.min(...n)>=t}return!0}function Rs(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];const r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}function jy(e){return(String(e).split(".")[1]||"").length}function Iy(e,t){const n=Math.pow(10,t);return Math.round(e*n)/n}var bu=lu,Dy=mu,ky=hu,Ly=gu;const Ut=i.forwardRef(({className:e,...t},n)=>f.jsxs(bu,{ref:n,className:U("relative flex w-full touch-none select-none items-center",e),...t,children:[f.jsx(Dy,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:f.jsx(ky,{className:"absolute h-full bg-gray-700"})}),f.jsx(Ly,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]}));Ut.displayName=bu.displayName;var xu={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},Ma=V.createContext&&V.createContext(xu),qe=function(){return qe=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++){t=arguments[n];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o])}return e},qe.apply(this,arguments)},Fy=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function yu(e){return e&&e.map(function(t,n){return V.createElement(t.tag,qe({key:n},t.attr),yu(t.child))})}function By(e){return function(t){return V.createElement(Uy,qe({attr:qe({},e.attr)},t),yu(e.child))}}function Uy(e){var t=function(n){var r=e.attr,o=e.size,s=e.title,a=Fy(e,["attr","size","title"]),c=o||n.size||"1em",l;return n.className&&(l=n.className),e.className&&(l=(l?l+" ":"")+e.className),V.createElement("svg",qe({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},n.attr,r,a,{className:l,style:qe(qe({color:e.color||n.color},n.style),e.style),height:c,width:c,xmlns:"http://www.w3.org/2000/svg"}),s&&V.createElement("title",null,s),e.children)};return Ma!==void 0?V.createElement(Ma.Consumer,null,function(n){return t(n)}):t(xu)}function Wy(e){return By({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M125.7 160H176c17.7 0 32 14.3 32 32s-14.3 32-32 32H48c-17.7 0-32-14.3-32-32V64c0-17.7 14.3-32 32-32s32 14.3 32 32v51.2L97.6 97.6c87.5-87.5 229.3-87.5 316.8 0s87.5 229.3 0 316.8s-229.3 87.5-316.8 0c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0c62.5 62.5 163.8 62.5 226.3 0s62.5-163.8 0-226.3s-163.8-62.5-226.3 0L125.7 160z"}}]})(e)}const Vy=({angle:e,setAngle:t})=>f.jsxs("div",{className:"flex w-full items-center space-x-2",children:[f.jsx(Ut,{value:[e],onValueChange:n=>t(n[0]),max:360,step:1,className:"flex-grow"}),f.jsxs("span",{className:"text-sm font-medium text-primary",children:[e,"°"]}),f.jsx(Wy,{className:"flex h-full cursor-pointer items-center justify-center text-primary",onClick:()=>t(90)})]}),Hy=({gradientType:e,setGradientType:t})=>f.jsxs(Co,{children:[f.jsx(Eo,{className:"inline-flex",children:f.jsxs("div",{className:"flex w-32 items-center justify-between rounded-md border border-border bg-secondary p-2 text-sm text-primary",children:[f.jsx("span",{children:e.charAt(0).toUpperCase()+e.slice(1)}),f.jsx(Ht,{className:"h-4 w-4"})]})}),f.jsx(Vn,{align:"end",className:"w-32 rounded-md border border-border bg-secondary p-1",children:["background","text"].map(n=>f.jsx(Hn,{onSelect:()=>t(n),className:"hover:bg-primary/10 cursor-pointer rounded px-2 py-1.5 text-sm text-primary",children:n.charAt(0).toUpperCase()+n.slice(1)},n))})]}),Wt={hexToRGB:e=>{const t=parseInt(e.slice(1,3),16),n=parseInt(e.slice(3,5),16),r=parseInt(e.slice(5,7),16);return{r:t,g:n,b:r}},rgbToHSL:(e,t,n)=>{e/=255,t/=255,n/=255;const r=Math.max(e,t,n),o=Math.min(e,t,n);let s=0,a=0;const c=(r+o)/2;if(r!==o){const l=r-o;switch(a=c>.5?l/(2-r-o):l/(r+o),r){case e:s=((t-n)/l+(t<n?6:0))*60;break;case t:s=((n-e)/l+2)*60;break;case n:s=((e-t)/l+4)*60;break}}return{h:Math.round(s),s:Math.round(a*100),l:Math.round(c*100)}},getUniqueColors:e=>{const t=new Set;return e.forEach(n=>{n.colorsname&&Array.isArray(n.colorsname)&&n.colorsname.forEach(r=>{r&&typeof r=="string"&&t.add(r.toLowerCase().trim())})}),Array.from(t).sort()},getColorCategories:e=>{const t={"🔴 Red":[],"🩷 Pink":[],"🟠 Orange":[],"🟡 Yellow":[],"🟢 Green":[],"🔵 Blue":[],"🟣 Purple":[],"🟤 Brown":[],"⚫ Black":[],"⚪ White":[],"🔘 Gray":[],"🎨 Other":[]};return e.forEach(n=>{const r=n.toLowerCase();r.includes("red")?t["🔴 Red"].push(n):r.includes("pink")?t["🩷 Pink"].push(n):r.includes("orange")||r.includes("peach")?t["🟠 Orange"].push(n):r.includes("yellow")?t["🟡 Yellow"].push(n):r.includes("green")||r.includes("olive")||r.includes("teal")||r.includes("cyan")?t["🟢 Green"].push(n):r.includes("blue")||r.includes("indigo")?t["🔵 Blue"].push(n):r.includes("purple")||r.includes("violet")||r.includes("magenta")?t["🟣 Purple"].push(n):r.includes("brown")||r.includes("beige")?t["🟤 Brown"].push(n):r.includes("black")?t["⚫ Black"].push(n):r.includes("white")?t["⚪ White"].push(n):r.includes("gray")||r.includes("grey")?t["🔘 Gray"].push(n):t["🎨 Other"].push(n)}),Object.keys(t).forEach(n=>{t[n].length===0&&delete t[n]}),t},getBasicColors:e=>{const t=Wt.getUniqueColors(e),n=["red","pink","orange","yellow","green","blue","purple","brown","black","white","gray","grey","teal","cyan"];return t.filter(r=>{const o=r.toLowerCase();return n.some(s=>o.includes(s))})},getBasicColorCategories:e=>{const t=e.filter(n=>{const r=n.toLowerCase();return["red","pink","orange","yellow","green","blue","purple","brown","black","white","gray","grey","teal","cyan"].some(s=>r.includes(s))});return Wt.getColorCategories(t)}},zy=()=>{const[e,t]=i.useState("HEX"),[n,r]=i.useState("background");return{selectedColorFormat:e,setSelectedColorFormat:a=>{t(a)},getColorInFormat:a=>{switch(e){case"RGB":{const{r:c,g:l,b:u}=Wt.hexToRGB(a);return`rgb(${c}, ${l}, ${u})`}case"HSL":{const{r:c,g:l,b:u}=Wt.hexToRGB(a),{h:d,s:p,l:g}=Wt.rgbToHSL(c,l,u);return`hsl(${d}, ${p}%, ${g}%)`}default:return a.toUpperCase()}},gradientType:n,setGradientType:r}},Gy=()=>{const[e,t]=i.useState({tailwind:!1,css:!1,sass:!1,bootstrap:!1,colors:!1}),n=(r,o)=>{navigator.clipboard.writeText(r),t(s=>({...s,[o]:!0})),Vr({title:"Copied to clipboard ✅",description:`The ${o} code has been copied to your clipboard.`})};return i.useEffect(()=>{const r=setTimeout(()=>{t({tailwind:!1,css:!1,sass:!1,bootstrap:!1,colors:!1})},2e3);return()=>clearTimeout(r)},[e]),{copiedStates:e,copyToClipboard:n}},Ky=({code:e,copiedStates:t,activeTab:n,copyToClipboard:r})=>f.jsxs("div",{className:"relative mt-2 w-full",children:[f.jsx("pre",{className:"line-clamp-1 w-full overflow-hidden rounded-md border border-border bg-secondary p-2 text-xs text-muted-foreground",children:e}),f.jsx("button",{className:"absolute right-0 top-0 flex h-full w-[30px] items-center justify-center border border-border bg-secondary p-0 text-primary",onClick:()=>r(e,n),children:f.jsx(Ya,{children:t[n]?f.jsx(pe.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},transition:{duration:.2},children:f.jsx(Lo,{className:"text-success h-4 w-4"})}):f.jsx(pe.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},transition:{duration:.2},children:f.jsx(Xa,{className:"h-4 w-4"})})})})]});function Ye(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function ja(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function wu(...e){return t=>{let n=!1;const r=e.map(o=>{const s=ja(o,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let o=0;o<r.length;o++){const s=r[o];typeof s=="function"?s():ja(e[o],null)}}}}function dt(...e){return i.useCallback(wu(...e),e)}function qy(e,t){const n=i.createContext(t),r=s=>{const{children:a,...c}=s,l=i.useMemo(()=>c,Object.values(c));return f.jsx(n.Provider,{value:l,children:a})};r.displayName=e+"Provider";function o(s){const a=i.useContext(n);if(a)return a;if(t!==void 0)return t;throw new Error(`\`${s}\` must be used within \`${e}\``)}return[r,o]}function Yy(e,t=[]){let n=[];function r(s,a){const c=i.createContext(a),l=n.length;n=[...n,a];const u=p=>{var x;const{scope:g,children:v,...h}=p,m=((x=g==null?void 0:g[e])==null?void 0:x[l])||c,b=i.useMemo(()=>h,Object.values(h));return f.jsx(m.Provider,{value:b,children:v})};u.displayName=s+"Provider";function d(p,g){var m;const v=((m=g==null?void 0:g[e])==null?void 0:m[l])||c,h=i.useContext(v);if(h)return h;if(a!==void 0)return a;throw new Error(`\`${p}\` must be used within \`${s}\``)}return[u,d]}const o=()=>{const s=n.map(a=>i.createContext(a));return function(c){const l=(c==null?void 0:c[e])||s;return i.useMemo(()=>({[`__scope${e}`]:{...c,[e]:l}}),[c,l])}};return o.scopeName=e,[r,Xy(o,...t)]}function Xy(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const a=r.reduce((c,{useScope:l,scopeName:u})=>{const p=l(s)[`__scope${u}`];return{...c,...p}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return n.scopeName=t.scopeName,n}var Gn=globalThis!=null&&globalThis.document?i.useLayoutEffect:()=>{},Jy=Jt.useId||(()=>{}),Zy=0;function kr(e){const[t,n]=i.useState(Jy());return Gn(()=>{e||n(r=>r??String(Zy++))},[e]),e||(t?`radix-${t}`:"")}function at(e){const t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function Qy({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=ew({defaultProp:t,onChange:n}),s=e!==void 0,a=s?e:r,c=at(n),l=i.useCallback(u=>{if(s){const p=typeof u=="function"?u(e):u;p!==e&&c(p)}else o(u)},[s,e,o,c]);return[a,l]}function ew({defaultProp:e,onChange:t}){const n=i.useState(e),[r]=n,o=i.useRef(r),s=at(t);return i.useEffect(()=>{o.current!==r&&(s(r),o.current=r)},[r,o,s]),n}var Ps=i.forwardRef((e,t)=>{const{children:n,...r}=e,o=i.Children.toArray(n),s=o.find(nw);if(s){const a=s.props.children,c=o.map(l=>l===s?i.Children.count(a)>1?i.Children.only(null):i.isValidElement(a)?a.props.children:null:l);return f.jsx(No,{...r,ref:t,children:i.isValidElement(a)?i.cloneElement(a,void 0,c):null})}return f.jsx(No,{...r,ref:t,children:n})});Ps.displayName="Slot";var No=i.forwardRef((e,t)=>{const{children:n,...r}=e;if(i.isValidElement(n)){const o=ow(n);return i.cloneElement(n,{...rw(r,n.props),ref:t?wu(t,o):o})}return i.Children.count(n)>1?i.Children.only(null):null});No.displayName="SlotClone";var tw=({children:e})=>f.jsx(f.Fragment,{children:e});function nw(e){return i.isValidElement(e)&&e.type===tw}function rw(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...c)=>{s(...c),o(...c)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function ow(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var sw=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],Ve=sw.reduce((e,t)=>{const n=i.forwardRef((r,o)=>{const{asChild:s,...a}=r,c=s?Ps:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),f.jsx(c,{...a,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function aw(e,t){e&&Me.flushSync(()=>e.dispatchEvent(t))}function iw(e,t=globalThis==null?void 0:globalThis.document){const n=at(e);i.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var cw="DismissableLayer",To="dismissableLayer.update",lw="dismissableLayer.pointerDownOutside",uw="dismissableLayer.focusOutside",Ia,Cu=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Eu=i.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:s,onInteractOutside:a,onDismiss:c,...l}=e,u=i.useContext(Cu),[d,p]=i.useState(null),g=(d==null?void 0:d.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,v]=i.useState({}),h=dt(t,E=>p(E)),m=Array.from(u.layers),[b]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),x=m.indexOf(b),y=d?m.indexOf(d):-1,w=u.layersWithOutsidePointerEventsDisabled.size>0,C=y>=x,S=pw(E=>{const $=E.target,N=[...u.branches].some(A=>A.contains($));!C||N||(o==null||o(E),a==null||a(E),E.defaultPrevented||c==null||c())},g),P=mw(E=>{const $=E.target;[...u.branches].some(A=>A.contains($))||(s==null||s(E),a==null||a(E),E.defaultPrevented||c==null||c())},g);return iw(E=>{y===u.layers.size-1&&(r==null||r(E),!E.defaultPrevented&&c&&(E.preventDefault(),c()))},g),i.useEffect(()=>{if(d)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(Ia=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(d)),u.layers.add(d),Da(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(g.body.style.pointerEvents=Ia)}},[d,g,n,u]),i.useEffect(()=>()=>{d&&(u.layers.delete(d),u.layersWithOutsidePointerEventsDisabled.delete(d),Da())},[d,u]),i.useEffect(()=>{const E=()=>v({});return document.addEventListener(To,E),()=>document.removeEventListener(To,E)},[]),f.jsx(Ve.div,{...l,ref:h,style:{pointerEvents:w?C?"auto":"none":void 0,...e.style},onFocusCapture:Ye(e.onFocusCapture,P.onFocusCapture),onBlurCapture:Ye(e.onBlurCapture,P.onBlurCapture),onPointerDownCapture:Ye(e.onPointerDownCapture,S.onPointerDownCapture)})});Eu.displayName=cw;var dw="DismissableLayerBranch",fw=i.forwardRef((e,t)=>{const n=i.useContext(Cu),r=i.useRef(null),o=dt(t,r);return i.useEffect(()=>{const s=r.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),f.jsx(Ve.div,{...e,ref:o})});fw.displayName=dw;function pw(e,t=globalThis==null?void 0:globalThis.document){const n=at(e),r=i.useRef(!1),o=i.useRef(()=>{});return i.useEffect(()=>{const s=c=>{if(c.target&&!r.current){let l=function(){Su(lw,n,u,{discrete:!0})};const u={originalEvent:c};c.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=l,t.addEventListener("click",o.current,{once:!0})):l()}else t.removeEventListener("click",o.current);r.current=!1},a=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(a),t.removeEventListener("pointerdown",s),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function mw(e,t=globalThis==null?void 0:globalThis.document){const n=at(e),r=i.useRef(!1);return i.useEffect(()=>{const o=s=>{s.target&&!r.current&&Su(uw,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Da(){const e=new CustomEvent(To);document.dispatchEvent(e)}function Su(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?aw(o,s):o.dispatchEvent(s)}var Lr="focusScope.autoFocusOnMount",Fr="focusScope.autoFocusOnUnmount",ka={bubbles:!1,cancelable:!0},hw="FocusScope",$u=i.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:s,...a}=e,[c,l]=i.useState(null),u=at(o),d=at(s),p=i.useRef(null),g=dt(t,m=>l(m)),v=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(r){let m=function(w){if(v.paused||!c)return;const C=w.target;c.contains(C)?p.current=C:Ge(p.current,{select:!0})},b=function(w){if(v.paused||!c)return;const C=w.relatedTarget;C!==null&&(c.contains(C)||Ge(p.current,{select:!0}))},x=function(w){if(document.activeElement===document.body)for(const S of w)S.removedNodes.length>0&&Ge(c)};document.addEventListener("focusin",m),document.addEventListener("focusout",b);const y=new MutationObserver(x);return c&&y.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",m),document.removeEventListener("focusout",b),y.disconnect()}}},[r,c,v.paused]),i.useEffect(()=>{if(c){Fa.add(v);const m=document.activeElement;if(!c.contains(m)){const x=new CustomEvent(Lr,ka);c.addEventListener(Lr,u),c.dispatchEvent(x),x.defaultPrevented||(gw(ww(Ru(c)),{select:!0}),document.activeElement===m&&Ge(c))}return()=>{c.removeEventListener(Lr,u),setTimeout(()=>{const x=new CustomEvent(Fr,ka);c.addEventListener(Fr,d),c.dispatchEvent(x),x.defaultPrevented||Ge(m??document.body,{select:!0}),c.removeEventListener(Fr,d),Fa.remove(v)},0)}}},[c,u,d,v]);const h=i.useCallback(m=>{if(!n&&!r||v.paused)return;const b=m.key==="Tab"&&!m.altKey&&!m.ctrlKey&&!m.metaKey,x=document.activeElement;if(b&&x){const y=m.currentTarget,[w,C]=vw(y);w&&C?!m.shiftKey&&x===C?(m.preventDefault(),n&&Ge(w,{select:!0})):m.shiftKey&&x===w&&(m.preventDefault(),n&&Ge(C,{select:!0})):x===y&&m.preventDefault()}},[n,r,v.paused]);return f.jsx(Ve.div,{tabIndex:-1,...a,ref:g,onKeyDown:h})});$u.displayName=hw;function gw(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(Ge(r,{select:t}),document.activeElement!==n)return}function vw(e){const t=Ru(e),n=La(t,e),r=La(t.reverse(),e);return[n,r]}function Ru(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function La(e,t){for(const n of e)if(!bw(n,{upTo:t}))return n}function bw(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function xw(e){return e instanceof HTMLInputElement&&"select"in e}function Ge(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&xw(e)&&t&&e.select()}}var Fa=yw();function yw(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=Ba(e,t),e.unshift(t)},remove(t){var n;e=Ba(e,t),(n=e[0])==null||n.resume()}}}function Ba(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function ww(e){return e.filter(t=>t.tagName!=="A")}var Cw="Portal",Pu=i.forwardRef((e,t)=>{var c;const{container:n,...r}=e,[o,s]=i.useState(!1);Gn(()=>s(!0),[]);const a=n||o&&((c=globalThis==null?void 0:globalThis.document)==null?void 0:c.body);return a?ko.createPortal(f.jsx(Ve.div,{...r,ref:t}),a):null});Pu.displayName=Cw;function Ew(e,t){return i.useReducer((n,r)=>t[n][r]??n,e)}var xr=e=>{const{present:t,children:n}=e,r=Sw(t),o=typeof n=="function"?n({present:r.isPresent}):i.Children.only(n),s=dt(r.ref,$w(o));return typeof n=="function"||r.isPresent?i.cloneElement(o,{ref:s}):null};xr.displayName="Presence";function Sw(e){const[t,n]=i.useState(),r=i.useRef({}),o=i.useRef(e),s=i.useRef("none"),a=e?"mounted":"unmounted",[c,l]=Ew(a,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return i.useEffect(()=>{const u=bn(r.current);s.current=c==="mounted"?u:"none"},[c]),Gn(()=>{const u=r.current,d=o.current;if(d!==e){const g=s.current,v=bn(u);e?l("MOUNT"):v==="none"||(u==null?void 0:u.display)==="none"?l("UNMOUNT"):l(d&&g!==v?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),Gn(()=>{if(t){let u;const d=t.ownerDocument.defaultView??window,p=v=>{const m=bn(r.current).includes(v.animationName);if(v.target===t&&m&&(l("ANIMATION_END"),!o.current)){const b=t.style.animationFillMode;t.style.animationFillMode="forwards",u=d.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=b)})}},g=v=>{v.target===t&&(s.current=bn(r.current))};return t.addEventListener("animationstart",g),t.addEventListener("animationcancel",p),t.addEventListener("animationend",p),()=>{d.clearTimeout(u),t.removeEventListener("animationstart",g),t.removeEventListener("animationcancel",p),t.removeEventListener("animationend",p)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:i.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function bn(e){return(e==null?void 0:e.animationName)||"none"}function $w(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Br=0;function Rw(){i.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Ua()),document.body.insertAdjacentElement("beforeend",e[1]??Ua()),Br++,()=>{Br===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Br--}},[])}function Ua(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Nu=ps(),Ur=function(){},yr=i.forwardRef(function(e,t){var n=i.useRef(null),r=i.useState({onScrollCapture:Ur,onWheelCapture:Ur,onTouchMoveCapture:Ur}),o=r[0],s=r[1],a=e.forwardProps,c=e.children,l=e.className,u=e.removeScrollBar,d=e.enabled,p=e.shards,g=e.sideCar,v=e.noIsolation,h=e.inert,m=e.allowPinchZoom,b=e.as,x=b===void 0?"div":b,y=e.gapMode,w=sr(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=g,S=fs([n,t]),P=Q(Q({},w),o);return i.createElement(i.Fragment,null,d&&i.createElement(C,{sideCar:Nu,removeScrollBar:u,shards:p,noIsolation:v,inert:h,setCallbacks:s,allowPinchZoom:!!m,lockRef:n,gapMode:y}),a?i.cloneElement(i.Children.only(c),Q(Q({},P),{ref:S})):i.createElement(x,Q({},P,{className:l,ref:S}),c))});yr.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};yr.classNames={fullWidth:Ct,zeroRight:wt};var Ao=!1;if(typeof window<"u")try{var xn=Object.defineProperty({},"passive",{get:function(){return Ao=!0,!0}});window.addEventListener("test",xn,xn),window.removeEventListener("test",xn,xn)}catch{Ao=!1}var vt=Ao?{passive:!1}:!1,Pw=function(e){return e.tagName==="TEXTAREA"},Tu=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Pw(e)&&n[t]==="visible")},Nw=function(e){return Tu(e,"overflowY")},Tw=function(e){return Tu(e,"overflowX")},Wa=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=Au(e,r);if(o){var s=Ou(e,r),a=s[1],c=s[2];if(a>c)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Aw=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},Ow=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Au=function(e,t){return e==="v"?Nw(t):Tw(t)},Ou=function(e,t){return e==="v"?Aw(t):Ow(t)},_w=function(e,t){return e==="h"&&t==="rtl"?-1:1},Mw=function(e,t,n,r,o){var s=_w(e,window.getComputedStyle(t).direction),a=s*r,c=n.target,l=t.contains(c),u=!1,d=a>0,p=0,g=0;do{var v=Ou(e,c),h=v[0],m=v[1],b=v[2],x=m-b-s*h;(h||x)&&Au(e,c)&&(p+=x,g+=h),c instanceof ShadowRoot?c=c.host:c=c.parentNode}while(!l&&c!==document.body||l&&(t.contains(c)||t===c));return(d&&(o&&Math.abs(p)<1||!o&&a>p)||!d&&(o&&Math.abs(g)<1||!o&&-a>g))&&(u=!0),u},yn=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Va=function(e){return[e.deltaX,e.deltaY]},Ha=function(e){return e&&"current"in e?e.current:e},jw=function(e,t){return e[0]===t[0]&&e[1]===t[1]},Iw=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},Dw=0,bt=[];function kw(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),o=i.useState(Dw++)[0],s=i.useState(ir)[0],a=i.useRef(e);i.useEffect(function(){a.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var m=ds([e.lockRef.current],(e.shards||[]).map(Ha),!0).filter(Boolean);return m.forEach(function(b){return b.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),m.forEach(function(b){return b.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=i.useCallback(function(m,b){if("touches"in m&&m.touches.length===2||m.type==="wheel"&&m.ctrlKey)return!a.current.allowPinchZoom;var x=yn(m),y=n.current,w="deltaX"in m?m.deltaX:y[0]-x[0],C="deltaY"in m?m.deltaY:y[1]-x[1],S,P=m.target,E=Math.abs(w)>Math.abs(C)?"h":"v";if("touches"in m&&E==="h"&&P.type==="range")return!1;var $=Wa(E,P);if(!$)return!0;if($?S=E:(S=E==="v"?"h":"v",$=Wa(E,P)),!$)return!1;if(!r.current&&"changedTouches"in m&&(w||C)&&(r.current=S),!S)return!0;var N=r.current||S;return Mw(N,b,m,N==="h"?w:C,!0)},[]),l=i.useCallback(function(m){var b=m;if(!(!bt.length||bt[bt.length-1]!==s)){var x="deltaY"in b?Va(b):yn(b),y=t.current.filter(function(S){return S.name===b.type&&(S.target===b.target||b.target===S.shadowParent)&&jw(S.delta,x)})[0];if(y&&y.should){b.cancelable&&b.preventDefault();return}if(!y){var w=(a.current.shards||[]).map(Ha).filter(Boolean).filter(function(S){return S.contains(b.target)}),C=w.length>0?c(b,w[0]):!a.current.noIsolation;C&&b.cancelable&&b.preventDefault()}}},[]),u=i.useCallback(function(m,b,x,y){var w={name:m,delta:b,target:x,should:y,shadowParent:Lw(x)};t.current.push(w),setTimeout(function(){t.current=t.current.filter(function(C){return C!==w})},1)},[]),d=i.useCallback(function(m){n.current=yn(m),r.current=void 0},[]),p=i.useCallback(function(m){u(m.type,Va(m),m.target,c(m,e.lockRef.current))},[]),g=i.useCallback(function(m){u(m.type,yn(m),m.target,c(m,e.lockRef.current))},[]);i.useEffect(function(){return bt.push(s),e.setCallbacks({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:g}),document.addEventListener("wheel",l,vt),document.addEventListener("touchmove",l,vt),document.addEventListener("touchstart",d,vt),function(){bt=bt.filter(function(m){return m!==s}),document.removeEventListener("wheel",l,vt),document.removeEventListener("touchmove",l,vt),document.removeEventListener("touchstart",d,vt)}},[]);var v=e.removeScrollBar,h=e.inert;return i.createElement(i.Fragment,null,h?i.createElement(s,{styles:Iw(o)}):null,v?i.createElement(hs,{gapMode:e.gapMode}):null)}function Lw(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const Fw=ms(Nu,kw);var _u=i.forwardRef(function(e,t){return i.createElement(yr,Q({},e,{ref:t,sideCar:Fw}))});_u.classNames=yr.classNames;const Bw=_u;var Ns="Dialog",[Mu,pC]=Yy(Ns),[Uw,Pe]=Mu(Ns),ju=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:s,modal:a=!0}=e,c=i.useRef(null),l=i.useRef(null),[u=!1,d]=Qy({prop:r,defaultProp:o,onChange:s});return f.jsx(Uw,{scope:t,triggerRef:c,contentRef:l,contentId:kr(),titleId:kr(),descriptionId:kr(),open:u,onOpenChange:d,onOpenToggle:i.useCallback(()=>d(p=>!p),[d]),modal:a,children:n})};ju.displayName=Ns;var Iu="DialogTrigger",Ww=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Pe(Iu,n),s=dt(t,o.triggerRef);return f.jsx(Ve.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":Os(o.open),...r,ref:s,onClick:Ye(e.onClick,o.onOpenToggle)})});Ww.displayName=Iu;var Ts="DialogPortal",[Vw,Du]=Mu(Ts,{forceMount:void 0}),ku=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,s=Pe(Ts,t);return f.jsx(Vw,{scope:t,forceMount:n,children:i.Children.map(r,a=>f.jsx(xr,{present:n||s.open,children:f.jsx(Pu,{asChild:!0,container:o,children:a})}))})};ku.displayName=Ts;var Kn="DialogOverlay",Lu=i.forwardRef((e,t)=>{const n=Du(Kn,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=Pe(Kn,e.__scopeDialog);return s.modal?f.jsx(xr,{present:r||s.open,children:f.jsx(Hw,{...o,ref:t})}):null});Lu.displayName=Kn;var Hw=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Pe(Kn,n);return f.jsx(Bw,{as:Ps,allowPinchZoom:!0,shards:[o.contentRef],children:f.jsx(Ve.div,{"data-state":Os(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),it="DialogContent",Fu=i.forwardRef((e,t)=>{const n=Du(it,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=Pe(it,e.__scopeDialog);return f.jsx(xr,{present:r||s.open,children:s.modal?f.jsx(zw,{...o,ref:t}):f.jsx(Gw,{...o,ref:t})})});Fu.displayName=it;var zw=i.forwardRef((e,t)=>{const n=Pe(it,e.__scopeDialog),r=i.useRef(null),o=dt(t,n.contentRef,r);return i.useEffect(()=>{const s=r.current;if(s)return us(s)},[]),f.jsx(Bu,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:Ye(e.onCloseAutoFocus,s=>{var a;s.preventDefault(),(a=n.triggerRef.current)==null||a.focus()}),onPointerDownOutside:Ye(e.onPointerDownOutside,s=>{const a=s.detail.originalEvent,c=a.button===0&&a.ctrlKey===!0;(a.button===2||c)&&s.preventDefault()}),onFocusOutside:Ye(e.onFocusOutside,s=>s.preventDefault())})}),Gw=i.forwardRef((e,t)=>{const n=Pe(it,e.__scopeDialog),r=i.useRef(!1),o=i.useRef(!1);return f.jsx(Bu,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{var a,c;(a=e.onCloseAutoFocus)==null||a.call(e,s),s.defaultPrevented||(r.current||(c=n.triggerRef.current)==null||c.focus(),s.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:s=>{var l,u;(l=e.onInteractOutside)==null||l.call(e,s),s.defaultPrevented||(r.current=!0,s.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const a=s.target;((u=n.triggerRef.current)==null?void 0:u.contains(a))&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&o.current&&s.preventDefault()}})}),Bu=i.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:s,...a}=e,c=Pe(it,n),l=i.useRef(null),u=dt(t,l);return Rw(),f.jsxs(f.Fragment,{children:[f.jsx($u,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:s,children:f.jsx(Eu,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":Os(c.open),...a,ref:u,onDismiss:()=>c.onOpenChange(!1)})}),f.jsxs(f.Fragment,{children:[f.jsx(Kw,{titleId:c.titleId}),f.jsx(Yw,{contentRef:l,descriptionId:c.descriptionId})]})]})}),As="DialogTitle",Uu=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Pe(As,n);return f.jsx(Ve.h2,{id:o.titleId,...r,ref:t})});Uu.displayName=As;var Wu="DialogDescription",Vu=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Pe(Wu,n);return f.jsx(Ve.p,{id:o.descriptionId,...r,ref:t})});Vu.displayName=Wu;var Hu="DialogClose",zu=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Pe(Hu,n);return f.jsx(Ve.button,{type:"button",...r,ref:t,onClick:Ye(e.onClick,()=>o.onOpenChange(!1))})});zu.displayName=Hu;function Os(e){return e?"open":"closed"}var Gu="DialogTitleWarning",[mC,Ku]=qy(Gu,{contentName:it,titleName:As,docsSlug:"dialog"}),Kw=({titleId:e})=>{const t=Ku(Gu),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return i.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},qw="DialogDescriptionWarning",Yw=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Ku(qw).contentName}}.`;return i.useEffect(()=>{var s;const o=(s=e.current)==null?void 0:s.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},Xw=ju,Jw=ku,qu=Lu,Yu=Fu,Xu=Uu,Ju=Vu,Zw=zu;const Qw=Xw,e0=Jw,Zu=i.forwardRef(({className:e,...t},n)=>f.jsx(qu,{ref:n,className:U("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));Zu.displayName=qu.displayName;const Qu=i.forwardRef(({className:e,children:t,...n},r)=>f.jsxs(e0,{children:[f.jsx(Zu,{}),f.jsxs(Yu,{ref:r,className:U("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...n,children:[t,f.jsxs(Zw,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[f.jsx(Hd,{className:"h-4 w-4"}),f.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));Qu.displayName=Yu.displayName;const ed=({className:e,...t})=>f.jsx("div",{className:U("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});ed.displayName="DialogHeader";const td=i.forwardRef(({className:e,...t},n)=>f.jsx(Xu,{ref:n,className:U("text-lg font-semibold leading-none tracking-tight",e),...t}));td.displayName=Xu.displayName;const nd=i.forwardRef(({className:e,...t},n)=>f.jsx(Ju,{ref:n,className:U("text-sm text-muted-foreground",e),...t}));nd.displayName=Ju.displayName;function t0(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function n0(...e){return t=>e.forEach(n=>t0(n,t))}var rd=i.forwardRef((e,t)=>{const{children:n,...r}=e,o=i.Children.toArray(n),s=o.find(o0);if(s){const a=s.props.children,c=o.map(l=>l===s?i.Children.count(a)>1?i.Children.only(null):i.isValidElement(a)?a.props.children:null:l);return f.jsx(Oo,{...r,ref:t,children:i.isValidElement(a)?i.cloneElement(a,void 0,c):null})}return f.jsx(Oo,{...r,ref:t,children:n})});rd.displayName="Slot";var Oo=i.forwardRef((e,t)=>{const{children:n,...r}=e;if(i.isValidElement(n)){const o=a0(n);return i.cloneElement(n,{...s0(r,n.props),ref:t?n0(t,o):o})}return i.Children.count(n)>1?i.Children.only(null):null});Oo.displayName="SlotClone";var r0=({children:e})=>f.jsx(f.Fragment,{children:e});function o0(e){return i.isValidElement(e)&&e.type===r0}function s0(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...c)=>{s(...c),o(...c)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function a0(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var i0=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],c0=i0.reduce((e,t)=>{const n=i.forwardRef((r,o)=>{const{asChild:s,...a}=r,c=s?rd:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),f.jsx(c,{...a,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),l0="Label",od=i.forwardRef((e,t)=>f.jsx(c0.label,{...e,ref:t,onMouseDown:n=>{var o;n.target.closest("button, input, select, textarea")||((o=e.onMouseDown)==null||o.call(e,n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}}));od.displayName=l0;var sd=od;const u0=zd("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),Ke=i.forwardRef(({className:e,...t},n)=>f.jsx(sd,{ref:n,className:U(u0(),e),...t}));Ke.displayName=sd.displayName;const Z={generateSVG:(e,t)=>{const{colors:n,angle:r,name:o}=e,{width:s,height:a}=t,c=r*Math.PI/180,l=Math.round(50+50*Math.cos(c+Math.PI/2)),u=Math.round(50+50*Math.sin(c+Math.PI/2)),d=Math.round(50+50*Math.cos(c-Math.PI/2)),p=Math.round(50+50*Math.sin(c-Math.PI/2)),g=n.map((v,h)=>`<stop offset="${n.length===1?0:h/(n.length-1)*100}%" stop-color="${v}" />`).join(`
    `);return`<?xml version="1.0" encoding="UTF-8"?>
<svg width="${s}" height="${a}" viewBox="0 0 ${s} ${a}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="${l}%" y1="${u}%" x2="${d}%" y2="${p}%">
      ${g}
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#gradient)" />
  <text x="50%" y="95%" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="rgba(255,255,255,0.8)">${o}</text>
</svg>`},convertSVGToFormat:async(e,t)=>new Promise((n,r)=>{const o=document.createElement("canvas"),s=o.getContext("2d");if(!s){r(new Error("Canvas context not available"));return}o.width=t.width,o.height=t.height;const a=new Image,c=new Blob([e],{type:"image/svg+xml;charset=utf-8"}),l=URL.createObjectURL(c);a.onload=()=>{s.drawImage(a,0,0,t.width,t.height);let u="image/png",d;switch(t.format){case"png":u="image/png";break;case"jpg":u="image/jpeg",d=t.quality||.9;break;case"webp":u="image/webp",d=t.quality||.9;break}const p=o.toDataURL(u,d);URL.revokeObjectURL(l),n(p)},a.onerror=()=>{URL.revokeObjectURL(l),r(new Error("Failed to load SVG"))},a.src=l}),downloadFile:(e,t,n)=>{const r=document.createElement("a");r.download=`${t}.${n}`,r.href=e,document.body.appendChild(r),r.click(),document.body.removeChild(r)},exportGradient:async(e,t)=>{try{const n=e.name.toLowerCase().replace(/\s+/g,"-");switch(t.format){case"svg":const r=Z.generateSVG(e,t),o=new Blob([r],{type:"image/svg+xml;charset=utf-8"}),s=URL.createObjectURL(o);Z.downloadFile(s,n,"svg"),URL.revokeObjectURL(s);break;case"png":case"jpg":case"webp":const a=Z.generateSVG(e,t),c=await Z.convertSVGToFormat(a,t);Z.downloadFile(c,n,t.format);break;case"css":const l=Z.generateCSS(e,t);await Z.copyToClipboard(l);break;case"json":const u=Z.generateJSON(e,t),d=new Blob([u],{type:"application/json"}),p=URL.createObjectURL(d);Z.downloadFile(p,n,"json"),URL.revokeObjectURL(p);break;case"android":const g=Z.generateAndroidXML(e,t),v=new Blob([g],{type:"text/xml"}),h=URL.createObjectURL(v);Z.downloadFile(h,n,"xml"),URL.revokeObjectURL(h);break;case"ios":const m=Z.generateiOSSwift(e,t);await Z.copyToClipboard(m);break;case"less":const b=Z.generateLess(e,t);await Z.copyToClipboard(b);break;case"scss":const x=Z.generateSCSS(e,t);await Z.copyToClipboard(x);break;case"figma":const y=Z.generateFigma(e,t);await Z.copyToClipboard(y);break;case"sketch":const w=Z.generateSketch(e,t);await Z.copyToClipboard(w);break;case"mesh":const C=Z.generateMeshGradient(e,t),S=new Blob([C],{type:"image/svg+xml;charset=utf-8"}),P=URL.createObjectURL(S);Z.downloadFile(P,`${n}-mesh`,"svg"),URL.revokeObjectURL(P);break;default:throw new Error(`Unsupported format: ${t.format}`)}}catch(n){throw console.error("Export failed:",n),n}},getSizePresets:()=>[{name:"Small",width:400,height:300},{name:"Medium",width:800,height:600},{name:"Large",width:1200,height:900},{name:"HD",width:1920,height:1080},{name:"Square Small",width:400,height:400},{name:"Square Medium",width:800,height:800},{name:"Square Large",width:1200,height:1200}],getFormatOptions:()=>[{value:"svg",label:"SVG",description:"Vector format, scalable"},{value:"png",label:"PNG",description:"High quality, transparent background support"},{value:"jpg",label:"JPG",description:"Smaller file size, good for photos"},{value:"webp",label:"WebP",description:"Modern format, excellent compression"},{value:"json",label:"JSON",description:"Data format for design tools"},{value:"css",label:"CSS",description:"CSS gradient code"},{value:"android",label:"Android XML",description:"Android gradient drawable"},{value:"ios",label:"iOS Swift",description:"Swift CAGradientLayer code"},{value:"less",label:"Less",description:"Less preprocessor code"},{value:"scss",label:"SCSS",description:"SCSS preprocessor code"},{value:"figma",label:"Figma",description:"Figma plugin compatible"},{value:"sketch",label:"Sketch",description:"Sketch plugin compatible"},{value:"mesh",label:"Mesh Gradient",description:"SVG mesh gradient"}],generateCSS:(e,t)=>{const{colors:n,angle:r=45}=e,o=t.angle||r,s=n.join(", ");return`background: linear-gradient(${o}deg, ${s});`},generateJSON:(e,t)=>{const n={name:e.name,colors:e.colors,angle:t.angle||45,type:"linear-gradient",format:"css",metadata:{exportedAt:new Date().toISOString(),tool:"GradientsCSS",version:"1.0.0"}};return JSON.stringify(n,null,2)},generateAndroidXML:(e,t)=>{const{colors:n,name:r}=e,o=t.angle||45,s=n.map((a,c)=>`    <item android:offset="${n.length===1?0:c/(n.length-1)}" android:color="${a}" />`).join(`
`);return`<?xml version="1.0" encoding="utf-8"?>
<!-- ${r} gradient -->
<shape xmlns:android="http://schemas.android.com/apk/res/android">
  <gradient
    android:type="linear"
    android:angle="${o}"
    android:startColor="${n[0]}"
    android:endColor="${n[n.length-1]}">
${s}
  </gradient>
</shape>`},generateiOSSwift:(e,t)=>{const{colors:n,name:r}=e,s=(t.angle||45)*Math.PI/180,a=.5+.5*Math.cos(s+Math.PI),c=.5+.5*Math.sin(s+Math.PI),l=.5+.5*Math.cos(s),u=.5+.5*Math.sin(s),d=n.map(g=>`UIColor(hex: "${g}").cgColor`).join(", "),p=n.map((g,v)=>n.length===1?"0.0":(v/(n.length-1)).toFixed(2)).join(", ");return`// ${r} gradient
let ${r.toLowerCase().replace(/\s+/g,"")}Gradient = CAGradientLayer()
${r.toLowerCase().replace(/\s+/g,"")}Gradient.colors = [${d}]
${r.toLowerCase().replace(/\s+/g,"")}Gradient.locations = [${p}]
${r.toLowerCase().replace(/\s+/g,"")}Gradient.startPoint = CGPoint(x: ${a.toFixed(2)}, y: ${c.toFixed(2)})
${r.toLowerCase().replace(/\s+/g,"")}Gradient.endPoint = CGPoint(x: ${l.toFixed(2)}, y: ${u.toFixed(2)})
${r.toLowerCase().replace(/\s+/g,"")}Gradient.frame = view.bounds
view.layer.insertSublayer(${r.toLowerCase().replace(/\s+/g,"")}Gradient, at: 0)`},generateLess:(e,t)=>{const{colors:n,name:r}=e,o=t.angle||45,s=n.join(", "),a=r.toLowerCase().replace(/\s+/g,"-");return`.${a}-gradient() {
  background: linear-gradient(${o}deg, ${s});
}

// Usage: .${a}-gradient();`},generateSCSS:(e,t)=>{const{colors:n,name:r}=e,o=t.angle||45,s=n.join(", "),a=r.toLowerCase().replace(/\s+/g,"-");return`@mixin ${a}-gradient($angle: ${o}deg) {
  background: linear-gradient($angle, ${s});
}

// Usage: @include ${a}-gradient();`},generateFigma:(e,t)=>{const{colors:n,name:r}=e,o=t.angle||45,s=n.map((c,l)=>({color:c,position:n.length===1?0:l/(n.length-1)})),a={name:r,type:"GRADIENT_LINEAR",gradientStops:s,gradientTransform:[[Math.cos(o*Math.PI/180),Math.sin(o*Math.PI/180),0],[-Math.sin(o*Math.PI/180),Math.cos(o*Math.PI/180),0]]};return JSON.stringify(a,null,2)},generateSketch:(e,t)=>{const{colors:n}=e,r={_class:"gradient",elipseLength:0,from:"{0.5, 0}",gradientType:0,to:"{0.5, 1}",stops:n.map((o,s)=>({_class:"gradientStop",color:{_class:"color",alpha:1,blue:parseInt(o.slice(5,7),16)/255,green:parseInt(o.slice(3,5),16)/255,red:parseInt(o.slice(1,3),16)/255},position:n.length===1?0:s/(n.length-1)}))};return JSON.stringify(r,null,2)},generateMeshGradient:(e,t)=>{const{colors:n,name:r}=e,{width:o,height:s,meshComplexity:a=5}=t,c=[];for(let d=0;d<a;d++){const p=(Math.random()*.8+.1)*100,g=(Math.random()*.8+.1)*100,v=n[Math.floor(Math.random()*n.length)],h=Math.random()*30+20;c.push({id:`mesh-${d}`,x:p,y:g,color:v,radius:h})}const l=c.map(d=>`
    <radialGradient id="${d.id}" cx="${d.x}%" cy="${d.y}%" r="${d.radius}%">
      <stop offset="0%" stop-color="${d.color}" stop-opacity="0.8"/>
      <stop offset="100%" stop-color="${d.color}" stop-opacity="0"/>
    </radialGradient>`).join(""),u=c.map(d=>`
    <circle cx="${d.x}%" cy="${d.y}%" r="${d.radius}%" fill="url(#${d.id})"/>`).join("");return`<?xml version="1.0" encoding="UTF-8"?>
<svg width="${o}" height="${s}" viewBox="0 0 ${o} ${s}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    ${l}
  </defs>
  <rect width="100%" height="100%" fill="${n[0]}"/>
  ${u}
  <text x="50%" y="95%" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="rgba(255,255,255,0.8)">${r}</text>
</svg>`},copyToClipboard:async e=>{try{await navigator.clipboard.writeText(e)}catch{const n=document.createElement("textarea");n.value=e,document.body.appendChild(n),n.select(),document.execCommand("copy"),document.body.removeChild(n)}}};function d0({isOpen:e,onClose:t,gradientData:n}){const[r,o]=i.useState("png"),[s,a]=i.useState("Medium"),[c,l]=i.useState(800),[u,d]=i.useState(600),[p,g]=i.useState([90]),[v,h]=i.useState([45]),[m,b]=i.useState([5]),[x,y]=i.useState(!1),w=Z.getSizePresets(),C=Z.getFormatOptions(),S=()=>s==="Custom"?{width:c,height:u}:w.find(N=>N.name===s)||{width:800,height:600},P=async()=>{y(!0);try{const $=S(),N={format:r,width:$.width,height:$.height,quality:r==="jpg"||r==="webp"?p[0]/100:void 0,angle:v[0],meshComplexity:m[0]};await Z.exportGradient(n,N);const A=["css","ios","less","scss","figma","sketch"].includes(r);Vr({title:"Export Successful",description:A?`${n.name} code copied to clipboard`:`${n.name} exported as ${r.toUpperCase()}`}),t()}catch{Vr({title:"Export Failed",description:"There was an error exporting your gradient. Please try again.",variant:"destructive"})}finally{y(!1)}},E=$=>{switch($){case"svg":return f.jsx(Ls,{className:"h-4 w-4"});case"png":case"jpg":case"webp":return f.jsx(ks,{className:"h-4 w-4"});case"json":return f.jsx(sf,{className:"h-4 w-4"});case"css":case"less":case"scss":return f.jsx(rf,{className:"h-4 w-4"});case"android":return f.jsx(df,{className:"h-4 w-4"});case"ios":return f.jsx(lf,{className:"h-4 w-4"});case"figma":case"sketch":return f.jsx(cf,{className:"h-4 w-4"});case"mesh":return f.jsx(Ls,{className:"h-4 w-4"});default:return f.jsx(ks,{className:"h-4 w-4"})}};return f.jsx(Qw,{open:e,onOpenChange:t,children:f.jsxs(Qu,{className:"sm:max-w-md",children:[f.jsxs(ed,{children:[f.jsxs(td,{className:"flex items-center gap-2",children:[f.jsx(Hr,{className:"h-5 w-5"}),"Export Gradient"]}),f.jsxs(nd,{children:['Export "',n.name,'" in various formats for different platforms and use cases']})]}),f.jsxs("div",{className:"space-y-6",children:[f.jsxs("div",{className:"space-y-2",children:[f.jsx(Ke,{children:"Format"}),f.jsxs(io,{value:r,onValueChange:$=>o($),children:[f.jsx(Fn,{children:f.jsx(co,{})}),f.jsx(Bn,{children:C.map($=>f.jsx(St,{value:$.value,children:f.jsxs("div",{className:"flex items-center gap-2",children:[E($.value),f.jsxs("div",{children:[f.jsx("div",{className:"font-medium",children:$.label}),f.jsx("div",{className:"text-xs text-muted-foreground",children:$.description})]})]})},$.value))})]})]}),!["css","ios","less","scss","figma","sketch","json"].includes(r)&&f.jsxs("div",{className:"space-y-2",children:[f.jsx(Ke,{children:"Size"}),f.jsxs(io,{value:s,onValueChange:a,children:[f.jsx(Fn,{children:f.jsx(co,{})}),f.jsxs(Bn,{children:[w.map($=>f.jsxs(St,{value:$.name,children:[$.name," (",$.width," × ",$.height,")"]},$.name)),f.jsx(St,{value:"Custom",children:"Custom Size"})]})]})]}),s==="Custom"&&!["css","ios","less","scss","figma","sketch","json"].includes(r)&&f.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[f.jsxs("div",{className:"space-y-2",children:[f.jsx(Ke,{children:"Width"}),f.jsx("input",{type:"number",value:c,onChange:$=>l(Number($.target.value)),className:"w-full rounded-md border border-border bg-background px-3 py-2 text-sm",min:"100",max:"4000"})]}),f.jsxs("div",{className:"space-y-2",children:[f.jsx(Ke,{children:"Height"}),f.jsx("input",{type:"number",value:u,onChange:$=>d(Number($.target.value)),className:"w-full rounded-md border border-border bg-background px-3 py-2 text-sm",min:"100",max:"4000"})]})]}),(r==="jpg"||r==="webp")&&f.jsxs("div",{className:"space-y-2",children:[f.jsxs(Ke,{children:["Quality: ",p[0],"%"]}),f.jsx(Ut,{value:p,onValueChange:g,max:100,min:10,step:5,className:"w-full"})]}),!["json","figma","sketch"].includes(r)&&f.jsxs("div",{className:"space-y-2",children:[f.jsxs(Ke,{children:["Gradient Angle: ",v[0],"°"]}),f.jsx(Ut,{value:v,onValueChange:h,max:360,min:0,step:15,className:"w-full"})]}),r==="mesh"&&f.jsxs("div",{className:"space-y-2",children:[f.jsxs(Ke,{children:["Mesh Complexity: ",m[0]]}),f.jsx(Ut,{value:m,onValueChange:b,max:10,min:1,step:1,className:"w-full"})]}),f.jsxs("div",{className:"rounded-lg bg-muted p-3 text-sm",children:[f.jsx("div",{className:"mb-1 font-medium",children:"Export Preview"}),f.jsxs("div",{className:"text-muted-foreground",children:["Format: ",r.toUpperCase(),!["css","ios","less","scss","figma","sketch"].includes(r)&&` • Size: ${S().width} × ${S().height}px`,(r==="jpg"||r==="webp")&&` • Quality: ${p[0]}%`,!["json","figma","sketch"].includes(r)&&` • Angle: ${v[0]}°`,r==="mesh"&&` • Complexity: ${m[0]}`]})]})]}),f.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[f.jsx(Tn,{variant:"outline",onClick:t,children:"Cancel"}),f.jsx(Tn,{onClick:P,disabled:x,children:x?f.jsxs(f.Fragment,{children:[f.jsx("div",{className:"mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"}),"Exporting..."]}):f.jsx(f.Fragment,{children:["css","ios","less","scss","figma","sketch"].includes(r)?f.jsxs(f.Fragment,{children:[f.jsx(Xa,{className:"mr-2 h-4 w-4"}),"Copy Code"]}):f.jsxs(f.Fragment,{children:[f.jsx(Hr,{className:"mr-2 h-4 w-4"}),"Export"]})})})]})]})})}function f0({gradient:e,isFavorite:t,onFavoriteToggle:n}){const[r,o]=i.useState("tailwind"),[s,a]=i.useState(90),[c,l]=i.useState(!1),{selectedColorFormat:u,setSelectedColorFormat:d,getColorInFormat:p,gradientType:g,setGradientType:v}=zy(),{copiedStates:h,copyToClipboard:m}=Gy(),b=["HEX","RGB","HSL"],x={name:e.name,colors:e.colors,angle:s,type:g},y=()=>{l(!0)},w=()=>{const E=e.colors.map(N=>p(N)),$=`linear-gradient(${s}deg, ${E.join(", ")})`;return g==="background"?{backgroundImage:$}:{color:"transparent",backgroundImage:$,WebkitBackgroundClip:"text"}},C=E=>{const $=e.colors.map(D=>p(D)),N=$[0],A=$[$.length-1],O=$.length===3,_=O?$[1]:null,L=`linear-gradient(${s}deg, ${$.join(", ")})`;switch(E){case"tailwind":return g==="background"?O?`bg-gradient-to-r from-[${N}] via-[${_}] to-[${A}]`:`bg-gradient-to-r from-[${N}] to-[${A}]`:`text-transparent bg-clip-text bg-gradient-to-r from-[${N}] ${O?`via-[${_}] `:""}to-[${A}]`;case"css":return g==="background"?`background-image: ${L};`:`color: transparent;
background-image: ${L};
-webkit-background-clip: text;
background-clip: text;`;case"sass":return`// Gradient using ${u} colors
$color-1: ${N};
${O?`$color-2: ${_};`:""}
$color-final: ${A};
$gradient-angle: ${s}deg;
${g==="background"?"background-image":"color"}: linear-gradient(
  $gradient-angle,
  $color-1,
  ${O?"$color-2,":""}
  $color-final
);
${g==="text"?`
color: transparent;
background-image: linear-gradient(
  $gradient-angle,
  $color-1,
  ${O?"$color-2,":""}
  $color-final
);
-webkit-background-clip: text;
background-clip: text;`:""}`;case"bootstrap":return`// Bootstrap gradient using ${u} colors
$gradient-degrees: ${s};
$start-color: ${N};
${O?`$middle-color: ${_};`:""}
$end-color: ${A};

.gradient {
  ${g==="background"?`
  background-image: linear-gradient(
    #{$gradient-degrees}deg,
    $start-color,
    ${O?"$middle-color,":""}
    $end-color
  );`:`
  color: transparent;
  background-image: linear-gradient(
    #{$gradient-degrees}deg,
    $start-color,
    ${O?"$middle-color,":""}
    $end-color
  );
  -webkit-background-clip: text;
  background-clip: text;`}
}`;default:return""}},S=()=>{n(e.name)},{theme:P}=Pv();return f.jsxs(ey,{gradientColor:P==="dark"?"#262626":"#282828",className:"overflow-hidden transition-all duration-300",children:[f.jsx(Ya,{mode:"wait",children:f.jsx(pe.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3},children:f.jsx(ty,{style:w(),gradientType:g,gradient:e})},g)}),f.jsxs("footer",{className:"flex flex-col items-start space-y-4 p-4",children:[f.jsx("div",{className:"flex w-full items-center justify-between",children:f.jsx(ny,{name:e.name,isFavorite:t,onFavoriteToggle:S,onExport:y})}),f.jsxs("div",{className:"flex w-full items-center justify-between gap-2",children:[f.jsx(ry,{colors:e.colors,getColorInFormat:p,copyToClipboard:(E,$)=>{m(E,$)}}),f.jsxs("div",{className:"flex gap-1",children:[f.jsxs(Co,{children:[f.jsx(Eo,{className:"nofocus inline-flex",children:f.jsxs("div",{className:"flex w-24 items-center justify-between rounded-md border border-border bg-secondary p-2 text-sm text-primary",children:[f.jsx("span",{children:u}),f.jsx(Ht,{className:"h-4 w-4"})]})}),f.jsx(Vn,{align:"end",className:"w-24 rounded-md border border-border bg-secondary p-1",children:b.map(E=>f.jsx(Hn,{onSelect:()=>{d(E)},className:"hover:bg-primary/10 cursor-pointer rounded px-2 py-1.5 text-sm text-primary",children:E},E))})]}),f.jsx("div",{}),f.jsx(Hy,{gradientType:g,setGradientType:E=>{v(E)}})]})]}),f.jsx(Vy,{angle:s,setAngle:E=>{a(E)}}),f.jsxs(Co,{children:[f.jsx(Eo,{className:"nofocus relative w-full",children:f.jsxs("div",{className:"flex items-center justify-between rounded-md border border-border bg-secondary p-2 text-sm text-primary",children:[f.jsx("span",{children:r}),f.jsx(Ht,{className:"h-4 w-4"})]})}),f.jsx(Vn,{align:"end",className:"w-48 rounded-md border border-border bg-secondary p-1",children:["tailwind","css","sass","bootstrap"].map(E=>f.jsx(Hn,{onSelect:()=>{o(E)},className:"hover:bg-primary/10 cursor-pointer rounded px-2 py-1.5 text-sm text-primary",children:E.charAt(0).toUpperCase()+E.slice(1)},E))})]}),f.jsx(Ky,{code:C(r),copiedStates:h,activeTab:r,copyToClipboard:(E,$)=>{m(E,$)}})]}),f.jsx(d0,{isOpen:c,onClose:()=>l(!1),gradientData:x})]})}function Ae({className:e,...t}){return f.jsx("div",{className:U("animate-pulse rounded-md bg-muted",e),...t})}const ad=i.forwardRef(({className:e,...t},n)=>f.jsx("div",{ref:n,className:U("rounded-lg bg-[var(--card-background)]",e),...t}));ad.displayName="Card";const p0=i.forwardRef(({className:e,...t},n)=>f.jsx("div",{ref:n,className:U("flex flex-row items-center justify-between space-y-1.5 p-6",e),...t}));p0.displayName="CardHeader";const m0=i.forwardRef(({className:e,...t},n)=>f.jsx("h3",{ref:n,className:U("text-base font-semibold leading-none tracking-tight text-[var(--headline)]",e),...t}));m0.displayName="CardTitle";const h0=i.forwardRef(({className:e,...t},n)=>f.jsx("p",{ref:n,className:U("text-sm text-[var(--paragraph)]",e),...t}));h0.displayName="CardDescription";const id=i.forwardRef(({className:e,...t},n)=>f.jsx("div",{ref:n,className:U("p-6 pt-0",e),...t}));id.displayName="CardContent";const cd=i.forwardRef(({className:e,...t},n)=>f.jsx("div",{ref:n,className:U("flex items-start gap-2 p-6 pt-0",e),...t}));cd.displayName="CardFooter";function ke(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function _s(e,t=[]){let n=[];function r(s,a){const c=i.createContext(a),l=n.length;n=[...n,a];function u(p){const{scope:g,children:v,...h}=p,m=(g==null?void 0:g[e][l])||c,b=i.useMemo(()=>h,Object.values(h));return f.jsx(m.Provider,{value:b,children:v})}function d(p,g){const v=(g==null?void 0:g[e][l])||c,h=i.useContext(v);if(h)return h;if(a!==void 0)return a;throw new Error(`\`${p}\` must be used within \`${s}\``)}return u.displayName=s+"Provider",[u,d]}const o=()=>{const s=n.map(a=>i.createContext(a));return function(c){const l=(c==null?void 0:c[e])||s;return i.useMemo(()=>({[`__scope${e}`]:{...c,[e]:l}}),[c,l])}};return o.scopeName=e,[r,g0(o,...t)]}function g0(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const a=r.reduce((c,{useScope:l,scopeName:u})=>{const p=l(s)[`__scope${u}`];return{...c,...p}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return n.scopeName=t.scopeName,n}function v0(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function ld(...e){return t=>e.forEach(n=>v0(n,t))}function qn(...e){return i.useCallback(ld(...e),e)}var Yn=i.forwardRef((e,t)=>{const{children:n,...r}=e,o=i.Children.toArray(n),s=o.find(x0);if(s){const a=s.props.children,c=o.map(l=>l===s?i.Children.count(a)>1?i.Children.only(null):i.isValidElement(a)?a.props.children:null:l);return f.jsx(_o,{...r,ref:t,children:i.isValidElement(a)?i.cloneElement(a,void 0,c):null})}return f.jsx(_o,{...r,ref:t,children:n})});Yn.displayName="Slot";var _o=i.forwardRef((e,t)=>{const{children:n,...r}=e;if(i.isValidElement(n)){const o=w0(n);return i.cloneElement(n,{...y0(r,n.props),ref:t?ld(t,o):o})}return i.Children.count(n)>1?i.Children.only(null):null});_o.displayName="SlotClone";var b0=({children:e})=>f.jsx(f.Fragment,{children:e});function x0(e){return i.isValidElement(e)&&e.type===b0}function y0(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...c)=>{s(...c),o(...c)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function w0(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function C0(e){const t=e+"CollectionProvider",[n,r]=_s(t),[o,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=v=>{const{scope:h,children:m}=v,b=V.useRef(null),x=V.useRef(new Map).current;return f.jsx(o,{scope:h,itemMap:x,collectionRef:b,children:m})};a.displayName=t;const c=e+"CollectionSlot",l=V.forwardRef((v,h)=>{const{scope:m,children:b}=v,x=s(c,m),y=qn(h,x.collectionRef);return f.jsx(Yn,{ref:y,children:b})});l.displayName=c;const u=e+"CollectionItemSlot",d="data-radix-collection-item",p=V.forwardRef((v,h)=>{const{scope:m,children:b,...x}=v,y=V.useRef(null),w=qn(h,y),C=s(u,m);return V.useEffect(()=>(C.itemMap.set(y,{ref:y,...x}),()=>void C.itemMap.delete(y))),f.jsx(Yn,{[d]:"",ref:w,children:b})});p.displayName=u;function g(v){const h=s(e+"CollectionConsumer",v);return V.useCallback(()=>{const b=h.collectionRef.current;if(!b)return[];const x=Array.from(b.querySelectorAll(`[${d}]`));return Array.from(h.itemMap.values()).sort((C,S)=>x.indexOf(C.ref.current)-x.indexOf(S.ref.current))},[h.collectionRef,h.itemMap])}return[{Provider:a,Slot:l,ItemSlot:p},g,r]}var Mo=globalThis!=null&&globalThis.document?i.useLayoutEffect:()=>{},E0=Jt.useId||(()=>{}),S0=0;function ud(e){const[t,n]=i.useState(E0());return Mo(()=>{e||n(r=>r??String(S0++))},[e]),e||(t?`radix-${t}`:"")}var $0=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],kt=$0.reduce((e,t)=>{const n=i.forwardRef((r,o)=>{const{asChild:s,...a}=r,c=s?Yn:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),f.jsx(c,{...a,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function Ms(e){const t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function dd({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=R0({defaultProp:t,onChange:n}),s=e!==void 0,a=s?e:r,c=Ms(n),l=i.useCallback(u=>{if(s){const p=typeof u=="function"?u(e):u;p!==e&&c(p)}else o(u)},[s,e,o,c]);return[a,l]}function R0({defaultProp:e,onChange:t}){const n=i.useState(e),[r]=n,o=i.useRef(r),s=Ms(t);return i.useEffect(()=>{o.current!==r&&(s(r),o.current=r)},[r,o,s]),n}var P0=i.createContext(void 0);function fd(e){const t=i.useContext(P0);return e||t||"ltr"}var Wr="rovingFocusGroup.onEntryFocus",N0={bubbles:!1,cancelable:!0},wr="RovingFocusGroup",[jo,pd,T0]=C0(wr),[A0,md]=_s(wr,[T0]),[O0,_0]=A0(wr),hd=i.forwardRef((e,t)=>f.jsx(jo.Provider,{scope:e.__scopeRovingFocusGroup,children:f.jsx(jo.Slot,{scope:e.__scopeRovingFocusGroup,children:f.jsx(M0,{...e,ref:t})})}));hd.displayName=wr;var M0=i.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:s,currentTabStopId:a,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:l,onEntryFocus:u,preventScrollOnEntryFocus:d=!1,...p}=e,g=i.useRef(null),v=qn(t,g),h=fd(s),[m=null,b]=dd({prop:a,defaultProp:c,onChange:l}),[x,y]=i.useState(!1),w=Ms(u),C=pd(n),S=i.useRef(!1),[P,E]=i.useState(0);return i.useEffect(()=>{const $=g.current;if($)return $.addEventListener(Wr,w),()=>$.removeEventListener(Wr,w)},[w]),f.jsx(O0,{scope:n,orientation:r,dir:h,loop:o,currentTabStopId:m,onItemFocus:i.useCallback($=>b($),[b]),onItemShiftTab:i.useCallback(()=>y(!0),[]),onFocusableItemAdd:i.useCallback(()=>E($=>$+1),[]),onFocusableItemRemove:i.useCallback(()=>E($=>$-1),[]),children:f.jsx(kt.div,{tabIndex:x||P===0?-1:0,"data-orientation":r,...p,ref:v,style:{outline:"none",...e.style},onMouseDown:ke(e.onMouseDown,()=>{S.current=!0}),onFocus:ke(e.onFocus,$=>{const N=!S.current;if($.target===$.currentTarget&&N&&!x){const A=new CustomEvent(Wr,N0);if($.currentTarget.dispatchEvent(A),!A.defaultPrevented){const O=C().filter(k=>k.focusable),_=O.find(k=>k.active),L=O.find(k=>k.id===m),M=[_,L,...O].filter(Boolean).map(k=>k.ref.current);bd(M,d)}}S.current=!1}),onBlur:ke(e.onBlur,()=>y(!1))})})}),gd="RovingFocusGroupItem",vd=i.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:s,...a}=e,c=ud(),l=s||c,u=_0(gd,n),d=u.currentTabStopId===l,p=pd(n),{onFocusableItemAdd:g,onFocusableItemRemove:v}=u;return i.useEffect(()=>{if(r)return g(),()=>v()},[r,g,v]),f.jsx(jo.ItemSlot,{scope:n,id:l,focusable:r,active:o,children:f.jsx(kt.span,{tabIndex:d?0:-1,"data-orientation":u.orientation,...a,ref:t,onMouseDown:ke(e.onMouseDown,h=>{r?u.onItemFocus(l):h.preventDefault()}),onFocus:ke(e.onFocus,()=>u.onItemFocus(l)),onKeyDown:ke(e.onKeyDown,h=>{if(h.key==="Tab"&&h.shiftKey){u.onItemShiftTab();return}if(h.target!==h.currentTarget)return;const m=D0(h,u.orientation,u.dir);if(m!==void 0){if(h.metaKey||h.ctrlKey||h.altKey||h.shiftKey)return;h.preventDefault();let x=p().filter(y=>y.focusable).map(y=>y.ref.current);if(m==="last")x.reverse();else if(m==="prev"||m==="next"){m==="prev"&&x.reverse();const y=x.indexOf(h.currentTarget);x=u.loop?k0(x,y+1):x.slice(y+1)}setTimeout(()=>bd(x))}})})})});vd.displayName=gd;var j0={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function I0(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function D0(e,t,n){const r=I0(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return j0[r]}function bd(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function k0(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var L0=hd,F0=vd;function B0(e,t){return i.useReducer((n,r)=>t[n][r]??n,e)}var xd=e=>{const{present:t,children:n}=e,r=U0(t),o=typeof n=="function"?n({present:r.isPresent}):i.Children.only(n),s=qn(r.ref,W0(o));return typeof n=="function"||r.isPresent?i.cloneElement(o,{ref:s}):null};xd.displayName="Presence";function U0(e){const[t,n]=i.useState(),r=i.useRef({}),o=i.useRef(e),s=i.useRef("none"),a=e?"mounted":"unmounted",[c,l]=B0(a,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return i.useEffect(()=>{const u=wn(r.current);s.current=c==="mounted"?u:"none"},[c]),Mo(()=>{const u=r.current,d=o.current;if(d!==e){const g=s.current,v=wn(u);e?l("MOUNT"):v==="none"||(u==null?void 0:u.display)==="none"?l("UNMOUNT"):l(d&&g!==v?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),Mo(()=>{if(t){const u=p=>{const v=wn(r.current).includes(p.animationName);p.target===t&&v&&Me.flushSync(()=>l("ANIMATION_END"))},d=p=>{p.target===t&&(s.current=wn(r.current))};return t.addEventListener("animationstart",d),t.addEventListener("animationcancel",u),t.addEventListener("animationend",u),()=>{t.removeEventListener("animationstart",d),t.removeEventListener("animationcancel",u),t.removeEventListener("animationend",u)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:i.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function wn(e){return(e==null?void 0:e.animationName)||"none"}function W0(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var js="Tabs",[V0,hC]=_s(js,[md]),yd=md(),[H0,Is]=V0(js),wd=i.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,onValueChange:o,defaultValue:s,orientation:a="horizontal",dir:c,activationMode:l="automatic",...u}=e,d=fd(c),[p,g]=dd({prop:r,onChange:o,defaultProp:s});return f.jsx(H0,{scope:n,baseId:ud(),value:p,onValueChange:g,orientation:a,dir:d,activationMode:l,children:f.jsx(kt.div,{dir:d,"data-orientation":a,...u,ref:t})})});wd.displayName=js;var Cd="TabsList",Ed=i.forwardRef((e,t)=>{const{__scopeTabs:n,loop:r=!0,...o}=e,s=Is(Cd,n),a=yd(n);return f.jsx(L0,{asChild:!0,...a,orientation:s.orientation,dir:s.dir,loop:r,children:f.jsx(kt.div,{role:"tablist","aria-orientation":s.orientation,...o,ref:t})})});Ed.displayName=Cd;var Sd="TabsTrigger",$d=i.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,disabled:o=!1,...s}=e,a=Is(Sd,n),c=yd(n),l=Nd(a.baseId,r),u=Td(a.baseId,r),d=r===a.value;return f.jsx(F0,{asChild:!0,...c,focusable:!o,active:d,children:f.jsx(kt.button,{type:"button",role:"tab","aria-selected":d,"aria-controls":u,"data-state":d?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:l,...s,ref:t,onMouseDown:ke(e.onMouseDown,p=>{!o&&p.button===0&&p.ctrlKey===!1?a.onValueChange(r):p.preventDefault()}),onKeyDown:ke(e.onKeyDown,p=>{[" ","Enter"].includes(p.key)&&a.onValueChange(r)}),onFocus:ke(e.onFocus,()=>{const p=a.activationMode!=="manual";!d&&!o&&p&&a.onValueChange(r)})})})});$d.displayName=Sd;var Rd="TabsContent",Pd=i.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,forceMount:o,children:s,...a}=e,c=Is(Rd,n),l=Nd(c.baseId,r),u=Td(c.baseId,r),d=r===c.value,p=i.useRef(d);return i.useEffect(()=>{const g=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(g)},[]),f.jsx(xd,{present:o||d,children:({present:g})=>f.jsx(kt.div,{"data-state":d?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":l,hidden:!g,id:u,tabIndex:0,...a,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:g&&s})})});Pd.displayName=Rd;function Nd(e,t){return`${e}-trigger-${t}`}function Td(e,t){return`${e}-content-${t}`}var z0=wd,Ad=Ed,Od=$d,_d=Pd;const G0=z0,Md=i.forwardRef(({className:e,...t},n)=>f.jsx(Ad,{ref:n,className:U("inline-flex h-10 items-center justify-center rounded-md p-1 text-[var(--paragraph)]",e),...t}));Md.displayName=Ad.displayName;const Io=i.forwardRef(({className:e,...t},n)=>f.jsx(Od,{ref:n,className:U("px- inline-flex items-center justify-center whitespace-nowrap rounded-sm p-2 py-1.5 text-sm font-medium text-[var(--paragraph)] ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-[var(--card-background)] data-[state=active]:text-[var(--headline)] data-[state=active]:shadow-sm",e),...t}));Io.displayName=Od.displayName;const Do=i.forwardRef(({className:e,...t},n)=>f.jsx(_d,{ref:n,className:U("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));Do.displayName=_d.displayName;function K0(){return f.jsxs(ad,{className:"overflow-hidden shadow-md",children:[f.jsx(id,{className:"p-0",children:f.jsx(Ae,{className:"h-48 w-full"})}),f.jsxs(cd,{className:"flex flex-col items-start space-y-4 p-4",children:[f.jsxs("div",{className:"flex w-full items-center justify-between",children:[f.jsx(Ae,{className:"h-6 w-32"}),f.jsx(Ae,{className:"h-8 w-8 rounded-full"})]}),f.jsx("div",{className:"flex w-full flex-wrap gap-2",children:[...Array(5)].map((e,t)=>f.jsx(Ae,{className:"h-6 w-6 rounded-full"},t))}),f.jsx(Ae,{className:"h-8 w-full"}),f.jsxs(G0,{defaultValue:"tailwind",className:"w-full",children:[f.jsxs(Md,{className:"grid w-full grid-cols-2",children:[f.jsx(Io,{value:"tailwind",children:f.jsx(Ae,{className:"h-4 w-16"})}),f.jsx(Io,{value:"css",children:f.jsx(Ae,{className:"h-4 w-8"})})]}),f.jsx(Do,{value:"tailwind",className:"mt-2",children:f.jsx(Ae,{className:"h-8 w-full"})}),f.jsx(Do,{value:"css",className:"mt-2",children:f.jsx(Ae,{className:"h-8 w-full"})})]})]})]})}function q0({gradients:e,favorites:t,toggleFavorite:n,isLoading:r}){return f.jsx("div",{className:"grid grid-cols-1 gap-6 pt-6 sm:grid-cols-2 lg:grid-cols-3",children:r?Array.from({length:6}).map((o,s)=>f.jsx(pe.div,{layout:!0,initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},transition:{duration:.3},children:f.jsx(K0,{})},`skeleton-${s}`)):e.length===0?f.jsxs(pe.div,{layout:!0,initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},transition:{duration:.3},className:"col-span-full flex items-center justify-center gap-1 pt-16 text-center text-muted-foreground",children:[f.jsx(af,{}),f.jsx("span",{children:"No gradients found"})]},"no-gradients"):e.map(o=>f.jsx("div",{children:f.jsx(f0,{gradient:o,isFavorite:t.includes(o.name),onFavoriteToggle:()=>n(o.name)})},o.name))})}function Y0({currentPage:e,totalPages:t,onPageChange:n}){return f.jsxs(pe.div,{className:"flex items-center justify-center gap-4 pt-16 max-md:py-4",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5,delay:.3},children:[f.jsx(Tn,{className:"h-max w-max rounded-md border-border bg-card p-2 text-primary",onClick:()=>n("prev"),disabled:e===1,children:f.jsx(ef,{className:"h-4 w-4 text-primary"})}),f.jsxs(pe.div,{className:"text-sm font-semibold text-muted-foreground",initial:{opacity:0},animate:{opacity:1},transition:{duration:.3,delay:.1},children:[e," / ",t]}),f.jsx(Tn,{className:"h-max w-max rounded-md border-border bg-card p-2 text-primary",onClick:()=>n("next"),disabled:e===t,children:f.jsx(Ja,{className:"h-4 w-4"})})]})}const X0="https://alshaer.vercel.app/";function J0(){return f.jsxs("footer",{className:`container mx-auto flex h-16 items-center justify-between gap-2 px-4 py-4
      max-md:flex-col max-md:h-auto max-md:gap-2 max-md:text-center max-md:items-center max-md:justify-center`,children:[f.jsxs("p",{className:"flex items-center text-sm text-primary",children:["Developed by"," ",f.jsxs("a",{target:"_blank",rel:"noopener noreferrer",className:"flex items-center ps-1 font-medium text-[var(--link)] hover:text-[var(--link-hover)]",href:X0,children:["Baraa",f.jsx(of,{className:"ms-1 h-3 w-3"})]})]}),f.jsxs("p",{className:"text-xs text-muted-foreground",children:["© ",new Date().getFullYear()," GradientsCSS. All rights reserved."]})]})}const Z0=({children:e,className:t,shimmerWidth:n=100})=>f.jsx("p",{style:{"--shiny-width":`${n}px`},className:U("mx-auto max-w-md text-neutral-600/70 dark:text-neutral-400/70","animate-shiny-text bg-clip-text bg-no-repeat [background-position:0_0] [background-size:var(--shiny-width)_100%] [transition:background-position_1s_cubic-bezier(.6,.6,0,1)_infinite]","bg-gradient-to-r from-transparent via-black/80 via-50% to-transparent dark:via-white/80",t),children:e});function Q0({value:e,direction:t="up",delay:n=0,className:r,decimalPlaces:o=0}){const s=i.useRef(null),a=Vt(t==="down"?e:0),c=qd(a,{damping:60,stiffness:100}),l=Zd(s,{once:!0,margin:"0px"});return i.useEffect(()=>{l&&setTimeout(()=>{a.set(t==="down"?0:e)},n*1e3)},[a,l,n,e,t]),i.useEffect(()=>c.on("change",u=>{s.current&&(s.current.textContent=Intl.NumberFormat("en-US",{minimumFractionDigits:o,maximumFractionDigits:o}).format(Number(u.toFixed(o))))}),[c,o]),f.jsx("span",{className:U("inline-block ps-1 tabular-nums tracking-wider text-[var(--headline)] dark:text-white",r),ref:s})}const eC=()=>{const e=["https://bundui-images.netlify.app/avatars/01.png","https://bundui-images.netlify.app/avatars/03.png","https://bundui-images.netlify.app/avatars/05.png","https://bundui-images.netlify.app/avatars/06.png"],t=()=>f.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"orange",stroke:"orange",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round",className:"lucide lucide-star h-5 w-5",children:f.jsx("polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"})}),n=({src:r})=>{const[o,s]=i.useState(!0);return f.jsxs("span",{className:"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",children:[o&&f.jsx(Ae,{className:"h-10 w-10"}),f.jsx("img",{alt:"shadcn ui kit avatar",src:r,onLoad:()=>s(!1),className:o?"hidden":"block"})]})};return f.jsx("div",{children:f.jsxs("div",{className:"mt-3 flex flex-col gap-4 lg:flex-row",children:[f.jsx("div",{className:"flex justify-center -space-x-4",children:e.map((r,o)=>f.jsx(n,{src:r},o))}),f.jsxs("div",{className:"flex flex-col",children:[f.jsx("span",{className:"mt-1 flex justify-center gap-1 lg:justify-start",children:Array.from({length:5}).map((r,o)=>f.jsx(t,{},o))}),f.jsxs("span",{className:"text-sm text-muted-foreground",children:["More than",f.jsx(Q0,{value:50}),"+ happy users"]})]})]})})};function tC(){const[e,t]=i.useState([]),[n,r]=i.useState(!0),[o,s]=i.useState(null);return i.useEffect(()=>{let a=!1;return(async()=>{r(!0),s(null);try{const l=await ne.get("https://gist.githubusercontent.com/balshaer/69d1f26f366d2dcf2d58d6d644f0aff4/raw/6350bd8c935e9d9f937ec95cd250f819bfc57afc/data.json");a||t(l.data)}catch(l){a||s(l instanceof Error?l.message:"Failed to fetch gradients")}finally{a||r(!1)}})(),()=>{a=!0}},[]),{gradients:e,isLoading:n,error:o}}function nC(){const[e,t]=i.useState([]);return i.useEffect(()=>{const r=localStorage.getItem("gradientFavorites");r&&t(JSON.parse(r))},[]),i.useEffect(()=>{localStorage.setItem("gradientFavorites",JSON.stringify(e))},[e]),{favorites:e,toggleFavorite:r=>{t(o=>o.includes(r)?o.filter(s=>s!==r):[...o,r])}}}function rC(e,t){const[n,r]=i.useState(e);return i.useEffect(()=>{const o=setTimeout(()=>r(e),t);return()=>clearTimeout(o)},[e,t]),n}const gC=()=>{const[e,t]=i.useState(""),n=rC(e,250),[r,o]=i.useState(1),[s,a]=i.useState("all"),[c,l]=i.useState(""),u=[],d=9,{gradients:p,isLoading:g,error:v}=tC(),{favorites:h,toggleFavorite:m}=nC(),b=i.useMemo(()=>p.length?p.filter(C=>{const S=!n||C.name.toLowerCase().includes(n.toLowerCase())||C.colorsname.some($=>$.toLowerCase().includes(n.toLowerCase()))||C.keywords.some($=>$.some(N=>N.toLowerCase().includes(n.toLowerCase()))),P=s==="all"||s==="favorites"&&h.includes(C.name),E=u.length===0||u.some($=>C.colorsname.some(N=>N.toLowerCase().includes($.toLowerCase())));return S&&P&&E}):[],[n,p,h,s,u]),x=Math.ceil(b.length/d),y=i.useMemo(()=>{const C=(r-1)*d;return b.slice(C,C+d)},[r,b,d]),w=C=>{o(S=>C==="next"?S+1:S-1)};return f.jsx(Sv,{children:f.jsxs("div",{className:"container relative",style:{background:c},children:[f.jsxs("div",{className:"mx-auto max-w-6xl space-y-2 pt-12",children:[f.jsxs("header",{className:"relative mx-auto max-w-6xl space-y-2 pt-12",children:[f.jsx("a",{href:"https://github.com/balshaer/gradients-css",target:"_blank",rel:"noopener noreferrer",children:f.jsx("div",{className:"flex w-full items-center justify-center",children:f.jsx("div",{className:U("group rounded-full border border-border bg-card text-base text-[var(--muted)] transition-all ease-in hover:cursor-pointer dark:border-white/5 dark:bg-neutral-900 dark:hover:bg-neutral-800"),children:f.jsxs(Z0,{className:"inline-flex items-center justify-center px-4 py-1 text-primary transition ease-out hover:duration-300 max-md:text-xs",children:[f.jsx("span",{children:"✨ Contribute to The Project"}),f.jsx(Qd,{className:"ml-1 size-3 transition-transform duration-300 ease-in-out group-hover:translate-x-0.5"})]})})})}),f.jsxs("h1",{className:"pt-6 text-center text-3xl font-medium text-primary dark:text-gray-50 sm:text-6xl",children:["Collection of modern,",f.jsxs("span",{className:"relative ps-1",children:["Gradients",f.jsx("img",{className:"absolute bottom-[-10px] left-0 right-0",src:"https://uploads-ssl.webflow.com/618ce467f09b34ebf2fdf6be/62779adeac94b82ea2fe08ec_Underline%202.svg",alt:""})]})]}),f.jsx("p",{className:"m-auto mt-[-120px] max-w-2xl py-0 pb-0 pt-3 text-center text-lg leading-6 text-muted-foreground dark:text-gray-200",children:"Ready-to-use gradients with powerful export options. Export as SVG, PNG, CSS, iOS Swift, Android XML, and more for any platform or design tool."}),f.jsxs("div",{className:"flex flex-wrap items-center justify-center gap-2 pt-4 pb-2",children:[f.jsxs("div",{className:"flex items-center gap-1 rounded-full bg-primary/10 px-3 py-1 text-xs font-medium text-primary",children:[f.jsx("span",{children:"📱"})," iOS & Android"]}),f.jsxs("div",{className:"flex items-center gap-1 rounded-full bg-primary/10 px-3 py-1 text-xs font-medium text-primary",children:[f.jsx("span",{children:"🎨"})," Figma & Sketch"]}),f.jsxs("div",{className:"flex items-center gap-1 rounded-full bg-primary/10 px-3 py-1 text-xs font-medium text-primary",children:[f.jsx("span",{children:"💻"})," CSS & SCSS"]}),f.jsxs("div",{className:"flex items-center gap-1 rounded-full bg-primary/10 px-3 py-1 text-xs font-medium text-primary",children:[f.jsx("span",{children:"🖼️"})," PNG & SVG"]}),f.jsxs("div",{className:"flex items-center gap-1 rounded-full bg-primary/10 px-3 py-1 text-xs font-medium text-primary",children:[f.jsx("span",{children:"🌐"})," JSON Export"]})]}),f.jsx("div",{className:"flex w-full items-center justify-center pb-6",children:f.jsx(eC,{})})]}),f.jsxs("div",{className:"flex flex-col gap-4",children:[f.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row",children:[f.jsxs("div",{className:"relative w-full",id:"input",children:[f.jsx(Ci,{placeholder:"Search by color name or keyword...",className:"hover:border-brand-500-secondary invalid:border-error-500 invalid:focus:border-error-500 text-placeholder peer block h-full w-full appearance-none overflow-hidden overflow-ellipsis text-nowrap rounded-md border border-border bg-input px-3 py-2 pr-[48px] text-sm outline-none focus:border-none focus:shadow-none focus:outline-none",id:"floating_outlined",type:"text",value:e,onChange:C=>{t(C.target.value),o(1)}}),f.jsx(uf,{className:"absolute bottom-0 right-2 top-0 m-auto h-5 w-5 text-primary"})]}),f.jsxs(io,{value:s,onValueChange:C=>{a(C),o(1)},children:[f.jsx(Fn,{className:"nofocus nohover w-full border-none outline-none sm:w-[180px]",children:f.jsx(co,{placeholder:"Filter"})}),f.jsxs(Bn,{className:"nofocus nohover border-none outline-none",children:[f.jsx(St,{value:"all",children:"All Gradients"}),f.jsx(St,{value:"favorites",children:"Favorites"})]})]})]}),(u.length>0||e||s!=="all")&&f.jsxs("div",{className:"text-sm text-muted-foreground",children:["Showing ",b.length," gradient",b.length!==1?"s":"",u.length>0&&f.jsxs("span",{children:[" with colors: ",u.join(", ")]})]})]}),v&&f.jsxs("div",{className:"flex flex-col items-center justify-center py-12 text-center",children:[f.jsxs("p",{className:"text-red-500 mb-4",children:["Error loading gradients: ",v]}),f.jsx("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90",children:"Retry"})]}),!v&&f.jsx(q0,{gradients:y,favorites:h,toggleFavorite:m,setBackground:l,isLoading:g}),f.jsx(Y0,{currentPage:r,totalPages:x,onPageChange:w})]}),f.jsx(J0,{})]})})};export{gC as default};
