function ly(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const i=Object.getOwnPropertyDescriptor(r,o);i&&Object.defineProperty(e,o,i.get?i:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();var uy=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Kl(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var gp={exports:{}},as={},yp={exports:{}},B={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ro=Symbol.for("react.element"),cy=Symbol.for("react.portal"),dy=Symbol.for("react.fragment"),fy=Symbol.for("react.strict_mode"),py=Symbol.for("react.profiler"),hy=Symbol.for("react.provider"),my=Symbol.for("react.context"),vy=Symbol.for("react.forward_ref"),gy=Symbol.for("react.suspense"),yy=Symbol.for("react.memo"),wy=Symbol.for("react.lazy"),Rc=Symbol.iterator;function xy(e){return e===null||typeof e!="object"?null:(e=Rc&&e[Rc]||e["@@iterator"],typeof e=="function"?e:null)}var wp={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},xp=Object.assign,Sp={};function Sr(e,t,n){this.props=e,this.context=t,this.refs=Sp,this.updater=n||wp}Sr.prototype.isReactComponent={};Sr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Sr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Ep(){}Ep.prototype=Sr.prototype;function Gl(e,t,n){this.props=e,this.context=t,this.refs=Sp,this.updater=n||wp}var Ql=Gl.prototype=new Ep;Ql.constructor=Gl;xp(Ql,Sr.prototype);Ql.isPureReactComponent=!0;var Ac=Array.isArray,Tp=Object.prototype.hasOwnProperty,Yl={current:null},Cp={key:!0,ref:!0,__self:!0,__source:!0};function Pp(e,t,n){var r,o={},i=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(i=""+t.key),t)Tp.call(t,r)&&!Cp.hasOwnProperty(r)&&(o[r]=t[r]);var a=arguments.length-2;if(a===1)o.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];o.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)o[r]===void 0&&(o[r]=a[r]);return{$$typeof:Ro,type:e,key:i,ref:s,props:o,_owner:Yl.current}}function Sy(e,t){return{$$typeof:Ro,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Xl(e){return typeof e=="object"&&e!==null&&e.$$typeof===Ro}function Ey(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Mc=/\/+/g;function Os(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Ey(""+e.key):t.toString(36)}function li(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case Ro:case cy:s=!0}}if(s)return s=e,o=o(s),e=r===""?"."+Os(s,0):r,Ac(o)?(n="",e!=null&&(n=e.replace(Mc,"$&/")+"/"),li(o,t,n,"",function(u){return u})):o!=null&&(Xl(o)&&(o=Sy(o,n+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(Mc,"$&/")+"/")+e)),t.push(o)),1;if(s=0,r=r===""?".":r+":",Ac(e))for(var a=0;a<e.length;a++){i=e[a];var l=r+Os(i,a);s+=li(i,t,n,l,o)}else if(l=xy(e),typeof l=="function")for(e=l.call(e),a=0;!(i=e.next()).done;)i=i.value,l=r+Os(i,a++),s+=li(i,t,n,l,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function Io(e,t,n){if(e==null)return e;var r=[],o=0;return li(e,r,"","",function(i){return t.call(n,i,o++)}),r}function Ty(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ne={current:null},ui={transition:null},Cy={ReactCurrentDispatcher:Ne,ReactCurrentBatchConfig:ui,ReactCurrentOwner:Yl};B.Children={map:Io,forEach:function(e,t,n){Io(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Io(e,function(){t++}),t},toArray:function(e){return Io(e,function(t){return t})||[]},only:function(e){if(!Xl(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};B.Component=Sr;B.Fragment=dy;B.Profiler=py;B.PureComponent=Gl;B.StrictMode=fy;B.Suspense=gy;B.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Cy;B.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=xp({},e.props),o=e.key,i=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,s=Yl.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)Tp.call(t,l)&&!Cp.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:Ro,type:e.type,key:o,ref:i,props:r,_owner:s}};B.createContext=function(e){return e={$$typeof:my,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:hy,_context:e},e.Consumer=e};B.createElement=Pp;B.createFactory=function(e){var t=Pp.bind(null,e);return t.type=e,t};B.createRef=function(){return{current:null}};B.forwardRef=function(e){return{$$typeof:vy,render:e}};B.isValidElement=Xl;B.lazy=function(e){return{$$typeof:wy,_payload:{_status:-1,_result:e},_init:Ty}};B.memo=function(e,t){return{$$typeof:yy,type:e,compare:t===void 0?null:t}};B.startTransition=function(e){var t=ui.transition;ui.transition={};try{e()}finally{ui.transition=t}};B.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")};B.useCallback=function(e,t){return Ne.current.useCallback(e,t)};B.useContext=function(e){return Ne.current.useContext(e)};B.useDebugValue=function(){};B.useDeferredValue=function(e){return Ne.current.useDeferredValue(e)};B.useEffect=function(e,t){return Ne.current.useEffect(e,t)};B.useId=function(){return Ne.current.useId()};B.useImperativeHandle=function(e,t,n){return Ne.current.useImperativeHandle(e,t,n)};B.useInsertionEffect=function(e,t){return Ne.current.useInsertionEffect(e,t)};B.useLayoutEffect=function(e,t){return Ne.current.useLayoutEffect(e,t)};B.useMemo=function(e,t){return Ne.current.useMemo(e,t)};B.useReducer=function(e,t,n){return Ne.current.useReducer(e,t,n)};B.useRef=function(e){return Ne.current.useRef(e)};B.useState=function(e){return Ne.current.useState(e)};B.useSyncExternalStore=function(e,t,n){return Ne.current.useSyncExternalStore(e,t,n)};B.useTransition=function(){return Ne.current.useTransition()};B.version="18.2.0";yp.exports=B;var g=yp.exports;const vt=Kl(g),Py=ly({__proto__:null,default:vt},[g]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ky=g,by=Symbol.for("react.element"),Ry=Symbol.for("react.fragment"),Ay=Object.prototype.hasOwnProperty,My=ky.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,_y={key:!0,ref:!0,__self:!0,__source:!0};function kp(e,t,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)Ay.call(t,r)&&!_y.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:by,type:e,key:i,ref:s,props:o,_owner:My.current}}as.Fragment=Ry;as.jsx=kp;as.jsxs=kp;gp.exports=as;var T=gp.exports,bp={exports:{}},Xe={},Rp={exports:{}},Ap={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(A,j){var O=A.length;A.push(j);e:for(;0<O;){var $=O-1>>>1,W=A[$];if(0<o(W,j))A[$]=j,A[O]=W,O=$;else break e}}function n(A){return A.length===0?null:A[0]}function r(A){if(A.length===0)return null;var j=A[0],O=A.pop();if(O!==j){A[0]=O;e:for(var $=0,W=A.length,fe=W>>>1;$<fe;){var te=2*($+1)-1,Le=A[te],ge=te+1,ae=A[ge];if(0>o(Le,O))ge<W&&0>o(ae,Le)?(A[$]=ae,A[ge]=O,$=ge):(A[$]=Le,A[te]=O,$=te);else if(ge<W&&0>o(ae,O))A[$]=ae,A[ge]=O,$=ge;else break e}}return j}function o(A,j){var O=A.sortIndex-j.sortIndex;return O!==0?O:A.id-j.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var s=Date,a=s.now();e.unstable_now=function(){return s.now()-a}}var l=[],u=[],c=1,d=null,f=3,m=!1,w=!1,y=!1,x=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function v(A){for(var j=n(u);j!==null;){if(j.callback===null)r(u);else if(j.startTime<=A)r(u),j.sortIndex=j.expirationTime,t(l,j);else break;j=n(u)}}function S(A){if(y=!1,v(A),!w)if(n(l)!==null)w=!0,K(E);else{var j=n(u);j!==null&&z(S,j.startTime-A)}}function E(A,j){w=!1,y&&(y=!1,h(C),C=-1),m=!0;var O=f;try{for(v(j),d=n(l);d!==null&&(!(d.expirationTime>j)||A&&!_());){var $=d.callback;if(typeof $=="function"){d.callback=null,f=d.priorityLevel;var W=$(d.expirationTime<=j);j=e.unstable_now(),typeof W=="function"?d.callback=W:d===n(l)&&r(l),v(j)}else r(l);d=n(l)}if(d!==null)var fe=!0;else{var te=n(u);te!==null&&z(S,te.startTime-j),fe=!1}return fe}finally{d=null,f=O,m=!1}}var P=!1,k=null,C=-1,L=5,N=-1;function _(){return!(e.unstable_now()-N<L)}function b(){if(k!==null){var A=e.unstable_now();N=A;var j=!0;try{j=k(!0,A)}finally{j?I():(P=!1,k=null)}}else P=!1}var I;if(typeof p=="function")I=function(){p(b)};else if(typeof MessageChannel<"u"){var M=new MessageChannel,U=M.port2;M.port1.onmessage=b,I=function(){U.postMessage(null)}}else I=function(){x(b,0)};function K(A){k=A,P||(P=!0,I())}function z(A,j){C=x(function(){A(e.unstable_now())},j)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(A){A.callback=null},e.unstable_continueExecution=function(){w||m||(w=!0,K(E))},e.unstable_forceFrameRate=function(A){0>A||125<A?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):L=0<A?Math.floor(1e3/A):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(A){switch(f){case 1:case 2:case 3:var j=3;break;default:j=f}var O=f;f=j;try{return A()}finally{f=O}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(A,j){switch(A){case 1:case 2:case 3:case 4:case 5:break;default:A=3}var O=f;f=A;try{return j()}finally{f=O}},e.unstable_scheduleCallback=function(A,j,O){var $=e.unstable_now();switch(typeof O=="object"&&O!==null?(O=O.delay,O=typeof O=="number"&&0<O?$+O:$):O=$,A){case 1:var W=-1;break;case 2:W=250;break;case 5:W=**********;break;case 4:W=1e4;break;default:W=5e3}return W=O+W,A={id:c++,callback:j,priorityLevel:A,startTime:O,expirationTime:W,sortIndex:-1},O>$?(A.sortIndex=O,t(u,A),n(l)===null&&A===n(u)&&(y?(h(C),C=-1):y=!0,z(S,O-$))):(A.sortIndex=W,t(l,A),w||m||(w=!0,K(E))),A},e.unstable_shouldYield=_,e.unstable_wrapCallback=function(A){var j=f;return function(){var O=f;f=j;try{return A.apply(this,arguments)}finally{f=O}}}})(Ap);Rp.exports=Ap;var Ny=Rp.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Mp=g,Qe=Ny;function R(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var _p=new Set,io={};function Vn(e,t){dr(e,t),dr(e+"Capture",t)}function dr(e,t){for(io[e]=t,e=0;e<t.length;e++)_p.add(t[e])}var _t=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ka=Object.prototype.hasOwnProperty,Dy=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,_c={},Nc={};function Ly(e){return ka.call(Nc,e)?!0:ka.call(_c,e)?!1:Dy.test(e)?Nc[e]=!0:(_c[e]=!0,!1)}function jy(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Oy(e,t,n,r){if(t===null||typeof t>"u"||jy(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function De(e,t,n,r,o,i,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=s}var Te={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Te[e]=new De(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Te[t]=new De(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Te[e]=new De(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Te[e]=new De(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Te[e]=new De(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Te[e]=new De(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Te[e]=new De(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Te[e]=new De(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Te[e]=new De(e,5,!1,e.toLowerCase(),null,!1,!1)});var Zl=/[\-:]([a-z])/g;function ql(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Zl,ql);Te[t]=new De(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Zl,ql);Te[t]=new De(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Zl,ql);Te[t]=new De(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Te[e]=new De(e,1,!1,e.toLowerCase(),null,!1,!1)});Te.xlinkHref=new De("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Te[e]=new De(e,1,!1,e.toLowerCase(),null,!0,!0)});function Jl(e,t,n,r){var o=Te.hasOwnProperty(t)?Te[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Oy(t,n,o,r)&&(n=null),r||o===null?Ly(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Vt=Mp.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Fo=Symbol.for("react.element"),Bn=Symbol.for("react.portal"),Un=Symbol.for("react.fragment"),eu=Symbol.for("react.strict_mode"),ba=Symbol.for("react.profiler"),Np=Symbol.for("react.provider"),Dp=Symbol.for("react.context"),tu=Symbol.for("react.forward_ref"),Ra=Symbol.for("react.suspense"),Aa=Symbol.for("react.suspense_list"),nu=Symbol.for("react.memo"),Kt=Symbol.for("react.lazy"),Lp=Symbol.for("react.offscreen"),Dc=Symbol.iterator;function br(e){return e===null||typeof e!="object"?null:(e=Dc&&e[Dc]||e["@@iterator"],typeof e=="function"?e:null)}var se=Object.assign,Vs;function Ir(e){if(Vs===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Vs=t&&t[1]||""}return`
`+Vs+e}var Is=!1;function Fs(e,t){if(!e||Is)return"";Is=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),s=o.length-1,a=i.length-1;1<=s&&0<=a&&o[s]!==i[a];)a--;for(;1<=s&&0<=a;s--,a--)if(o[s]!==i[a]){if(s!==1||a!==1)do if(s--,a--,0>a||o[s]!==i[a]){var l=`
`+o[s].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=s&&0<=a);break}}}finally{Is=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Ir(e):""}function Vy(e){switch(e.tag){case 5:return Ir(e.type);case 16:return Ir("Lazy");case 13:return Ir("Suspense");case 19:return Ir("SuspenseList");case 0:case 2:case 15:return e=Fs(e.type,!1),e;case 11:return e=Fs(e.type.render,!1),e;case 1:return e=Fs(e.type,!0),e;default:return""}}function Ma(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Un:return"Fragment";case Bn:return"Portal";case ba:return"Profiler";case eu:return"StrictMode";case Ra:return"Suspense";case Aa:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Dp:return(e.displayName||"Context")+".Consumer";case Np:return(e._context.displayName||"Context")+".Provider";case tu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case nu:return t=e.displayName||null,t!==null?t:Ma(e.type)||"Memo";case Kt:t=e._payload,e=e._init;try{return Ma(e(t))}catch{}}return null}function Iy(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Ma(t);case 8:return t===eu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function ln(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function jp(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Fy(e){var t=jp(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(s){r=""+s,i.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function zo(e){e._valueTracker||(e._valueTracker=Fy(e))}function Op(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=jp(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Pi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function _a(e,t){var n=t.checked;return se({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Lc(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=ln(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Vp(e,t){t=t.checked,t!=null&&Jl(e,"checked",t,!1)}function Na(e,t){Vp(e,t);var n=ln(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Da(e,t.type,n):t.hasOwnProperty("defaultValue")&&Da(e,t.type,ln(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function jc(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Da(e,t,n){(t!=="number"||Pi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Fr=Array.isArray;function or(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+ln(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function La(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(R(91));return se({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Oc(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(R(92));if(Fr(n)){if(1<n.length)throw Error(R(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:ln(n)}}function Ip(e,t){var n=ln(t.value),r=ln(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Vc(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Fp(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ja(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Fp(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Bo,zp=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Bo=Bo||document.createElement("div"),Bo.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Bo.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function so(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Hr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},zy=["Webkit","ms","Moz","O"];Object.keys(Hr).forEach(function(e){zy.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Hr[t]=Hr[e]})});function Bp(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Hr.hasOwnProperty(e)&&Hr[e]?(""+t).trim():t+"px"}function Up(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Bp(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var By=se({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Oa(e,t){if(t){if(By[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(R(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(R(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(R(61))}if(t.style!=null&&typeof t.style!="object")throw Error(R(62))}}function Va(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ia=null;function ru(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Fa=null,ir=null,sr=null;function Ic(e){if(e=_o(e)){if(typeof Fa!="function")throw Error(R(280));var t=e.stateNode;t&&(t=fs(t),Fa(e.stateNode,e.type,t))}}function $p(e){ir?sr?sr.push(e):sr=[e]:ir=e}function Wp(){if(ir){var e=ir,t=sr;if(sr=ir=null,Ic(e),t)for(e=0;e<t.length;e++)Ic(t[e])}}function Hp(e,t){return e(t)}function Kp(){}var zs=!1;function Gp(e,t,n){if(zs)return e(t,n);zs=!0;try{return Hp(e,t,n)}finally{zs=!1,(ir!==null||sr!==null)&&(Kp(),Wp())}}function ao(e,t){var n=e.stateNode;if(n===null)return null;var r=fs(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(R(231,t,typeof n));return n}var za=!1;if(_t)try{var Rr={};Object.defineProperty(Rr,"passive",{get:function(){za=!0}}),window.addEventListener("test",Rr,Rr),window.removeEventListener("test",Rr,Rr)}catch{za=!1}function Uy(e,t,n,r,o,i,s,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Kr=!1,ki=null,bi=!1,Ba=null,$y={onError:function(e){Kr=!0,ki=e}};function Wy(e,t,n,r,o,i,s,a,l){Kr=!1,ki=null,Uy.apply($y,arguments)}function Hy(e,t,n,r,o,i,s,a,l){if(Wy.apply(this,arguments),Kr){if(Kr){var u=ki;Kr=!1,ki=null}else throw Error(R(198));bi||(bi=!0,Ba=u)}}function In(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Qp(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Fc(e){if(In(e)!==e)throw Error(R(188))}function Ky(e){var t=e.alternate;if(!t){if(t=In(e),t===null)throw Error(R(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return Fc(o),e;if(i===r)return Fc(o),t;i=i.sibling}throw Error(R(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,a=o.child;a;){if(a===n){s=!0,n=o,r=i;break}if(a===r){s=!0,r=o,n=i;break}a=a.sibling}if(!s){for(a=i.child;a;){if(a===n){s=!0,n=i,r=o;break}if(a===r){s=!0,r=i,n=o;break}a=a.sibling}if(!s)throw Error(R(189))}}if(n.alternate!==r)throw Error(R(190))}if(n.tag!==3)throw Error(R(188));return n.stateNode.current===n?e:t}function Yp(e){return e=Ky(e),e!==null?Xp(e):null}function Xp(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Xp(e);if(t!==null)return t;e=e.sibling}return null}var Zp=Qe.unstable_scheduleCallback,zc=Qe.unstable_cancelCallback,Gy=Qe.unstable_shouldYield,Qy=Qe.unstable_requestPaint,de=Qe.unstable_now,Yy=Qe.unstable_getCurrentPriorityLevel,ou=Qe.unstable_ImmediatePriority,qp=Qe.unstable_UserBlockingPriority,Ri=Qe.unstable_NormalPriority,Xy=Qe.unstable_LowPriority,Jp=Qe.unstable_IdlePriority,ls=null,yt=null;function Zy(e){if(yt&&typeof yt.onCommitFiberRoot=="function")try{yt.onCommitFiberRoot(ls,e,void 0,(e.current.flags&128)===128)}catch{}}var dt=Math.clz32?Math.clz32:e0,qy=Math.log,Jy=Math.LN2;function e0(e){return e>>>=0,e===0?32:31-(qy(e)/Jy|0)|0}var Uo=64,$o=4194304;function zr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ai(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,s=n&268435455;if(s!==0){var a=s&~o;a!==0?r=zr(a):(i&=s,i!==0&&(r=zr(i)))}else s=n&~o,s!==0?r=zr(s):i!==0&&(r=zr(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-dt(t),o=1<<n,r|=e[n],t&=~o;return r}function t0(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function n0(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var s=31-dt(i),a=1<<s,l=o[s];l===-1?(!(a&n)||a&r)&&(o[s]=t0(a,t)):l<=t&&(e.expiredLanes|=a),i&=~a}}function Ua(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function eh(){var e=Uo;return Uo<<=1,!(Uo&4194240)&&(Uo=64),e}function Bs(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ao(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-dt(t),e[t]=n}function r0(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-dt(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function iu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-dt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var G=0;function th(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var nh,su,rh,oh,ih,$a=!1,Wo=[],Jt=null,en=null,tn=null,lo=new Map,uo=new Map,Qt=[],o0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Bc(e,t){switch(e){case"focusin":case"focusout":Jt=null;break;case"dragenter":case"dragleave":en=null;break;case"mouseover":case"mouseout":tn=null;break;case"pointerover":case"pointerout":lo.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":uo.delete(t.pointerId)}}function Ar(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=_o(t),t!==null&&su(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function i0(e,t,n,r,o){switch(t){case"focusin":return Jt=Ar(Jt,e,t,n,r,o),!0;case"dragenter":return en=Ar(en,e,t,n,r,o),!0;case"mouseover":return tn=Ar(tn,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return lo.set(i,Ar(lo.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,uo.set(i,Ar(uo.get(i)||null,e,t,n,r,o)),!0}return!1}function sh(e){var t=Tn(e.target);if(t!==null){var n=In(t);if(n!==null){if(t=n.tag,t===13){if(t=Qp(n),t!==null){e.blockedOn=t,ih(e.priority,function(){rh(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ci(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Wa(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Ia=r,n.target.dispatchEvent(r),Ia=null}else return t=_o(n),t!==null&&su(t),e.blockedOn=n,!1;t.shift()}return!0}function Uc(e,t,n){ci(e)&&n.delete(t)}function s0(){$a=!1,Jt!==null&&ci(Jt)&&(Jt=null),en!==null&&ci(en)&&(en=null),tn!==null&&ci(tn)&&(tn=null),lo.forEach(Uc),uo.forEach(Uc)}function Mr(e,t){e.blockedOn===t&&(e.blockedOn=null,$a||($a=!0,Qe.unstable_scheduleCallback(Qe.unstable_NormalPriority,s0)))}function co(e){function t(o){return Mr(o,e)}if(0<Wo.length){Mr(Wo[0],e);for(var n=1;n<Wo.length;n++){var r=Wo[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Jt!==null&&Mr(Jt,e),en!==null&&Mr(en,e),tn!==null&&Mr(tn,e),lo.forEach(t),uo.forEach(t),n=0;n<Qt.length;n++)r=Qt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Qt.length&&(n=Qt[0],n.blockedOn===null);)sh(n),n.blockedOn===null&&Qt.shift()}var ar=Vt.ReactCurrentBatchConfig,Mi=!0;function a0(e,t,n,r){var o=G,i=ar.transition;ar.transition=null;try{G=1,au(e,t,n,r)}finally{G=o,ar.transition=i}}function l0(e,t,n,r){var o=G,i=ar.transition;ar.transition=null;try{G=4,au(e,t,n,r)}finally{G=o,ar.transition=i}}function au(e,t,n,r){if(Mi){var o=Wa(e,t,n,r);if(o===null)Zs(e,t,r,_i,n),Bc(e,r);else if(i0(o,e,t,n,r))r.stopPropagation();else if(Bc(e,r),t&4&&-1<o0.indexOf(e)){for(;o!==null;){var i=_o(o);if(i!==null&&nh(i),i=Wa(e,t,n,r),i===null&&Zs(e,t,r,_i,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Zs(e,t,r,null,n)}}var _i=null;function Wa(e,t,n,r){if(_i=null,e=ru(r),e=Tn(e),e!==null)if(t=In(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Qp(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return _i=e,null}function ah(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Yy()){case ou:return 1;case qp:return 4;case Ri:case Xy:return 16;case Jp:return 536870912;default:return 16}default:return 16}}var Xt=null,lu=null,di=null;function lh(){if(di)return di;var e,t=lu,n=t.length,r,o="value"in Xt?Xt.value:Xt.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===o[i-r];r++);return di=o.slice(e,1<r?1-r:void 0)}function fi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ho(){return!0}function $c(){return!1}function Ze(e){function t(n,r,o,i,s){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Ho:$c,this.isPropagationStopped=$c,this}return se(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ho)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ho)},persist:function(){},isPersistent:Ho}),t}var Er={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},uu=Ze(Er),Mo=se({},Er,{view:0,detail:0}),u0=Ze(Mo),Us,$s,_r,us=se({},Mo,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:cu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==_r&&(_r&&e.type==="mousemove"?(Us=e.screenX-_r.screenX,$s=e.screenY-_r.screenY):$s=Us=0,_r=e),Us)},movementY:function(e){return"movementY"in e?e.movementY:$s}}),Wc=Ze(us),c0=se({},us,{dataTransfer:0}),d0=Ze(c0),f0=se({},Mo,{relatedTarget:0}),Ws=Ze(f0),p0=se({},Er,{animationName:0,elapsedTime:0,pseudoElement:0}),h0=Ze(p0),m0=se({},Er,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),v0=Ze(m0),g0=se({},Er,{data:0}),Hc=Ze(g0),y0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},w0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},x0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function S0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=x0[e])?!!t[e]:!1}function cu(){return S0}var E0=se({},Mo,{key:function(e){if(e.key){var t=y0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=fi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?w0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:cu,charCode:function(e){return e.type==="keypress"?fi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?fi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),T0=Ze(E0),C0=se({},us,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Kc=Ze(C0),P0=se({},Mo,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:cu}),k0=Ze(P0),b0=se({},Er,{propertyName:0,elapsedTime:0,pseudoElement:0}),R0=Ze(b0),A0=se({},us,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),M0=Ze(A0),_0=[9,13,27,32],du=_t&&"CompositionEvent"in window,Gr=null;_t&&"documentMode"in document&&(Gr=document.documentMode);var N0=_t&&"TextEvent"in window&&!Gr,uh=_t&&(!du||Gr&&8<Gr&&11>=Gr),Gc=" ",Qc=!1;function ch(e,t){switch(e){case"keyup":return _0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function dh(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var $n=!1;function D0(e,t){switch(e){case"compositionend":return dh(t);case"keypress":return t.which!==32?null:(Qc=!0,Gc);case"textInput":return e=t.data,e===Gc&&Qc?null:e;default:return null}}function L0(e,t){if($n)return e==="compositionend"||!du&&ch(e,t)?(e=lh(),di=lu=Xt=null,$n=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return uh&&t.locale!=="ko"?null:t.data;default:return null}}var j0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Yc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!j0[e.type]:t==="textarea"}function fh(e,t,n,r){$p(r),t=Ni(t,"onChange"),0<t.length&&(n=new uu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Qr=null,fo=null;function O0(e){Th(e,0)}function cs(e){var t=Kn(e);if(Op(t))return e}function V0(e,t){if(e==="change")return t}var ph=!1;if(_t){var Hs;if(_t){var Ks="oninput"in document;if(!Ks){var Xc=document.createElement("div");Xc.setAttribute("oninput","return;"),Ks=typeof Xc.oninput=="function"}Hs=Ks}else Hs=!1;ph=Hs&&(!document.documentMode||9<document.documentMode)}function Zc(){Qr&&(Qr.detachEvent("onpropertychange",hh),fo=Qr=null)}function hh(e){if(e.propertyName==="value"&&cs(fo)){var t=[];fh(t,fo,e,ru(e)),Gp(O0,t)}}function I0(e,t,n){e==="focusin"?(Zc(),Qr=t,fo=n,Qr.attachEvent("onpropertychange",hh)):e==="focusout"&&Zc()}function F0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return cs(fo)}function z0(e,t){if(e==="click")return cs(t)}function B0(e,t){if(e==="input"||e==="change")return cs(t)}function U0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var pt=typeof Object.is=="function"?Object.is:U0;function po(e,t){if(pt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!ka.call(t,o)||!pt(e[o],t[o]))return!1}return!0}function qc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Jc(e,t){var n=qc(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=qc(n)}}function mh(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?mh(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function vh(){for(var e=window,t=Pi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Pi(e.document)}return t}function fu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function $0(e){var t=vh(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&mh(n.ownerDocument.documentElement,n)){if(r!==null&&fu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=Jc(n,i);var s=Jc(n,r);o&&s&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var W0=_t&&"documentMode"in document&&11>=document.documentMode,Wn=null,Ha=null,Yr=null,Ka=!1;function ed(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ka||Wn==null||Wn!==Pi(r)||(r=Wn,"selectionStart"in r&&fu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Yr&&po(Yr,r)||(Yr=r,r=Ni(Ha,"onSelect"),0<r.length&&(t=new uu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Wn)))}function Ko(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Hn={animationend:Ko("Animation","AnimationEnd"),animationiteration:Ko("Animation","AnimationIteration"),animationstart:Ko("Animation","AnimationStart"),transitionend:Ko("Transition","TransitionEnd")},Gs={},gh={};_t&&(gh=document.createElement("div").style,"AnimationEvent"in window||(delete Hn.animationend.animation,delete Hn.animationiteration.animation,delete Hn.animationstart.animation),"TransitionEvent"in window||delete Hn.transitionend.transition);function ds(e){if(Gs[e])return Gs[e];if(!Hn[e])return e;var t=Hn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in gh)return Gs[e]=t[n];return e}var yh=ds("animationend"),wh=ds("animationiteration"),xh=ds("animationstart"),Sh=ds("transitionend"),Eh=new Map,td="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function fn(e,t){Eh.set(e,t),Vn(t,[e])}for(var Qs=0;Qs<td.length;Qs++){var Ys=td[Qs],H0=Ys.toLowerCase(),K0=Ys[0].toUpperCase()+Ys.slice(1);fn(H0,"on"+K0)}fn(yh,"onAnimationEnd");fn(wh,"onAnimationIteration");fn(xh,"onAnimationStart");fn("dblclick","onDoubleClick");fn("focusin","onFocus");fn("focusout","onBlur");fn(Sh,"onTransitionEnd");dr("onMouseEnter",["mouseout","mouseover"]);dr("onMouseLeave",["mouseout","mouseover"]);dr("onPointerEnter",["pointerout","pointerover"]);dr("onPointerLeave",["pointerout","pointerover"]);Vn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Vn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Vn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Vn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Vn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Vn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Br="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),G0=new Set("cancel close invalid load scroll toggle".split(" ").concat(Br));function nd(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Hy(r,t,void 0,e),e.currentTarget=null}function Th(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var s=r.length-1;0<=s;s--){var a=r[s],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==i&&o.isPropagationStopped())break e;nd(o,a,u),i=l}else for(s=0;s<r.length;s++){if(a=r[s],l=a.instance,u=a.currentTarget,a=a.listener,l!==i&&o.isPropagationStopped())break e;nd(o,a,u),i=l}}}if(bi)throw e=Ba,bi=!1,Ba=null,e}function Z(e,t){var n=t[Za];n===void 0&&(n=t[Za]=new Set);var r=e+"__bubble";n.has(r)||(Ch(t,e,2,!1),n.add(r))}function Xs(e,t,n){var r=0;t&&(r|=4),Ch(n,e,r,t)}var Go="_reactListening"+Math.random().toString(36).slice(2);function ho(e){if(!e[Go]){e[Go]=!0,_p.forEach(function(n){n!=="selectionchange"&&(G0.has(n)||Xs(n,!1,e),Xs(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Go]||(t[Go]=!0,Xs("selectionchange",!1,t))}}function Ch(e,t,n,r){switch(ah(t)){case 1:var o=a0;break;case 4:o=l0;break;default:o=au}n=o.bind(null,t,n,e),o=void 0,!za||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Zs(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var a=r.stateNode.containerInfo;if(a===o||a.nodeType===8&&a.parentNode===o)break;if(s===4)for(s=r.return;s!==null;){var l=s.tag;if((l===3||l===4)&&(l=s.stateNode.containerInfo,l===o||l.nodeType===8&&l.parentNode===o))return;s=s.return}for(;a!==null;){if(s=Tn(a),s===null)return;if(l=s.tag,l===5||l===6){r=i=s;continue e}a=a.parentNode}}r=r.return}Gp(function(){var u=i,c=ru(n),d=[];e:{var f=Eh.get(e);if(f!==void 0){var m=uu,w=e;switch(e){case"keypress":if(fi(n)===0)break e;case"keydown":case"keyup":m=T0;break;case"focusin":w="focus",m=Ws;break;case"focusout":w="blur",m=Ws;break;case"beforeblur":case"afterblur":m=Ws;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=Wc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=d0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=k0;break;case yh:case wh:case xh:m=h0;break;case Sh:m=R0;break;case"scroll":m=u0;break;case"wheel":m=M0;break;case"copy":case"cut":case"paste":m=v0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=Kc}var y=(t&4)!==0,x=!y&&e==="scroll",h=y?f!==null?f+"Capture":null:f;y=[];for(var p=u,v;p!==null;){v=p;var S=v.stateNode;if(v.tag===5&&S!==null&&(v=S,h!==null&&(S=ao(p,h),S!=null&&y.push(mo(p,S,v)))),x)break;p=p.return}0<y.length&&(f=new m(f,w,null,n,c),d.push({event:f,listeners:y}))}}if(!(t&7)){e:{if(f=e==="mouseover"||e==="pointerover",m=e==="mouseout"||e==="pointerout",f&&n!==Ia&&(w=n.relatedTarget||n.fromElement)&&(Tn(w)||w[Nt]))break e;if((m||f)&&(f=c.window===c?c:(f=c.ownerDocument)?f.defaultView||f.parentWindow:window,m?(w=n.relatedTarget||n.toElement,m=u,w=w?Tn(w):null,w!==null&&(x=In(w),w!==x||w.tag!==5&&w.tag!==6)&&(w=null)):(m=null,w=u),m!==w)){if(y=Wc,S="onMouseLeave",h="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(y=Kc,S="onPointerLeave",h="onPointerEnter",p="pointer"),x=m==null?f:Kn(m),v=w==null?f:Kn(w),f=new y(S,p+"leave",m,n,c),f.target=x,f.relatedTarget=v,S=null,Tn(c)===u&&(y=new y(h,p+"enter",w,n,c),y.target=v,y.relatedTarget=x,S=y),x=S,m&&w)t:{for(y=m,h=w,p=0,v=y;v;v=zn(v))p++;for(v=0,S=h;S;S=zn(S))v++;for(;0<p-v;)y=zn(y),p--;for(;0<v-p;)h=zn(h),v--;for(;p--;){if(y===h||h!==null&&y===h.alternate)break t;y=zn(y),h=zn(h)}y=null}else y=null;m!==null&&rd(d,f,m,y,!1),w!==null&&x!==null&&rd(d,x,w,y,!0)}}e:{if(f=u?Kn(u):window,m=f.nodeName&&f.nodeName.toLowerCase(),m==="select"||m==="input"&&f.type==="file")var E=V0;else if(Yc(f))if(ph)E=B0;else{E=F0;var P=I0}else(m=f.nodeName)&&m.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(E=z0);if(E&&(E=E(e,u))){fh(d,E,n,c);break e}P&&P(e,f,u),e==="focusout"&&(P=f._wrapperState)&&P.controlled&&f.type==="number"&&Da(f,"number",f.value)}switch(P=u?Kn(u):window,e){case"focusin":(Yc(P)||P.contentEditable==="true")&&(Wn=P,Ha=u,Yr=null);break;case"focusout":Yr=Ha=Wn=null;break;case"mousedown":Ka=!0;break;case"contextmenu":case"mouseup":case"dragend":Ka=!1,ed(d,n,c);break;case"selectionchange":if(W0)break;case"keydown":case"keyup":ed(d,n,c)}var k;if(du)e:{switch(e){case"compositionstart":var C="onCompositionStart";break e;case"compositionend":C="onCompositionEnd";break e;case"compositionupdate":C="onCompositionUpdate";break e}C=void 0}else $n?ch(e,n)&&(C="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(C="onCompositionStart");C&&(uh&&n.locale!=="ko"&&($n||C!=="onCompositionStart"?C==="onCompositionEnd"&&$n&&(k=lh()):(Xt=c,lu="value"in Xt?Xt.value:Xt.textContent,$n=!0)),P=Ni(u,C),0<P.length&&(C=new Hc(C,e,null,n,c),d.push({event:C,listeners:P}),k?C.data=k:(k=dh(n),k!==null&&(C.data=k)))),(k=N0?D0(e,n):L0(e,n))&&(u=Ni(u,"onBeforeInput"),0<u.length&&(c=new Hc("onBeforeInput","beforeinput",null,n,c),d.push({event:c,listeners:u}),c.data=k))}Th(d,t)})}function mo(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ni(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=ao(e,n),i!=null&&r.unshift(mo(e,i,o)),i=ao(e,t),i!=null&&r.push(mo(e,i,o))),e=e.return}return r}function zn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function rd(e,t,n,r,o){for(var i=t._reactName,s=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,o?(l=ao(n,i),l!=null&&s.unshift(mo(n,l,a))):o||(l=ao(n,i),l!=null&&s.push(mo(n,l,a)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var Q0=/\r\n?/g,Y0=/\u0000|\uFFFD/g;function od(e){return(typeof e=="string"?e:""+e).replace(Q0,`
`).replace(Y0,"")}function Qo(e,t,n){if(t=od(t),od(e)!==t&&n)throw Error(R(425))}function Di(){}var Ga=null,Qa=null;function Ya(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Xa=typeof setTimeout=="function"?setTimeout:void 0,X0=typeof clearTimeout=="function"?clearTimeout:void 0,id=typeof Promise=="function"?Promise:void 0,Z0=typeof queueMicrotask=="function"?queueMicrotask:typeof id<"u"?function(e){return id.resolve(null).then(e).catch(q0)}:Xa;function q0(e){setTimeout(function(){throw e})}function qs(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),co(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);co(t)}function nn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function sd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Tr=Math.random().toString(36).slice(2),gt="__reactFiber$"+Tr,vo="__reactProps$"+Tr,Nt="__reactContainer$"+Tr,Za="__reactEvents$"+Tr,J0="__reactListeners$"+Tr,e1="__reactHandles$"+Tr;function Tn(e){var t=e[gt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Nt]||n[gt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=sd(e);e!==null;){if(n=e[gt])return n;e=sd(e)}return t}e=n,n=e.parentNode}return null}function _o(e){return e=e[gt]||e[Nt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Kn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(R(33))}function fs(e){return e[vo]||null}var qa=[],Gn=-1;function pn(e){return{current:e}}function q(e){0>Gn||(e.current=qa[Gn],qa[Gn]=null,Gn--)}function Y(e,t){Gn++,qa[Gn]=e.current,e.current=t}var un={},Ae=pn(un),Ie=pn(!1),_n=un;function fr(e,t){var n=e.type.contextTypes;if(!n)return un;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Fe(e){return e=e.childContextTypes,e!=null}function Li(){q(Ie),q(Ae)}function ad(e,t,n){if(Ae.current!==un)throw Error(R(168));Y(Ae,t),Y(Ie,n)}function Ph(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(R(108,Iy(e)||"Unknown",o));return se({},n,r)}function ji(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||un,_n=Ae.current,Y(Ae,e),Y(Ie,Ie.current),!0}function ld(e,t,n){var r=e.stateNode;if(!r)throw Error(R(169));n?(e=Ph(e,t,_n),r.__reactInternalMemoizedMergedChildContext=e,q(Ie),q(Ae),Y(Ae,e)):q(Ie),Y(Ie,n)}var Pt=null,ps=!1,Js=!1;function kh(e){Pt===null?Pt=[e]:Pt.push(e)}function t1(e){ps=!0,kh(e)}function hn(){if(!Js&&Pt!==null){Js=!0;var e=0,t=G;try{var n=Pt;for(G=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Pt=null,ps=!1}catch(o){throw Pt!==null&&(Pt=Pt.slice(e+1)),Zp(ou,hn),o}finally{G=t,Js=!1}}return null}var Qn=[],Yn=0,Oi=null,Vi=0,et=[],tt=0,Nn=null,kt=1,bt="";function yn(e,t){Qn[Yn++]=Vi,Qn[Yn++]=Oi,Oi=e,Vi=t}function bh(e,t,n){et[tt++]=kt,et[tt++]=bt,et[tt++]=Nn,Nn=e;var r=kt;e=bt;var o=32-dt(r)-1;r&=~(1<<o),n+=1;var i=32-dt(t)+o;if(30<i){var s=o-o%5;i=(r&(1<<s)-1).toString(32),r>>=s,o-=s,kt=1<<32-dt(t)+o|n<<o|r,bt=i+e}else kt=1<<i|n<<o|r,bt=e}function pu(e){e.return!==null&&(yn(e,1),bh(e,1,0))}function hu(e){for(;e===Oi;)Oi=Qn[--Yn],Qn[Yn]=null,Vi=Qn[--Yn],Qn[Yn]=null;for(;e===Nn;)Nn=et[--tt],et[tt]=null,bt=et[--tt],et[tt]=null,kt=et[--tt],et[tt]=null}var Ke=null,He=null,ee=!1,ct=null;function Rh(e,t){var n=nt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function ud(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ke=e,He=nn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ke=e,He=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Nn!==null?{id:kt,overflow:bt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=nt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ke=e,He=null,!0):!1;default:return!1}}function Ja(e){return(e.mode&1)!==0&&(e.flags&128)===0}function el(e){if(ee){var t=He;if(t){var n=t;if(!ud(e,t)){if(Ja(e))throw Error(R(418));t=nn(n.nextSibling);var r=Ke;t&&ud(e,t)?Rh(r,n):(e.flags=e.flags&-4097|2,ee=!1,Ke=e)}}else{if(Ja(e))throw Error(R(418));e.flags=e.flags&-4097|2,ee=!1,Ke=e}}}function cd(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ke=e}function Yo(e){if(e!==Ke)return!1;if(!ee)return cd(e),ee=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ya(e.type,e.memoizedProps)),t&&(t=He)){if(Ja(e))throw Ah(),Error(R(418));for(;t;)Rh(e,t),t=nn(t.nextSibling)}if(cd(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(R(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){He=nn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}He=null}}else He=Ke?nn(e.stateNode.nextSibling):null;return!0}function Ah(){for(var e=He;e;)e=nn(e.nextSibling)}function pr(){He=Ke=null,ee=!1}function mu(e){ct===null?ct=[e]:ct.push(e)}var n1=Vt.ReactCurrentBatchConfig;function lt(e,t){if(e&&e.defaultProps){t=se({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}var Ii=pn(null),Fi=null,Xn=null,vu=null;function gu(){vu=Xn=Fi=null}function yu(e){var t=Ii.current;q(Ii),e._currentValue=t}function tl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function lr(e,t){Fi=e,vu=Xn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ve=!0),e.firstContext=null)}function ot(e){var t=e._currentValue;if(vu!==e)if(e={context:e,memoizedValue:t,next:null},Xn===null){if(Fi===null)throw Error(R(308));Xn=e,Fi.dependencies={lanes:0,firstContext:e}}else Xn=Xn.next=e;return t}var Cn=null;function wu(e){Cn===null?Cn=[e]:Cn.push(e)}function Mh(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,wu(t)):(n.next=o.next,o.next=n),t.interleaved=n,Dt(e,r)}function Dt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Gt=!1;function xu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function _h(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Rt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function rn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,H&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Dt(e,n)}return o=r.interleaved,o===null?(t.next=t,wu(r)):(t.next=o.next,o.next=t),r.interleaved=t,Dt(e,n)}function pi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,iu(e,n)}}function dd(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=s:i=i.next=s,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function zi(e,t,n,r){var o=e.updateQueue;Gt=!1;var i=o.firstBaseUpdate,s=o.lastBaseUpdate,a=o.shared.pending;if(a!==null){o.shared.pending=null;var l=a,u=l.next;l.next=null,s===null?i=u:s.next=u,s=l;var c=e.alternate;c!==null&&(c=c.updateQueue,a=c.lastBaseUpdate,a!==s&&(a===null?c.firstBaseUpdate=u:a.next=u,c.lastBaseUpdate=l))}if(i!==null){var d=o.baseState;s=0,c=u=l=null,a=i;do{var f=a.lane,m=a.eventTime;if((r&f)===f){c!==null&&(c=c.next={eventTime:m,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var w=e,y=a;switch(f=t,m=n,y.tag){case 1:if(w=y.payload,typeof w=="function"){d=w.call(m,d,f);break e}d=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=y.payload,f=typeof w=="function"?w.call(m,d,f):w,f==null)break e;d=se({},d,f);break e;case 2:Gt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,f=o.effects,f===null?o.effects=[a]:f.push(a))}else m={eventTime:m,lane:f,tag:a.tag,payload:a.payload,callback:a.callback,next:null},c===null?(u=c=m,l=d):c=c.next=m,s|=f;if(a=a.next,a===null){if(a=o.shared.pending,a===null)break;f=a,a=f.next,f.next=null,o.lastBaseUpdate=f,o.shared.pending=null}}while(!0);if(c===null&&(l=d),o.baseState=l,o.firstBaseUpdate=u,o.lastBaseUpdate=c,t=o.shared.interleaved,t!==null){o=t;do s|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);Ln|=s,e.lanes=s,e.memoizedState=d}}function fd(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(R(191,o));o.call(r)}}}var Nh=new Mp.Component().refs;function nl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:se({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var hs={isMounted:function(e){return(e=e._reactInternals)?In(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=_e(),o=sn(e),i=Rt(r,o);i.payload=t,n!=null&&(i.callback=n),t=rn(e,i,o),t!==null&&(ft(t,e,o,r),pi(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=_e(),o=sn(e),i=Rt(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=rn(e,i,o),t!==null&&(ft(t,e,o,r),pi(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=_e(),r=sn(e),o=Rt(n,r);o.tag=2,t!=null&&(o.callback=t),t=rn(e,o,r),t!==null&&(ft(t,e,r,n),pi(t,e,r))}};function pd(e,t,n,r,o,i,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,s):t.prototype&&t.prototype.isPureReactComponent?!po(n,r)||!po(o,i):!0}function Dh(e,t,n){var r=!1,o=un,i=t.contextType;return typeof i=="object"&&i!==null?i=ot(i):(o=Fe(t)?_n:Ae.current,r=t.contextTypes,i=(r=r!=null)?fr(e,o):un),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=hs,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function hd(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&hs.enqueueReplaceState(t,t.state,null)}function rl(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs=Nh,xu(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=ot(i):(i=Fe(t)?_n:Ae.current,o.context=fr(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(nl(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&hs.enqueueReplaceState(o,o.state,null),zi(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function Nr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(R(309));var r=n.stateNode}if(!r)throw Error(R(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(s){var a=o.refs;a===Nh&&(a=o.refs={}),s===null?delete a[i]:a[i]=s},t._stringRef=i,t)}if(typeof e!="string")throw Error(R(284));if(!n._owner)throw Error(R(290,e))}return e}function Xo(e,t){throw e=Object.prototype.toString.call(t),Error(R(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function md(e){var t=e._init;return t(e._payload)}function Lh(e){function t(h,p){if(e){var v=h.deletions;v===null?(h.deletions=[p],h.flags|=16):v.push(p)}}function n(h,p){if(!e)return null;for(;p!==null;)t(h,p),p=p.sibling;return null}function r(h,p){for(h=new Map;p!==null;)p.key!==null?h.set(p.key,p):h.set(p.index,p),p=p.sibling;return h}function o(h,p){return h=an(h,p),h.index=0,h.sibling=null,h}function i(h,p,v){return h.index=v,e?(v=h.alternate,v!==null?(v=v.index,v<p?(h.flags|=2,p):v):(h.flags|=2,p)):(h.flags|=1048576,p)}function s(h){return e&&h.alternate===null&&(h.flags|=2),h}function a(h,p,v,S){return p===null||p.tag!==6?(p=sa(v,h.mode,S),p.return=h,p):(p=o(p,v),p.return=h,p)}function l(h,p,v,S){var E=v.type;return E===Un?c(h,p,v.props.children,S,v.key):p!==null&&(p.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===Kt&&md(E)===p.type)?(S=o(p,v.props),S.ref=Nr(h,p,v),S.return=h,S):(S=wi(v.type,v.key,v.props,null,h.mode,S),S.ref=Nr(h,p,v),S.return=h,S)}function u(h,p,v,S){return p===null||p.tag!==4||p.stateNode.containerInfo!==v.containerInfo||p.stateNode.implementation!==v.implementation?(p=aa(v,h.mode,S),p.return=h,p):(p=o(p,v.children||[]),p.return=h,p)}function c(h,p,v,S,E){return p===null||p.tag!==7?(p=An(v,h.mode,S,E),p.return=h,p):(p=o(p,v),p.return=h,p)}function d(h,p,v){if(typeof p=="string"&&p!==""||typeof p=="number")return p=sa(""+p,h.mode,v),p.return=h,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case Fo:return v=wi(p.type,p.key,p.props,null,h.mode,v),v.ref=Nr(h,null,p),v.return=h,v;case Bn:return p=aa(p,h.mode,v),p.return=h,p;case Kt:var S=p._init;return d(h,S(p._payload),v)}if(Fr(p)||br(p))return p=An(p,h.mode,v,null),p.return=h,p;Xo(h,p)}return null}function f(h,p,v,S){var E=p!==null?p.key:null;if(typeof v=="string"&&v!==""||typeof v=="number")return E!==null?null:a(h,p,""+v,S);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case Fo:return v.key===E?l(h,p,v,S):null;case Bn:return v.key===E?u(h,p,v,S):null;case Kt:return E=v._init,f(h,p,E(v._payload),S)}if(Fr(v)||br(v))return E!==null?null:c(h,p,v,S,null);Xo(h,v)}return null}function m(h,p,v,S,E){if(typeof S=="string"&&S!==""||typeof S=="number")return h=h.get(v)||null,a(p,h,""+S,E);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case Fo:return h=h.get(S.key===null?v:S.key)||null,l(p,h,S,E);case Bn:return h=h.get(S.key===null?v:S.key)||null,u(p,h,S,E);case Kt:var P=S._init;return m(h,p,v,P(S._payload),E)}if(Fr(S)||br(S))return h=h.get(v)||null,c(p,h,S,E,null);Xo(p,S)}return null}function w(h,p,v,S){for(var E=null,P=null,k=p,C=p=0,L=null;k!==null&&C<v.length;C++){k.index>C?(L=k,k=null):L=k.sibling;var N=f(h,k,v[C],S);if(N===null){k===null&&(k=L);break}e&&k&&N.alternate===null&&t(h,k),p=i(N,p,C),P===null?E=N:P.sibling=N,P=N,k=L}if(C===v.length)return n(h,k),ee&&yn(h,C),E;if(k===null){for(;C<v.length;C++)k=d(h,v[C],S),k!==null&&(p=i(k,p,C),P===null?E=k:P.sibling=k,P=k);return ee&&yn(h,C),E}for(k=r(h,k);C<v.length;C++)L=m(k,h,C,v[C],S),L!==null&&(e&&L.alternate!==null&&k.delete(L.key===null?C:L.key),p=i(L,p,C),P===null?E=L:P.sibling=L,P=L);return e&&k.forEach(function(_){return t(h,_)}),ee&&yn(h,C),E}function y(h,p,v,S){var E=br(v);if(typeof E!="function")throw Error(R(150));if(v=E.call(v),v==null)throw Error(R(151));for(var P=E=null,k=p,C=p=0,L=null,N=v.next();k!==null&&!N.done;C++,N=v.next()){k.index>C?(L=k,k=null):L=k.sibling;var _=f(h,k,N.value,S);if(_===null){k===null&&(k=L);break}e&&k&&_.alternate===null&&t(h,k),p=i(_,p,C),P===null?E=_:P.sibling=_,P=_,k=L}if(N.done)return n(h,k),ee&&yn(h,C),E;if(k===null){for(;!N.done;C++,N=v.next())N=d(h,N.value,S),N!==null&&(p=i(N,p,C),P===null?E=N:P.sibling=N,P=N);return ee&&yn(h,C),E}for(k=r(h,k);!N.done;C++,N=v.next())N=m(k,h,C,N.value,S),N!==null&&(e&&N.alternate!==null&&k.delete(N.key===null?C:N.key),p=i(N,p,C),P===null?E=N:P.sibling=N,P=N);return e&&k.forEach(function(b){return t(h,b)}),ee&&yn(h,C),E}function x(h,p,v,S){if(typeof v=="object"&&v!==null&&v.type===Un&&v.key===null&&(v=v.props.children),typeof v=="object"&&v!==null){switch(v.$$typeof){case Fo:e:{for(var E=v.key,P=p;P!==null;){if(P.key===E){if(E=v.type,E===Un){if(P.tag===7){n(h,P.sibling),p=o(P,v.props.children),p.return=h,h=p;break e}}else if(P.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===Kt&&md(E)===P.type){n(h,P.sibling),p=o(P,v.props),p.ref=Nr(h,P,v),p.return=h,h=p;break e}n(h,P);break}else t(h,P);P=P.sibling}v.type===Un?(p=An(v.props.children,h.mode,S,v.key),p.return=h,h=p):(S=wi(v.type,v.key,v.props,null,h.mode,S),S.ref=Nr(h,p,v),S.return=h,h=S)}return s(h);case Bn:e:{for(P=v.key;p!==null;){if(p.key===P)if(p.tag===4&&p.stateNode.containerInfo===v.containerInfo&&p.stateNode.implementation===v.implementation){n(h,p.sibling),p=o(p,v.children||[]),p.return=h,h=p;break e}else{n(h,p);break}else t(h,p);p=p.sibling}p=aa(v,h.mode,S),p.return=h,h=p}return s(h);case Kt:return P=v._init,x(h,p,P(v._payload),S)}if(Fr(v))return w(h,p,v,S);if(br(v))return y(h,p,v,S);Xo(h,v)}return typeof v=="string"&&v!==""||typeof v=="number"?(v=""+v,p!==null&&p.tag===6?(n(h,p.sibling),p=o(p,v),p.return=h,h=p):(n(h,p),p=sa(v,h.mode,S),p.return=h,h=p),s(h)):n(h,p)}return x}var hr=Lh(!0),jh=Lh(!1),No={},wt=pn(No),go=pn(No),yo=pn(No);function Pn(e){if(e===No)throw Error(R(174));return e}function Su(e,t){switch(Y(yo,t),Y(go,e),Y(wt,No),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ja(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=ja(t,e)}q(wt),Y(wt,t)}function mr(){q(wt),q(go),q(yo)}function Oh(e){Pn(yo.current);var t=Pn(wt.current),n=ja(t,e.type);t!==n&&(Y(go,e),Y(wt,n))}function Eu(e){go.current===e&&(q(wt),q(go))}var re=pn(0);function Bi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ea=[];function Tu(){for(var e=0;e<ea.length;e++)ea[e]._workInProgressVersionPrimary=null;ea.length=0}var hi=Vt.ReactCurrentDispatcher,ta=Vt.ReactCurrentBatchConfig,Dn=0,ie=null,me=null,ye=null,Ui=!1,Xr=!1,wo=0,r1=0;function Ce(){throw Error(R(321))}function Cu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!pt(e[n],t[n]))return!1;return!0}function Pu(e,t,n,r,o,i){if(Dn=i,ie=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,hi.current=e===null||e.memoizedState===null?a1:l1,e=n(r,o),Xr){i=0;do{if(Xr=!1,wo=0,25<=i)throw Error(R(301));i+=1,ye=me=null,t.updateQueue=null,hi.current=u1,e=n(r,o)}while(Xr)}if(hi.current=$i,t=me!==null&&me.next!==null,Dn=0,ye=me=ie=null,Ui=!1,t)throw Error(R(300));return e}function ku(){var e=wo!==0;return wo=0,e}function mt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ye===null?ie.memoizedState=ye=e:ye=ye.next=e,ye}function it(){if(me===null){var e=ie.alternate;e=e!==null?e.memoizedState:null}else e=me.next;var t=ye===null?ie.memoizedState:ye.next;if(t!==null)ye=t,me=e;else{if(e===null)throw Error(R(310));me=e,e={memoizedState:me.memoizedState,baseState:me.baseState,baseQueue:me.baseQueue,queue:me.queue,next:null},ye===null?ie.memoizedState=ye=e:ye=ye.next=e}return ye}function xo(e,t){return typeof t=="function"?t(e):t}function na(e){var t=it(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=me,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var a=s=null,l=null,u=i;do{var c=u.lane;if((Dn&c)===c)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=d,s=r):l=l.next=d,ie.lanes|=c,Ln|=c}u=u.next}while(u!==null&&u!==i);l===null?s=r:l.next=a,pt(r,t.memoizedState)||(Ve=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,ie.lanes|=i,Ln|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ra(e){var t=it(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var s=o=o.next;do i=e(i,s.action),s=s.next;while(s!==o);pt(i,t.memoizedState)||(Ve=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Vh(){}function Ih(e,t){var n=ie,r=it(),o=t(),i=!pt(r.memoizedState,o);if(i&&(r.memoizedState=o,Ve=!0),r=r.queue,bu(Bh.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||ye!==null&&ye.memoizedState.tag&1){if(n.flags|=2048,So(9,zh.bind(null,n,r,o,t),void 0,null),we===null)throw Error(R(349));Dn&30||Fh(n,t,o)}return o}function Fh(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ie.updateQueue,t===null?(t={lastEffect:null,stores:null},ie.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function zh(e,t,n,r){t.value=n,t.getSnapshot=r,Uh(t)&&$h(e)}function Bh(e,t,n){return n(function(){Uh(t)&&$h(e)})}function Uh(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!pt(e,n)}catch{return!0}}function $h(e){var t=Dt(e,1);t!==null&&ft(t,e,1,-1)}function vd(e){var t=mt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:xo,lastRenderedState:e},t.queue=e,e=e.dispatch=s1.bind(null,ie,e),[t.memoizedState,e]}function So(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=ie.updateQueue,t===null?(t={lastEffect:null,stores:null},ie.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Wh(){return it().memoizedState}function mi(e,t,n,r){var o=mt();ie.flags|=e,o.memoizedState=So(1|t,n,void 0,r===void 0?null:r)}function ms(e,t,n,r){var o=it();r=r===void 0?null:r;var i=void 0;if(me!==null){var s=me.memoizedState;if(i=s.destroy,r!==null&&Cu(r,s.deps)){o.memoizedState=So(t,n,i,r);return}}ie.flags|=e,o.memoizedState=So(1|t,n,i,r)}function gd(e,t){return mi(8390656,8,e,t)}function bu(e,t){return ms(2048,8,e,t)}function Hh(e,t){return ms(4,2,e,t)}function Kh(e,t){return ms(4,4,e,t)}function Gh(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Qh(e,t,n){return n=n!=null?n.concat([e]):null,ms(4,4,Gh.bind(null,t,e),n)}function Ru(){}function Yh(e,t){var n=it();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Cu(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Xh(e,t){var n=it();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Cu(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Zh(e,t,n){return Dn&21?(pt(n,t)||(n=eh(),ie.lanes|=n,Ln|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ve=!0),e.memoizedState=n)}function o1(e,t){var n=G;G=n!==0&&4>n?n:4,e(!0);var r=ta.transition;ta.transition={};try{e(!1),t()}finally{G=n,ta.transition=r}}function qh(){return it().memoizedState}function i1(e,t,n){var r=sn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Jh(e))em(t,n);else if(n=Mh(e,t,n,r),n!==null){var o=_e();ft(n,e,r,o),tm(n,t,r)}}function s1(e,t,n){var r=sn(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Jh(e))em(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var s=t.lastRenderedState,a=i(s,n);if(o.hasEagerState=!0,o.eagerState=a,pt(a,s)){var l=t.interleaved;l===null?(o.next=o,wu(t)):(o.next=l.next,l.next=o),t.interleaved=o;return}}catch{}finally{}n=Mh(e,t,o,r),n!==null&&(o=_e(),ft(n,e,r,o),tm(n,t,r))}}function Jh(e){var t=e.alternate;return e===ie||t!==null&&t===ie}function em(e,t){Xr=Ui=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function tm(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,iu(e,n)}}var $i={readContext:ot,useCallback:Ce,useContext:Ce,useEffect:Ce,useImperativeHandle:Ce,useInsertionEffect:Ce,useLayoutEffect:Ce,useMemo:Ce,useReducer:Ce,useRef:Ce,useState:Ce,useDebugValue:Ce,useDeferredValue:Ce,useTransition:Ce,useMutableSource:Ce,useSyncExternalStore:Ce,useId:Ce,unstable_isNewReconciler:!1},a1={readContext:ot,useCallback:function(e,t){return mt().memoizedState=[e,t===void 0?null:t],e},useContext:ot,useEffect:gd,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,mi(4194308,4,Gh.bind(null,t,e),n)},useLayoutEffect:function(e,t){return mi(4194308,4,e,t)},useInsertionEffect:function(e,t){return mi(4,2,e,t)},useMemo:function(e,t){var n=mt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=mt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=i1.bind(null,ie,e),[r.memoizedState,e]},useRef:function(e){var t=mt();return e={current:e},t.memoizedState=e},useState:vd,useDebugValue:Ru,useDeferredValue:function(e){return mt().memoizedState=e},useTransition:function(){var e=vd(!1),t=e[0];return e=o1.bind(null,e[1]),mt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ie,o=mt();if(ee){if(n===void 0)throw Error(R(407));n=n()}else{if(n=t(),we===null)throw Error(R(349));Dn&30||Fh(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,gd(Bh.bind(null,r,i,e),[e]),r.flags|=2048,So(9,zh.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=mt(),t=we.identifierPrefix;if(ee){var n=bt,r=kt;n=(r&~(1<<32-dt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=wo++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=r1++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},l1={readContext:ot,useCallback:Yh,useContext:ot,useEffect:bu,useImperativeHandle:Qh,useInsertionEffect:Hh,useLayoutEffect:Kh,useMemo:Xh,useReducer:na,useRef:Wh,useState:function(){return na(xo)},useDebugValue:Ru,useDeferredValue:function(e){var t=it();return Zh(t,me.memoizedState,e)},useTransition:function(){var e=na(xo)[0],t=it().memoizedState;return[e,t]},useMutableSource:Vh,useSyncExternalStore:Ih,useId:qh,unstable_isNewReconciler:!1},u1={readContext:ot,useCallback:Yh,useContext:ot,useEffect:bu,useImperativeHandle:Qh,useInsertionEffect:Hh,useLayoutEffect:Kh,useMemo:Xh,useReducer:ra,useRef:Wh,useState:function(){return ra(xo)},useDebugValue:Ru,useDeferredValue:function(e){var t=it();return me===null?t.memoizedState=e:Zh(t,me.memoizedState,e)},useTransition:function(){var e=ra(xo)[0],t=it().memoizedState;return[e,t]},useMutableSource:Vh,useSyncExternalStore:Ih,useId:qh,unstable_isNewReconciler:!1};function vr(e,t){try{var n="",r=t;do n+=Vy(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function oa(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function ol(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var c1=typeof WeakMap=="function"?WeakMap:Map;function nm(e,t,n){n=Rt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Hi||(Hi=!0,hl=r),ol(e,t)},n}function rm(e,t,n){n=Rt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){ol(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){ol(e,t),typeof r!="function"&&(on===null?on=new Set([this]):on.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function yd(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new c1;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=C1.bind(null,e,t,n),t.then(e,e))}function wd(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function xd(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Rt(-1,1),t.tag=2,rn(n,t,1))),n.lanes|=1),e)}var d1=Vt.ReactCurrentOwner,Ve=!1;function Me(e,t,n,r){t.child=e===null?jh(t,null,n,r):hr(t,e.child,n,r)}function Sd(e,t,n,r,o){n=n.render;var i=t.ref;return lr(t,o),r=Pu(e,t,n,r,i,o),n=ku(),e!==null&&!Ve?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Lt(e,t,o)):(ee&&n&&pu(t),t.flags|=1,Me(e,t,r,o),t.child)}function Ed(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!Ou(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,om(e,t,i,r,o)):(e=wi(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var s=i.memoizedProps;if(n=n.compare,n=n!==null?n:po,n(s,r)&&e.ref===t.ref)return Lt(e,t,o)}return t.flags|=1,e=an(i,r),e.ref=t.ref,e.return=t,t.child=e}function om(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(po(i,r)&&e.ref===t.ref)if(Ve=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(Ve=!0);else return t.lanes=e.lanes,Lt(e,t,o)}return il(e,t,n,r,o)}function im(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Y(qn,$e),$e|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Y(qn,$e),$e|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,Y(qn,$e),$e|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,Y(qn,$e),$e|=r;return Me(e,t,o,n),t.child}function sm(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function il(e,t,n,r,o){var i=Fe(n)?_n:Ae.current;return i=fr(t,i),lr(t,o),n=Pu(e,t,n,r,i,o),r=ku(),e!==null&&!Ve?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Lt(e,t,o)):(ee&&r&&pu(t),t.flags|=1,Me(e,t,n,o),t.child)}function Td(e,t,n,r,o){if(Fe(n)){var i=!0;ji(t)}else i=!1;if(lr(t,o),t.stateNode===null)vi(e,t),Dh(t,n,r),rl(t,n,r,o),r=!0;else if(e===null){var s=t.stateNode,a=t.memoizedProps;s.props=a;var l=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=ot(u):(u=Fe(n)?_n:Ae.current,u=fr(t,u));var c=n.getDerivedStateFromProps,d=typeof c=="function"||typeof s.getSnapshotBeforeUpdate=="function";d||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==r||l!==u)&&hd(t,s,r,u),Gt=!1;var f=t.memoizedState;s.state=f,zi(t,r,s,o),l=t.memoizedState,a!==r||f!==l||Ie.current||Gt?(typeof c=="function"&&(nl(t,n,c,r),l=t.memoizedState),(a=Gt||pd(t,n,a,r,f,l,u))?(d||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),s.props=r,s.state=l,s.context=u,r=a):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,_h(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:lt(t.type,a),s.props=u,d=t.pendingProps,f=s.context,l=n.contextType,typeof l=="object"&&l!==null?l=ot(l):(l=Fe(n)?_n:Ae.current,l=fr(t,l));var m=n.getDerivedStateFromProps;(c=typeof m=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==d||f!==l)&&hd(t,s,r,l),Gt=!1,f=t.memoizedState,s.state=f,zi(t,r,s,o);var w=t.memoizedState;a!==d||f!==w||Ie.current||Gt?(typeof m=="function"&&(nl(t,n,m,r),w=t.memoizedState),(u=Gt||pd(t,n,u,r,f,w,l)||!1)?(c||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,w,l),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,w,l)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),s.props=r,s.state=w,s.context=l,r=u):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return sl(e,t,n,r,i,o)}function sl(e,t,n,r,o,i){sm(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return o&&ld(t,n,!1),Lt(e,t,i);r=t.stateNode,d1.current=t;var a=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=hr(t,e.child,null,i),t.child=hr(t,null,a,i)):Me(e,t,a,i),t.memoizedState=r.state,o&&ld(t,n,!0),t.child}function am(e){var t=e.stateNode;t.pendingContext?ad(e,t.pendingContext,t.pendingContext!==t.context):t.context&&ad(e,t.context,!1),Su(e,t.containerInfo)}function Cd(e,t,n,r,o){return pr(),mu(o),t.flags|=256,Me(e,t,n,r),t.child}var al={dehydrated:null,treeContext:null,retryLane:0};function ll(e){return{baseLanes:e,cachePool:null,transitions:null}}function lm(e,t,n){var r=t.pendingProps,o=re.current,i=!1,s=(t.flags&128)!==0,a;if((a=s)||(a=e!==null&&e.memoizedState===null?!1:(o&2)!==0),a?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),Y(re,o&1),e===null)return el(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,i?(r=t.mode,i=t.child,s={mode:"hidden",children:s},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=s):i=ys(s,r,0,null),e=An(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=ll(n),t.memoizedState=al,e):Au(t,s));if(o=e.memoizedState,o!==null&&(a=o.dehydrated,a!==null))return f1(e,t,s,r,a,o,n);if(i){i=r.fallback,s=t.mode,o=e.child,a=o.sibling;var l={mode:"hidden",children:r.children};return!(s&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=an(o,l),r.subtreeFlags=o.subtreeFlags&14680064),a!==null?i=an(a,i):(i=An(i,s,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,s=e.child.memoizedState,s=s===null?ll(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=al,r}return i=e.child,e=i.sibling,r=an(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Au(e,t){return t=ys({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Zo(e,t,n,r){return r!==null&&mu(r),hr(t,e.child,null,n),e=Au(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function f1(e,t,n,r,o,i,s){if(n)return t.flags&256?(t.flags&=-257,r=oa(Error(R(422))),Zo(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=ys({mode:"visible",children:r.children},o,0,null),i=An(i,o,s,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&hr(t,e.child,null,s),t.child.memoizedState=ll(s),t.memoizedState=al,i);if(!(t.mode&1))return Zo(e,t,s,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var a=r.dgst;return r=a,i=Error(R(419)),r=oa(i,r,void 0),Zo(e,t,s,r)}if(a=(s&e.childLanes)!==0,Ve||a){if(r=we,r!==null){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|s)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,Dt(e,o),ft(r,e,o,-1))}return ju(),r=oa(Error(R(421))),Zo(e,t,s,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=P1.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,He=nn(o.nextSibling),Ke=t,ee=!0,ct=null,e!==null&&(et[tt++]=kt,et[tt++]=bt,et[tt++]=Nn,kt=e.id,bt=e.overflow,Nn=t),t=Au(t,r.children),t.flags|=4096,t)}function Pd(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),tl(e.return,t,n)}function ia(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function um(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(Me(e,t,r.children,n),r=re.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Pd(e,n,t);else if(e.tag===19)Pd(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Y(re,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Bi(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),ia(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Bi(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}ia(t,!0,n,null,i);break;case"together":ia(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function vi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Lt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Ln|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(R(153));if(t.child!==null){for(e=t.child,n=an(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=an(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function p1(e,t,n){switch(t.tag){case 3:am(t),pr();break;case 5:Oh(t);break;case 1:Fe(t.type)&&ji(t);break;case 4:Su(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;Y(Ii,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(Y(re,re.current&1),t.flags|=128,null):n&t.child.childLanes?lm(e,t,n):(Y(re,re.current&1),e=Lt(e,t,n),e!==null?e.sibling:null);Y(re,re.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return um(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),Y(re,re.current),r)break;return null;case 22:case 23:return t.lanes=0,im(e,t,n)}return Lt(e,t,n)}var cm,ul,dm,fm;cm=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ul=function(){};dm=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Pn(wt.current);var i=null;switch(n){case"input":o=_a(e,o),r=_a(e,r),i=[];break;case"select":o=se({},o,{value:void 0}),r=se({},r,{value:void 0}),i=[];break;case"textarea":o=La(e,o),r=La(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Di)}Oa(n,r);var s;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var a=o[u];for(s in a)a.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(io.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var l=r[u];if(a=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(s in a)!a.hasOwnProperty(s)||l&&l.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in l)l.hasOwnProperty(s)&&a[s]!==l[s]&&(n||(n={}),n[s]=l[s])}else n||(i||(i=[]),i.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(i=i||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(i=i||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(io.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&Z("scroll",e),i||a===l||(i=[])):(i=i||[]).push(u,l))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};fm=function(e,t,n,r){n!==r&&(t.flags|=4)};function Dr(e,t){if(!ee)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Pe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function h1(e,t,n){var r=t.pendingProps;switch(hu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Pe(t),null;case 1:return Fe(t.type)&&Li(),Pe(t),null;case 3:return r=t.stateNode,mr(),q(Ie),q(Ae),Tu(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Yo(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,ct!==null&&(gl(ct),ct=null))),ul(e,t),Pe(t),null;case 5:Eu(t);var o=Pn(yo.current);if(n=t.type,e!==null&&t.stateNode!=null)dm(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(R(166));return Pe(t),null}if(e=Pn(wt.current),Yo(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[gt]=t,r[vo]=i,e=(t.mode&1)!==0,n){case"dialog":Z("cancel",r),Z("close",r);break;case"iframe":case"object":case"embed":Z("load",r);break;case"video":case"audio":for(o=0;o<Br.length;o++)Z(Br[o],r);break;case"source":Z("error",r);break;case"img":case"image":case"link":Z("error",r),Z("load",r);break;case"details":Z("toggle",r);break;case"input":Lc(r,i),Z("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},Z("invalid",r);break;case"textarea":Oc(r,i),Z("invalid",r)}Oa(n,i),o=null;for(var s in i)if(i.hasOwnProperty(s)){var a=i[s];s==="children"?typeof a=="string"?r.textContent!==a&&(i.suppressHydrationWarning!==!0&&Qo(r.textContent,a,e),o=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&Qo(r.textContent,a,e),o=["children",""+a]):io.hasOwnProperty(s)&&a!=null&&s==="onScroll"&&Z("scroll",r)}switch(n){case"input":zo(r),jc(r,i,!0);break;case"textarea":zo(r),Vc(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=Di)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Fp(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[gt]=t,e[vo]=r,cm(e,t,!1,!1),t.stateNode=e;e:{switch(s=Va(n,r),n){case"dialog":Z("cancel",e),Z("close",e),o=r;break;case"iframe":case"object":case"embed":Z("load",e),o=r;break;case"video":case"audio":for(o=0;o<Br.length;o++)Z(Br[o],e);o=r;break;case"source":Z("error",e),o=r;break;case"img":case"image":case"link":Z("error",e),Z("load",e),o=r;break;case"details":Z("toggle",e),o=r;break;case"input":Lc(e,r),o=_a(e,r),Z("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=se({},r,{value:void 0}),Z("invalid",e);break;case"textarea":Oc(e,r),o=La(e,r),Z("invalid",e);break;default:o=r}Oa(n,o),a=o;for(i in a)if(a.hasOwnProperty(i)){var l=a[i];i==="style"?Up(e,l):i==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&zp(e,l)):i==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&so(e,l):typeof l=="number"&&so(e,""+l):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(io.hasOwnProperty(i)?l!=null&&i==="onScroll"&&Z("scroll",e):l!=null&&Jl(e,i,l,s))}switch(n){case"input":zo(e),jc(e,r,!1);break;case"textarea":zo(e),Vc(e);break;case"option":r.value!=null&&e.setAttribute("value",""+ln(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?or(e,!!r.multiple,i,!1):r.defaultValue!=null&&or(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=Di)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Pe(t),null;case 6:if(e&&t.stateNode!=null)fm(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(R(166));if(n=Pn(yo.current),Pn(wt.current),Yo(t)){if(r=t.stateNode,n=t.memoizedProps,r[gt]=t,(i=r.nodeValue!==n)&&(e=Ke,e!==null))switch(e.tag){case 3:Qo(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Qo(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[gt]=t,t.stateNode=r}return Pe(t),null;case 13:if(q(re),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ee&&He!==null&&t.mode&1&&!(t.flags&128))Ah(),pr(),t.flags|=98560,i=!1;else if(i=Yo(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(R(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(R(317));i[gt]=t}else pr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Pe(t),i=!1}else ct!==null&&(gl(ct),ct=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||re.current&1?ve===0&&(ve=3):ju())),t.updateQueue!==null&&(t.flags|=4),Pe(t),null);case 4:return mr(),ul(e,t),e===null&&ho(t.stateNode.containerInfo),Pe(t),null;case 10:return yu(t.type._context),Pe(t),null;case 17:return Fe(t.type)&&Li(),Pe(t),null;case 19:if(q(re),i=t.memoizedState,i===null)return Pe(t),null;if(r=(t.flags&128)!==0,s=i.rendering,s===null)if(r)Dr(i,!1);else{if(ve!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=Bi(e),s!==null){for(t.flags|=128,Dr(i,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,s=i.alternate,s===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Y(re,re.current&1|2),t.child}e=e.sibling}i.tail!==null&&de()>gr&&(t.flags|=128,r=!0,Dr(i,!1),t.lanes=4194304)}else{if(!r)if(e=Bi(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Dr(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!ee)return Pe(t),null}else 2*de()-i.renderingStartTime>gr&&n!==1073741824&&(t.flags|=128,r=!0,Dr(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(n=i.last,n!==null?n.sibling=s:t.child=s,i.last=s)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=de(),t.sibling=null,n=re.current,Y(re,r?n&1|2:n&1),t):(Pe(t),null);case 22:case 23:return Lu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?$e&1073741824&&(Pe(t),t.subtreeFlags&6&&(t.flags|=8192)):Pe(t),null;case 24:return null;case 25:return null}throw Error(R(156,t.tag))}function m1(e,t){switch(hu(t),t.tag){case 1:return Fe(t.type)&&Li(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return mr(),q(Ie),q(Ae),Tu(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Eu(t),null;case 13:if(q(re),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(R(340));pr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return q(re),null;case 4:return mr(),null;case 10:return yu(t.type._context),null;case 22:case 23:return Lu(),null;case 24:return null;default:return null}}var qo=!1,be=!1,v1=typeof WeakSet=="function"?WeakSet:Set,D=null;function Zn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ue(e,t,r)}else n.current=null}function cl(e,t,n){try{n()}catch(r){ue(e,t,r)}}var kd=!1;function g1(e,t){if(Ga=Mi,e=vh(),fu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var s=0,a=-1,l=-1,u=0,c=0,d=e,f=null;t:for(;;){for(var m;d!==n||o!==0&&d.nodeType!==3||(a=s+o),d!==i||r!==0&&d.nodeType!==3||(l=s+r),d.nodeType===3&&(s+=d.nodeValue.length),(m=d.firstChild)!==null;)f=d,d=m;for(;;){if(d===e)break t;if(f===n&&++u===o&&(a=s),f===i&&++c===r&&(l=s),(m=d.nextSibling)!==null)break;d=f,f=d.parentNode}d=m}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(Qa={focusedElem:e,selectionRange:n},Mi=!1,D=t;D!==null;)if(t=D,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,D=e;else for(;D!==null;){t=D;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var y=w.memoizedProps,x=w.memoizedState,h=t.stateNode,p=h.getSnapshotBeforeUpdate(t.elementType===t.type?y:lt(t.type,y),x);h.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var v=t.stateNode.containerInfo;v.nodeType===1?v.textContent="":v.nodeType===9&&v.documentElement&&v.removeChild(v.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(R(163))}}catch(S){ue(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,D=e;break}D=t.return}return w=kd,kd=!1,w}function Zr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&cl(t,n,i)}o=o.next}while(o!==r)}}function vs(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function dl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function pm(e){var t=e.alternate;t!==null&&(e.alternate=null,pm(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[gt],delete t[vo],delete t[Za],delete t[J0],delete t[e1])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function hm(e){return e.tag===5||e.tag===3||e.tag===4}function bd(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||hm(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function fl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Di));else if(r!==4&&(e=e.child,e!==null))for(fl(e,t,n),e=e.sibling;e!==null;)fl(e,t,n),e=e.sibling}function pl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(pl(e,t,n),e=e.sibling;e!==null;)pl(e,t,n),e=e.sibling}var xe=null,ut=!1;function Ut(e,t,n){for(n=n.child;n!==null;)mm(e,t,n),n=n.sibling}function mm(e,t,n){if(yt&&typeof yt.onCommitFiberUnmount=="function")try{yt.onCommitFiberUnmount(ls,n)}catch{}switch(n.tag){case 5:be||Zn(n,t);case 6:var r=xe,o=ut;xe=null,Ut(e,t,n),xe=r,ut=o,xe!==null&&(ut?(e=xe,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):xe.removeChild(n.stateNode));break;case 18:xe!==null&&(ut?(e=xe,n=n.stateNode,e.nodeType===8?qs(e.parentNode,n):e.nodeType===1&&qs(e,n),co(e)):qs(xe,n.stateNode));break;case 4:r=xe,o=ut,xe=n.stateNode.containerInfo,ut=!0,Ut(e,t,n),xe=r,ut=o;break;case 0:case 11:case 14:case 15:if(!be&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,s=i.destroy;i=i.tag,s!==void 0&&(i&2||i&4)&&cl(n,t,s),o=o.next}while(o!==r)}Ut(e,t,n);break;case 1:if(!be&&(Zn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){ue(n,t,a)}Ut(e,t,n);break;case 21:Ut(e,t,n);break;case 22:n.mode&1?(be=(r=be)||n.memoizedState!==null,Ut(e,t,n),be=r):Ut(e,t,n);break;default:Ut(e,t,n)}}function Rd(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new v1),t.forEach(function(r){var o=k1.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function st(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,s=t,a=s;e:for(;a!==null;){switch(a.tag){case 5:xe=a.stateNode,ut=!1;break e;case 3:xe=a.stateNode.containerInfo,ut=!0;break e;case 4:xe=a.stateNode.containerInfo,ut=!0;break e}a=a.return}if(xe===null)throw Error(R(160));mm(i,s,o),xe=null,ut=!1;var l=o.alternate;l!==null&&(l.return=null),o.return=null}catch(u){ue(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)vm(t,e),t=t.sibling}function vm(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(st(t,e),ht(e),r&4){try{Zr(3,e,e.return),vs(3,e)}catch(y){ue(e,e.return,y)}try{Zr(5,e,e.return)}catch(y){ue(e,e.return,y)}}break;case 1:st(t,e),ht(e),r&512&&n!==null&&Zn(n,n.return);break;case 5:if(st(t,e),ht(e),r&512&&n!==null&&Zn(n,n.return),e.flags&32){var o=e.stateNode;try{so(o,"")}catch(y){ue(e,e.return,y)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,s=n!==null?n.memoizedProps:i,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&Vp(o,i),Va(a,s);var u=Va(a,i);for(s=0;s<l.length;s+=2){var c=l[s],d=l[s+1];c==="style"?Up(o,d):c==="dangerouslySetInnerHTML"?zp(o,d):c==="children"?so(o,d):Jl(o,c,d,u)}switch(a){case"input":Na(o,i);break;case"textarea":Ip(o,i);break;case"select":var f=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var m=i.value;m!=null?or(o,!!i.multiple,m,!1):f!==!!i.multiple&&(i.defaultValue!=null?or(o,!!i.multiple,i.defaultValue,!0):or(o,!!i.multiple,i.multiple?[]:"",!1))}o[vo]=i}catch(y){ue(e,e.return,y)}}break;case 6:if(st(t,e),ht(e),r&4){if(e.stateNode===null)throw Error(R(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(y){ue(e,e.return,y)}}break;case 3:if(st(t,e),ht(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{co(t.containerInfo)}catch(y){ue(e,e.return,y)}break;case 4:st(t,e),ht(e);break;case 13:st(t,e),ht(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(Nu=de())),r&4&&Rd(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(be=(u=be)||c,st(t,e),be=u):st(t,e),ht(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(D=e,c=e.child;c!==null;){for(d=D=c;D!==null;){switch(f=D,m=f.child,f.tag){case 0:case 11:case 14:case 15:Zr(4,f,f.return);break;case 1:Zn(f,f.return);var w=f.stateNode;if(typeof w.componentWillUnmount=="function"){r=f,n=f.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(y){ue(r,n,y)}}break;case 5:Zn(f,f.return);break;case 22:if(f.memoizedState!==null){Md(d);continue}}m!==null?(m.return=f,D=m):Md(d)}c=c.sibling}e:for(c=null,d=e;;){if(d.tag===5){if(c===null){c=d;try{o=d.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=d.stateNode,l=d.memoizedProps.style,s=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=Bp("display",s))}catch(y){ue(e,e.return,y)}}}else if(d.tag===6){if(c===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(y){ue(e,e.return,y)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;c===d&&(c=null),d=d.return}c===d&&(c=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:st(t,e),ht(e),r&4&&Rd(e);break;case 21:break;default:st(t,e),ht(e)}}function ht(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(hm(n)){var r=n;break e}n=n.return}throw Error(R(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(so(o,""),r.flags&=-33);var i=bd(e);pl(e,i,o);break;case 3:case 4:var s=r.stateNode.containerInfo,a=bd(e);fl(e,a,s);break;default:throw Error(R(161))}}catch(l){ue(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function y1(e,t,n){D=e,gm(e)}function gm(e,t,n){for(var r=(e.mode&1)!==0;D!==null;){var o=D,i=o.child;if(o.tag===22&&r){var s=o.memoizedState!==null||qo;if(!s){var a=o.alternate,l=a!==null&&a.memoizedState!==null||be;a=qo;var u=be;if(qo=s,(be=l)&&!u)for(D=o;D!==null;)s=D,l=s.child,s.tag===22&&s.memoizedState!==null?_d(o):l!==null?(l.return=s,D=l):_d(o);for(;i!==null;)D=i,gm(i),i=i.sibling;D=o,qo=a,be=u}Ad(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,D=i):Ad(e)}}function Ad(e){for(;D!==null;){var t=D;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:be||vs(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!be)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:lt(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&fd(t,i,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}fd(t,s,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var d=c.dehydrated;d!==null&&co(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(R(163))}be||t.flags&512&&dl(t)}catch(f){ue(t,t.return,f)}}if(t===e){D=null;break}if(n=t.sibling,n!==null){n.return=t.return,D=n;break}D=t.return}}function Md(e){for(;D!==null;){var t=D;if(t===e){D=null;break}var n=t.sibling;if(n!==null){n.return=t.return,D=n;break}D=t.return}}function _d(e){for(;D!==null;){var t=D;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{vs(4,t)}catch(l){ue(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(l){ue(t,o,l)}}var i=t.return;try{dl(t)}catch(l){ue(t,i,l)}break;case 5:var s=t.return;try{dl(t)}catch(l){ue(t,s,l)}}}catch(l){ue(t,t.return,l)}if(t===e){D=null;break}var a=t.sibling;if(a!==null){a.return=t.return,D=a;break}D=t.return}}var w1=Math.ceil,Wi=Vt.ReactCurrentDispatcher,Mu=Vt.ReactCurrentOwner,rt=Vt.ReactCurrentBatchConfig,H=0,we=null,pe=null,Ee=0,$e=0,qn=pn(0),ve=0,Eo=null,Ln=0,gs=0,_u=0,qr=null,Oe=null,Nu=0,gr=1/0,Ct=null,Hi=!1,hl=null,on=null,Jo=!1,Zt=null,Ki=0,Jr=0,ml=null,gi=-1,yi=0;function _e(){return H&6?de():gi!==-1?gi:gi=de()}function sn(e){return e.mode&1?H&2&&Ee!==0?Ee&-Ee:n1.transition!==null?(yi===0&&(yi=eh()),yi):(e=G,e!==0||(e=window.event,e=e===void 0?16:ah(e.type)),e):1}function ft(e,t,n,r){if(50<Jr)throw Jr=0,ml=null,Error(R(185));Ao(e,n,r),(!(H&2)||e!==we)&&(e===we&&(!(H&2)&&(gs|=n),ve===4&&Yt(e,Ee)),ze(e,r),n===1&&H===0&&!(t.mode&1)&&(gr=de()+500,ps&&hn()))}function ze(e,t){var n=e.callbackNode;n0(e,t);var r=Ai(e,e===we?Ee:0);if(r===0)n!==null&&zc(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&zc(n),t===1)e.tag===0?t1(Nd.bind(null,e)):kh(Nd.bind(null,e)),Z0(function(){!(H&6)&&hn()}),n=null;else{switch(th(r)){case 1:n=ou;break;case 4:n=qp;break;case 16:n=Ri;break;case 536870912:n=Jp;break;default:n=Ri}n=Pm(n,ym.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ym(e,t){if(gi=-1,yi=0,H&6)throw Error(R(327));var n=e.callbackNode;if(ur()&&e.callbackNode!==n)return null;var r=Ai(e,e===we?Ee:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Gi(e,r);else{t=r;var o=H;H|=2;var i=xm();(we!==e||Ee!==t)&&(Ct=null,gr=de()+500,Rn(e,t));do try{E1();break}catch(a){wm(e,a)}while(!0);gu(),Wi.current=i,H=o,pe!==null?t=0:(we=null,Ee=0,t=ve)}if(t!==0){if(t===2&&(o=Ua(e),o!==0&&(r=o,t=vl(e,o))),t===1)throw n=Eo,Rn(e,0),Yt(e,r),ze(e,de()),n;if(t===6)Yt(e,r);else{if(o=e.current.alternate,!(r&30)&&!x1(o)&&(t=Gi(e,r),t===2&&(i=Ua(e),i!==0&&(r=i,t=vl(e,i))),t===1))throw n=Eo,Rn(e,0),Yt(e,r),ze(e,de()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(R(345));case 2:wn(e,Oe,Ct);break;case 3:if(Yt(e,r),(r&130023424)===r&&(t=Nu+500-de(),10<t)){if(Ai(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){_e(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Xa(wn.bind(null,e,Oe,Ct),t);break}wn(e,Oe,Ct);break;case 4:if(Yt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var s=31-dt(r);i=1<<s,s=t[s],s>o&&(o=s),r&=~i}if(r=o,r=de()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*w1(r/1960))-r,10<r){e.timeoutHandle=Xa(wn.bind(null,e,Oe,Ct),r);break}wn(e,Oe,Ct);break;case 5:wn(e,Oe,Ct);break;default:throw Error(R(329))}}}return ze(e,de()),e.callbackNode===n?ym.bind(null,e):null}function vl(e,t){var n=qr;return e.current.memoizedState.isDehydrated&&(Rn(e,t).flags|=256),e=Gi(e,t),e!==2&&(t=Oe,Oe=n,t!==null&&gl(t)),e}function gl(e){Oe===null?Oe=e:Oe.push.apply(Oe,e)}function x1(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!pt(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Yt(e,t){for(t&=~_u,t&=~gs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-dt(t),r=1<<n;e[n]=-1,t&=~r}}function Nd(e){if(H&6)throw Error(R(327));ur();var t=Ai(e,0);if(!(t&1))return ze(e,de()),null;var n=Gi(e,t);if(e.tag!==0&&n===2){var r=Ua(e);r!==0&&(t=r,n=vl(e,r))}if(n===1)throw n=Eo,Rn(e,0),Yt(e,t),ze(e,de()),n;if(n===6)throw Error(R(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,wn(e,Oe,Ct),ze(e,de()),null}function Du(e,t){var n=H;H|=1;try{return e(t)}finally{H=n,H===0&&(gr=de()+500,ps&&hn())}}function jn(e){Zt!==null&&Zt.tag===0&&!(H&6)&&ur();var t=H;H|=1;var n=rt.transition,r=G;try{if(rt.transition=null,G=1,e)return e()}finally{G=r,rt.transition=n,H=t,!(H&6)&&hn()}}function Lu(){$e=qn.current,q(qn)}function Rn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,X0(n)),pe!==null)for(n=pe.return;n!==null;){var r=n;switch(hu(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Li();break;case 3:mr(),q(Ie),q(Ae),Tu();break;case 5:Eu(r);break;case 4:mr();break;case 13:q(re);break;case 19:q(re);break;case 10:yu(r.type._context);break;case 22:case 23:Lu()}n=n.return}if(we=e,pe=e=an(e.current,null),Ee=$e=t,ve=0,Eo=null,_u=gs=Ln=0,Oe=qr=null,Cn!==null){for(t=0;t<Cn.length;t++)if(n=Cn[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var s=i.next;i.next=o,r.next=s}n.pending=r}Cn=null}return e}function wm(e,t){do{var n=pe;try{if(gu(),hi.current=$i,Ui){for(var r=ie.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Ui=!1}if(Dn=0,ye=me=ie=null,Xr=!1,wo=0,Mu.current=null,n===null||n.return===null){ve=1,Eo=t,pe=null;break}e:{var i=e,s=n.return,a=n,l=t;if(t=Ee,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,c=a,d=c.tag;if(!(c.mode&1)&&(d===0||d===11||d===15)){var f=c.alternate;f?(c.updateQueue=f.updateQueue,c.memoizedState=f.memoizedState,c.lanes=f.lanes):(c.updateQueue=null,c.memoizedState=null)}var m=wd(s);if(m!==null){m.flags&=-257,xd(m,s,a,i,t),m.mode&1&&yd(i,u,t),t=m,l=u;var w=t.updateQueue;if(w===null){var y=new Set;y.add(l),t.updateQueue=y}else w.add(l);break e}else{if(!(t&1)){yd(i,u,t),ju();break e}l=Error(R(426))}}else if(ee&&a.mode&1){var x=wd(s);if(x!==null){!(x.flags&65536)&&(x.flags|=256),xd(x,s,a,i,t),mu(vr(l,a));break e}}i=l=vr(l,a),ve!==4&&(ve=2),qr===null?qr=[i]:qr.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var h=nm(i,l,t);dd(i,h);break e;case 1:a=l;var p=i.type,v=i.stateNode;if(!(i.flags&128)&&(typeof p.getDerivedStateFromError=="function"||v!==null&&typeof v.componentDidCatch=="function"&&(on===null||!on.has(v)))){i.flags|=65536,t&=-t,i.lanes|=t;var S=rm(i,a,t);dd(i,S);break e}}i=i.return}while(i!==null)}Em(n)}catch(E){t=E,pe===n&&n!==null&&(pe=n=n.return);continue}break}while(!0)}function xm(){var e=Wi.current;return Wi.current=$i,e===null?$i:e}function ju(){(ve===0||ve===3||ve===2)&&(ve=4),we===null||!(Ln&268435455)&&!(gs&268435455)||Yt(we,Ee)}function Gi(e,t){var n=H;H|=2;var r=xm();(we!==e||Ee!==t)&&(Ct=null,Rn(e,t));do try{S1();break}catch(o){wm(e,o)}while(!0);if(gu(),H=n,Wi.current=r,pe!==null)throw Error(R(261));return we=null,Ee=0,ve}function S1(){for(;pe!==null;)Sm(pe)}function E1(){for(;pe!==null&&!Gy();)Sm(pe)}function Sm(e){var t=Cm(e.alternate,e,$e);e.memoizedProps=e.pendingProps,t===null?Em(e):pe=t,Mu.current=null}function Em(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=m1(n,t),n!==null){n.flags&=32767,pe=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ve=6,pe=null;return}}else if(n=h1(n,t,$e),n!==null){pe=n;return}if(t=t.sibling,t!==null){pe=t;return}pe=t=e}while(t!==null);ve===0&&(ve=5)}function wn(e,t,n){var r=G,o=rt.transition;try{rt.transition=null,G=1,T1(e,t,n,r)}finally{rt.transition=o,G=r}return null}function T1(e,t,n,r){do ur();while(Zt!==null);if(H&6)throw Error(R(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(R(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(r0(e,i),e===we&&(pe=we=null,Ee=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Jo||(Jo=!0,Pm(Ri,function(){return ur(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=rt.transition,rt.transition=null;var s=G;G=1;var a=H;H|=4,Mu.current=null,g1(e,n),vm(n,e),$0(Qa),Mi=!!Ga,Qa=Ga=null,e.current=n,y1(n),Qy(),H=a,G=s,rt.transition=i}else e.current=n;if(Jo&&(Jo=!1,Zt=e,Ki=o),i=e.pendingLanes,i===0&&(on=null),Zy(n.stateNode),ze(e,de()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Hi)throw Hi=!1,e=hl,hl=null,e;return Ki&1&&e.tag!==0&&ur(),i=e.pendingLanes,i&1?e===ml?Jr++:(Jr=0,ml=e):Jr=0,hn(),null}function ur(){if(Zt!==null){var e=th(Ki),t=rt.transition,n=G;try{if(rt.transition=null,G=16>e?16:e,Zt===null)var r=!1;else{if(e=Zt,Zt=null,Ki=0,H&6)throw Error(R(331));var o=H;for(H|=4,D=e.current;D!==null;){var i=D,s=i.child;if(D.flags&16){var a=i.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(D=u;D!==null;){var c=D;switch(c.tag){case 0:case 11:case 15:Zr(8,c,i)}var d=c.child;if(d!==null)d.return=c,D=d;else for(;D!==null;){c=D;var f=c.sibling,m=c.return;if(pm(c),c===u){D=null;break}if(f!==null){f.return=m,D=f;break}D=m}}}var w=i.alternate;if(w!==null){var y=w.child;if(y!==null){w.child=null;do{var x=y.sibling;y.sibling=null,y=x}while(y!==null)}}D=i}}if(i.subtreeFlags&2064&&s!==null)s.return=i,D=s;else e:for(;D!==null;){if(i=D,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Zr(9,i,i.return)}var h=i.sibling;if(h!==null){h.return=i.return,D=h;break e}D=i.return}}var p=e.current;for(D=p;D!==null;){s=D;var v=s.child;if(s.subtreeFlags&2064&&v!==null)v.return=s,D=v;else e:for(s=p;D!==null;){if(a=D,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:vs(9,a)}}catch(E){ue(a,a.return,E)}if(a===s){D=null;break e}var S=a.sibling;if(S!==null){S.return=a.return,D=S;break e}D=a.return}}if(H=o,hn(),yt&&typeof yt.onPostCommitFiberRoot=="function")try{yt.onPostCommitFiberRoot(ls,e)}catch{}r=!0}return r}finally{G=n,rt.transition=t}}return!1}function Dd(e,t,n){t=vr(n,t),t=nm(e,t,1),e=rn(e,t,1),t=_e(),e!==null&&(Ao(e,1,t),ze(e,t))}function ue(e,t,n){if(e.tag===3)Dd(e,e,n);else for(;t!==null;){if(t.tag===3){Dd(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(on===null||!on.has(r))){e=vr(n,e),e=rm(t,e,1),t=rn(t,e,1),e=_e(),t!==null&&(Ao(t,1,e),ze(t,e));break}}t=t.return}}function C1(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=_e(),e.pingedLanes|=e.suspendedLanes&n,we===e&&(Ee&n)===n&&(ve===4||ve===3&&(Ee&130023424)===Ee&&500>de()-Nu?Rn(e,0):_u|=n),ze(e,t)}function Tm(e,t){t===0&&(e.mode&1?(t=$o,$o<<=1,!($o&130023424)&&($o=4194304)):t=1);var n=_e();e=Dt(e,t),e!==null&&(Ao(e,t,n),ze(e,n))}function P1(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Tm(e,n)}function k1(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(R(314))}r!==null&&r.delete(t),Tm(e,n)}var Cm;Cm=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ie.current)Ve=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ve=!1,p1(e,t,n);Ve=!!(e.flags&131072)}else Ve=!1,ee&&t.flags&1048576&&bh(t,Vi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;vi(e,t),e=t.pendingProps;var o=fr(t,Ae.current);lr(t,n),o=Pu(null,t,r,e,o,n);var i=ku();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Fe(r)?(i=!0,ji(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,xu(t),o.updater=hs,t.stateNode=o,o._reactInternals=t,rl(t,r,e,n),t=sl(null,t,r,!0,i,n)):(t.tag=0,ee&&i&&pu(t),Me(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(vi(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=R1(r),e=lt(r,e),o){case 0:t=il(null,t,r,e,n);break e;case 1:t=Td(null,t,r,e,n);break e;case 11:t=Sd(null,t,r,e,n);break e;case 14:t=Ed(null,t,r,lt(r.type,e),n);break e}throw Error(R(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:lt(r,o),il(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:lt(r,o),Td(e,t,r,o,n);case 3:e:{if(am(t),e===null)throw Error(R(387));r=t.pendingProps,i=t.memoizedState,o=i.element,_h(e,t),zi(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=vr(Error(R(423)),t),t=Cd(e,t,r,n,o);break e}else if(r!==o){o=vr(Error(R(424)),t),t=Cd(e,t,r,n,o);break e}else for(He=nn(t.stateNode.containerInfo.firstChild),Ke=t,ee=!0,ct=null,n=jh(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(pr(),r===o){t=Lt(e,t,n);break e}Me(e,t,r,n)}t=t.child}return t;case 5:return Oh(t),e===null&&el(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,s=o.children,Ya(r,o)?s=null:i!==null&&Ya(r,i)&&(t.flags|=32),sm(e,t),Me(e,t,s,n),t.child;case 6:return e===null&&el(t),null;case 13:return lm(e,t,n);case 4:return Su(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=hr(t,null,r,n):Me(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:lt(r,o),Sd(e,t,r,o,n);case 7:return Me(e,t,t.pendingProps,n),t.child;case 8:return Me(e,t,t.pendingProps.children,n),t.child;case 12:return Me(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,s=o.value,Y(Ii,r._currentValue),r._currentValue=s,i!==null)if(pt(i.value,s)){if(i.children===o.children&&!Ie.current){t=Lt(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var a=i.dependencies;if(a!==null){s=i.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(i.tag===1){l=Rt(-1,n&-n),l.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?l.next=l:(l.next=c.next,c.next=l),u.pending=l}}i.lanes|=n,l=i.alternate,l!==null&&(l.lanes|=n),tl(i.return,n,t),a.lanes|=n;break}l=l.next}}else if(i.tag===10)s=i.type===t.type?null:i.child;else if(i.tag===18){if(s=i.return,s===null)throw Error(R(341));s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),tl(s,n,t),s=i.sibling}else s=i.child;if(s!==null)s.return=i;else for(s=i;s!==null;){if(s===t){s=null;break}if(i=s.sibling,i!==null){i.return=s.return,s=i;break}s=s.return}i=s}Me(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,lr(t,n),o=ot(o),r=r(o),t.flags|=1,Me(e,t,r,n),t.child;case 14:return r=t.type,o=lt(r,t.pendingProps),o=lt(r.type,o),Ed(e,t,r,o,n);case 15:return om(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:lt(r,o),vi(e,t),t.tag=1,Fe(r)?(e=!0,ji(t)):e=!1,lr(t,n),Dh(t,r,o),rl(t,r,o,n),sl(null,t,r,!0,e,n);case 19:return um(e,t,n);case 22:return im(e,t,n)}throw Error(R(156,t.tag))};function Pm(e,t){return Zp(e,t)}function b1(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function nt(e,t,n,r){return new b1(e,t,n,r)}function Ou(e){return e=e.prototype,!(!e||!e.isReactComponent)}function R1(e){if(typeof e=="function")return Ou(e)?1:0;if(e!=null){if(e=e.$$typeof,e===tu)return 11;if(e===nu)return 14}return 2}function an(e,t){var n=e.alternate;return n===null?(n=nt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function wi(e,t,n,r,o,i){var s=2;if(r=e,typeof e=="function")Ou(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case Un:return An(n.children,o,i,t);case eu:s=8,o|=8;break;case ba:return e=nt(12,n,t,o|2),e.elementType=ba,e.lanes=i,e;case Ra:return e=nt(13,n,t,o),e.elementType=Ra,e.lanes=i,e;case Aa:return e=nt(19,n,t,o),e.elementType=Aa,e.lanes=i,e;case Lp:return ys(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Np:s=10;break e;case Dp:s=9;break e;case tu:s=11;break e;case nu:s=14;break e;case Kt:s=16,r=null;break e}throw Error(R(130,e==null?e:typeof e,""))}return t=nt(s,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function An(e,t,n,r){return e=nt(7,e,r,t),e.lanes=n,e}function ys(e,t,n,r){return e=nt(22,e,r,t),e.elementType=Lp,e.lanes=n,e.stateNode={isHidden:!1},e}function sa(e,t,n){return e=nt(6,e,null,t),e.lanes=n,e}function aa(e,t,n){return t=nt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function A1(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Bs(0),this.expirationTimes=Bs(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Bs(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Vu(e,t,n,r,o,i,s,a,l){return e=new A1(e,t,n,a,l),t===1?(t=1,i===!0&&(t|=8)):t=0,i=nt(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},xu(i),e}function M1(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Bn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function km(e){if(!e)return un;e=e._reactInternals;e:{if(In(e)!==e||e.tag!==1)throw Error(R(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Fe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(R(171))}if(e.tag===1){var n=e.type;if(Fe(n))return Ph(e,n,t)}return t}function bm(e,t,n,r,o,i,s,a,l){return e=Vu(n,r,!0,e,o,i,s,a,l),e.context=km(null),n=e.current,r=_e(),o=sn(n),i=Rt(r,o),i.callback=t??null,rn(n,i,o),e.current.lanes=o,Ao(e,o,r),ze(e,r),e}function ws(e,t,n,r){var o=t.current,i=_e(),s=sn(o);return n=km(n),t.context===null?t.context=n:t.pendingContext=n,t=Rt(i,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=rn(o,t,s),e!==null&&(ft(e,o,s,i),pi(e,o,s)),s}function Qi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Ld(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Iu(e,t){Ld(e,t),(e=e.alternate)&&Ld(e,t)}function _1(){return null}var Rm=typeof reportError=="function"?reportError:function(e){console.error(e)};function Fu(e){this._internalRoot=e}xs.prototype.render=Fu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(R(409));ws(e,t,null,null)};xs.prototype.unmount=Fu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;jn(function(){ws(null,e,null,null)}),t[Nt]=null}};function xs(e){this._internalRoot=e}xs.prototype.unstable_scheduleHydration=function(e){if(e){var t=oh();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Qt.length&&t!==0&&t<Qt[n].priority;n++);Qt.splice(n,0,e),n===0&&sh(e)}};function zu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ss(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function jd(){}function N1(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=Qi(s);i.call(u)}}var s=bm(t,r,e,0,null,!1,!1,"",jd);return e._reactRootContainer=s,e[Nt]=s.current,ho(e.nodeType===8?e.parentNode:e),jn(),s}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var a=r;r=function(){var u=Qi(l);a.call(u)}}var l=Vu(e,0,!1,null,null,!1,!1,"",jd);return e._reactRootContainer=l,e[Nt]=l.current,ho(e.nodeType===8?e.parentNode:e),jn(function(){ws(t,l,n,r)}),l}function Es(e,t,n,r,o){var i=n._reactRootContainer;if(i){var s=i;if(typeof o=="function"){var a=o;o=function(){var l=Qi(s);a.call(l)}}ws(t,s,e,o)}else s=N1(n,t,e,o,r);return Qi(s)}nh=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=zr(t.pendingLanes);n!==0&&(iu(t,n|1),ze(t,de()),!(H&6)&&(gr=de()+500,hn()))}break;case 13:jn(function(){var r=Dt(e,1);if(r!==null){var o=_e();ft(r,e,1,o)}}),Iu(e,1)}};su=function(e){if(e.tag===13){var t=Dt(e,134217728);if(t!==null){var n=_e();ft(t,e,134217728,n)}Iu(e,134217728)}};rh=function(e){if(e.tag===13){var t=sn(e),n=Dt(e,t);if(n!==null){var r=_e();ft(n,e,t,r)}Iu(e,t)}};oh=function(){return G};ih=function(e,t){var n=G;try{return G=e,t()}finally{G=n}};Fa=function(e,t,n){switch(t){case"input":if(Na(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=fs(r);if(!o)throw Error(R(90));Op(r),Na(r,o)}}}break;case"textarea":Ip(e,n);break;case"select":t=n.value,t!=null&&or(e,!!n.multiple,t,!1)}};Hp=Du;Kp=jn;var D1={usingClientEntryPoint:!1,Events:[_o,Kn,fs,$p,Wp,Du]},Lr={findFiberByHostInstance:Tn,bundleType:0,version:"18.2.0",rendererPackageName:"react-dom"},L1={bundleType:Lr.bundleType,version:Lr.version,rendererPackageName:Lr.rendererPackageName,rendererConfig:Lr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Vt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Yp(e),e===null?null:e.stateNode},findFiberByHostInstance:Lr.findFiberByHostInstance||_1,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.2.0-next-9e3b772b8-20220608"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ei=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ei.isDisabled&&ei.supportsFiber)try{ls=ei.inject(L1),yt=ei}catch{}}Xe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=D1;Xe.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!zu(t))throw Error(R(200));return M1(e,t,null,n)};Xe.createRoot=function(e,t){if(!zu(e))throw Error(R(299));var n=!1,r="",o=Rm;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=Vu(e,1,!1,null,null,n,!1,r,o),e[Nt]=t.current,ho(e.nodeType===8?e.parentNode:e),new Fu(t)};Xe.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(R(188)):(e=Object.keys(e).join(","),Error(R(268,e)));return e=Yp(t),e=e===null?null:e.stateNode,e};Xe.flushSync=function(e){return jn(e)};Xe.hydrate=function(e,t,n){if(!Ss(t))throw Error(R(200));return Es(null,e,t,!0,n)};Xe.hydrateRoot=function(e,t,n){if(!zu(e))throw Error(R(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",s=Rm;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=bm(t,null,e,1,n??null,o,!1,i,s),e[Nt]=t.current,ho(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new xs(t)};Xe.render=function(e,t,n){if(!Ss(t))throw Error(R(200));return Es(null,e,t,!1,n)};Xe.unmountComponentAtNode=function(e){if(!Ss(e))throw Error(R(40));return e._reactRootContainer?(jn(function(){Es(null,null,e,!1,function(){e._reactRootContainer=null,e[Nt]=null})}),!0):!1};Xe.unstable_batchedUpdates=Du;Xe.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ss(n))throw Error(R(200));if(e==null||e._reactInternals===void 0)throw Error(R(38));return Es(e,t,n,!1,r)};Xe.version="18.2.0-next-9e3b772b8-20220608";function Am(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Am)}catch(e){console.error(e)}}Am(),bp.exports=Xe;var Bu=bp.exports;const Mm=Kl(Bu);/**
 * @remix-run/router v1.15.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Yi(){return Yi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Yi.apply(this,arguments)}var qt;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(qt||(qt={}));const Od="popstate";function j1(e){e===void 0&&(e={});function t(r,o){let{pathname:i,search:s,hash:a}=r.location;return yl("",{pathname:i,search:s,hash:a},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function n(r,o){return typeof o=="string"?o:Nm(o)}return V1(t,n,null,e)}function Be(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function _m(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function O1(){return Math.random().toString(36).substr(2,8)}function Vd(e,t){return{usr:e.state,key:e.key,idx:t}}function yl(e,t,n,r){return n===void 0&&(n=null),Yi({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Ts(t):t,{state:n,key:t&&t.key||r||O1()})}function Nm(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function Ts(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function V1(e,t,n,r){r===void 0&&(r={});let{window:o=document.defaultView,v5Compat:i=!1}=r,s=o.history,a=qt.Pop,l=null,u=c();u==null&&(u=0,s.replaceState(Yi({},s.state,{idx:u}),""));function c(){return(s.state||{idx:null}).idx}function d(){a=qt.Pop;let x=c(),h=x==null?null:x-u;u=x,l&&l({action:a,location:y.location,delta:h})}function f(x,h){a=qt.Push;let p=yl(y.location,x,h);n&&n(p,x),u=c()+1;let v=Vd(p,u),S=y.createHref(p);try{s.pushState(v,"",S)}catch(E){if(E instanceof DOMException&&E.name==="DataCloneError")throw E;o.location.assign(S)}i&&l&&l({action:a,location:y.location,delta:1})}function m(x,h){a=qt.Replace;let p=yl(y.location,x,h);n&&n(p,x),u=c();let v=Vd(p,u),S=y.createHref(p);s.replaceState(v,"",S),i&&l&&l({action:a,location:y.location,delta:0})}function w(x){let h=o.location.origin!=="null"?o.location.origin:o.location.href,p=typeof x=="string"?x:Nm(x);return p=p.replace(/ $/,"%20"),Be(h,"No window.location.(origin|href) available to create URL for href: "+p),new URL(p,h)}let y={get action(){return a},get location(){return e(o,s)},listen(x){if(l)throw new Error("A history only accepts one active listener");return o.addEventListener(Od,d),l=x,()=>{o.removeEventListener(Od,d),l=null}},createHref(x){return t(o,x)},createURL:w,encodeLocation(x){let h=w(x);return{pathname:h.pathname,search:h.search,hash:h.hash}},push:f,replace:m,go(x){return s.go(x)}};return y}var Id;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Id||(Id={}));function I1(e,t,n){n===void 0&&(n="/");let r=typeof t=="string"?Ts(t):t,o=jm(r.pathname||"/",n);if(o==null)return null;let i=Dm(e);F1(i);let s=null;for(let a=0;s==null&&a<i.length;++a){let l=Z1(o);s=Q1(i[a],l)}return s}function Dm(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let o=(i,s,a)=>{let l={relativePath:a===void 0?i.path||"":a,caseSensitive:i.caseSensitive===!0,childrenIndex:s,route:i};l.relativePath.startsWith("/")&&(Be(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let u=cr([r,l.relativePath]),c=n.concat(l);i.children&&i.children.length>0&&(Be(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),Dm(i.children,t,c,u)),!(i.path==null&&!i.index)&&t.push({path:u,score:K1(u,i.index),routesMeta:c})};return e.forEach((i,s)=>{var a;if(i.path===""||!((a=i.path)!=null&&a.includes("?")))o(i,s);else for(let l of Lm(i.path))o(i,s,l)}),t}function Lm(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,o=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return o?[i,""]:[i];let s=Lm(r.join("/")),a=[];return a.push(...s.map(l=>l===""?i:[i,l].join("/"))),o&&a.push(...s),a.map(l=>e.startsWith("/")&&l===""?"/":l)}function F1(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:G1(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const z1=/^:[\w-]+$/,B1=3,U1=2,$1=1,W1=10,H1=-2,Fd=e=>e==="*";function K1(e,t){let n=e.split("/"),r=n.length;return n.some(Fd)&&(r+=H1),t&&(r+=U1),n.filter(o=>!Fd(o)).reduce((o,i)=>o+(z1.test(i)?B1:i===""?$1:W1),r)}function G1(e,t){return e.length===t.length&&e.slice(0,-1).every((r,o)=>r===t[o])?e[e.length-1]-t[t.length-1]:0}function Q1(e,t){let{routesMeta:n}=e,r={},o="/",i=[];for(let s=0;s<n.length;++s){let a=n[s],l=s===n.length-1,u=o==="/"?t:t.slice(o.length)||"/",c=Y1({path:a.relativePath,caseSensitive:a.caseSensitive,end:l},u);if(!c)return null;Object.assign(r,c.params);let d=a.route;i.push({params:r,pathname:cr([o,c.pathname]),pathnameBase:q1(cr([o,c.pathnameBase])),route:d}),c.pathnameBase!=="/"&&(o=cr([o,c.pathnameBase]))}return i}function Y1(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=X1(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let i=o[0],s=i.replace(/(.)\/+$/,"$1"),a=o.slice(1);return{params:r.reduce((u,c,d)=>{let{paramName:f,isOptional:m}=c;if(f==="*"){let y=a[d]||"";s=i.slice(0,i.length-y.length).replace(/(.)\/+$/,"$1")}const w=a[d];return m&&!w?u[f]=void 0:u[f]=(w||"").replace(/%2F/g,"/"),u},{}),pathname:i,pathnameBase:s,pattern:e}}function X1(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),_m(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(s,a,l)=>(r.push({paramName:a,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function Z1(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return _m(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function jm(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}const cr=e=>e.join("/").replace(/\/\/+/g,"/"),q1=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/");function J1(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Om=["post","put","patch","delete"];new Set(Om);const ew=["get",...Om];new Set(ew);/**
 * React Router v6.22.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Xi(){return Xi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Xi.apply(this,arguments)}const tw=g.createContext(null),nw=g.createContext(null),Vm=g.createContext(null),Cs=g.createContext(null),Ps=g.createContext({outlet:null,matches:[],isDataRoute:!1}),Im=g.createContext(null);function Uu(){return g.useContext(Cs)!=null}function rw(){return Uu()||Be(!1),g.useContext(Cs).location}function ow(e,t){return iw(e,t)}function iw(e,t,n,r){Uu()||Be(!1);let{navigator:o}=g.useContext(Vm),{matches:i}=g.useContext(Ps),s=i[i.length-1],a=s?s.params:{};s&&s.pathname;let l=s?s.pathnameBase:"/";s&&s.route;let u=rw(),c;if(t){var d;let x=typeof t=="string"?Ts(t):t;l==="/"||(d=x.pathname)!=null&&d.startsWith(l)||Be(!1),c=x}else c=u;let f=c.pathname||"/",m=f;if(l!=="/"){let x=l.replace(/^\//,"").split("/");m="/"+f.replace(/^\//,"").split("/").slice(x.length).join("/")}let w=I1(e,{pathname:m}),y=cw(w&&w.map(x=>Object.assign({},x,{params:Object.assign({},a,x.params),pathname:cr([l,o.encodeLocation?o.encodeLocation(x.pathname).pathname:x.pathname]),pathnameBase:x.pathnameBase==="/"?l:cr([l,o.encodeLocation?o.encodeLocation(x.pathnameBase).pathname:x.pathnameBase])})),i,n,r);return t&&y?g.createElement(Cs.Provider,{value:{location:Xi({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:qt.Pop}},y):y}function sw(){let e=hw(),t=J1(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return g.createElement(g.Fragment,null,g.createElement("h2",null,"Unexpected Application Error!"),g.createElement("h3",{style:{fontStyle:"italic"}},t),n?g.createElement("pre",{style:o},n):null,null)}const aw=g.createElement(sw,null);class lw extends g.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?g.createElement(Ps.Provider,{value:this.props.routeContext},g.createElement(Im.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function uw(e){let{routeContext:t,match:n,children:r}=e,o=g.useContext(tw);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),g.createElement(Ps.Provider,{value:t},r)}function cw(e,t,n,r){var o;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var i;if((i=n)!=null&&i.errors)e=n.matches;else return null}let s=e,a=(o=n)==null?void 0:o.errors;if(a!=null){let c=s.findIndex(d=>d.route.id&&(a==null?void 0:a[d.route.id]));c>=0||Be(!1),s=s.slice(0,Math.min(s.length,c+1))}let l=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let c=0;c<s.length;c++){let d=s[c];if((d.route.HydrateFallback||d.route.hydrateFallbackElement)&&(u=c),d.route.id){let{loaderData:f,errors:m}=n,w=d.route.loader&&f[d.route.id]===void 0&&(!m||m[d.route.id]===void 0);if(d.route.lazy||w){l=!0,u>=0?s=s.slice(0,u+1):s=[s[0]];break}}}return s.reduceRight((c,d,f)=>{let m,w=!1,y=null,x=null;n&&(m=a&&d.route.id?a[d.route.id]:void 0,y=d.route.errorElement||aw,l&&(u<0&&f===0?(mw("route-fallback",!1),w=!0,x=null):u===f&&(w=!0,x=d.route.hydrateFallbackElement||null)));let h=t.concat(s.slice(0,f+1)),p=()=>{let v;return m?v=y:w?v=x:d.route.Component?v=g.createElement(d.route.Component,null):d.route.element?v=d.route.element:v=c,g.createElement(uw,{match:d,routeContext:{outlet:c,matches:h,isDataRoute:n!=null},children:v})};return n&&(d.route.ErrorBoundary||d.route.errorElement||f===0)?g.createElement(lw,{location:n.location,revalidation:n.revalidation,component:y,error:m,children:p(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):p()},null)}var wl=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(wl||{});function dw(e){let t=g.useContext(nw);return t||Be(!1),t}function fw(e){let t=g.useContext(Ps);return t||Be(!1),t}function pw(e){let t=fw(),n=t.matches[t.matches.length-1];return n.route.id||Be(!1),n.route.id}function hw(){var e;let t=g.useContext(Im),n=dw(wl.UseRouteError),r=pw(wl.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}const zd={};function mw(e,t,n){!t&&!zd[e]&&(zd[e]=!0)}function xl(e){Be(!1)}function vw(e){let{basename:t="/",children:n=null,location:r,navigationType:o=qt.Pop,navigator:i,static:s=!1,future:a}=e;Uu()&&Be(!1);let l=t.replace(/^\/*/,"/"),u=g.useMemo(()=>({basename:l,navigator:i,static:s,future:Xi({v7_relativeSplatPath:!1},a)}),[l,a,i,s]);typeof r=="string"&&(r=Ts(r));let{pathname:c="/",search:d="",hash:f="",state:m=null,key:w="default"}=r,y=g.useMemo(()=>{let x=jm(c,l);return x==null?null:{location:{pathname:x,search:d,hash:f,state:m,key:w},navigationType:o}},[l,c,d,f,m,w,o]);return y==null?null:g.createElement(Vm.Provider,{value:u},g.createElement(Cs.Provider,{children:n,value:y}))}function gw(e){let{children:t,location:n}=e;return ow(Sl(t),n)}new Promise(()=>{});function Sl(e,t){t===void 0&&(t=[]);let n=[];return g.Children.forEach(e,(r,o)=>{if(!g.isValidElement(r))return;let i=[...t,o];if(r.type===g.Fragment){n.push.apply(n,Sl(r.props.children,i));return}r.type!==xl&&Be(!1),!r.props.index||!r.props.children||Be(!1);let s={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(s.children=Sl(r.props.children,i)),n.push(s)}),n}/**
 * React Router DOM v6.22.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const yw="6";try{window.__reactRouterVersion=yw}catch{}const ww="startTransition",Bd=Py[ww];function xw(e){let{basename:t,children:n,future:r,window:o}=e,i=g.useRef();i.current==null&&(i.current=j1({window:o,v5Compat:!0}));let s=i.current,[a,l]=g.useState({action:s.action,location:s.location}),{v7_startTransition:u}=r||{},c=g.useCallback(d=>{u&&Bd?Bd(()=>l(d)):l(d)},[l,u]);return g.useLayoutEffect(()=>s.listen(c),[s,c]),g.createElement(vw,{basename:t,children:n,location:a.location,navigationType:a.action,navigator:s,future:r})}var Ud;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Ud||(Ud={}));var $d;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})($d||($d={}));const Sw="modulepreload",Ew=function(e){return"/"+e},Wd={},Tw=function(t,n,r){let o=Promise.resolve();if(n&&n.length>0){const i=document.getElementsByTagName("link");o=Promise.all(n.map(s=>{if(s=Ew(s),s in Wd)return;Wd[s]=!0;const a=s.endsWith(".css"),l=a?'[rel="stylesheet"]':"";if(!!r)for(let d=i.length-1;d>=0;d--){const f=i[d];if(f.href===s&&(!a||f.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${s}"]${l}`))return;const c=document.createElement("link");if(c.rel=a?"stylesheet":Sw,a||(c.as="script",c.crossOrigin=""),c.href=s,document.head.appendChild(c),a)return new Promise((d,f)=>{c.addEventListener("load",d),c.addEventListener("error",()=>f(new Error(`Unable to preload CSS for ${s}`)))})}))}return o.then(()=>t()).catch(i=>{const s=new Event("vite:preloadError",{cancelable:!0});if(s.payload=i,window.dispatchEvent(s),!s.defaultPrevented)throw i})},$u=g.createContext({});function Wu(e){const t=g.useRef(null);return t.current===null&&(t.current=e()),t.current}const ks=g.createContext(null),Hu=g.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"});class Cw extends g.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function Pw({children:e,isPresent:t}){const n=g.useId(),r=g.useRef(null),o=g.useRef({width:0,height:0,top:0,left:0}),{nonce:i}=g.useContext(Hu);return g.useInsertionEffect(()=>{const{width:s,height:a,top:l,left:u}=o.current;if(t||!r.current||!s||!a)return;r.current.dataset.motionPopId=n;const c=document.createElement("style");return i&&(c.nonce=i),document.head.appendChild(c),c.sheet&&c.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${s}px !important;
            height: ${a}px !important;
            top: ${l}px !important;
            left: ${u}px !important;
          }
        `),()=>{document.head.removeChild(c)}},[t]),T.jsx(Cw,{isPresent:t,childRef:r,sizeRef:o,children:g.cloneElement(e,{ref:r})})}const kw=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:o,presenceAffectsLayout:i,mode:s})=>{const a=Wu(bw),l=g.useId(),u=g.useCallback(d=>{a.set(d,!0);for(const f of a.values())if(!f)return;r&&r()},[a,r]),c=g.useMemo(()=>({id:l,initial:t,isPresent:n,custom:o,onExitComplete:u,register:d=>(a.set(d,!1),()=>a.delete(d))}),i?[Math.random(),u]:[n,u]);return g.useMemo(()=>{a.forEach((d,f)=>a.set(f,!1))},[n]),g.useEffect(()=>{!n&&!a.size&&r&&r()},[n]),s==="popLayout"&&(e=T.jsx(Pw,{isPresent:n,children:e})),T.jsx(ks.Provider,{value:c,children:e})};function bw(){return new Map}function Fm(e=!0){const t=g.useContext(ks);if(t===null)return[!0,null];const{isPresent:n,onExitComplete:r,register:o}=t,i=g.useId();g.useEffect(()=>{e&&o(i)},[e]);const s=g.useCallback(()=>e&&r&&r(i),[i,r,e]);return!n&&r?[!1,s]:[!0]}const ti=e=>e.key||"";function Hd(e){const t=[];return g.Children.forEach(e,n=>{g.isValidElement(n)&&t.push(n)}),t}const Ku=typeof window<"u",zm=Ku?g.useLayoutEffect:g.useEffect,Kd=({children:e,custom:t,initial:n=!0,onExitComplete:r,presenceAffectsLayout:o=!0,mode:i="sync",propagate:s=!1})=>{const[a,l]=Fm(s),u=g.useMemo(()=>Hd(e),[e]),c=s&&!a?[]:u.map(ti),d=g.useRef(!0),f=g.useRef(u),m=Wu(()=>new Map),[w,y]=g.useState(u),[x,h]=g.useState(u);zm(()=>{d.current=!1,f.current=u;for(let S=0;S<x.length;S++){const E=ti(x[S]);c.includes(E)?m.delete(E):m.get(E)!==!0&&m.set(E,!1)}},[x,c.length,c.join("-")]);const p=[];if(u!==w){let S=[...u];for(let E=0;E<x.length;E++){const P=x[E],k=ti(P);c.includes(k)||(S.splice(E,0,P),p.push(P))}i==="wait"&&p.length&&(S=p),h(Hd(S)),y(u);return}const{forceRender:v}=g.useContext($u);return T.jsx(T.Fragment,{children:x.map(S=>{const E=ti(S),P=s&&!a?!1:u===x||c.includes(E),k=()=>{if(m.has(E))m.set(E,!0);else return;let C=!0;m.forEach(L=>{L||(C=!1)}),C&&(v==null||v(),h(f.current),s&&(l==null||l()),r&&r())};return T.jsx(kw,{isPresent:P,initial:!d.current||n?void 0:!1,custom:P?void 0:t,presenceAffectsLayout:o,mode:i,onExitComplete:P?void 0:k,children:S},E)})})},Ge=e=>e;let Bm=Ge;function Gu(e){let t;return()=>(t===void 0&&(t=e()),t)}const yr=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},At=e=>e*1e3,Mt=e=>e/1e3,Rw={skipAnimations:!1,useManualTiming:!1};function Aw(e){let t=new Set,n=new Set,r=!1,o=!1;const i=new WeakSet;let s={delta:0,timestamp:0,isProcessing:!1};function a(u){i.has(u)&&(l.schedule(u),e()),u(s)}const l={schedule:(u,c=!1,d=!1)=>{const m=d&&r?t:n;return c&&i.add(u),m.has(u)||m.add(u),u},cancel:u=>{n.delete(u),i.delete(u)},process:u=>{if(s=u,r){o=!0;return}r=!0,[t,n]=[n,t],t.forEach(a),t.clear(),r=!1,o&&(o=!1,l.process(u))}};return l}const ni=["read","resolveKeyframes","update","preRender","render","postRender"],Mw=40;function Um(e,t){let n=!1,r=!0;const o={delta:0,timestamp:0,isProcessing:!1},i=()=>n=!0,s=ni.reduce((h,p)=>(h[p]=Aw(i),h),{}),{read:a,resolveKeyframes:l,update:u,preRender:c,render:d,postRender:f}=s,m=()=>{const h=performance.now();n=!1,o.delta=r?1e3/60:Math.max(Math.min(h-o.timestamp,Mw),1),o.timestamp=h,o.isProcessing=!0,a.process(o),l.process(o),u.process(o),c.process(o),d.process(o),f.process(o),o.isProcessing=!1,n&&t&&(r=!1,e(m))},w=()=>{n=!0,r=!0,o.isProcessing||e(m)};return{schedule:ni.reduce((h,p)=>{const v=s[p];return h[p]=(S,E=!1,P=!1)=>(n||w(),v.schedule(S,E,P)),h},{}),cancel:h=>{for(let p=0;p<ni.length;p++)s[ni[p]].cancel(h)},state:o,steps:s}}const{schedule:J,cancel:cn,state:Se,steps:la}=Um(typeof requestAnimationFrame<"u"?requestAnimationFrame:Ge,!0),$m=g.createContext({strict:!1}),Gd={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},wr={};for(const e in Gd)wr[e]={isEnabled:t=>Gd[e].some(n=>!!t[n])};function _w(e){for(const t in e)wr[t]={...wr[t],...e[t]}}const Nw=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Zi(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||Nw.has(e)}let Wm=e=>!Zi(e);function Dw(e){e&&(Wm=t=>t.startsWith("on")?!Zi(t):e(t))}try{Dw(require("@emotion/is-prop-valid").default)}catch{}function Lw(e,t,n){const r={};for(const o in e)o==="values"&&typeof e.values=="object"||(Wm(o)||n===!0&&Zi(o)||!t&&!Zi(o)||e.draggable&&o.startsWith("onDrag"))&&(r[o]=e[o]);return r}function jw(e){if(typeof Proxy>"u")return e;const t=new Map,n=(...r)=>e(...r);return new Proxy(n,{get:(r,o)=>o==="create"?e:(t.has(o)||t.set(o,e(o)),t.get(o))})}const bs=g.createContext({});function To(e){return typeof e=="string"||Array.isArray(e)}function Rs(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Qu=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Yu=["initial",...Qu];function As(e){return Rs(e.animate)||Yu.some(t=>To(e[t]))}function Hm(e){return!!(As(e)||e.variants)}function Ow(e,t){if(As(e)){const{initial:n,animate:r}=e;return{initial:n===!1||To(n)?n:void 0,animate:To(r)?r:void 0}}return e.inherit!==!1?t:{}}function Vw(e){const{initial:t,animate:n}=Ow(e,g.useContext(bs));return g.useMemo(()=>({initial:t,animate:n}),[Qd(t),Qd(n)])}function Qd(e){return Array.isArray(e)?e.join(" "):e}const Iw=Symbol.for("motionComponentSymbol");function Jn(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function Fw(e,t,n){return g.useCallback(r=>{r&&e.onMount&&e.onMount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):Jn(n)&&(n.current=r))},[t])}const Xu=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),zw="framerAppearId",Km="data-"+Xu(zw),{schedule:Zu,cancel:gk}=Um(queueMicrotask,!1),Gm=g.createContext({});function Bw(e,t,n,r,o){var i,s;const{visualElement:a}=g.useContext(bs),l=g.useContext($m),u=g.useContext(ks),c=g.useContext(Hu).reducedMotion,d=g.useRef(null);r=r||l.renderer,!d.current&&r&&(d.current=r(e,{visualState:t,parent:a,props:n,presenceContext:u,blockInitialAnimation:u?u.initial===!1:!1,reducedMotionConfig:c}));const f=d.current,m=g.useContext(Gm);f&&!f.projection&&o&&(f.type==="html"||f.type==="svg")&&Uw(d.current,n,o,m);const w=g.useRef(!1);g.useInsertionEffect(()=>{f&&w.current&&f.update(n,u)});const y=n[Km],x=g.useRef(!!y&&!(!((i=window.MotionHandoffIsComplete)===null||i===void 0)&&i.call(window,y))&&((s=window.MotionHasOptimisedAnimation)===null||s===void 0?void 0:s.call(window,y)));return zm(()=>{f&&(w.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),Zu.render(f.render),x.current&&f.animationState&&f.animationState.animateChanges())}),g.useEffect(()=>{f&&(!x.current&&f.animationState&&f.animationState.animateChanges(),x.current&&(queueMicrotask(()=>{var h;(h=window.MotionHandoffMarkAsComplete)===null||h===void 0||h.call(window,y)}),x.current=!1))}),f}function Uw(e,t,n,r){const{layoutId:o,layout:i,drag:s,dragConstraints:a,layoutScroll:l,layoutRoot:u}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:Qm(e.parent)),e.projection.setOptions({layoutId:o,layout:i,alwaysMeasureLayout:!!s||a&&Jn(a),visualElement:e,animationType:typeof i=="string"?i:"both",initialPromotionConfig:r,layoutScroll:l,layoutRoot:u})}function Qm(e){if(e)return e.options.allowProjection!==!1?e.projection:Qm(e.parent)}function $w({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:o}){var i,s;e&&_w(e);function a(u,c){let d;const f={...g.useContext(Hu),...u,layoutId:Ww(u)},{isStatic:m}=f,w=Vw(u),y=r(u,m);if(!m&&Ku){Hw();const x=Kw(f);d=x.MeasureLayout,w.visualElement=Bw(o,y,f,t,x.ProjectionNode)}return T.jsxs(bs.Provider,{value:w,children:[d&&w.visualElement?T.jsx(d,{visualElement:w.visualElement,...f}):null,n(o,u,Fw(y,w.visualElement,c),y,m,w.visualElement)]})}a.displayName=`motion.${typeof o=="string"?o:`create(${(s=(i=o.displayName)!==null&&i!==void 0?i:o.name)!==null&&s!==void 0?s:""})`}`;const l=g.forwardRef(a);return l[Iw]=o,l}function Ww({layoutId:e}){const t=g.useContext($u).id;return t&&e!==void 0?t+"-"+e:e}function Hw(e,t){g.useContext($m).strict}function Kw(e){const{drag:t,layout:n}=wr;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t!=null&&t.isEnabled(e)||n!=null&&n.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const Gw=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function qu(e){return typeof e!="string"||e.includes("-")?!1:!!(Gw.indexOf(e)>-1||/[A-Z]/u.test(e))}function Yd(e){const t=[{},{}];return e==null||e.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function Ju(e,t,n,r){if(typeof t=="function"){const[o,i]=Yd(r);t=t(n!==void 0?n:e.custom,o,i)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[o,i]=Yd(r);t=t(n!==void 0?n:e.custom,o,i)}return t}const El=e=>Array.isArray(e),Qw=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),Yw=e=>El(e)?e[e.length-1]||0:e,Re=e=>!!(e&&e.getVelocity);function xi(e){const t=Re(e)?e.get():e;return Qw(t)?t.toValue():t}function Xw({scrapeMotionValuesFromProps:e,createRenderState:t,onUpdate:n},r,o,i){const s={latestValues:Zw(r,o,i,e),renderState:t()};return n&&(s.onMount=a=>n({props:r,current:a,...s}),s.onUpdate=a=>n(a)),s}const Ym=e=>(t,n)=>{const r=g.useContext(bs),o=g.useContext(ks),i=()=>Xw(e,t,r,o);return n?i():Wu(i)};function Zw(e,t,n,r){const o={},i=r(e,{});for(const f in i)o[f]=xi(i[f]);let{initial:s,animate:a}=e;const l=As(e),u=Hm(e);t&&u&&!l&&e.inherit!==!1&&(s===void 0&&(s=t.initial),a===void 0&&(a=t.animate));let c=n?n.initial===!1:!1;c=c||s===!1;const d=c?a:s;if(d&&typeof d!="boolean"&&!Rs(d)){const f=Array.isArray(d)?d:[d];for(let m=0;m<f.length;m++){const w=Ju(e,f[m]);if(w){const{transitionEnd:y,transition:x,...h}=w;for(const p in h){let v=h[p];if(Array.isArray(v)){const S=c?v.length-1:0;v=v[S]}v!==null&&(o[p]=v)}for(const p in y)o[p]=y[p]}}}return o}const Cr=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Fn=new Set(Cr),Xm=e=>t=>typeof t=="string"&&t.startsWith(e),Zm=Xm("--"),qw=Xm("var(--"),ec=e=>qw(e)?Jw.test(e.split("/*")[0].trim()):!1,Jw=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,qm=(e,t)=>t&&typeof e=="number"?t.transform(e):e,jt=(e,t,n)=>n>t?t:n<e?e:n,Pr={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},Co={...Pr,transform:e=>jt(0,1,e)},ri={...Pr,default:1},Do=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),Ht=Do("deg"),xt=Do("%"),V=Do("px"),ex=Do("vh"),tx=Do("vw"),Xd={...xt,parse:e=>xt.parse(e)/100,transform:e=>xt.transform(e*100)},nx={borderWidth:V,borderTopWidth:V,borderRightWidth:V,borderBottomWidth:V,borderLeftWidth:V,borderRadius:V,radius:V,borderTopLeftRadius:V,borderTopRightRadius:V,borderBottomRightRadius:V,borderBottomLeftRadius:V,width:V,maxWidth:V,height:V,maxHeight:V,top:V,right:V,bottom:V,left:V,padding:V,paddingTop:V,paddingRight:V,paddingBottom:V,paddingLeft:V,margin:V,marginTop:V,marginRight:V,marginBottom:V,marginLeft:V,backgroundPositionX:V,backgroundPositionY:V},rx={rotate:Ht,rotateX:Ht,rotateY:Ht,rotateZ:Ht,scale:ri,scaleX:ri,scaleY:ri,scaleZ:ri,skew:Ht,skewX:Ht,skewY:Ht,distance:V,translateX:V,translateY:V,translateZ:V,x:V,y:V,z:V,perspective:V,transformPerspective:V,opacity:Co,originX:Xd,originY:Xd,originZ:V},Zd={...Pr,transform:Math.round},tc={...nx,...rx,zIndex:Zd,size:V,fillOpacity:Co,strokeOpacity:Co,numOctaves:Zd},ox={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},ix=Cr.length;function sx(e,t,n){let r="",o=!0;for(let i=0;i<ix;i++){const s=Cr[i],a=e[s];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(s.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||n){const u=qm(a,tc[s]);if(!l){o=!1;const c=ox[s]||s;r+=`${c}(${u}) `}n&&(t[s]=u)}}return r=r.trim(),n?r=n(t,o?"":r):o&&(r="none"),r}function nc(e,t,n){const{style:r,vars:o,transformOrigin:i}=e;let s=!1,a=!1;for(const l in t){const u=t[l];if(Fn.has(l)){s=!0;continue}else if(Zm(l)){o[l]=u;continue}else{const c=qm(u,tc[l]);l.startsWith("origin")?(a=!0,i[l]=c):r[l]=c}}if(t.transform||(s||n?r.transform=sx(t,e.transform,n):r.transform&&(r.transform="none")),a){const{originX:l="50%",originY:u="50%",originZ:c=0}=i;r.transformOrigin=`${l} ${u} ${c}`}}const ax={offset:"stroke-dashoffset",array:"stroke-dasharray"},lx={offset:"strokeDashoffset",array:"strokeDasharray"};function ux(e,t,n=1,r=0,o=!0){e.pathLength=1;const i=o?ax:lx;e[i.offset]=V.transform(-r);const s=V.transform(t),a=V.transform(n);e[i.array]=`${s} ${a}`}function qd(e,t,n){return typeof e=="string"?e:V.transform(t+n*e)}function cx(e,t,n){const r=qd(t,e.x,e.width),o=qd(n,e.y,e.height);return`${r} ${o}`}function rc(e,{attrX:t,attrY:n,attrScale:r,originX:o,originY:i,pathLength:s,pathSpacing:a=1,pathOffset:l=0,...u},c,d){if(nc(e,u,d),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:f,style:m,dimensions:w}=e;f.transform&&(w&&(m.transform=f.transform),delete f.transform),w&&(o!==void 0||i!==void 0||m.transform)&&(m.transformOrigin=cx(w,o!==void 0?o:.5,i!==void 0?i:.5)),t!==void 0&&(f.x=t),n!==void 0&&(f.y=n),r!==void 0&&(f.scale=r),s!==void 0&&ux(f,s,a,l,!1)}const oc=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),Jm=()=>({...oc(),attrs:{}}),ic=e=>typeof e=="string"&&e.toLowerCase()==="svg";function ev(e,{style:t,vars:n},r,o){Object.assign(e.style,t,o&&o.getProjectionStyles(r));for(const i in n)e.style.setProperty(i,n[i])}const tv=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function nv(e,t,n,r){ev(e,t,void 0,r);for(const o in t.attrs)e.setAttribute(tv.has(o)?o:Xu(o),t.attrs[o])}const qi={};function dx(e){Object.assign(qi,e)}function rv(e,{layout:t,layoutId:n}){return Fn.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!qi[e]||e==="opacity")}function sc(e,t,n){var r;const{style:o}=e,i={};for(const s in o)(Re(o[s])||t.style&&Re(t.style[s])||rv(s,e)||((r=n==null?void 0:n.getValue(s))===null||r===void 0?void 0:r.liveStyle)!==void 0)&&(i[s]=o[s]);return i}function ov(e,t,n){const r=sc(e,t,n);for(const o in e)if(Re(e[o])||Re(t[o])){const i=Cr.indexOf(o)!==-1?"attr"+o.charAt(0).toUpperCase()+o.substring(1):o;r[i]=e[o]}return r}function fx(e,t){try{t.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{t.dimensions={x:0,y:0,width:0,height:0}}}const Jd=["x","y","width","height","cx","cy","r"],px={useVisualState:Ym({scrapeMotionValuesFromProps:ov,createRenderState:Jm,onUpdate:({props:e,prevProps:t,current:n,renderState:r,latestValues:o})=>{if(!n)return;let i=!!e.drag;if(!i){for(const a in o)if(Fn.has(a)){i=!0;break}}if(!i)return;let s=!t;if(t)for(let a=0;a<Jd.length;a++){const l=Jd[a];e[l]!==t[l]&&(s=!0)}s&&J.read(()=>{fx(n,r),J.render(()=>{rc(r,o,ic(n.tagName),e.transformTemplate),nv(n,r)})})}})},hx={useVisualState:Ym({scrapeMotionValuesFromProps:sc,createRenderState:oc})};function iv(e,t,n){for(const r in t)!Re(t[r])&&!rv(r,n)&&(e[r]=t[r])}function mx({transformTemplate:e},t){return g.useMemo(()=>{const n=oc();return nc(n,t,e),Object.assign({},n.vars,n.style)},[t])}function vx(e,t){const n=e.style||{},r={};return iv(r,n,e),Object.assign(r,mx(e,t)),r}function gx(e,t){const n={},r=vx(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}function yx(e,t,n,r){const o=g.useMemo(()=>{const i=Jm();return rc(i,t,ic(r),e.transformTemplate),{...i.attrs,style:{...i.style}}},[t]);if(e.style){const i={};iv(i,e.style,e),o.style={...i,...o.style}}return o}function wx(e=!1){return(n,r,o,{latestValues:i},s)=>{const l=(qu(n)?yx:gx)(r,i,s,n),u=Lw(r,typeof n=="string",e),c=n!==g.Fragment?{...u,...l,ref:o}:{},{children:d}=r,f=g.useMemo(()=>Re(d)?d.get():d,[d]);return g.createElement(n,{...c,children:f})}}function xx(e,t){return function(r,{forwardMotionProps:o}={forwardMotionProps:!1}){const s={...qu(r)?px:hx,preloadedFeatures:e,useRender:wx(o),createVisualElement:t,Component:r};return $w(s)}}function sv(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function Ms(e,t,n){const r=e.getProps();return Ju(r,t,n!==void 0?n:r.custom,e)}const Sx=Gu(()=>window.ScrollTimeline!==void 0);class Ex{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,n){for(let r=0;r<this.animations.length;r++)this.animations[r][t]=n}attachTimeline(t,n){const r=this.animations.map(o=>{if(Sx()&&o.attachTimeline)return o.attachTimeline(t);if(typeof n=="function")return n(o)});return()=>{r.forEach((o,i)=>{o&&o(),this.animations[i].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let n=0;n<this.animations.length;n++)t=Math.max(t,this.animations[n].duration);return t}runAll(t){this.animations.forEach(n=>n[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class Tx extends Ex{then(t,n){return Promise.all(this.animations).then(t).catch(n)}}function ac(e,t){return e?e[t]||e.default||e:void 0}const Tl=2e4;function av(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<Tl;)t+=n,r=e.next(t);return t>=Tl?1/0:t}function lc(e){return typeof e=="function"}function ef(e,t){e.timeline=t,e.onfinish=null}const uc=e=>Array.isArray(e)&&typeof e[0]=="number",Cx={linearEasing:void 0};function Px(e,t){const n=Gu(e);return()=>{var r;return(r=Cx[t])!==null&&r!==void 0?r:n()}}const Ji=Px(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),lv=(e,t,n=10)=>{let r="";const o=Math.max(Math.round(t/n),2);for(let i=0;i<o;i++)r+=e(yr(0,o-1,i))+", ";return`linear(${r.substring(0,r.length-2)})`};function uv(e){return!!(typeof e=="function"&&Ji()||!e||typeof e=="string"&&(e in Cl||Ji())||uc(e)||Array.isArray(e)&&e.every(uv))}const Ur=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Cl={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Ur([0,.65,.55,1]),circOut:Ur([.55,0,1,.45]),backIn:Ur([.31,.01,.66,-.59]),backOut:Ur([.33,1.53,.69,.99])};function cv(e,t){if(e)return typeof e=="function"&&Ji()?lv(e,t):uc(e)?Ur(e):Array.isArray(e)?e.map(n=>cv(n,t)||Cl.easeOut):Cl[e]}const at={x:!1,y:!1};function dv(){return at.x||at.y}function kx(e,t,n){var r;if(e instanceof Element)return[e];if(typeof e=="string"){let o=document;t&&(o=t.current);const i=(r=n==null?void 0:n[e])!==null&&r!==void 0?r:o.querySelectorAll(e);return i?Array.from(i):[]}return Array.from(e)}function fv(e,t){const n=kx(e),r=new AbortController,o={passive:!0,...t,signal:r.signal};return[n,o,()=>r.abort()]}function tf(e){return t=>{t.pointerType==="touch"||dv()||e(t)}}function bx(e,t,n={}){const[r,o,i]=fv(e,n),s=tf(a=>{const{target:l}=a,u=t(a);if(typeof u!="function"||!l)return;const c=tf(d=>{u(d),l.removeEventListener("pointerleave",c)});l.addEventListener("pointerleave",c,o)});return r.forEach(a=>{a.addEventListener("pointerenter",s,o)}),i}const pv=(e,t)=>t?e===t?!0:pv(e,t.parentElement):!1,cc=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1,Rx=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function Ax(e){return Rx.has(e.tagName)||e.tabIndex!==-1}const $r=new WeakSet;function nf(e){return t=>{t.key==="Enter"&&e(t)}}function ua(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}const Mx=(e,t)=>{const n=e.currentTarget;if(!n)return;const r=nf(()=>{if($r.has(n))return;ua(n,"down");const o=nf(()=>{ua(n,"up")}),i=()=>ua(n,"cancel");n.addEventListener("keyup",o,t),n.addEventListener("blur",i,t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function rf(e){return cc(e)&&!dv()}function _x(e,t,n={}){const[r,o,i]=fv(e,n),s=a=>{const l=a.currentTarget;if(!rf(a)||$r.has(l))return;$r.add(l);const u=t(a),c=(m,w)=>{window.removeEventListener("pointerup",d),window.removeEventListener("pointercancel",f),!(!rf(m)||!$r.has(l))&&($r.delete(l),typeof u=="function"&&u(m,{success:w}))},d=m=>{c(m,n.useGlobalTarget||pv(l,m.target))},f=m=>{c(m,!1)};window.addEventListener("pointerup",d,o),window.addEventListener("pointercancel",f,o)};return r.forEach(a=>{!Ax(a)&&a.getAttribute("tabindex")===null&&(a.tabIndex=0),(n.useGlobalTarget?window:a).addEventListener("pointerdown",s,o),a.addEventListener("focus",u=>Mx(u,o),o)}),i}function Nx(e){return e==="x"||e==="y"?at[e]?null:(at[e]=!0,()=>{at[e]=!1}):at.x||at.y?null:(at.x=at.y=!0,()=>{at.x=at.y=!1})}const hv=new Set(["width","height","top","left","right","bottom",...Cr]);let Si;function Dx(){Si=void 0}const St={now:()=>(Si===void 0&&St.set(Se.isProcessing||Rw.useManualTiming?Se.timestamp:performance.now()),Si),set:e=>{Si=e,queueMicrotask(Dx)}};function dc(e,t){e.indexOf(t)===-1&&e.push(t)}function fc(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class pc{constructor(){this.subscriptions=[]}add(t){return dc(this.subscriptions,t),()=>fc(this.subscriptions,t)}notify(t,n,r){const o=this.subscriptions.length;if(o)if(o===1)this.subscriptions[0](t,n,r);else for(let i=0;i<o;i++){const s=this.subscriptions[i];s&&s(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function mv(e,t){return t?e*(1e3/t):0}const of=30,Lx=e=>!isNaN(parseFloat(e));class jx{constructor(t,n={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(r,o=!0)=>{const i=St.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),o&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=n.owner}setCurrent(t){this.current=t,this.updatedAt=St.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=Lx(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new pc);const r=this.events[t].add(n);return t==="change"?()=>{r(),J.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,n=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=St.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>of)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,of);return mv(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Po(e,t){return new jx(e,t)}function Ox(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Po(n))}function Vx(e,t){const n=Ms(e,t);let{transitionEnd:r={},transition:o={},...i}=n||{};i={...i,...r};for(const s in i){const a=Yw(i[s]);Ox(e,s,a)}}function Ix(e){return!!(Re(e)&&e.add)}function Pl(e,t){const n=e.getValue("willChange");if(Ix(n))return n.add(t)}function vv(e){return e.props[Km]}const gv=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,Fx=1e-7,zx=12;function Bx(e,t,n,r,o){let i,s,a=0;do s=t+(n-t)/2,i=gv(s,r,o)-e,i>0?n=s:t=s;while(Math.abs(i)>Fx&&++a<zx);return s}function Lo(e,t,n,r){if(e===t&&n===r)return Ge;const o=i=>Bx(i,0,1,e,n);return i=>i===0||i===1?i:gv(o(i),t,r)}const yv=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,wv=e=>t=>1-e(1-t),xv=Lo(.33,1.53,.69,.99),hc=wv(xv),Sv=yv(hc),Ev=e=>(e*=2)<1?.5*hc(e):.5*(2-Math.pow(2,-10*(e-1))),mc=e=>1-Math.sin(Math.acos(e)),Tv=wv(mc),Cv=yv(mc),Pv=e=>/^0[^.\s]+$/u.test(e);function Ux(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||Pv(e):!0}const eo=e=>Math.round(e*1e5)/1e5,vc=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function $x(e){return e==null}const Wx=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,gc=(e,t)=>n=>!!(typeof n=="string"&&Wx.test(n)&&n.startsWith(e)||t&&!$x(n)&&Object.prototype.hasOwnProperty.call(n,t)),kv=(e,t,n)=>r=>{if(typeof r!="string")return r;const[o,i,s,a]=r.match(vc);return{[e]:parseFloat(o),[t]:parseFloat(i),[n]:parseFloat(s),alpha:a!==void 0?parseFloat(a):1}},Hx=e=>jt(0,255,e),ca={...Pr,transform:e=>Math.round(Hx(e))},kn={test:gc("rgb","red"),parse:kv("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+ca.transform(e)+", "+ca.transform(t)+", "+ca.transform(n)+", "+eo(Co.transform(r))+")"};function Kx(e){let t="",n="",r="",o="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),o=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),o=e.substring(4,5),t+=t,n+=n,r+=r,o+=o),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:o?parseInt(o,16)/255:1}}const kl={test:gc("#"),parse:Kx,transform:kn.transform},er={test:gc("hsl","hue"),parse:kv("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+xt.transform(eo(t))+", "+xt.transform(eo(n))+", "+eo(Co.transform(r))+")"},ke={test:e=>kn.test(e)||kl.test(e)||er.test(e),parse:e=>kn.test(e)?kn.parse(e):er.test(e)?er.parse(e):kl.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?kn.transform(e):er.transform(e)},Gx=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Qx(e){var t,n;return isNaN(e)&&typeof e=="string"&&(((t=e.match(vc))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(Gx))===null||n===void 0?void 0:n.length)||0)>0}const bv="number",Rv="color",Yx="var",Xx="var(",sf="${}",Zx=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ko(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},o=[];let i=0;const a=t.replace(Zx,l=>(ke.test(l)?(r.color.push(i),o.push(Rv),n.push(ke.parse(l))):l.startsWith(Xx)?(r.var.push(i),o.push(Yx),n.push(l)):(r.number.push(i),o.push(bv),n.push(parseFloat(l))),++i,sf)).split(sf);return{values:n,split:a,indexes:r,types:o}}function Av(e){return ko(e).values}function Mv(e){const{split:t,types:n}=ko(e),r=t.length;return o=>{let i="";for(let s=0;s<r;s++)if(i+=t[s],o[s]!==void 0){const a=n[s];a===bv?i+=eo(o[s]):a===Rv?i+=ke.transform(o[s]):i+=o[s]}return i}}const qx=e=>typeof e=="number"?0:e;function Jx(e){const t=Av(e);return Mv(e)(t.map(qx))}const dn={test:Qx,parse:Av,createTransformer:Mv,getAnimatableNone:Jx},eS=new Set(["brightness","contrast","saturate","opacity"]);function tS(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(vc)||[];if(!r)return e;const o=n.replace(r,"");let i=eS.has(t)?1:0;return r!==n&&(i*=100),t+"("+i+o+")"}const nS=/\b([a-z-]*)\(.*?\)/gu,bl={...dn,getAnimatableNone:e=>{const t=e.match(nS);return t?t.map(tS).join(" "):e}},rS={...tc,color:ke,backgroundColor:ke,outlineColor:ke,fill:ke,stroke:ke,borderColor:ke,borderTopColor:ke,borderRightColor:ke,borderBottomColor:ke,borderLeftColor:ke,filter:bl,WebkitFilter:bl},yc=e=>rS[e];function _v(e,t){let n=yc(e);return n!==bl&&(n=dn),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const oS=new Set(["auto","none","0"]);function iS(e,t,n){let r=0,o;for(;r<e.length&&!o;){const i=e[r];typeof i=="string"&&!oS.has(i)&&ko(i).values.length&&(o=e[r]),r++}if(o&&n)for(const i of t)e[i]=_v(n,o)}const af=e=>e===Pr||e===V,lf=(e,t)=>parseFloat(e.split(", ")[t]),uf=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const o=r.match(/^matrix3d\((.+)\)$/u);if(o)return lf(o[1],t);{const i=r.match(/^matrix\((.+)\)$/u);return i?lf(i[1],e):0}},sS=new Set(["x","y","z"]),aS=Cr.filter(e=>!sS.has(e));function lS(e){const t=[];return aS.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t}const xr={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:uf(4,13),y:uf(5,14)};xr.translateX=xr.x;xr.translateY=xr.y;const Mn=new Set;let Rl=!1,Al=!1;function Nv(){if(Al){const e=Array.from(Mn).filter(r=>r.needsMeasurement),t=new Set(e.map(r=>r.element)),n=new Map;t.forEach(r=>{const o=lS(r);o.length&&(n.set(r,o),r.render())}),e.forEach(r=>r.measureInitialState()),t.forEach(r=>{r.render();const o=n.get(r);o&&o.forEach(([i,s])=>{var a;(a=r.getValue(i))===null||a===void 0||a.set(s)})}),e.forEach(r=>r.measureEndState()),e.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}Al=!1,Rl=!1,Mn.forEach(e=>e.complete()),Mn.clear()}function Dv(){Mn.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(Al=!0)})}function uS(){Dv(),Nv()}class wc{constructor(t,n,r,o,i,s=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=n,this.name=r,this.motionValue=o,this.element=i,this.isAsync=s}scheduleResolve(){this.isScheduled=!0,this.isAsync?(Mn.add(this),Rl||(Rl=!0,J.read(Dv),J.resolveKeyframes(Nv))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:n,element:r,motionValue:o}=this;for(let i=0;i<t.length;i++)if(t[i]===null)if(i===0){const s=o==null?void 0:o.get(),a=t[t.length-1];if(s!==void 0)t[0]=s;else if(r&&n){const l=r.readValue(n,a);l!=null&&(t[0]=l)}t[0]===void 0&&(t[0]=a),o&&s===void 0&&o.set(t[0])}else t[i]=t[i-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),Mn.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,Mn.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const Lv=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),cS=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function dS(e){const t=cS.exec(e);if(!t)return[,];const[,n,r,o]=t;return[`--${n??r}`,o]}function jv(e,t,n=1){const[r,o]=dS(e);if(!r)return;const i=window.getComputedStyle(t).getPropertyValue(r);if(i){const s=i.trim();return Lv(s)?parseFloat(s):s}return ec(o)?jv(o,t,n+1):o}const Ov=e=>t=>t.test(e),fS={test:e=>e==="auto",parse:e=>e},Vv=[Pr,V,xt,Ht,tx,ex,fS],cf=e=>Vv.find(Ov(e));class Iv extends wc{constructor(t,n,r,o,i){super(t,n,r,o,i,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:n,name:r}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<t.length;l++){let u=t[l];if(typeof u=="string"&&(u=u.trim(),ec(u))){const c=jv(u,n.current);c!==void 0&&(t[l]=c),l===t.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!hv.has(r)||t.length!==2)return;const[o,i]=t,s=cf(o),a=cf(i);if(s!==a)if(af(s)&&af(a))for(let l=0;l<t.length;l++){const u=t[l];typeof u=="string"&&(t[l]=parseFloat(u))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,r=[];for(let o=0;o<t.length;o++)Ux(t[o])&&r.push(o);r.length&&iS(t,r,n)}measureInitialState(){const{element:t,unresolvedKeyframes:n,name:r}=this;if(!t||!t.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=xr[r](t.measureViewportBox(),window.getComputedStyle(t.current)),n[0]=this.measuredOrigin;const o=n[n.length-1];o!==void 0&&t.getValue(r,o).jump(o,!1)}measureEndState(){var t;const{element:n,name:r,unresolvedKeyframes:o}=this;if(!n||!n.current)return;const i=n.getValue(r);i&&i.jump(this.measuredOrigin,!1);const s=o.length-1,a=o[s];o[s]=xr[r](n.measureViewportBox(),window.getComputedStyle(n.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),!((t=this.removedTransforms)===null||t===void 0)&&t.length&&this.removedTransforms.forEach(([l,u])=>{n.getValue(l).set(u)}),this.resolveNoneKeyframes()}}const df=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(dn.test(e)||e==="0")&&!e.startsWith("url("));function pS(e){const t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function hS(e,t,n,r){const o=e[0];if(o===null)return!1;if(t==="display"||t==="visibility")return!0;const i=e[e.length-1],s=df(o,t),a=df(i,t);return!s||!a?!1:pS(e)||(n==="spring"||lc(n))&&r}const mS=e=>e!==null;function _s(e,{repeat:t,repeatType:n="loop"},r){const o=e.filter(mS),i=t&&n!=="loop"&&t%2===1?0:o.length-1;return!i||r===void 0?o[i]:r}const vS=40;class Fv{constructor({autoplay:t=!0,delay:n=0,type:r="keyframes",repeat:o=0,repeatDelay:i=0,repeatType:s="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=St.now(),this.options={autoplay:t,delay:n,type:r,repeat:o,repeatDelay:i,repeatType:s,...a},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>vS?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&uS(),this._resolved}onKeyframesResolved(t,n){this.resolvedAt=St.now(),this.hasAttemptedResolve=!0;const{name:r,type:o,velocity:i,delay:s,onComplete:a,onUpdate:l,isGenerator:u}=this.options;if(!u&&!hS(t,r,o,i))if(s)this.options.duration=0;else{l&&l(_s(t,this.options,n)),a&&a(),this.resolveFinishedPromise();return}const c=this.initPlayback(t,n);c!==!1&&(this._resolved={keyframes:t,finalKeyframe:n,...c},this.onPostResolved())}onPostResolved(){}then(t,n){return this.currentFinishedPromise.then(t,n)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}const oe=(e,t,n)=>e+(t-e)*n;function da(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function gS({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let o=0,i=0,s=0;if(!t)o=i=s=n;else{const a=n<.5?n*(1+t):n+t-n*t,l=2*n-a;o=da(l,a,e+1/3),i=da(l,a,e),s=da(l,a,e-1/3)}return{red:Math.round(o*255),green:Math.round(i*255),blue:Math.round(s*255),alpha:r}}function es(e,t){return n=>n>0?t:e}const fa=(e,t,n)=>{const r=e*e,o=n*(t*t-r)+r;return o<0?0:Math.sqrt(o)},yS=[kl,kn,er],wS=e=>yS.find(t=>t.test(e));function ff(e){const t=wS(e);if(!t)return!1;let n=t.parse(e);return t===er&&(n=gS(n)),n}const pf=(e,t)=>{const n=ff(e),r=ff(t);if(!n||!r)return es(e,t);const o={...n};return i=>(o.red=fa(n.red,r.red,i),o.green=fa(n.green,r.green,i),o.blue=fa(n.blue,r.blue,i),o.alpha=oe(n.alpha,r.alpha,i),kn.transform(o))},xS=(e,t)=>n=>t(e(n)),jo=(...e)=>e.reduce(xS),Ml=new Set(["none","hidden"]);function SS(e,t){return Ml.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function ES(e,t){return n=>oe(e,t,n)}function xc(e){return typeof e=="number"?ES:typeof e=="string"?ec(e)?es:ke.test(e)?pf:PS:Array.isArray(e)?zv:typeof e=="object"?ke.test(e)?pf:TS:es}function zv(e,t){const n=[...e],r=n.length,o=e.map((i,s)=>xc(i)(i,t[s]));return i=>{for(let s=0;s<r;s++)n[s]=o[s](i);return n}}function TS(e,t){const n={...e,...t},r={};for(const o in n)e[o]!==void 0&&t[o]!==void 0&&(r[o]=xc(e[o])(e[o],t[o]));return o=>{for(const i in r)n[i]=r[i](o);return n}}function CS(e,t){var n;const r=[],o={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){const s=t.types[i],a=e.indexes[s][o[s]],l=(n=e.values[a])!==null&&n!==void 0?n:0;r[i]=l,o[s]++}return r}const PS=(e,t)=>{const n=dn.createTransformer(t),r=ko(e),o=ko(t);return r.indexes.var.length===o.indexes.var.length&&r.indexes.color.length===o.indexes.color.length&&r.indexes.number.length>=o.indexes.number.length?Ml.has(e)&&!o.values.length||Ml.has(t)&&!r.values.length?SS(e,t):jo(zv(CS(r,o),o.values),n):es(e,t)};function Bv(e,t,n){return typeof e=="number"&&typeof t=="number"&&typeof n=="number"?oe(e,t,n):xc(e)(e,t)}const kS=5;function Uv(e,t,n){const r=Math.max(t-kS,0);return mv(n-e(r),t-r)}const le={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},pa=.001;function bS({duration:e=le.duration,bounce:t=le.bounce,velocity:n=le.velocity,mass:r=le.mass}){let o,i,s=1-t;s=jt(le.minDamping,le.maxDamping,s),e=jt(le.minDuration,le.maxDuration,Mt(e)),s<1?(o=u=>{const c=u*s,d=c*e,f=c-n,m=_l(u,s),w=Math.exp(-d);return pa-f/m*w},i=u=>{const d=u*s*e,f=d*n+n,m=Math.pow(s,2)*Math.pow(u,2)*e,w=Math.exp(-d),y=_l(Math.pow(u,2),s);return(-o(u)+pa>0?-1:1)*((f-m)*w)/y}):(o=u=>{const c=Math.exp(-u*e),d=(u-n)*e+1;return-pa+c*d},i=u=>{const c=Math.exp(-u*e),d=(n-u)*(e*e);return c*d});const a=5/e,l=AS(o,i,a);if(e=At(e),isNaN(l))return{stiffness:le.stiffness,damping:le.damping,duration:e};{const u=Math.pow(l,2)*r;return{stiffness:u,damping:s*2*Math.sqrt(r*u),duration:e}}}const RS=12;function AS(e,t,n){let r=n;for(let o=1;o<RS;o++)r=r-e(r)/t(r);return r}function _l(e,t){return e*Math.sqrt(1-t*t)}const MS=["duration","bounce"],_S=["stiffness","damping","mass"];function hf(e,t){return t.some(n=>e[n]!==void 0)}function NS(e){let t={velocity:le.velocity,stiffness:le.stiffness,damping:le.damping,mass:le.mass,isResolvedFromDuration:!1,...e};if(!hf(e,_S)&&hf(e,MS))if(e.visualDuration){const n=e.visualDuration,r=2*Math.PI/(n*1.2),o=r*r,i=2*jt(.05,1,1-(e.bounce||0))*Math.sqrt(o);t={...t,mass:le.mass,stiffness:o,damping:i}}else{const n=bS(e);t={...t,...n,mass:le.mass},t.isResolvedFromDuration=!0}return t}function $v(e=le.visualDuration,t=le.bounce){const n=typeof e!="object"?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:r,restDelta:o}=n;const i=n.keyframes[0],s=n.keyframes[n.keyframes.length-1],a={done:!1,value:i},{stiffness:l,damping:u,mass:c,duration:d,velocity:f,isResolvedFromDuration:m}=NS({...n,velocity:-Mt(n.velocity||0)}),w=f||0,y=u/(2*Math.sqrt(l*c)),x=s-i,h=Mt(Math.sqrt(l/c)),p=Math.abs(x)<5;r||(r=p?le.restSpeed.granular:le.restSpeed.default),o||(o=p?le.restDelta.granular:le.restDelta.default);let v;if(y<1){const E=_l(h,y);v=P=>{const k=Math.exp(-y*h*P);return s-k*((w+y*h*x)/E*Math.sin(E*P)+x*Math.cos(E*P))}}else if(y===1)v=E=>s-Math.exp(-h*E)*(x+(w+h*x)*E);else{const E=h*Math.sqrt(y*y-1);v=P=>{const k=Math.exp(-y*h*P),C=Math.min(E*P,300);return s-k*((w+y*h*x)*Math.sinh(C)+E*x*Math.cosh(C))/E}}const S={calculatedDuration:m&&d||null,next:E=>{const P=v(E);if(m)a.done=E>=d;else{let k=0;y<1&&(k=E===0?At(w):Uv(v,E,P));const C=Math.abs(k)<=r,L=Math.abs(s-P)<=o;a.done=C&&L}return a.value=a.done?s:P,a},toString:()=>{const E=Math.min(av(S),Tl),P=lv(k=>S.next(E*k).value,E,30);return E+"ms "+P}};return S}function mf({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:o=10,bounceStiffness:i=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:c}){const d=e[0],f={done:!1,value:d},m=C=>a!==void 0&&C<a||l!==void 0&&C>l,w=C=>a===void 0?l:l===void 0||Math.abs(a-C)<Math.abs(l-C)?a:l;let y=n*t;const x=d+y,h=s===void 0?x:s(x);h!==x&&(y=h-d);const p=C=>-y*Math.exp(-C/r),v=C=>h+p(C),S=C=>{const L=p(C),N=v(C);f.done=Math.abs(L)<=u,f.value=f.done?h:N};let E,P;const k=C=>{m(f.value)&&(E=C,P=$v({keyframes:[f.value,w(f.value)],velocity:Uv(v,C,f.value),damping:o,stiffness:i,restDelta:u,restSpeed:c}))};return k(0),{calculatedDuration:null,next:C=>{let L=!1;return!P&&E===void 0&&(L=!0,S(C),k(C)),E!==void 0&&C>=E?P.next(C-E):(!L&&S(C),f)}}}const DS=Lo(.42,0,1,1),LS=Lo(0,0,.58,1),Wv=Lo(.42,0,.58,1),jS=e=>Array.isArray(e)&&typeof e[0]!="number",OS={linear:Ge,easeIn:DS,easeInOut:Wv,easeOut:LS,circIn:mc,circInOut:Cv,circOut:Tv,backIn:hc,backInOut:Sv,backOut:xv,anticipate:Ev},vf=e=>{if(uc(e)){Bm(e.length===4);const[t,n,r,o]=e;return Lo(t,n,r,o)}else if(typeof e=="string")return OS[e];return e};function VS(e,t,n){const r=[],o=n||Bv,i=e.length-1;for(let s=0;s<i;s++){let a=o(e[s],e[s+1]);if(t){const l=Array.isArray(t)?t[s]||Ge:t;a=jo(l,a)}r.push(a)}return r}function IS(e,t,{clamp:n=!0,ease:r,mixer:o}={}){const i=e.length;if(Bm(i===t.length),i===1)return()=>t[0];if(i===2&&t[0]===t[1])return()=>t[1];const s=e[0]===e[1];e[0]>e[i-1]&&(e=[...e].reverse(),t=[...t].reverse());const a=VS(t,r,o),l=a.length,u=c=>{if(s&&c<e[0])return t[0];let d=0;if(l>1)for(;d<e.length-2&&!(c<e[d+1]);d++);const f=yr(e[d],e[d+1],c);return a[d](f)};return n?c=>u(jt(e[0],e[i-1],c)):u}function FS(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const o=yr(0,t,r);e.push(oe(n,1,o))}}function zS(e){const t=[0];return FS(t,e.length-1),t}function BS(e,t){return e.map(n=>n*t)}function US(e,t){return e.map(()=>t||Wv).splice(0,e.length-1)}function ts({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const o=jS(r)?r.map(vf):vf(r),i={done:!1,value:t[0]},s=BS(n&&n.length===t.length?n:zS(t),e),a=IS(s,t,{ease:Array.isArray(o)?o:US(t,o)});return{calculatedDuration:e,next:l=>(i.value=a(l),i.done=l>=e,i)}}const $S=e=>{const t=({timestamp:n})=>e(n);return{start:()=>J.update(t,!0),stop:()=>cn(t),now:()=>Se.isProcessing?Se.timestamp:St.now()}},WS={decay:mf,inertia:mf,tween:ts,keyframes:ts,spring:$v},HS=e=>e/100;class Ns extends Fv{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:l}=this.options;l&&l()};const{name:n,motionValue:r,element:o,keyframes:i}=this.options,s=(o==null?void 0:o.KeyframeResolver)||wc,a=(l,u)=>this.onKeyframesResolved(l,u);this.resolver=new s(i,a,n,r,o),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){const{type:n="keyframes",repeat:r=0,repeatDelay:o=0,repeatType:i,velocity:s=0}=this.options,a=lc(n)?n:WS[n]||ts;let l,u;a!==ts&&typeof t[0]!="number"&&(l=jo(HS,Bv(t[0],t[1])),t=[0,100]);const c=a({...this.options,keyframes:t});i==="mirror"&&(u=a({...this.options,keyframes:[...t].reverse(),velocity:-s})),c.calculatedDuration===null&&(c.calculatedDuration=av(c));const{calculatedDuration:d}=c,f=d+o,m=f*(r+1)-o;return{generator:c,mirroredGenerator:u,mapPercentToKeyframes:l,calculatedDuration:d,resolvedDuration:f,totalDuration:m}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!t?this.pause():this.state=this.pendingPlayState}tick(t,n=!1){const{resolved:r}=this;if(!r){const{keyframes:C}=this.options;return{done:!0,value:C[C.length-1]}}const{finalKeyframe:o,generator:i,mirroredGenerator:s,mapPercentToKeyframes:a,keyframes:l,calculatedDuration:u,totalDuration:c,resolvedDuration:d}=r;if(this.startTime===null)return i.next(0);const{delay:f,repeat:m,repeatType:w,repeatDelay:y,onUpdate:x}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-c/this.speed,this.startTime)),n?this.currentTime=t:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const h=this.currentTime-f*(this.speed>=0?1:-1),p=this.speed>=0?h<0:h>c;this.currentTime=Math.max(h,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=c);let v=this.currentTime,S=i;if(m){const C=Math.min(this.currentTime,c)/d;let L=Math.floor(C),N=C%1;!N&&C>=1&&(N=1),N===1&&L--,L=Math.min(L,m+1),!!(L%2)&&(w==="reverse"?(N=1-N,y&&(N-=y/d)):w==="mirror"&&(S=s)),v=jt(0,1,N)*d}const E=p?{done:!1,value:l[0]}:S.next(v);a&&(E.value=a(E.value));let{done:P}=E;!p&&u!==null&&(P=this.speed>=0?this.currentTime>=c:this.currentTime<=0);const k=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&P);return k&&o!==void 0&&(E.value=_s(l,this.options,o)),x&&x(E.value),k&&this.finish(),E}get duration(){const{resolved:t}=this;return t?Mt(t.calculatedDuration):0}get time(){return Mt(this.currentTime)}set time(t){t=At(t),this.currentTime=t,this.holdTime!==null||this.speed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=Mt(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:t=$S,onPlay:n,startTime:r}=this.options;this.driver||(this.driver=t(i=>this.tick(i))),n&&n();const o=this.driver.now();this.holdTime!==null?this.startTime=o-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=o):this.startTime=r??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(t=this.currentTime)!==null&&t!==void 0?t:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}function yk(e){return new Ns(e)}const KS=new Set(["opacity","clipPath","filter","transform"]);function GS(e,t,n,{delay:r=0,duration:o=300,repeat:i=0,repeatType:s="loop",ease:a="easeInOut",times:l}={}){const u={[t]:n};l&&(u.offset=l);const c=cv(a,o);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:o,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:i+1,direction:s==="reverse"?"alternate":"normal"})}const QS=Gu(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),ns=10,YS=2e4;function XS(e){return lc(e.type)||e.type==="spring"||!uv(e.ease)}function ZS(e,t){const n=new Ns({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0});let r={done:!1,value:e[0]};const o=[];let i=0;for(;!r.done&&i<YS;)r=n.sample(i),o.push(r.value),i+=ns;return{times:void 0,keyframes:o,duration:i-ns,ease:"linear"}}const Hv={anticipate:Ev,backInOut:Sv,circInOut:Cv};function qS(e){return e in Hv}class gf extends Fv{constructor(t){super(t);const{name:n,motionValue:r,element:o,keyframes:i}=this.options;this.resolver=new Iv(i,(s,a)=>this.onKeyframesResolved(s,a),n,r,o),this.resolver.scheduleResolve()}initPlayback(t,n){let{duration:r=300,times:o,ease:i,type:s,motionValue:a,name:l,startTime:u}=this.options;if(!a.owner||!a.owner.current)return!1;if(typeof i=="string"&&Ji()&&qS(i)&&(i=Hv[i]),XS(this.options)){const{onComplete:d,onUpdate:f,motionValue:m,element:w,...y}=this.options,x=ZS(t,y);t=x.keyframes,t.length===1&&(t[1]=t[0]),r=x.duration,o=x.times,i=x.ease,s="keyframes"}const c=GS(a.owner.current,l,t,{...this.options,duration:r,times:o,ease:i});return c.startTime=u??this.calcStartTime(),this.pendingTimeline?(ef(c,this.pendingTimeline),this.pendingTimeline=void 0):c.onfinish=()=>{const{onComplete:d}=this.options;a.set(_s(t,this.options,n)),d&&d(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:r,times:o,type:s,ease:i,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:n}=t;return Mt(n)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:n}=t;return Mt(n.currentTime||0)}set time(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.currentTime=At(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:n}=t;return n.playbackRate}set speed(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:n}=t;return n.playState}get startTime(){const{resolved:t}=this;if(!t)return null;const{animation:n}=t;return n.startTime}attachTimeline(t){if(!this._resolved)this.pendingTimeline=t;else{const{resolved:n}=this;if(!n)return Ge;const{animation:r}=n;ef(r,t)}return Ge}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:t}=this;if(!t)return;const{animation:n,keyframes:r,duration:o,type:i,ease:s,times:a}=t;if(n.playState==="idle"||n.playState==="finished")return;if(this.time){const{motionValue:u,onUpdate:c,onComplete:d,element:f,...m}=this.options,w=new Ns({...m,keyframes:r,duration:o,type:i,ease:s,times:a,isGenerator:!0}),y=At(this.time);u.setWithVelocity(w.sample(y-ns).value,w.sample(y).value,ns)}const{onStop:l}=this.options;l&&l(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:n,name:r,repeatDelay:o,repeatType:i,damping:s,type:a}=t;if(!n||!n.owner||!(n.owner.current instanceof HTMLElement))return!1;const{onUpdate:l,transformTemplate:u}=n.owner.getProps();return QS()&&r&&KS.has(r)&&!l&&!u&&!o&&i!=="mirror"&&s!==0&&a!=="inertia"}}const JS={type:"spring",stiffness:500,damping:25,restSpeed:10},eE=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),tE={type:"keyframes",duration:.8},nE={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},rE=(e,{keyframes:t})=>t.length>2?tE:Fn.has(e)?e.startsWith("scale")?eE(t[1]):JS:nE;function oE({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:o,repeat:i,repeatType:s,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}const Sc=(e,t,n,r={},o,i)=>s=>{const a=ac(r,e)||{},l=a.delay||r.delay||0;let{elapsed:u=0}=r;u=u-At(l);let c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-u,onUpdate:f=>{t.set(f),a.onUpdate&&a.onUpdate(f)},onComplete:()=>{s(),a.onComplete&&a.onComplete()},name:e,motionValue:t,element:i?void 0:o};oE(a)||(c={...c,...rE(e,c)}),c.duration&&(c.duration=At(c.duration)),c.repeatDelay&&(c.repeatDelay=At(c.repeatDelay)),c.from!==void 0&&(c.keyframes[0]=c.from);let d=!1;if((c.type===!1||c.duration===0&&!c.repeatDelay)&&(c.duration=0,c.delay===0&&(d=!0)),d&&!i&&t.get()!==void 0){const f=_s(c.keyframes,a);if(f!==void 0)return J.update(()=>{c.onUpdate(f),c.onComplete()}),new Tx([])}return!i&&gf.supports(c)?new gf(c):new Ns(c)};function iE({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function Kv(e,t,{delay:n=0,transitionOverride:r,type:o}={}){var i;let{transition:s=e.getDefaultTransition(),transitionEnd:a,...l}=t;r&&(s=r);const u=[],c=o&&e.animationState&&e.animationState.getState()[o];for(const d in l){const f=e.getValue(d,(i=e.latestValues[d])!==null&&i!==void 0?i:null),m=l[d];if(m===void 0||c&&iE(c,d))continue;const w={delay:n,...ac(s||{},d)};let y=!1;if(window.MotionHandoffAnimation){const h=vv(e);if(h){const p=window.MotionHandoffAnimation(h,d,J);p!==null&&(w.startTime=p,y=!0)}}Pl(e,d),f.start(Sc(d,f,m,e.shouldReduceMotion&&hv.has(d)?{type:!1}:w,e,y));const x=f.animation;x&&u.push(x)}return a&&Promise.all(u).then(()=>{J.update(()=>{a&&Vx(e,a)})}),u}function Nl(e,t,n={}){var r;const o=Ms(e,t,n.type==="exit"?(r=e.presenceContext)===null||r===void 0?void 0:r.custom:void 0);let{transition:i=e.getDefaultTransition()||{}}=o||{};n.transitionOverride&&(i=n.transitionOverride);const s=o?()=>Promise.all(Kv(e,o,n)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(u=0)=>{const{delayChildren:c=0,staggerChildren:d,staggerDirection:f}=i;return sE(e,t,c+u,d,f,n)}:()=>Promise.resolve(),{when:l}=i;if(l){const[u,c]=l==="beforeChildren"?[s,a]:[a,s];return u().then(()=>c())}else return Promise.all([s(),a(n.delay)])}function sE(e,t,n=0,r=0,o=1,i){const s=[],a=(e.variantChildren.size-1)*r,l=o===1?(u=0)=>u*r:(u=0)=>a-u*r;return Array.from(e.variantChildren).sort(aE).forEach((u,c)=>{u.notify("AnimationStart",t),s.push(Nl(u,t,{...i,delay:n+l(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(s)}function aE(e,t){return e.sortNodePosition(t)}function lE(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const o=t.map(i=>Nl(e,i,n));r=Promise.all(o)}else if(typeof t=="string")r=Nl(e,t,n);else{const o=typeof t=="function"?Ms(e,t,n.custom):t;r=Promise.all(Kv(e,o,n))}return r.then(()=>{e.notify("AnimationComplete",t)})}const uE=Yu.length;function Gv(e){if(!e)return;if(!e.isControllingVariants){const n=e.parent?Gv(e.parent)||{}:{};return e.props.initial!==void 0&&(n.initial=e.props.initial),n}const t={};for(let n=0;n<uE;n++){const r=Yu[n],o=e.props[r];(To(o)||o===!1)&&(t[r]=o)}return t}const cE=[...Qu].reverse(),dE=Qu.length;function fE(e){return t=>Promise.all(t.map(({animation:n,options:r})=>lE(e,n,r)))}function pE(e){let t=fE(e),n=yf(),r=!0;const o=l=>(u,c)=>{var d;const f=Ms(e,c,l==="exit"?(d=e.presenceContext)===null||d===void 0?void 0:d.custom:void 0);if(f){const{transition:m,transitionEnd:w,...y}=f;u={...u,...y,...w}}return u};function i(l){t=l(e)}function s(l){const{props:u}=e,c=Gv(e.parent)||{},d=[],f=new Set;let m={},w=1/0;for(let x=0;x<dE;x++){const h=cE[x],p=n[h],v=u[h]!==void 0?u[h]:c[h],S=To(v),E=h===l?p.isActive:null;E===!1&&(w=x);let P=v===c[h]&&v!==u[h]&&S;if(P&&r&&e.manuallyAnimateOnMount&&(P=!1),p.protectedKeys={...m},!p.isActive&&E===null||!v&&!p.prevProp||Rs(v)||typeof v=="boolean")continue;const k=hE(p.prevProp,v);let C=k||h===l&&p.isActive&&!P&&S||x>w&&S,L=!1;const N=Array.isArray(v)?v:[v];let _=N.reduce(o(h),{});E===!1&&(_={});const{prevResolvedValues:b={}}=p,I={...b,..._},M=z=>{C=!0,f.has(z)&&(L=!0,f.delete(z)),p.needsAnimating[z]=!0;const A=e.getValue(z);A&&(A.liveStyle=!1)};for(const z in I){const A=_[z],j=b[z];if(m.hasOwnProperty(z))continue;let O=!1;El(A)&&El(j)?O=!sv(A,j):O=A!==j,O?A!=null?M(z):f.add(z):A!==void 0&&f.has(z)?M(z):p.protectedKeys[z]=!0}p.prevProp=v,p.prevResolvedValues=_,p.isActive&&(m={...m,..._}),r&&e.blockInitialAnimation&&(C=!1),C&&(!(P&&k)||L)&&d.push(...N.map(z=>({animation:z,options:{type:h}})))}if(f.size){const x={};f.forEach(h=>{const p=e.getBaseTarget(h),v=e.getValue(h);v&&(v.liveStyle=!0),x[h]=p??null}),d.push({animation:x})}let y=!!d.length;return r&&(u.initial===!1||u.initial===u.animate)&&!e.manuallyAnimateOnMount&&(y=!1),r=!1,y?t(d):Promise.resolve()}function a(l,u){var c;if(n[l].isActive===u)return Promise.resolve();(c=e.variantChildren)===null||c===void 0||c.forEach(f=>{var m;return(m=f.animationState)===null||m===void 0?void 0:m.setActive(l,u)}),n[l].isActive=u;const d=s(l);for(const f in n)n[f].protectedKeys={};return d}return{animateChanges:s,setActive:a,setAnimateFunction:i,getState:()=>n,reset:()=>{n=yf(),r=!0}}}function hE(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!sv(t,e):!1}function gn(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function yf(){return{animate:gn(!0),whileInView:gn(),whileHover:gn(),whileTap:gn(),whileDrag:gn(),whileFocus:gn(),exit:gn()}}class mn{constructor(t){this.isMounted=!1,this.node=t}update(){}}class mE extends mn{constructor(t){super(t),t.animationState||(t.animationState=pE(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();Rs(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),(t=this.unmountControls)===null||t===void 0||t.call(this)}}let vE=0;class gE extends mn{constructor(){super(...arguments),this.id=vE++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;const o=this.node.animationState.setActive("exit",!t);n&&!t&&o.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const yE={animation:{Feature:mE},exit:{Feature:gE}};function bo(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function Oo(e){return{point:{x:e.pageX,y:e.pageY}}}const wE=e=>t=>cc(t)&&e(t,Oo(t));function to(e,t,n,r){return bo(e,t,wE(n),r)}const wf=(e,t)=>Math.abs(e-t);function xE(e,t){const n=wf(e.x,t.x),r=wf(e.y,t.y);return Math.sqrt(n**2+r**2)}class Qv{constructor(t,n,{transformPagePoint:r,contextWindow:o,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const d=ma(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,m=xE(d.offset,{x:0,y:0})>=3;if(!f&&!m)return;const{point:w}=d,{timestamp:y}=Se;this.history.push({...w,timestamp:y});const{onStart:x,onMove:h}=this.handlers;f||(x&&x(this.lastMoveEvent,d),this.startEvent=this.lastMoveEvent),h&&h(this.lastMoveEvent,d)},this.handlePointerMove=(d,f)=>{this.lastMoveEvent=d,this.lastMoveEventInfo=ha(f,this.transformPagePoint),J.update(this.updatePoint,!0)},this.handlePointerUp=(d,f)=>{this.end();const{onEnd:m,onSessionEnd:w,resumeAnimation:y}=this.handlers;if(this.dragSnapToOrigin&&y&&y(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const x=ma(d.type==="pointercancel"?this.lastMoveEventInfo:ha(f,this.transformPagePoint),this.history);this.startEvent&&m&&m(d,x),w&&w(d,x)},!cc(t))return;this.dragSnapToOrigin=i,this.handlers=n,this.transformPagePoint=r,this.contextWindow=o||window;const s=Oo(t),a=ha(s,this.transformPagePoint),{point:l}=a,{timestamp:u}=Se;this.history=[{...l,timestamp:u}];const{onSessionStart:c}=n;c&&c(t,ma(a,this.history)),this.removeListeners=jo(to(this.contextWindow,"pointermove",this.handlePointerMove),to(this.contextWindow,"pointerup",this.handlePointerUp),to(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),cn(this.updatePoint)}}function ha(e,t){return t?{point:t(e.point)}:e}function xf(e,t){return{x:e.x-t.x,y:e.y-t.y}}function ma({point:e},t){return{point:e,delta:xf(e,Yv(t)),offset:xf(e,SE(t)),velocity:EE(t,.1)}}function SE(e){return e[0]}function Yv(e){return e[e.length-1]}function EE(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const o=Yv(e);for(;n>=0&&(r=e[n],!(o.timestamp-r.timestamp>At(t)));)n--;if(!r)return{x:0,y:0};const i=Mt(o.timestamp-r.timestamp);if(i===0)return{x:0,y:0};const s={x:(o.x-r.x)/i,y:(o.y-r.y)/i};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}const Xv=1e-4,TE=1-Xv,CE=1+Xv,Zv=.01,PE=0-Zv,kE=0+Zv;function Ye(e){return e.max-e.min}function bE(e,t,n){return Math.abs(e-t)<=n}function Sf(e,t,n,r=.5){e.origin=r,e.originPoint=oe(t.min,t.max,e.origin),e.scale=Ye(n)/Ye(t),e.translate=oe(n.min,n.max,e.origin)-e.originPoint,(e.scale>=TE&&e.scale<=CE||isNaN(e.scale))&&(e.scale=1),(e.translate>=PE&&e.translate<=kE||isNaN(e.translate))&&(e.translate=0)}function no(e,t,n,r){Sf(e.x,t.x,n.x,r?r.originX:void 0),Sf(e.y,t.y,n.y,r?r.originY:void 0)}function Ef(e,t,n){e.min=n.min+t.min,e.max=e.min+Ye(t)}function RE(e,t,n){Ef(e.x,t.x,n.x),Ef(e.y,t.y,n.y)}function Tf(e,t,n){e.min=t.min-n.min,e.max=e.min+Ye(t)}function ro(e,t,n){Tf(e.x,t.x,n.x),Tf(e.y,t.y,n.y)}function AE(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?oe(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?oe(n,e,r.max):Math.min(e,n)),e}function Cf(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function ME(e,{top:t,left:n,bottom:r,right:o}){return{x:Cf(e.x,n,o),y:Cf(e.y,t,r)}}function Pf(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function _E(e,t){return{x:Pf(e.x,t.x),y:Pf(e.y,t.y)}}function NE(e,t){let n=.5;const r=Ye(e),o=Ye(t);return o>r?n=yr(t.min,t.max-r,e.min):r>o&&(n=yr(e.min,e.max-o,t.min)),jt(0,1,n)}function DE(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const Dl=.35;function LE(e=Dl){return e===!1?e=0:e===!0&&(e=Dl),{x:kf(e,"left","right"),y:kf(e,"top","bottom")}}function kf(e,t,n){return{min:bf(e,t),max:bf(e,n)}}function bf(e,t){return typeof e=="number"?e:e[t]||0}const Rf=()=>({translate:0,scale:1,origin:0,originPoint:0}),tr=()=>({x:Rf(),y:Rf()}),Af=()=>({min:0,max:0}),ce=()=>({x:Af(),y:Af()});function Je(e){return[e("x"),e("y")]}function qv({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function jE({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function OE(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function va(e){return e===void 0||e===1}function Ll({scale:e,scaleX:t,scaleY:n}){return!va(e)||!va(t)||!va(n)}function xn(e){return Ll(e)||Jv(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function Jv(e){return Mf(e.x)||Mf(e.y)}function Mf(e){return e&&e!=="0%"}function rs(e,t,n){const r=e-n,o=t*r;return n+o}function _f(e,t,n,r,o){return o!==void 0&&(e=rs(e,o,r)),rs(e,n,r)+t}function jl(e,t=0,n=1,r,o){e.min=_f(e.min,t,n,r,o),e.max=_f(e.max,t,n,r,o)}function eg(e,{x:t,y:n}){jl(e.x,t.translate,t.scale,t.originPoint),jl(e.y,n.translate,n.scale,n.originPoint)}const Nf=.999999999999,Df=1.0000000000001;function VE(e,t,n,r=!1){const o=n.length;if(!o)return;t.x=t.y=1;let i,s;for(let a=0;a<o;a++){i=n[a],s=i.projectionDelta;const{visualElement:l}=i.options;l&&l.props.style&&l.props.style.display==="contents"||(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&rr(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,eg(e,s)),r&&xn(i.latestValues)&&rr(e,i.latestValues))}t.x<Df&&t.x>Nf&&(t.x=1),t.y<Df&&t.y>Nf&&(t.y=1)}function nr(e,t){e.min=e.min+t,e.max=e.max+t}function Lf(e,t,n,r,o=.5){const i=oe(e.min,e.max,o);jl(e,t,n,i,r)}function rr(e,t){Lf(e.x,t.x,t.scaleX,t.scale,t.originX),Lf(e.y,t.y,t.scaleY,t.scale,t.originY)}function tg(e,t){return qv(OE(e.getBoundingClientRect(),t))}function IE(e,t,n){const r=tg(e,n),{scroll:o}=t;return o&&(nr(r.x,o.offset.x),nr(r.y,o.offset.y)),r}const ng=({current:e})=>e?e.ownerDocument.defaultView:null,FE=new WeakMap;class zE{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ce(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const o=c=>{const{dragSnapToOrigin:d}=this.getProps();d?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(Oo(c).point)},i=(c,d)=>{const{drag:f,dragPropagation:m,onDragStart:w}=this.getProps();if(f&&!m&&(this.openDragLock&&this.openDragLock(),this.openDragLock=Nx(f),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Je(x=>{let h=this.getAxisMotionValue(x).get()||0;if(xt.test(h)){const{projection:p}=this.visualElement;if(p&&p.layout){const v=p.layout.layoutBox[x];v&&(h=Ye(v)*(parseFloat(h)/100))}}this.originPoint[x]=h}),w&&J.postRender(()=>w(c,d)),Pl(this.visualElement,"transform");const{animationState:y}=this.visualElement;y&&y.setActive("whileDrag",!0)},s=(c,d)=>{const{dragPropagation:f,dragDirectionLock:m,onDirectionLock:w,onDrag:y}=this.getProps();if(!f&&!this.openDragLock)return;const{offset:x}=d;if(m&&this.currentDirection===null){this.currentDirection=BE(x),this.currentDirection!==null&&w&&w(this.currentDirection);return}this.updateAxis("x",d.point,x),this.updateAxis("y",d.point,x),this.visualElement.render(),y&&y(c,d)},a=(c,d)=>this.stop(c,d),l=()=>Je(c=>{var d;return this.getAnimationState(c)==="paused"&&((d=this.getAxisMotionValue(c).animation)===null||d===void 0?void 0:d.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new Qv(t,{onSessionStart:o,onStart:i,onMove:s,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:ng(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:o}=n;this.startAnimation(o);const{onDragEnd:i}=this.getProps();i&&J.postRender(()=>i(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:o}=this.getProps();if(!r||!oi(t,o,this.currentDirection))return;const i=this.getAxisMotionValue(t);let s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=AE(s,this.constraints[t],this.elastic[t])),i.set(s)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),o=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,i=this.constraints;n&&Jn(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&o?this.constraints=ME(o.layoutBox,n):this.constraints=!1,this.elastic=LE(r),i!==this.constraints&&o&&this.constraints&&!this.hasMutatedConstraints&&Je(s=>{this.constraints!==!1&&this.getAxisMotionValue(s)&&(this.constraints[s]=DE(o.layoutBox[s],this.constraints[s]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!Jn(t))return!1;const r=t.current,{projection:o}=this.visualElement;if(!o||!o.layout)return!1;const i=IE(r,o.root,this.visualElement.getTransformPagePoint());let s=_E(o.layout.layoutBox,i);if(n){const a=n(jE(s));this.hasMutatedConstraints=!!a,a&&(s=qv(a))}return s}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:o,dragTransition:i,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},u=Je(c=>{if(!oi(c,n,this.currentDirection))return;let d=l&&l[c]||{};s&&(d={min:0,max:0});const f=o?200:1e6,m=o?40:1e7,w={type:"inertia",velocity:r?t[c]:0,bounceStiffness:f,bounceDamping:m,timeConstant:750,restDelta:1,restSpeed:10,...i,...d};return this.startAxisValueAnimation(c,w)});return Promise.all(u).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return Pl(this.visualElement,t),r.start(Sc(t,r,0,n,this.visualElement,!1))}stopAnimation(){Je(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Je(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n=`_drag${t.toUpperCase()}`,r=this.visualElement.getProps(),o=r[n];return o||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){Je(n=>{const{drag:r}=this.getProps();if(!oi(n,r,this.currentDirection))return;const{projection:o}=this.visualElement,i=this.getAxisMotionValue(n);if(o&&o.layout){const{min:s,max:a}=o.layout.layoutBox[n];i.set(t[n]-oe(s,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!Jn(n)||!r||!this.constraints)return;this.stopAnimation();const o={x:0,y:0};Je(s=>{const a=this.getAxisMotionValue(s);if(a&&this.constraints!==!1){const l=a.get();o[s]=NE({min:l,max:l},this.constraints[s])}});const{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Je(s=>{if(!oi(s,t,null))return;const a=this.getAxisMotionValue(s),{min:l,max:u}=this.constraints[s];a.set(oe(l,u,o[s]))})}addListeners(){if(!this.visualElement.current)return;FE.set(this.visualElement,this);const t=this.visualElement.current,n=to(t,"pointerdown",l=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(l)}),r=()=>{const{dragConstraints:l}=this.getProps();Jn(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:o}=this.visualElement,i=o.addEventListener("measure",r);o&&!o.layout&&(o.root&&o.root.updateScroll(),o.updateLayout()),J.read(r);const s=bo(window,"resize",()=>this.scalePositionWithinConstraints()),a=o.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(Je(c=>{const d=this.getAxisMotionValue(c);d&&(this.originPoint[c]+=l[c].translate,d.set(d.get()+l[c].translate))}),this.visualElement.render())});return()=>{s(),n(),i(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:o=!1,dragConstraints:i=!1,dragElastic:s=Dl,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:o,dragConstraints:i,dragElastic:s,dragMomentum:a}}}function oi(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function BE(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class UE extends mn{constructor(t){super(t),this.removeGroupControls=Ge,this.removeListeners=Ge,this.controls=new zE(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Ge}unmount(){this.removeGroupControls(),this.removeListeners()}}const jf=e=>(t,n)=>{e&&J.postRender(()=>e(t,n))};class $E extends mn{constructor(){super(...arguments),this.removePointerDownListener=Ge}onPointerDown(t){this.session=new Qv(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ng(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:o}=this.node.getProps();return{onSessionStart:jf(t),onStart:jf(n),onMove:r,onEnd:(i,s)=>{delete this.session,o&&J.postRender(()=>o(i,s))}}}mount(){this.removePointerDownListener=to(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Ei={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Of(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const jr={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(V.test(e))e=parseFloat(e);else return e;const n=Of(e,t.target.x),r=Of(e,t.target.y);return`${n}% ${r}%`}},WE={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,o=dn.parse(e);if(o.length>5)return r;const i=dn.createTransformer(e),s=typeof o[0]!="number"?1:0,a=n.x.scale*t.x,l=n.y.scale*t.y;o[0+s]/=a,o[1+s]/=l;const u=oe(a,l,.5);return typeof o[2+s]=="number"&&(o[2+s]/=u),typeof o[3+s]=="number"&&(o[3+s]/=u),i(o)}};class HE extends g.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:o}=this.props,{projection:i}=t;dx(KE),i&&(n.group&&n.group.add(i),r&&r.register&&o&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),Ei.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:o,isPresent:i}=this.props,s=r.projection;return s&&(s.isPresent=i,o||t.layoutDependency!==n||n===void 0?s.willUpdate():this.safeToRemove(),t.isPresent!==i&&(i?s.promote():s.relegate()||J.postRender(()=>{const a=s.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Zu.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:o}=t;o&&(o.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(o),r&&r.deregister&&r.deregister(o))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function rg(e){const[t,n]=Fm(),r=g.useContext($u);return T.jsx(HE,{...e,layoutGroup:r,switchLayoutGroup:g.useContext(Gm),isPresent:t,safeToRemove:n})}const KE={borderRadius:{...jr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:jr,borderTopRightRadius:jr,borderBottomLeftRadius:jr,borderBottomRightRadius:jr,boxShadow:WE};function GE(e,t,n){const r=Re(e)?e:Po(e);return r.start(Sc("",r,t,n)),r.animation}function QE(e){return e instanceof SVGElement&&e.tagName!=="svg"}const YE=(e,t)=>e.depth-t.depth;class XE{constructor(){this.children=[],this.isDirty=!1}add(t){dc(this.children,t),this.isDirty=!0}remove(t){fc(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(YE),this.isDirty=!1,this.children.forEach(t)}}function ZE(e,t){const n=St.now(),r=({timestamp:o})=>{const i=o-n;i>=t&&(cn(r),e(i-t))};return J.read(r,!0),()=>cn(r)}const og=["TopLeft","TopRight","BottomLeft","BottomRight"],qE=og.length,Vf=e=>typeof e=="string"?parseFloat(e):e,If=e=>typeof e=="number"||V.test(e);function JE(e,t,n,r,o,i){o?(e.opacity=oe(0,n.opacity!==void 0?n.opacity:1,eT(r)),e.opacityExit=oe(t.opacity!==void 0?t.opacity:1,0,tT(r))):i&&(e.opacity=oe(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let s=0;s<qE;s++){const a=`border${og[s]}Radius`;let l=Ff(t,a),u=Ff(n,a);if(l===void 0&&u===void 0)continue;l||(l=0),u||(u=0),l===0||u===0||If(l)===If(u)?(e[a]=Math.max(oe(Vf(l),Vf(u),r),0),(xt.test(u)||xt.test(l))&&(e[a]+="%")):e[a]=u}(t.rotate||n.rotate)&&(e.rotate=oe(t.rotate||0,n.rotate||0,r))}function Ff(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const eT=ig(0,.5,Tv),tT=ig(.5,.95,Ge);function ig(e,t,n){return r=>r<e?0:r>t?1:n(yr(e,t,r))}function zf(e,t){e.min=t.min,e.max=t.max}function qe(e,t){zf(e.x,t.x),zf(e.y,t.y)}function Bf(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function Uf(e,t,n,r,o){return e-=t,e=rs(e,1/n,r),o!==void 0&&(e=rs(e,1/o,r)),e}function nT(e,t=0,n=1,r=.5,o,i=e,s=e){if(xt.test(t)&&(t=parseFloat(t),t=oe(s.min,s.max,t/100)-s.min),typeof t!="number")return;let a=oe(i.min,i.max,r);e===i&&(a-=t),e.min=Uf(e.min,t,n,a,o),e.max=Uf(e.max,t,n,a,o)}function $f(e,t,[n,r,o],i,s){nT(e,t[n],t[r],t[o],t.scale,i,s)}const rT=["x","scaleX","originX"],oT=["y","scaleY","originY"];function Wf(e,t,n,r){$f(e.x,t,rT,n?n.x:void 0,r?r.x:void 0),$f(e.y,t,oT,n?n.y:void 0,r?r.y:void 0)}function Hf(e){return e.translate===0&&e.scale===1}function sg(e){return Hf(e.x)&&Hf(e.y)}function Kf(e,t){return e.min===t.min&&e.max===t.max}function iT(e,t){return Kf(e.x,t.x)&&Kf(e.y,t.y)}function Gf(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function ag(e,t){return Gf(e.x,t.x)&&Gf(e.y,t.y)}function Qf(e){return Ye(e.x)/Ye(e.y)}function Yf(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class sT{constructor(){this.members=[]}add(t){dc(this.members,t),t.scheduleRender()}remove(t){if(fc(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(o=>t===o);if(n===0)return!1;let r;for(let o=n;o>=0;o--){const i=this.members[o];if(i.isPresent!==!1){r=i;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:o}=t.options;o===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function aT(e,t,n){let r="";const o=e.x.translate/t.x,i=e.y.translate/t.y,s=(n==null?void 0:n.z)||0;if((o||i||s)&&(r=`translate3d(${o}px, ${i}px, ${s}px) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{transformPerspective:u,rotate:c,rotateX:d,rotateY:f,skewX:m,skewY:w}=n;u&&(r=`perspective(${u}px) ${r}`),c&&(r+=`rotate(${c}deg) `),d&&(r+=`rotateX(${d}deg) `),f&&(r+=`rotateY(${f}deg) `),m&&(r+=`skewX(${m}deg) `),w&&(r+=`skewY(${w}deg) `)}const a=e.x.scale*t.x,l=e.y.scale*t.y;return(a!==1||l!==1)&&(r+=`scale(${a}, ${l})`),r||"none"}const Sn={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},Wr=typeof window<"u"&&window.MotionDebug!==void 0,ga=["","X","Y","Z"],lT={visibility:"hidden"},Xf=1e3;let uT=0;function ya(e,t,n,r){const{latestValues:o}=t;o[e]&&(n[e]=o[e],t.setStaticValue(e,0),r&&(r[e]=0))}function lg(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const n=vv(t);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:o,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",J,!(o||i))}const{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&lg(r)}function ug({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:o}){return class{constructor(s={},a=t==null?void 0:t()){this.id=uT++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,Wr&&(Sn.totalNodes=Sn.resolvedTargetDeltas=Sn.recalculatedProjection=0),this.nodes.forEach(fT),this.nodes.forEach(gT),this.nodes.forEach(yT),this.nodes.forEach(pT),Wr&&window.MotionDebug.record(Sn)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=s,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new XE)}addEventListener(s,a){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new pc),this.eventHandlers.get(s).add(a)}notifyListeners(s,...a){const l=this.eventHandlers.get(s);l&&l.notify(...a)}hasListeners(s){return this.eventHandlers.has(s)}mount(s,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=QE(s),this.instance=s;const{layoutId:l,layout:u,visualElement:c}=this.options;if(c&&!c.current&&c.mount(s),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(u||l)&&(this.isLayoutDirty=!0),e){let d;const f=()=>this.root.updateBlockedByResize=!1;e(s,()=>{this.root.updateBlockedByResize=!0,d&&d(),d=ZE(f,250),Ei.hasAnimatedSinceResize&&(Ei.hasAnimatedSinceResize=!1,this.nodes.forEach(qf))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&c&&(l||u)&&this.addEventListener("didUpdate",({delta:d,hasLayoutChanged:f,hasRelativeTargetChanged:m,layout:w})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const y=this.options.transition||c.getDefaultTransition()||TT,{onLayoutAnimationStart:x,onLayoutAnimationComplete:h}=c.getProps(),p=!this.targetLayout||!ag(this.targetLayout,w)||m,v=!f&&m;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||v||f&&(p||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(d,v);const S={...ac(y,"layout"),onPlay:x,onComplete:h};(c.shouldReduceMotion||this.options.layoutRoot)&&(S.delay=0,S.type=!1),this.startAnimation(S)}else f||qf(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=w})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const s=this.getStack();s&&s.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,cn(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(wT),this.animationId++)}getTransformTemplate(){const{visualElement:s}=this.options;return s&&s.getProps().transformTemplate}willUpdate(s=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&lg(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const d=this.path[c];d.shouldResetTransform=!0,d.updateScroll("snapshot"),d.options.layoutRoot&&d.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Zf);return}this.isUpdating||this.nodes.forEach(mT),this.isUpdating=!1,this.nodes.forEach(vT),this.nodes.forEach(cT),this.nodes.forEach(dT),this.clearAllSnapshots();const a=St.now();Se.delta=jt(0,1e3/60,a-Se.timestamp),Se.timestamp=a,Se.isProcessing=!0,la.update.process(Se),la.preRender.process(Se),la.render.process(Se),Se.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Zu.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(hT),this.sharedNodes.forEach(xT)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,J.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){J.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const s=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ce(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,s?s.layoutBox:void 0)}updateScroll(s="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(a=!1),a){const l=r(this.instance);this.scroll={animationId:this.root.animationId,phase:s,isRoot:l,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!o)return;const s=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!sg(this.projectionDelta),l=this.getTransformTemplate(),u=l?l(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;s&&(a||xn(this.latestValues)||c)&&(o(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return s&&(l=this.removeTransform(l)),CT(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var s;const{visualElement:a}=this.options;if(!a)return ce();const l=a.measureViewportBox();if(!(((s=this.scroll)===null||s===void 0?void 0:s.wasRoot)||this.path.some(PT))){const{scroll:c}=this.root;c&&(nr(l.x,c.offset.x),nr(l.y,c.offset.y))}return l}removeElementScroll(s){var a;const l=ce();if(qe(l,s),!((a=this.scroll)===null||a===void 0)&&a.wasRoot)return l;for(let u=0;u<this.path.length;u++){const c=this.path[u],{scroll:d,options:f}=c;c!==this.root&&d&&f.layoutScroll&&(d.wasRoot&&qe(l,s),nr(l.x,d.offset.x),nr(l.y,d.offset.y))}return l}applyTransform(s,a=!1){const l=ce();qe(l,s);for(let u=0;u<this.path.length;u++){const c=this.path[u];!a&&c.options.layoutScroll&&c.scroll&&c!==c.root&&rr(l,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),xn(c.latestValues)&&rr(l,c.latestValues)}return xn(this.latestValues)&&rr(l,this.latestValues),l}removeTransform(s){const a=ce();qe(a,s);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!xn(u.latestValues))continue;Ll(u.latestValues)&&u.updateSnapshot();const c=ce(),d=u.measurePageBox();qe(c,d),Wf(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return xn(this.latestValues)&&Wf(a,this.latestValues),a}setTargetDelta(s){this.targetDelta=s,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Se.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(s=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==l;if(!(s||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:d,layoutId:f}=this.options;if(!(!this.layout||!(d||f))){if(this.resolvedRelativeTargetAt=Se.timestamp,!this.targetDelta&&!this.relativeTarget){const m=this.getClosestProjectingParent();m&&m.layout&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ce(),this.relativeTargetOrigin=ce(),ro(this.relativeTargetOrigin,this.layout.layoutBox,m.layout.layoutBox),qe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=ce(),this.targetWithTransforms=ce()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),RE(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):qe(this.target,this.layout.layoutBox),eg(this.target,this.targetDelta)):qe(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const m=this.getClosestProjectingParent();m&&!!m.resumingFrom==!!this.resumingFrom&&!m.options.layoutScroll&&m.target&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ce(),this.relativeTargetOrigin=ce(),ro(this.relativeTargetOrigin,this.target,m.target),qe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}Wr&&Sn.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Ll(this.parent.latestValues)||Jv(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var s;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let u=!0;if((this.isProjectionDirty||!((s=this.parent)===null||s===void 0)&&s.isProjectionDirty)&&(u=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===Se.timestamp&&(u=!1),u)return;const{layout:c,layoutId:d}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||d))return;qe(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,m=this.treeScale.y;VE(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=ce());const{target:w}=a;if(!w){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(Bf(this.prevProjectionDelta.x,this.projectionDelta.x),Bf(this.prevProjectionDelta.y,this.projectionDelta.y)),no(this.projectionDelta,this.layoutCorrected,w,this.latestValues),(this.treeScale.x!==f||this.treeScale.y!==m||!Yf(this.projectionDelta.x,this.prevProjectionDelta.x)||!Yf(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",w)),Wr&&Sn.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){var a;if((a=this.options.visualElement)===null||a===void 0||a.scheduleRender(),s){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=tr(),this.projectionDelta=tr(),this.projectionDeltaWithTransform=tr()}setAnimationOrigin(s,a=!1){const l=this.snapshot,u=l?l.latestValues:{},c={...this.latestValues},d=tr();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=ce(),m=l?l.source:void 0,w=this.layout?this.layout.source:void 0,y=m!==w,x=this.getStack(),h=!x||x.members.length<=1,p=!!(y&&!h&&this.options.crossfade===!0&&!this.path.some(ET));this.animationProgress=0;let v;this.mixTargetDelta=S=>{const E=S/1e3;Jf(d.x,s.x,E),Jf(d.y,s.y,E),this.setTargetDelta(d),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(ro(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),ST(this.relativeTarget,this.relativeTargetOrigin,f,E),v&&iT(this.relativeTarget,v)&&(this.isProjectionDirty=!1),v||(v=ce()),qe(v,this.relativeTarget)),y&&(this.animationValues=c,JE(c,u,this.latestValues,E,p,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=E},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(s){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(cn(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=J.update(()=>{Ei.hasAnimatedSinceResize=!0,this.currentAnimation=GE(0,Xf,{...s,onUpdate:a=>{this.mixTargetDelta(a),s.onUpdate&&s.onUpdate(a)},onComplete:()=>{s.onComplete&&s.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const s=this.getStack();s&&s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Xf),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:a,target:l,layout:u,latestValues:c}=s;if(!(!a||!l||!u)){if(this!==s&&this.layout&&u&&cg(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||ce();const d=Ye(this.layout.layoutBox.x);l.x.min=s.target.x.min,l.x.max=l.x.min+d;const f=Ye(this.layout.layoutBox.y);l.y.min=s.target.y.min,l.y.max=l.y.min+f}qe(a,l),rr(a,c),no(this.projectionDeltaWithTransform,this.layoutCorrected,a,c)}}registerSharedNode(s,a){this.sharedNodes.has(s)||this.sharedNodes.set(s,new sT),this.sharedNodes.get(s).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:a}=this.options;return a?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:a}=this.options;return a?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:a,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),s&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetSkewAndRotation(){const{visualElement:s}=this.options;if(!s)return;let a=!1;const{latestValues:l}=s;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const u={};l.z&&ya("z",s,u,this.animationValues);for(let c=0;c<ga.length;c++)ya(`rotate${ga[c]}`,s,u,this.animationValues),ya(`skew${ga[c]}`,s,u,this.animationValues);s.render();for(const c in u)s.setStaticValue(c,u[c]),this.animationValues&&(this.animationValues[c]=u[c]);s.scheduleRender()}getProjectionStyles(s){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return lT;const u={visibility:""},c=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=xi(s==null?void 0:s.pointerEvents)||"",u.transform=c?c(this.latestValues,""):"none",u;const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){const y={};return this.options.layoutId&&(y.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,y.pointerEvents=xi(s==null?void 0:s.pointerEvents)||""),this.hasProjected&&!xn(this.latestValues)&&(y.transform=c?c({},""):"none",this.hasProjected=!1),y}const f=d.animationValues||d.latestValues;this.applyTransformsToTarget(),u.transform=aT(this.projectionDeltaWithTransform,this.treeScale,f),c&&(u.transform=c(f,u.transform));const{x:m,y:w}=this.projectionDelta;u.transformOrigin=`${m.origin*100}% ${w.origin*100}% 0`,d.animationValues?u.opacity=d===this?(l=(a=f.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:u.opacity=d===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const y in qi){if(f[y]===void 0)continue;const{correct:x,applyTo:h}=qi[y],p=u.transform==="none"?f[y]:x(f[y],d);if(h){const v=h.length;for(let S=0;S<v;S++)u[h[S]]=p}else u[y]=p}return this.options.layoutId&&(u.pointerEvents=d===this?xi(s==null?void 0:s.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var a;return(a=s.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(Zf),this.root.sharedNodes.clear()}}}function cT(e){e.updateLayout()}function dT(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:o}=e.layout,{animationType:i}=e.options,s=n.source!==e.layout.source;i==="size"?Je(d=>{const f=s?n.measuredBox[d]:n.layoutBox[d],m=Ye(f);f.min=r[d].min,f.max=f.min+m}):cg(i,n.layoutBox,r)&&Je(d=>{const f=s?n.measuredBox[d]:n.layoutBox[d],m=Ye(r[d]);f.max=f.min+m,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[d].max=e.relativeTarget[d].min+m)});const a=tr();no(a,r,n.layoutBox);const l=tr();s?no(l,e.applyTransform(o,!0),n.measuredBox):no(l,r,n.layoutBox);const u=!sg(a);let c=!1;if(!e.resumeFrom){const d=e.getClosestProjectingParent();if(d&&!d.resumeFrom){const{snapshot:f,layout:m}=d;if(f&&m){const w=ce();ro(w,n.layoutBox,f.layoutBox);const y=ce();ro(y,r,m.layoutBox),ag(w,y)||(c=!0),d.options.layoutRoot&&(e.relativeTarget=y,e.relativeTargetOrigin=w,e.relativeParent=d)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:u,hasRelativeTargetChanged:c})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function fT(e){Wr&&Sn.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function pT(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function hT(e){e.clearSnapshot()}function Zf(e){e.clearMeasurements()}function mT(e){e.isLayoutDirty=!1}function vT(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function qf(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function gT(e){e.resolveTargetDelta()}function yT(e){e.calcProjection()}function wT(e){e.resetSkewAndRotation()}function xT(e){e.removeLeadSnapshot()}function Jf(e,t,n){e.translate=oe(t.translate,0,n),e.scale=oe(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function ep(e,t,n,r){e.min=oe(t.min,n.min,r),e.max=oe(t.max,n.max,r)}function ST(e,t,n,r){ep(e.x,t.x,n.x,r),ep(e.y,t.y,n.y,r)}function ET(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const TT={duration:.45,ease:[.4,0,.1,1]},tp=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),np=tp("applewebkit/")&&!tp("chrome/")?Math.round:Ge;function rp(e){e.min=np(e.min),e.max=np(e.max)}function CT(e){rp(e.x),rp(e.y)}function cg(e,t,n){return e==="position"||e==="preserve-aspect"&&!bE(Qf(t),Qf(n),.2)}function PT(e){var t;return e!==e.root&&((t=e.scroll)===null||t===void 0?void 0:t.wasRoot)}const kT=ug({attachResizeListener:(e,t)=>bo(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),wa={current:void 0},dg=ug({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!wa.current){const e=new kT({});e.mount(window),e.setOptions({layoutScroll:!0}),wa.current=e}return wa.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),bT={pan:{Feature:$E},drag:{Feature:UE,ProjectionNode:dg,MeasureLayout:rg}};function op(e,t,n){const{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover",n==="Start");const o="onHover"+n,i=r[o];i&&J.postRender(()=>i(t,Oo(t)))}class RT extends mn{mount(){const{current:t}=this.node;t&&(this.unmount=bx(t,n=>(op(this.node,n,"Start"),r=>op(this.node,r,"End"))))}unmount(){}}class AT extends mn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=jo(bo(this.node.current,"focus",()=>this.onFocus()),bo(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function ip(e,t,n){const{props:r}=e;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap",n==="Start");const o="onTap"+(n==="End"?"":n),i=r[o];i&&J.postRender(()=>i(t,Oo(t)))}class MT extends mn{mount(){const{current:t}=this.node;t&&(this.unmount=_x(t,n=>(ip(this.node,n,"Start"),(r,{success:o})=>ip(this.node,r,o?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Ol=new WeakMap,xa=new WeakMap,_T=e=>{const t=Ol.get(e.target);t&&t(e)},NT=e=>{e.forEach(_T)};function DT({root:e,...t}){const n=e||document;xa.has(n)||xa.set(n,{});const r=xa.get(n),o=JSON.stringify(t);return r[o]||(r[o]=new IntersectionObserver(NT,{root:e,...t})),r[o]}function LT(e,t,n){const r=DT(t);return Ol.set(e,n),r.observe(e),()=>{Ol.delete(e),r.unobserve(e)}}const jT={some:0,all:1};class OT extends mn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:o="some",once:i}=t,s={root:n?n.current:void 0,rootMargin:r,threshold:typeof o=="number"?o:jT[o]},a=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,i&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:d}=this.node.getProps(),f=u?c:d;f&&f(l)};return LT(this.node.current,s,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(VT(t,n))&&this.startObserver()}unmount(){}}function VT({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const IT={inView:{Feature:OT},tap:{Feature:MT},focus:{Feature:AT},hover:{Feature:RT}},FT={layout:{ProjectionNode:dg,MeasureLayout:rg}},Vl={current:null},fg={current:!1};function zT(){if(fg.current=!0,!!Ku)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Vl.current=e.matches;e.addListener(t),t()}else Vl.current=!1}const BT=[...Vv,ke,dn],UT=e=>BT.find(Ov(e)),sp=new WeakMap;function $T(e,t,n){for(const r in t){const o=t[r],i=n[r];if(Re(o))e.addValue(r,o);else if(Re(i))e.addValue(r,Po(o,{owner:e}));else if(i!==o)if(e.hasValue(r)){const s=e.getValue(r);s.liveStyle===!0?s.jump(o):s.hasAnimated||s.set(o)}else{const s=e.getStaticValue(r);e.addValue(r,Po(s!==void 0?s:o,{owner:e}))}}for(const r in n)t[r]===void 0&&e.removeValue(r);return t}const ap=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class WT{scrapeMotionValuesFromProps(t,n,r){return{}}constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:o,blockInitialAnimation:i,visualState:s},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=wc,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const m=St.now();this.renderScheduledAt<m&&(this.renderScheduledAt=m,J.render(this.render,!1,!0))};const{latestValues:l,renderState:u,onUpdate:c}=s;this.onUpdate=c,this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=u,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=o,this.options=a,this.blockInitialAnimation=!!i,this.isControllingVariants=As(n),this.isVariantNode=Hm(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:d,...f}=this.scrapeMotionValuesFromProps(n,{},this);for(const m in f){const w=f[m];l[m]!==void 0&&Re(w)&&w.set(l[m],!1)}}mount(t){this.current=t,sp.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),fg.current||zT(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Vl.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){sp.delete(this.current),this.projection&&this.projection.unmount(),cn(this.notifyUpdate),cn(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const n=this.features[t];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(t,n){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const r=Fn.has(t),o=n.on("change",a=>{this.latestValues[t]=a,this.props.onUpdate&&J.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),i=n.on("renderRequest",this.scheduleRender);let s;window.MotionCheckAppearSync&&(s=window.MotionCheckAppearSync(this,t,n)),this.valueSubscriptions.set(t,()=>{o(),i(),s&&s(),n.owner&&n.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in wr){const n=wr[t];if(!n)continue;const{isEnabled:r,Feature:o}=n;if(!this.features[t]&&o&&r(this.props)&&(this.features[t]=new o(this)),this.features[t]){const i=this.features[t];i.isMounted?i.update():(i.mount(),i.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ce()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<ap.length;r++){const o=ap[r];this.propEventSubscriptions[o]&&(this.propEventSubscriptions[o](),delete this.propEventSubscriptions[o]);const i="on"+o,s=t[i];s&&(this.propEventSubscriptions[o]=this.on(o,s))}this.prevMotionValues=$T(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){const r=this.values.get(t);n!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,n),this.values.set(t,n),this.latestValues[t]=n.get())}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=Po(n===null?void 0:n,{owner:this}),this.addValue(t,r)),r}readValue(t,n){var r;let o=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(r=this.getBaseTargetFromProps(this.props,t))!==null&&r!==void 0?r:this.readValueFromInstance(this.current,t,this.options);return o!=null&&(typeof o=="string"&&(Lv(o)||Pv(o))?o=parseFloat(o):!UT(o)&&dn.test(n)&&(o=_v(t,n)),this.setBaseTarget(t,Re(o)?o.get():o)),Re(o)?o.get():o}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props;let o;if(typeof r=="string"||typeof r=="object"){const s=Ju(this.props,r,(n=this.presenceContext)===null||n===void 0?void 0:n.custom);s&&(o=s[t])}if(r&&o!==void 0)return o;const i=this.getBaseTargetFromProps(this.props,t);return i!==void 0&&!Re(i)?i:this.initialValues[t]!==void 0&&o===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new pc),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class pg extends WT{constructor(){super(...arguments),this.KeyframeResolver=Iv}sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Re(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function HT(e){return window.getComputedStyle(e)}class KT extends pg{constructor(){super(...arguments),this.type="html",this.renderInstance=ev}readValueFromInstance(t,n){if(Fn.has(n)){const r=yc(n);return r&&r.default||0}else{const r=HT(t),o=(Zm(n)?r.getPropertyValue(n):r[n])||0;return typeof o=="string"?o.trim():o}}measureInstanceViewportBox(t,{transformPagePoint:n}){return tg(t,n)}build(t,n,r){nc(t,n,r.transformTemplate)}scrapeMotionValuesFromProps(t,n,r){return sc(t,n,r)}}class GT extends pg{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ce}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(Fn.has(n)){const r=yc(n);return r&&r.default||0}return n=tv.has(n)?n:Xu(n),t.getAttribute(n)}scrapeMotionValuesFromProps(t,n,r){return ov(t,n,r)}build(t,n,r){rc(t,n,this.isSVGTag,r.transformTemplate)}renderInstance(t,n,r,o){nv(t,n,r,o)}mount(t){this.isSVGTag=ic(t.tagName),super.mount(t)}}const QT=(e,t)=>qu(e)?new GT(t):new KT(t,{allowProjection:e!==g.Fragment}),YT=xx({...yE,...IT,...bT,...FT},QT),En=jw(YT);function os(){return os=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},os.apply(this,arguments)}function XT(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function hg(...e){return t=>e.forEach(n=>XT(n,t))}function xk(...e){return g.useCallback(hg(...e),e)}const mg=g.forwardRef((e,t)=>{const{children:n,...r}=e,o=g.Children.toArray(n),i=o.find(qT);if(i){const s=i.props.children,a=o.map(l=>l===i?g.Children.count(s)>1?g.Children.only(null):g.isValidElement(s)?s.props.children:null:l);return g.createElement(Il,os({},r,{ref:t}),g.isValidElement(s)?g.cloneElement(s,void 0,a):null)}return g.createElement(Il,os({},r,{ref:t}),n)});mg.displayName="Slot";const Il=g.forwardRef((e,t)=>{const{children:n,...r}=e;return g.isValidElement(n)?g.cloneElement(n,{...JT(r,n.props),ref:t?hg(t,n.ref):n.ref}):g.Children.count(n)>1?g.Children.only(null):null});Il.displayName="SlotClone";const ZT=({children:e})=>g.createElement(g.Fragment,null,e);function qT(e){return g.isValidElement(e)&&e.type===ZT}function JT(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...a)=>{i(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function vg(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=vg(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}function eC(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=vg(e))&&(r&&(r+=" "),r+=t);return r}const lp=e=>typeof e=="boolean"?"".concat(e):e===0?"0":e,up=eC,gg=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return up(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:i}=t,s=Object.keys(o).map(u=>{const c=n==null?void 0:n[u],d=i==null?void 0:i[u];if(c===null)return null;const f=lp(c)||lp(d);return o[u][f]}),a=n&&Object.entries(n).reduce((u,c)=>{let[d,f]=c;return f===void 0||(u[d]=f),u},{}),l=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,c)=>{let{class:d,className:f,...m}=c;return Object.entries(m).every(w=>{let[y,x]=w;return Array.isArray(x)?x.includes({...i,...a}[y]):{...i,...a}[y]===x})?[...u,d,f]:u},[]);return up(e,s,l,n==null?void 0:n.class,n==null?void 0:n.className)};/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var tC={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nC=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),Vo=(e,t)=>{const n=g.forwardRef(({color:r="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:s,className:a="",children:l,...u},c)=>g.createElement("svg",{ref:c,...tC,width:o,height:o,stroke:r,strokeWidth:s?Number(i)*24/Number(o):i,className:["lucide",`lucide-${nC(e)}`,a].join(" "),...u},[...t.map(([d,f])=>g.createElement(d,f)),...Array.isArray(l)?l:[l]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rC=Vo("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oC=Vo("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iC=Vo("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sC=Vo("Wallet",[["path",{d:"M21 12V7H5a2 2 0 0 1 0-4h14v4",key:"195gfw"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h16v-5",key:"195n9w"}],["path",{d:"M18 12a2 2 0 0 0 0 4h4v-4Z",key:"vllfpd"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yg=Vo("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function wg(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=wg(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function aC(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=wg(e))&&(r&&(r+=" "),r+=t);return r}const Ec="-";function lC(e){const t=cC(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;function o(s){const a=s.split(Ec);return a[0]===""&&a.length!==1&&a.shift(),xg(a,t)||uC(s)}function i(s,a){const l=n[s]||[];return a&&r[s]?[...l,...r[s]]:l}return{getClassGroupId:o,getConflictingClassGroupIds:i}}function xg(e,t){var s;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?xg(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const i=e.join(Ec);return(s=t.validators.find(({validator:a})=>a(i)))==null?void 0:s.classGroupId}const cp=/^\[(.+)\]$/;function uC(e){if(cp.test(e)){const t=cp.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}}function cC(e){const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return fC(Object.entries(e.classGroups),n).forEach(([i,s])=>{Fl(s,r,i,t)}),r}function Fl(e,t,n,r){e.forEach(o=>{if(typeof o=="string"){const i=o===""?t:dp(t,o);i.classGroupId=n;return}if(typeof o=="function"){if(dC(o)){Fl(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([i,s])=>{Fl(s,dp(t,i),n,r)})})}function dp(e,t){let n=e;return t.split(Ec).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n}function dC(e){return e.isThemeGetter}function fC(e,t){return t?e.map(([n,r])=>{const o=r.map(i=>typeof i=="string"?t+i:typeof i=="object"?Object.fromEntries(Object.entries(i).map(([s,a])=>[t+s,a])):i);return[n,o]}):e}function pC(e){if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;function o(i,s){n.set(i,s),t++,t>e&&(t=0,r=n,n=new Map)}return{get(i){let s=n.get(i);if(s!==void 0)return s;if((s=r.get(i))!==void 0)return o(i,s),s},set(i,s){n.has(i)?n.set(i,s):o(i,s)}}}const Sg="!";function hC(e){const t=e.separator,n=t.length===1,r=t[0],o=t.length;return function(s){const a=[];let l=0,u=0,c;for(let y=0;y<s.length;y++){let x=s[y];if(l===0){if(x===r&&(n||s.slice(y,y+o)===t)){a.push(s.slice(u,y)),u=y+o;continue}if(x==="/"){c=y;continue}}x==="["?l++:x==="]"&&l--}const d=a.length===0?s:s.substring(u),f=d.startsWith(Sg),m=f?d.substring(1):d,w=c&&c>u?c-u:void 0;return{modifiers:a,hasImportantModifier:f,baseClassName:m,maybePostfixModifierPosition:w}}}function mC(e){if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t}function vC(e){return{cache:pC(e.cacheSize),splitModifiers:hC(e),...lC(e)}}const gC=/\s+/;function yC(e,t){const{splitModifiers:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,i=new Set;return e.trim().split(gC).map(s=>{const{modifiers:a,hasImportantModifier:l,baseClassName:u,maybePostfixModifierPosition:c}=n(s);let d=r(c?u.substring(0,c):u),f=!!c;if(!d){if(!c)return{isTailwindClass:!1,originalClassName:s};if(d=r(u),!d)return{isTailwindClass:!1,originalClassName:s};f=!1}const m=mC(a).join(":");return{isTailwindClass:!0,modifierId:l?m+Sg:m,classGroupId:d,originalClassName:s,hasPostfixModifier:f}}).reverse().filter(s=>{if(!s.isTailwindClass)return!0;const{modifierId:a,classGroupId:l,hasPostfixModifier:u}=s,c=a+l;return i.has(c)?!1:(i.add(c),o(l,u).forEach(d=>i.add(a+d)),!0)}).reverse().map(s=>s.originalClassName).join(" ")}function wC(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=Eg(t))&&(r&&(r+=" "),r+=n);return r}function Eg(e){if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Eg(e[r]))&&(n&&(n+=" "),n+=t);return n}function xC(e,...t){let n,r,o,i=s;function s(l){const u=t.reduce((c,d)=>d(c),e());return n=vC(u),r=n.cache.get,o=n.cache.set,i=a,a(l)}function a(l){const u=r(l);if(u)return u;const c=yC(l,n);return o(l,c),c}return function(){return i(wC.apply(null,arguments))}}function X(e){const t=n=>n[e]||[];return t.isThemeGetter=!0,t}const Tg=/^\[(?:([a-z-]+):)?(.+)\]$/i,SC=/^\d+\/\d+$/,EC=new Set(["px","full","screen"]),TC=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,CC=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,PC=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,kC=/^-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,bC=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/;function Tt(e){return bn(e)||EC.has(e)||SC.test(e)}function $t(e){return kr(e,"length",jC)}function bn(e){return!!e&&!Number.isNaN(Number(e))}function ii(e){return kr(e,"number",bn)}function Or(e){return!!e&&Number.isInteger(Number(e))}function RC(e){return e.endsWith("%")&&bn(e.slice(0,-1))}function F(e){return Tg.test(e)}function Wt(e){return TC.test(e)}const AC=new Set(["length","size","percentage"]);function MC(e){return kr(e,AC,Cg)}function _C(e){return kr(e,"position",Cg)}const NC=new Set(["image","url"]);function DC(e){return kr(e,NC,VC)}function LC(e){return kr(e,"",OC)}function Vr(){return!0}function kr(e,t,n){const r=Tg.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1}function jC(e){return CC.test(e)&&!PC.test(e)}function Cg(){return!1}function OC(e){return kC.test(e)}function VC(e){return bC.test(e)}function IC(){const e=X("colors"),t=X("spacing"),n=X("blur"),r=X("brightness"),o=X("borderColor"),i=X("borderRadius"),s=X("borderSpacing"),a=X("borderWidth"),l=X("contrast"),u=X("grayscale"),c=X("hueRotate"),d=X("invert"),f=X("gap"),m=X("gradientColorStops"),w=X("gradientColorStopPositions"),y=X("inset"),x=X("margin"),h=X("opacity"),p=X("padding"),v=X("saturate"),S=X("scale"),E=X("sepia"),P=X("skew"),k=X("space"),C=X("translate"),L=()=>["auto","contain","none"],N=()=>["auto","hidden","clip","visible","scroll"],_=()=>["auto",F,t],b=()=>[F,t],I=()=>["",Tt,$t],M=()=>["auto",bn,F],U=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],K=()=>["solid","dashed","dotted","double","none"],z=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter"],A=()=>["start","end","center","between","around","evenly","stretch"],j=()=>["","0",F],O=()=>["auto","avoid","all","avoid-page","page","left","right","column"],$=()=>[bn,ii],W=()=>[bn,F];return{cacheSize:500,separator:":",theme:{colors:[Vr],spacing:[Tt,$t],blur:["none","",Wt,F],brightness:$(),borderColor:[e],borderRadius:["none","","full",Wt,F],borderSpacing:b(),borderWidth:I(),contrast:$(),grayscale:j(),hueRotate:W(),invert:j(),gap:b(),gradientColorStops:[e],gradientColorStopPositions:[RC,$t],inset:_(),margin:_(),opacity:$(),padding:b(),saturate:$(),scale:$(),sepia:j(),skew:W(),space:b(),translate:b()},classGroups:{aspect:[{aspect:["auto","square","video",F]}],container:["container"],columns:[{columns:[Wt]}],"break-after":[{"break-after":O()}],"break-before":[{"break-before":O()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...U(),F]}],overflow:[{overflow:N()}],"overflow-x":[{"overflow-x":N()}],"overflow-y":[{"overflow-y":N()}],overscroll:[{overscroll:L()}],"overscroll-x":[{"overscroll-x":L()}],"overscroll-y":[{"overscroll-y":L()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[y]}],"inset-x":[{"inset-x":[y]}],"inset-y":[{"inset-y":[y]}],start:[{start:[y]}],end:[{end:[y]}],top:[{top:[y]}],right:[{right:[y]}],bottom:[{bottom:[y]}],left:[{left:[y]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Or,F]}],basis:[{basis:_()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",F]}],grow:[{grow:j()}],shrink:[{shrink:j()}],order:[{order:["first","last","none",Or,F]}],"grid-cols":[{"grid-cols":[Vr]}],"col-start-end":[{col:["auto",{span:["full",Or,F]},F]}],"col-start":[{"col-start":M()}],"col-end":[{"col-end":M()}],"grid-rows":[{"grid-rows":[Vr]}],"row-start-end":[{row:["auto",{span:[Or,F]},F]}],"row-start":[{"row-start":M()}],"row-end":[{"row-end":M()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",F]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",F]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...A()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...A(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...A(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[p]}],px:[{px:[p]}],py:[{py:[p]}],ps:[{ps:[p]}],pe:[{pe:[p]}],pt:[{pt:[p]}],pr:[{pr:[p]}],pb:[{pb:[p]}],pl:[{pl:[p]}],m:[{m:[x]}],mx:[{mx:[x]}],my:[{my:[x]}],ms:[{ms:[x]}],me:[{me:[x]}],mt:[{mt:[x]}],mr:[{mr:[x]}],mb:[{mb:[x]}],ml:[{ml:[x]}],"space-x":[{"space-x":[k]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[k]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",F,t]}],"min-w":[{"min-w":[F,t,"min","max","fit"]}],"max-w":[{"max-w":[F,t,"none","full","min","max","fit","prose",{screen:[Wt]},Wt]}],h:[{h:[F,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[F,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[F,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[F,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Wt,$t]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",ii]}],"font-family":[{font:[Vr]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",F]}],"line-clamp":[{"line-clamp":["none",bn,ii]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Tt,F]}],"list-image":[{"list-image":["none",F]}],"list-style-type":[{list:["none","disc","decimal",F]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[h]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[h]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...K(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Tt,$t]}],"underline-offset":[{"underline-offset":["auto",Tt,F]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:b()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",F]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",F]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[h]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...U(),_C]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",MC]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},DC]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[w]}],"gradient-via-pos":[{via:[w]}],"gradient-to-pos":[{to:[w]}],"gradient-from":[{from:[m]}],"gradient-via":[{via:[m]}],"gradient-to":[{to:[m]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[h]}],"border-style":[{border:[...K(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[h]}],"divide-style":[{divide:K()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...K()]}],"outline-offset":[{"outline-offset":[Tt,F]}],"outline-w":[{outline:[Tt,$t]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:I()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[h]}],"ring-offset-w":[{"ring-offset":[Tt,$t]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Wt,LC]}],"shadow-color":[{shadow:[Vr]}],opacity:[{opacity:[h]}],"mix-blend":[{"mix-blend":z()}],"bg-blend":[{"bg-blend":z()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",Wt,F]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[d]}],saturate:[{saturate:[v]}],sepia:[{sepia:[E]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[h]}],"backdrop-saturate":[{"backdrop-saturate":[v]}],"backdrop-sepia":[{"backdrop-sepia":[E]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",F]}],duration:[{duration:W()}],ease:[{ease:["linear","in","out","in-out",F]}],delay:[{delay:W()}],animate:[{animate:["none","spin","ping","pulse","bounce",F]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[S]}],"scale-x":[{"scale-x":[S]}],"scale-y":[{"scale-y":[S]}],rotate:[{rotate:[Or,F]}],"translate-x":[{"translate-x":[C]}],"translate-y":[{"translate-y":[C]}],"skew-x":[{"skew-x":[P]}],"skew-y":[{"skew-y":[P]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",F]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",F]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":b()}],"scroll-mx":[{"scroll-mx":b()}],"scroll-my":[{"scroll-my":b()}],"scroll-ms":[{"scroll-ms":b()}],"scroll-me":[{"scroll-me":b()}],"scroll-mt":[{"scroll-mt":b()}],"scroll-mr":[{"scroll-mr":b()}],"scroll-mb":[{"scroll-mb":b()}],"scroll-ml":[{"scroll-ml":b()}],"scroll-p":[{"scroll-p":b()}],"scroll-px":[{"scroll-px":b()}],"scroll-py":[{"scroll-py":b()}],"scroll-ps":[{"scroll-ps":b()}],"scroll-pe":[{"scroll-pe":b()}],"scroll-pt":[{"scroll-pt":b()}],"scroll-pr":[{"scroll-pr":b()}],"scroll-pb":[{"scroll-pb":b()}],"scroll-pl":[{"scroll-pl":b()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",F]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Tt,$t,ii]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}const FC=xC(IC);function vn(...e){return FC(aC(e))}const zC=gg("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"h-full gap-1 flex bg-[var(--link)] max-md:h-12 capitalize font-bold items-center hover:shadow-xl justify-center rounded-lg cursor-pointer px-6 py-3 transition duration-100 transform bg-[var(--button)] border-2 border-[var(--button-border)] text-[var(--button-text)] hoverd hover:bg-[var(--button-hover)] hover:text-[var(--button-text-hover)]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"h-full gap-1 flex max-md:h-12 capitalize font-bold items-center justify-center  bg-transparent rounded-lg cursor-pointer px-6 py-3 transition duration-100 transform border-[var(--button)] border-2 border-[var(--button-border)] text-[var(--outline-button-text)] hoverd hover:bg-[var(--button-hover)] hover:text-[var(--button-text-hover)]",secondary:"h-full gap-1 flex max-md:h-12 capitalize font-bold items-center justify-center rounded-lg cursor-pointer px-6 py-3 transition duration-100 transform bg-[var(--button)] border-2 border-[var(--button-border)] text-[var(--button-text)] hoverd hover:bg-[var(--button-hover)] hover:text-[var(--button-text-hover)]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",active:"transition duration-100 transform bg-[var(--active)] text-[var(--active-text)]"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),Tc=g.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,icon:o,isLoading:i,children:s,...a},l)=>{const u=r?mg:"button";return T.jsxs(u,{className:vn(zC({variant:t,size:n,className:e})),ref:l,disabled:i||a.disabled,...a,children:[i&&T.jsx(iC,{className:"mr-2 h-4 w-4 animate-spin"}),o&&!i&&T.jsx("span",{className:"mr-2",children:o}),s]})});Tc.displayName="Button";function BC({className:e,angle:t=65,cellSize:n=60,opacity:r=.5,lightLineColor:o="gray",darkLineColor:i="gray",...s}){const a={"--grid-angle":`${t}deg`,"--cell-size":`${n}px`,"--opacity":r,"--light-line":o,"--dark-line":i};return T.jsxs("div",{className:vn("pointer-events-none absolute size-full overflow-hidden [perspective:200px]","opacity-[30%]",e),style:a,...s,children:[T.jsx("div",{className:"notfoundeffect absolute inset-0 [transform:rotateX(var(--grid-angle))]",children:T.jsx("div",{className:"animate-grid [background-image:linear-gradient(to_right,var(--light-line)_1px,transparent_0),linear-gradient(to_bottom,var(--light-line)_1px,transparent_0)] [background-repeat:repeat] [background-size:var(--cell-size)_var(--cell-size)] [height:300vh] [inset:0%_0px] [margin-left:-200%] [transform-origin:100%_0_0] [width:600vw] dark:[background-image:linear-gradient(to_right,var(--dark-line)_1px,transparent_0),linear-gradient(to_bottom,var(--dark-line)_1px,transparent_0)]"})}),T.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-white to-transparent to-90% dark:from-black"})]})}function UC(){return T.jsxs("div",{className:"relative flex h-[100vh] w-full flex-col items-center justify-center overflow-hidden rounded-lg bg-[var(--background)] md:shadow-xl",children:[T.jsx(En.span,{initial:{opacity:0},animate:{opacity:1},transition:{duration:1},className:"pointer-events-none z-10 whitespace-pre-wrap bg-gradient-to-b from-[#ffd319] via-[#ff2975] to-[#8c1eff] bg-clip-text text-center text-7xl font-bold leading-none tracking-tighter text-transparent",children:"404 Not Found"}),T.jsx(BC,{})]})}const $C=vt.lazy(()=>Tw(()=>import("./HomePage-BhVAci5D.js"),__vite__mapDeps([]))),WC=()=>{const e=()=>T.jsx(En.div,{className:"flex h-screen items-center justify-center bg-[var(--background)]",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:T.jsx("div",{className:"text-base",children:T.jsx(Tc,{isLoading:!0,className:"flex cursor-text gap-0 border-none bg-transparent",children:"Loading..."})})});return T.jsx(g.Suspense,{fallback:T.jsx(e,{}),children:T.jsxs(gw,{children:[T.jsx(xl,{path:"/",element:T.jsx($C,{})}),T.jsx(xl,{path:"*",element:T.jsx(UC,{})})]})})};var HC="@vercel/analytics",KC="1.3.1",GC=()=>{window.va||(window.va=function(...t){(window.vaq=window.vaq||[]).push(t)})};function Pg(){return typeof window<"u"}function kg(){try{const e="production"}catch{}return"production"}function QC(e="auto"){if(e==="auto"){window.vam=kg();return}window.vam=e}function YC(){return(Pg()?window.vam:kg())||"production"}function Sa(){return YC()==="development"}var XC="https://va.vercel-scripts.com/v1/script.debug.js",ZC="/_vercel/insights/script.js";function qC(e={debug:!0}){var t;if(!Pg())return;QC(e.mode),GC(),e.beforeSend&&((t=window.va)==null||t.call(window,"beforeSend",e.beforeSend));const n=e.scriptSrc||(Sa()?XC:ZC);if(document.head.querySelector(`script[src*="${n}"]`))return;const r=document.createElement("script");r.src=n,r.defer=!0,r.dataset.sdkn=HC+(e.framework?`/${e.framework}`:""),r.dataset.sdkv=KC,e.disableAutoTrack&&(r.dataset.disableAutoTrack="1"),e.endpoint&&(r.dataset.endpoint=e.endpoint),e.dsn&&(r.dataset.dsn=e.dsn),r.onerror=()=>{const o=Sa()?"Please check if any ad blockers are enabled and try again.":"Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.";console.log(`[Vercel Web Analytics] Failed to load script from ${n}. ${o}`)},Sa()&&e.debug===!1&&(r.dataset.debug="false"),document.head.appendChild(r)}function We(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function JC(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function bg(...e){return t=>e.forEach(n=>JC(n,t))}function On(...e){return g.useCallback(bg(...e),e)}function eP(e,t=[]){let n=[];function r(i,s){const a=g.createContext(s),l=n.length;n=[...n,s];function u(d){const{scope:f,children:m,...w}=d,y=(f==null?void 0:f[e][l])||a,x=g.useMemo(()=>w,Object.values(w));return T.jsx(y.Provider,{value:x,children:m})}function c(d,f){const m=(f==null?void 0:f[e][l])||a,w=g.useContext(m);if(w)return w;if(s!==void 0)return s;throw new Error(`\`${d}\` must be used within \`${i}\``)}return u.displayName=i+"Provider",[u,c]}const o=()=>{const i=n.map(s=>g.createContext(s));return function(a){const l=(a==null?void 0:a[e])||i;return g.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,tP(o,...t)]}function tP(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:l,scopeName:u})=>{const d=l(i)[`__scope${u}`];return{...a,...d}},{});return g.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var is=g.forwardRef((e,t)=>{const{children:n,...r}=e,o=g.Children.toArray(n),i=o.find(rP);if(i){const s=i.props.children,a=o.map(l=>l===i?g.Children.count(s)>1?g.Children.only(null):g.isValidElement(s)?s.props.children:null:l);return T.jsx(zl,{...r,ref:t,children:g.isValidElement(s)?g.cloneElement(s,void 0,a):null})}return T.jsx(zl,{...r,ref:t,children:n})});is.displayName="Slot";var zl=g.forwardRef((e,t)=>{const{children:n,...r}=e;if(g.isValidElement(n)){const o=iP(n);return g.cloneElement(n,{...oP(r,n.props),ref:t?bg(t,o):o})}return g.Children.count(n)>1?g.Children.only(null):null});zl.displayName="SlotClone";var nP=({children:e})=>T.jsx(T.Fragment,{children:e});function rP(e){return g.isValidElement(e)&&e.type===nP}function oP(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...a)=>{i(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function iP(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function sP(e){const t=e+"CollectionProvider",[n,r]=eP(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=m=>{const{scope:w,children:y}=m,x=vt.useRef(null),h=vt.useRef(new Map).current;return T.jsx(o,{scope:w,itemMap:h,collectionRef:x,children:y})};s.displayName=t;const a=e+"CollectionSlot",l=vt.forwardRef((m,w)=>{const{scope:y,children:x}=m,h=i(a,y),p=On(w,h.collectionRef);return T.jsx(is,{ref:p,children:x})});l.displayName=a;const u=e+"CollectionItemSlot",c="data-radix-collection-item",d=vt.forwardRef((m,w)=>{const{scope:y,children:x,...h}=m,p=vt.useRef(null),v=On(w,p),S=i(u,y);return vt.useEffect(()=>(S.itemMap.set(p,{ref:p,...h}),()=>void S.itemMap.delete(p))),T.jsx(is,{[c]:"",ref:v,children:x})});d.displayName=u;function f(m){const w=i(e+"CollectionConsumer",m);return vt.useCallback(()=>{const x=w.collectionRef.current;if(!x)return[];const h=Array.from(x.querySelectorAll(`[${c}]`));return Array.from(w.itemMap.values()).sort((S,E)=>h.indexOf(S.ref.current)-h.indexOf(E.ref.current))},[w.collectionRef,w.itemMap])}return[{Provider:s,Slot:l,ItemSlot:d},f,r]}function aP(e,t=[]){let n=[];function r(i,s){const a=g.createContext(s),l=n.length;n=[...n,s];const u=d=>{var h;const{scope:f,children:m,...w}=d,y=((h=f==null?void 0:f[e])==null?void 0:h[l])||a,x=g.useMemo(()=>w,Object.values(w));return T.jsx(y.Provider,{value:x,children:m})};u.displayName=i+"Provider";function c(d,f){var y;const m=((y=f==null?void 0:f[e])==null?void 0:y[l])||a,w=g.useContext(m);if(w)return w;if(s!==void 0)return s;throw new Error(`\`${d}\` must be used within \`${i}\``)}return[u,c]}const o=()=>{const i=n.map(s=>g.createContext(s));return function(a){const l=(a==null?void 0:a[e])||i;return g.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,lP(o,...t)]}function lP(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:l,scopeName:u})=>{const d=l(i)[`__scope${u}`];return{...a,...d}},{});return g.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var uP=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],It=uP.reduce((e,t)=>{const n=g.forwardRef((r,o)=>{const{asChild:i,...s}=r,a=i?is:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),T.jsx(a,{...s,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function Rg(e,t){e&&Bu.flushSync(()=>e.dispatchEvent(t))}function Ot(e){const t=g.useRef(e);return g.useEffect(()=>{t.current=e}),g.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function cP(e,t=globalThis==null?void 0:globalThis.document){const n=Ot(e);g.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var dP="DismissableLayer",Bl="dismissableLayer.update",fP="dismissableLayer.pointerDownOutside",pP="dismissableLayer.focusOutside",fp,Ag=g.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Mg=g.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:s,onDismiss:a,...l}=e,u=g.useContext(Ag),[c,d]=g.useState(null),f=(c==null?void 0:c.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,m]=g.useState({}),w=On(t,k=>d(k)),y=Array.from(u.layers),[x]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),h=y.indexOf(x),p=c?y.indexOf(c):-1,v=u.layersWithOutsidePointerEventsDisabled.size>0,S=p>=h,E=mP(k=>{const C=k.target,L=[...u.branches].some(N=>N.contains(C));!S||L||(o==null||o(k),s==null||s(k),k.defaultPrevented||a==null||a())},f),P=vP(k=>{const C=k.target;[...u.branches].some(N=>N.contains(C))||(i==null||i(k),s==null||s(k),k.defaultPrevented||a==null||a())},f);return cP(k=>{p===u.layers.size-1&&(r==null||r(k),!k.defaultPrevented&&a&&(k.preventDefault(),a()))},f),g.useEffect(()=>{if(c)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(fp=f.body.style.pointerEvents,f.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(c)),u.layers.add(c),pp(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(f.body.style.pointerEvents=fp)}},[c,f,n,u]),g.useEffect(()=>()=>{c&&(u.layers.delete(c),u.layersWithOutsidePointerEventsDisabled.delete(c),pp())},[c,u]),g.useEffect(()=>{const k=()=>m({});return document.addEventListener(Bl,k),()=>document.removeEventListener(Bl,k)},[]),T.jsx(It.div,{...l,ref:w,style:{pointerEvents:v?S?"auto":"none":void 0,...e.style},onFocusCapture:We(e.onFocusCapture,P.onFocusCapture),onBlurCapture:We(e.onBlurCapture,P.onBlurCapture),onPointerDownCapture:We(e.onPointerDownCapture,E.onPointerDownCapture)})});Mg.displayName=dP;var hP="DismissableLayerBranch",_g=g.forwardRef((e,t)=>{const n=g.useContext(Ag),r=g.useRef(null),o=On(t,r);return g.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),T.jsx(It.div,{...e,ref:o})});_g.displayName=hP;function mP(e,t=globalThis==null?void 0:globalThis.document){const n=Ot(e),r=g.useRef(!1),o=g.useRef(()=>{});return g.useEffect(()=>{const i=a=>{if(a.target&&!r.current){let l=function(){Ng(fP,n,u,{discrete:!0})};const u={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=l,t.addEventListener("click",o.current,{once:!0})):l()}else t.removeEventListener("click",o.current);r.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function vP(e,t=globalThis==null?void 0:globalThis.document){const n=Ot(e),r=g.useRef(!1);return g.useEffect(()=>{const o=i=>{i.target&&!r.current&&Ng(pP,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function pp(){const e=new CustomEvent(Bl);document.dispatchEvent(e)}function Ng(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Rg(o,i):o.dispatchEvent(i)}var gP=Mg,yP=_g,ss=globalThis!=null&&globalThis.document?g.useLayoutEffect:()=>{},wP="Portal",Dg=g.forwardRef((e,t)=>{var a;const{container:n,...r}=e,[o,i]=g.useState(!1);ss(()=>i(!0),[]);const s=n||o&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return s?Mm.createPortal(T.jsx(It.div,{...r,ref:t}),s):null});Dg.displayName=wP;function xP(e,t){return g.useReducer((n,r)=>t[n][r]??n,e)}var Lg=e=>{const{present:t,children:n}=e,r=SP(t),o=typeof n=="function"?n({present:r.isPresent}):g.Children.only(n),i=On(r.ref,EP(o));return typeof n=="function"||r.isPresent?g.cloneElement(o,{ref:i}):null};Lg.displayName="Presence";function SP(e){const[t,n]=g.useState(),r=g.useRef({}),o=g.useRef(e),i=g.useRef("none"),s=e?"mounted":"unmounted",[a,l]=xP(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return g.useEffect(()=>{const u=si(r.current);i.current=a==="mounted"?u:"none"},[a]),ss(()=>{const u=r.current,c=o.current;if(c!==e){const f=i.current,m=si(u);e?l("MOUNT"):m==="none"||(u==null?void 0:u.display)==="none"?l("UNMOUNT"):l(c&&f!==m?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),ss(()=>{if(t){let u;const c=t.ownerDocument.defaultView??window,d=m=>{const y=si(r.current).includes(m.animationName);if(m.target===t&&y&&(l("ANIMATION_END"),!o.current)){const x=t.style.animationFillMode;t.style.animationFillMode="forwards",u=c.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=x)})}},f=m=>{m.target===t&&(i.current=si(r.current))};return t.addEventListener("animationstart",f),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{c.clearTimeout(u),t.removeEventListener("animationstart",f),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:g.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function si(e){return(e==null?void 0:e.animationName)||"none"}function EP(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function TP({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=CP({defaultProp:t,onChange:n}),i=e!==void 0,s=i?e:r,a=Ot(n),l=g.useCallback(u=>{if(i){const d=typeof u=="function"?u(e):u;d!==e&&a(d)}else o(u)},[i,e,o,a]);return[s,l]}function CP({defaultProp:e,onChange:t}){const n=g.useState(e),[r]=n,o=g.useRef(r),i=Ot(t);return g.useEffect(()=>{o.current!==r&&(i(r),o.current=r)},[r,o,i]),n}function PP(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function kP(...e){return t=>e.forEach(n=>PP(n,t))}var jg=g.forwardRef((e,t)=>{const{children:n,...r}=e,o=g.Children.toArray(n),i=o.find(RP);if(i){const s=i.props.children,a=o.map(l=>l===i?g.Children.count(s)>1?g.Children.only(null):g.isValidElement(s)?s.props.children:null:l);return T.jsx(Ul,{...r,ref:t,children:g.isValidElement(s)?g.cloneElement(s,void 0,a):null})}return T.jsx(Ul,{...r,ref:t,children:n})});jg.displayName="Slot";var Ul=g.forwardRef((e,t)=>{const{children:n,...r}=e;if(g.isValidElement(n)){const o=MP(n);return g.cloneElement(n,{...AP(r,n.props),ref:t?kP(t,o):o})}return g.Children.count(n)>1?g.Children.only(null):null});Ul.displayName="SlotClone";var bP=({children:e})=>T.jsx(T.Fragment,{children:e});function RP(e){return g.isValidElement(e)&&e.type===bP}function AP(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...a)=>{i(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function MP(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var _P=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],NP=_P.reduce((e,t)=>{const n=g.forwardRef((r,o)=>{const{asChild:i,...s}=r,a=i?jg:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),T.jsx(a,{...s,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),DP="VisuallyHidden",Ds=g.forwardRef((e,t)=>T.jsx(NP.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));Ds.displayName=DP;var Sk=Ds,Cc="ToastProvider",[Pc,LP,jP]=sP("Toast"),[Og,Ek]=aP("Toast",[jP]),[OP,Ls]=Og(Cc),Vg=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:i=50,children:s}=e,[a,l]=g.useState(null),[u,c]=g.useState(0),d=g.useRef(!1),f=g.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${Cc}\`. Expected non-empty \`string\`.`),T.jsx(Pc.Provider,{scope:t,children:T.jsx(OP,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:i,toastCount:u,viewport:a,onViewportChange:l,onToastAdd:g.useCallback(()=>c(m=>m+1),[]),onToastRemove:g.useCallback(()=>c(m=>m-1),[]),isFocusedToastEscapeKeyDownRef:d,isClosePausedRef:f,children:s})})};Vg.displayName=Cc;var Ig="ToastViewport",VP=["F8"],$l="toast.viewportPause",Wl="toast.viewportResume",Fg=g.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=VP,label:o="Notifications ({hotkey})",...i}=e,s=Ls(Ig,n),a=LP(n),l=g.useRef(null),u=g.useRef(null),c=g.useRef(null),d=g.useRef(null),f=On(t,d,s.onViewportChange),m=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),w=s.toastCount>0;g.useEffect(()=>{const x=h=>{var v;r.length!==0&&r.every(S=>h[S]||h.code===S)&&((v=d.current)==null||v.focus())};return document.addEventListener("keydown",x),()=>document.removeEventListener("keydown",x)},[r]),g.useEffect(()=>{const x=l.current,h=d.current;if(w&&x&&h){const p=()=>{if(!s.isClosePausedRef.current){const P=new CustomEvent($l);h.dispatchEvent(P),s.isClosePausedRef.current=!0}},v=()=>{if(s.isClosePausedRef.current){const P=new CustomEvent(Wl);h.dispatchEvent(P),s.isClosePausedRef.current=!1}},S=P=>{!x.contains(P.relatedTarget)&&v()},E=()=>{x.contains(document.activeElement)||v()};return x.addEventListener("focusin",p),x.addEventListener("focusout",S),x.addEventListener("pointermove",p),x.addEventListener("pointerleave",E),window.addEventListener("blur",p),window.addEventListener("focus",v),()=>{x.removeEventListener("focusin",p),x.removeEventListener("focusout",S),x.removeEventListener("pointermove",p),x.removeEventListener("pointerleave",E),window.removeEventListener("blur",p),window.removeEventListener("focus",v)}}},[w,s.isClosePausedRef]);const y=g.useCallback(({tabbingDirection:x})=>{const p=a().map(v=>{const S=v.ref.current,E=[S,...XP(S)];return x==="forwards"?E:E.reverse()});return(x==="forwards"?p.reverse():p).flat()},[a]);return g.useEffect(()=>{const x=d.current;if(x){const h=p=>{var E,P,k;const v=p.altKey||p.ctrlKey||p.metaKey;if(p.key==="Tab"&&!v){const C=document.activeElement,L=p.shiftKey;if(p.target===x&&L){(E=u.current)==null||E.focus();return}const b=y({tabbingDirection:L?"backwards":"forwards"}),I=b.findIndex(M=>M===C);Ea(b.slice(I+1))?p.preventDefault():L?(P=u.current)==null||P.focus():(k=c.current)==null||k.focus()}};return x.addEventListener("keydown",h),()=>x.removeEventListener("keydown",h)}},[a,y]),T.jsxs(yP,{ref:l,role:"region","aria-label":o.replace("{hotkey}",m),tabIndex:-1,style:{pointerEvents:w?void 0:"none"},children:[w&&T.jsx(Hl,{ref:u,onFocusFromOutsideViewport:()=>{const x=y({tabbingDirection:"forwards"});Ea(x)}}),T.jsx(Pc.Slot,{scope:n,children:T.jsx(It.ol,{tabIndex:-1,...i,ref:f})}),w&&T.jsx(Hl,{ref:c,onFocusFromOutsideViewport:()=>{const x=y({tabbingDirection:"backwards"});Ea(x)}})]})});Fg.displayName=Ig;var zg="ToastFocusProxy",Hl=g.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,i=Ls(zg,n);return T.jsx(Ds,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:s=>{var u;const a=s.relatedTarget;!((u=i.viewport)!=null&&u.contains(a))&&r()}})});Hl.displayName=zg;var js="Toast",IP="toast.swipeStart",FP="toast.swipeMove",zP="toast.swipeCancel",BP="toast.swipeEnd",Bg=g.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:i,...s}=e,[a=!0,l]=TP({prop:r,defaultProp:o,onChange:i});return T.jsx(Lg,{present:n||a,children:T.jsx(WP,{open:a,...s,ref:t,onClose:()=>l(!1),onPause:Ot(e.onPause),onResume:Ot(e.onResume),onSwipeStart:We(e.onSwipeStart,u=>{u.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:We(e.onSwipeMove,u=>{const{x:c,y:d}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","move"),u.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${c}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${d}px`)}),onSwipeCancel:We(e.onSwipeCancel,u=>{u.currentTarget.setAttribute("data-swipe","cancel"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:We(e.onSwipeEnd,u=>{const{x:c,y:d}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","end"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${c}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${d}px`),l(!1)})})})});Bg.displayName=js;var[UP,$P]=Og(js,{onClose(){}}),WP=g.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:o,open:i,onClose:s,onEscapeKeyDown:a,onPause:l,onResume:u,onSwipeStart:c,onSwipeMove:d,onSwipeCancel:f,onSwipeEnd:m,...w}=e,y=Ls(js,n),[x,h]=g.useState(null),p=On(t,M=>h(M)),v=g.useRef(null),S=g.useRef(null),E=o||y.duration,P=g.useRef(0),k=g.useRef(E),C=g.useRef(0),{onToastAdd:L,onToastRemove:N}=y,_=Ot(()=>{var U;(x==null?void 0:x.contains(document.activeElement))&&((U=y.viewport)==null||U.focus()),s()}),b=g.useCallback(M=>{!M||M===1/0||(window.clearTimeout(C.current),P.current=new Date().getTime(),C.current=window.setTimeout(_,M))},[_]);g.useEffect(()=>{const M=y.viewport;if(M){const U=()=>{b(k.current),u==null||u()},K=()=>{const z=new Date().getTime()-P.current;k.current=k.current-z,window.clearTimeout(C.current),l==null||l()};return M.addEventListener($l,K),M.addEventListener(Wl,U),()=>{M.removeEventListener($l,K),M.removeEventListener(Wl,U)}}},[y.viewport,E,l,u,b]),g.useEffect(()=>{i&&!y.isClosePausedRef.current&&b(E)},[i,E,y.isClosePausedRef,b]),g.useEffect(()=>(L(),()=>N()),[L,N]);const I=g.useMemo(()=>x?Qg(x):null,[x]);return y.viewport?T.jsxs(T.Fragment,{children:[I&&T.jsx(HP,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:I}),T.jsx(UP,{scope:n,onClose:_,children:Bu.createPortal(T.jsx(Pc.ItemSlot,{scope:n,children:T.jsx(gP,{asChild:!0,onEscapeKeyDown:We(a,()=>{y.isFocusedToastEscapeKeyDownRef.current||_(),y.isFocusedToastEscapeKeyDownRef.current=!1}),children:T.jsx(It.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":i?"open":"closed","data-swipe-direction":y.swipeDirection,...w,ref:p,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:We(e.onKeyDown,M=>{M.key==="Escape"&&(a==null||a(M.nativeEvent),M.nativeEvent.defaultPrevented||(y.isFocusedToastEscapeKeyDownRef.current=!0,_()))}),onPointerDown:We(e.onPointerDown,M=>{M.button===0&&(v.current={x:M.clientX,y:M.clientY})}),onPointerMove:We(e.onPointerMove,M=>{if(!v.current)return;const U=M.clientX-v.current.x,K=M.clientY-v.current.y,z=!!S.current,A=["left","right"].includes(y.swipeDirection),j=["left","up"].includes(y.swipeDirection)?Math.min:Math.max,O=A?j(0,U):0,$=A?0:j(0,K),W=M.pointerType==="touch"?10:2,fe={x:O,y:$},te={originalEvent:M,delta:fe};z?(S.current=fe,ai(FP,d,te,{discrete:!1})):hp(fe,y.swipeDirection,W)?(S.current=fe,ai(IP,c,te,{discrete:!1}),M.target.setPointerCapture(M.pointerId)):(Math.abs(U)>W||Math.abs(K)>W)&&(v.current=null)}),onPointerUp:We(e.onPointerUp,M=>{const U=S.current,K=M.target;if(K.hasPointerCapture(M.pointerId)&&K.releasePointerCapture(M.pointerId),S.current=null,v.current=null,U){const z=M.currentTarget,A={originalEvent:M,delta:U};hp(U,y.swipeDirection,y.swipeThreshold)?ai(BP,m,A,{discrete:!0}):ai(zP,f,A,{discrete:!0}),z.addEventListener("click",j=>j.preventDefault(),{once:!0})}})})})}),y.viewport)})]}):null}),HP=e=>{const{__scopeToast:t,children:n,...r}=e,o=Ls(js,t),[i,s]=g.useState(!1),[a,l]=g.useState(!1);return QP(()=>s(!0)),g.useEffect(()=>{const u=window.setTimeout(()=>l(!0),1e3);return()=>window.clearTimeout(u)},[]),a?null:T.jsx(Dg,{asChild:!0,children:T.jsx(Ds,{...r,children:i&&T.jsxs(T.Fragment,{children:[o.label," ",n]})})})},KP="ToastTitle",Ug=g.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return T.jsx(It.div,{...r,ref:t})});Ug.displayName=KP;var GP="ToastDescription",$g=g.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return T.jsx(It.div,{...r,ref:t})});$g.displayName=GP;var Wg="ToastAction",Hg=g.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?T.jsx(Gg,{altText:n,asChild:!0,children:T.jsx(kc,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${Wg}\`. Expected non-empty \`string\`.`),null)});Hg.displayName=Wg;var Kg="ToastClose",kc=g.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,o=$P(Kg,n);return T.jsx(Gg,{asChild:!0,children:T.jsx(It.button,{type:"button",...r,ref:t,onClick:We(e.onClick,o.onClose)})})});kc.displayName=Kg;var Gg=g.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return T.jsx(It.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function Qg(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),YP(r)){const o=r.ariaHidden||r.hidden||r.style.display==="none",i=r.dataset.radixToastAnnounceExclude==="";if(!o)if(i){const s=r.dataset.radixToastAnnounceAlt;s&&t.push(s)}else t.push(...Qg(r))}}),t}function ai(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Rg(o,i):o.dispatchEvent(i)}var hp=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),i=r>o;return t==="left"||t==="right"?i&&r>n:!i&&o>n};function QP(e=()=>{}){const t=Ot(e);ss(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function YP(e){return e.nodeType===e.ELEMENT_NODE}function XP(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Ea(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var ZP=Vg,Yg=Fg,Xg=Bg,Zg=Ug,qg=$g,Jg=Hg,ey=kc;const qP=ZP,ty=g.forwardRef(({className:e,...t},n)=>T.jsx(Yg,{ref:n,className:vn("fixed bottom-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));ty.displayName=Yg.displayName;const JP=gg("group pointer-events-auto  relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"bg-[var(--card)] rounded-md text-[var(--card-foreground)] shadow-sm border border-[var(--border)] p-2 py-4",destructive:"destructive group border-[var(--destructive)] bg-[var(--destructive)] text-[var(--destructive-foreground)]"}},defaultVariants:{variant:"default"}}),ny=g.forwardRef(({className:e,variant:t,...n},r)=>T.jsx(Xg,{ref:r,className:vn(JP({variant:t}),e),...n}));ny.displayName=Xg.displayName;const ek=g.forwardRef(({className:e,...t},n)=>T.jsx(Jg,{ref:n,className:vn(["inline-flex h-8 shrink-0 items-center justify-center rounded-md border border-[var(--border)] bg-transparent px-3 text-sm font-medium ring-offset-[var(--background)] transition-colors","hover:bg-[var(--secondary)] hover:text-[var(--secondary-foreground)]","focus:outline-none focus:ring-2 focus:ring-[var(--ring)] focus:ring-offset-2","disabled:pointer-events-none disabled:opacity-50","group-[.destructive]:focus:ring-[var(--destructive)] group-[.destructive]:border-[var(--muted)]/40 group-[.destructive]:hover:border-[var(--destructive)]/30","group-[.destructive]:hover:bg-[var(--destructive)] group-[.destructive]:hover:text-[var(--destructive-foreground)]"],e),...t}));ek.displayName=Jg.displayName;const ry=g.forwardRef(({className:e,...t},n)=>T.jsx(ey,{ref:n,className:vn(["absolute right-2 top-2 rounded-md p-1 opacity-0 transition-opacity","text-[color:var(--foreground)/0.5]","hover:text-[var(--foreground)]","focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-[var(--ring)] group-hover:opacity-100","group-[.destructive]:text-[var(--destructive-foreground)]/80","group-[.destructive]:hover:text-[var(--destructive-foreground)]","group-[.destructive]:focus:ring-[var(--destructive)] group-[.destructive]:focus:ring-offset-[var(--destructive)]"],e),"toast-close":"",...t,children:T.jsx(yg,{className:"h-4 w-4"})}));ry.displayName=ey.displayName;const oy=g.forwardRef(({className:e,...t},n)=>T.jsx(Zg,{ref:n,className:vn("text-sm font-semibold text-[var(--card-foreground)]",e),...t}));oy.displayName=Zg.displayName;const iy=g.forwardRef(({className:e,...t},n)=>T.jsx(qg,{ref:n,className:vn("text-sm opacity-90 text-[var(--muted-foreground)]",e),...t}));iy.displayName=qg.displayName;const tk=20,nk=1e3;let Ta=0;function rk(){return Ta=(Ta+1)%Number.MAX_SAFE_INTEGER,Ta.toString()}const Ca=new Map,mp=e=>{if(Ca.has(e))return;const t=setTimeout(()=>{Ca.delete(e),oo({type:"REMOVE_TOAST",toastId:e})},nk);Ca.set(e,t)},ok=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,tk)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(n=>n.id===t.toast.id?{...n,...t.toast}:n)};case"DISMISS_TOAST":{const{toastId:n}=t;return n?mp(n):e.toasts.forEach(r=>{mp(r.id)}),{...e,toasts:e.toasts.map(r=>r.id===n||n===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(n=>n.id!==t.toastId)}}},Ti=[];let Ci={toasts:[]};function oo(e){Ci=ok(Ci,e),Ti.forEach(t=>{t(Ci)})}function ik({...e}){const t=rk(),n=o=>oo({type:"UPDATE_TOAST",toast:{...o,id:t}}),r=()=>oo({type:"DISMISS_TOAST",toastId:t});return oo({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:o=>{o||r()}}}),{id:t,dismiss:r,update:n}}function sy(){const[e,t]=g.useState(Ci);return g.useEffect(()=>(Ti.push(t),()=>{const n=Ti.indexOf(t);n>-1&&Ti.splice(n,1)}),[e]),{...e,toast:ik,dismiss:n=>oo({type:"DISMISS_TOAST",toastId:n})}}function sk(){const{toasts:e}=sy();return T.jsxs(qP,{children:[e.map(function({id:t,title:n,description:r,action:o,...i}){return T.jsxs(ny,{className:"my-1 bg-gray-50",...i,children:[T.jsxs("div",{className:"grid gap-1",children:[n&&T.jsx(oy,{children:n}),r&&T.jsx(iy,{children:r})]}),o,T.jsx(ry,{})]},t)}),T.jsx(ty,{})]})}const ak=()=>T.jsx("div",{className:"absolute inset-0 -z-10 h-full w-full bg-[var(--background)] bg-[linear-gradient(to_right,var(--grid-color,rgba(139,92,246,0.025))_1px,transparent_1px),linear-gradient(to_bottom,var(--grid-color,rgba(139,92,246,0.025))_1px,transparent_1px)] bg-[size:14px_24px]"});function lk(){return T.jsx("div",{children:T.jsx("script",{type:"text/javascript",src:"https://cdnjs.buymeacoffee.com/1.0.0/button.prod.min.js","data-name":"bmc-button","data-slug":"alshaercond","data-color":"#BD5FFF","data-emoji":"☕","data-font":"Poppins","data-text":"Buy me a coffee","data-outline-color":"#000000","data-font-color":"#ffffff","data-coffee-color":"#FFDD00",children:"fdd"})})}const Pa="TUpNv8wRALuuKuDpH9PbbCatSFHVtqeByk",uk="https://link.trustwallet.com/send?coin=195&address=TUpNv8wRALuuKuDpH9PbbCatSFHVtqeByk&token_id=TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",ck="/app/assets/qrcode.jpg",dk={initial:{scale:1,rotate:0},animate:{scale:1.08,rotate:8},exit:{scale:1,rotate:0}},vp={hidden:{scale:.4,opacity:0,rotate:-30},visible:{scale:1,opacity:1,rotate:0,transition:{type:"spring",duration:.22}},exit:{scale:.4,opacity:0,rotate:-30,transition:{duration:.12}}},fk={closed:{opacity:0,y:30,transition:{duration:.13}},open:{opacity:1,y:0,transition:{duration:.2}}};function pk(){const[e,t]=g.useState(!1),[n,r]=g.useState(!1),{toast:o}=sy(),i=g.useCallback(async()=>{var s;try{if((s=navigator==null?void 0:navigator.clipboard)!=null&&s.writeText)await navigator.clipboard.writeText(Pa);else{const a=document.createElement("textarea");a.value=Pa,document.body.appendChild(a),a.select(),document.execCommand("copy"),document.body.removeChild(a)}r(!0),o==null||o({title:"Address copied!",description:"Thank you for supporting my work.",duration:1200}),setTimeout(()=>r(!1),1200)}catch{r(!1)}},[o]);return g.useEffect(()=>{if(!e)return;const s=a=>{a.key==="Escape"&&t(!1)};return window.addEventListener("keydown",s),()=>window.removeEventListener("keydown",s)},[e]),T.jsxs(T.Fragment,{children:[T.jsx(En.button,{type:"button","aria-pressed":e,title:"Support Me","aria-label":"Support Me",onClick:()=>t(s=>!s),tabIndex:0,initial:"initial",animate:e?"animate":"initial",variants:dk,whileTap:{scale:.93},className:`
          fixed z-50 bottom-10 right-10 max-md:bottom-4 max-md:right-4
          flex items-center justify-center
          w-14 h-14 max-md:w-12 max-md:h-12
          rounded-full shadow-xl border border-[var(--border)]
          bg-[var(--background)] text-[var(--primary)]
          hover:bg-[var(--muted)] hover:text-[var(--primary-foreground)]
          focus-visible:ring-4 focus-visible:ring-[var(--focus)]
          transition-all duration-200 outline-none
        `,children:T.jsx(Kd,{mode:"wait",initial:!1,children:e?T.jsx(En.span,{variants:vp,initial:"hidden",animate:"visible",exit:"exit",className:"flex",children:T.jsx(yg,{className:"w-6 h-6 text-[var(--primary)]","aria-hidden":"true"})},"x"):T.jsx(En.span,{variants:vp,initial:"hidden",animate:"visible",exit:"exit",className:"flex",children:T.jsx(oC,{className:"w-6 h-6 text-[var(--destructive)]","aria-hidden":"true"})},"heart")})}),T.jsx(Kd,{children:e&&T.jsxs(T.Fragment,{children:[T.jsx(En.div,{initial:{opacity:0},animate:{opacity:.48},exit:{opacity:0},onClick:()=>t(!1),className:"fixed inset-0 z-40",style:{background:"var(--background)",opacity:.48},"aria-label":"Close support modal",tabIndex:-1,role:"presentation"}),T.jsx(En.div,{initial:"closed",animate:"open",exit:"closed",variants:fk,style:{position:"fixed",bottom:"6rem",right:"1.5rem",zIndex:50,minWidth:"312px",maxWidth:"95vw"},className:`
                bg-[var(--card)] text-[var(--card-foreground)] border border-[var(--border)]
                rounded-[var(--radius)] shadow-lg px-0
              `,tabIndex:-1,"aria-modal":"true",role:"dialog",children:T.jsxs("div",{className:"flex flex-col items-center gap-4 py-4 px-6",children:[T.jsxs("span",{className:`flex items-center gap-1 px-2 py-1 bg-[var(--background)]
                    text-[var(--foreground)] text-[11px] rounded-[calc(var(--radius)*0.7)]
                    uppercase font-semibold tracking-wide border border-[var(--border)]
                    opacity-95 select-none mb-1`,children:[T.jsx(sC,{className:"w-3.5 h-3.5"}),"USDT (TRC20)"]}),T.jsx("div",{className:"flex items-center justify-center bg-[var(--background)] p-3 rounded-[var(--radius)] border border-[var(--border)]","aria-label":"QR code for USDT payment",role:"img",tabIndex:-1,children:T.jsx("img",{src:ck,alt:"QR code to send USDT (TRC20)",width:104,height:104,style:{display:"block"}})}),T.jsxs("div",{className:`
                    flex items-center gap-2 w-max bg-[var(--muted)] text-[var(--muted-foreground)]
                    px-2.5 py-2 rounded-[var(--radius)] border border-[var(--border)] font-mono
                    text-xs break-all focus-within:ring-2 focus-within:ring-[var(--focus)] transition
                  `,tabIndex:-1,"aria-label":"USDT TRC20 Wallet Address",role:"group",children:[T.jsx("span",{className:"truncate",children:Pa}),T.jsx("button",{type:"button","aria-label":"Copy wallet address","aria-pressed":n,onClick:i,className:"flex items-center hover:text-blue-600 focus:text-blue-700 transition",children:T.jsx(rC,{size:14})})]}),T.jsx("a",{href:uk,target:"_blank",rel:"noopener noreferrer","aria-label":"Pay via Trust Wallet",tabIndex:0,children:T.jsxs(Tc,{className:"bg-gray-700 gap-0 hover:bg-gray-800 border text-white border-none",children:[T.jsx("span",{children:"Trust Wallet"}),T.jsx("span",{className:"ml-2 h-[20px] w-[20px]",children:T.jsxs("svg",{width:"20",height:"22",viewBox:"0 0 444 501",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[T.jsx("path",{d:"M0.710022 72.41L222.16 0.109985V500.63C63.98 433.89 0.710022 305.98 0.710022 233.69V72.41Z",fill:"#0500FF"}),T.jsx("path",{d:"M443.62 72.41L222.17 0.109985V500.63C380.35 433.89 443.62 305.98 443.62 233.69V72.41Z",fill:"url(#trust__paint0_linear_3_10)"}),T.jsx("defs",{children:T.jsxs("linearGradient",{id:"trust__paint0_linear_3_10",x1:"385.26",y1:"-34.78",x2:"216.61",y2:"493.5",gradientUnits:"userSpaceOnUse",children:[T.jsx("stop",{offset:"0.02",stopColor:"#0000FF"}),T.jsx("stop",{offset:"0.08",stopColor:"#0094FF"}),T.jsx("stop",{offset:"0.16",stopColor:"#48FF91"}),T.jsx("stop",{offset:"0.42",stopColor:"#0094FF"}),T.jsx("stop",{offset:"0.68",stopColor:"#0038FF"}),T.jsx("stop",{offset:"0.9",stopColor:"#0500FF"})]})})]})})]})})]})},"supportMenu")]})})]})}const hk=()=>(qC(),T.jsxs("div",{dir:"ltr",className:"App pb-[30px] relative min-h-screen overflow-x-hidden",children:[T.jsx(pk,{}),T.jsx(lk,{}),T.jsx(ak,{}),T.jsx(sk,{}),T.jsx(WC,{})]}));var ay={exports:{}};(function(e,t){(function(n,r){e.exports=r()})(uy,function(){return function(n){function r(i){if(o[i])return o[i].exports;var s=o[i]={exports:{},id:i,loaded:!1};return n[i].call(s.exports,s,s.exports,r),s.loaded=!0,s.exports}var o={};return r.m=n,r.c=o,r.p="dist/",r(0)}([function(n,r,o){function i(M){return M&&M.__esModule?M:{default:M}}var s=Object.assign||function(M){for(var U=1;U<arguments.length;U++){var K=arguments[U];for(var z in K)Object.prototype.hasOwnProperty.call(K,z)&&(M[z]=K[z])}return M},a=o(1),l=(i(a),o(6)),u=i(l),c=o(7),d=i(c),f=o(8),m=i(f),w=o(9),y=i(w),x=o(10),h=i(x),p=o(11),v=i(p),S=o(14),E=i(S),P=[],k=!1,C={offset:120,delay:0,easing:"ease",duration:400,disable:!1,once:!1,startEvent:"DOMContentLoaded",throttleDelay:99,debounceDelay:50,disableMutationObserver:!1},L=function(){var M=arguments.length>0&&arguments[0]!==void 0&&arguments[0];if(M&&(k=!0),k)return P=(0,v.default)(P,C),(0,h.default)(P,C.once),P},N=function(){P=(0,E.default)(),L()},_=function(){P.forEach(function(M,U){M.node.removeAttribute("data-aos"),M.node.removeAttribute("data-aos-easing"),M.node.removeAttribute("data-aos-duration"),M.node.removeAttribute("data-aos-delay")})},b=function(M){return M===!0||M==="mobile"&&y.default.mobile()||M==="phone"&&y.default.phone()||M==="tablet"&&y.default.tablet()||typeof M=="function"&&M()===!0},I=function(M){C=s(C,M),P=(0,E.default)();var U=document.all&&!window.atob;return b(C.disable)||U?_():(C.disableMutationObserver||m.default.isSupported()||(console.info(`
      aos: MutationObserver is not supported on this browser,
      code mutations observing has been disabled.
      You may have to call "refreshHard()" by yourself.
    `),C.disableMutationObserver=!0),document.querySelector("body").setAttribute("data-aos-easing",C.easing),document.querySelector("body").setAttribute("data-aos-duration",C.duration),document.querySelector("body").setAttribute("data-aos-delay",C.delay),C.startEvent==="DOMContentLoaded"&&["complete","interactive"].indexOf(document.readyState)>-1?L(!0):C.startEvent==="load"?window.addEventListener(C.startEvent,function(){L(!0)}):document.addEventListener(C.startEvent,function(){L(!0)}),window.addEventListener("resize",(0,d.default)(L,C.debounceDelay,!0)),window.addEventListener("orientationchange",(0,d.default)(L,C.debounceDelay,!0)),window.addEventListener("scroll",(0,u.default)(function(){(0,h.default)(P,C.once)},C.throttleDelay)),C.disableMutationObserver||m.default.ready("[data-aos]",N),P)};n.exports={init:I,refresh:L,refreshHard:N}},function(n,r){},,,,,function(n,r){(function(o){function i(b,I,M){function U(Q){var je=te,Bt=Le;return te=Le=void 0,Ft=Q,ae=b.apply(Bt,je)}function K(Q){return Ft=Q,he=setTimeout(j,I),zt?U(Q):ae}function z(Q){var je=Q-Ue,Bt=Q-Ft,bc=I-je;return Et?N(bc,ge-Bt):bc}function A(Q){var je=Q-Ue,Bt=Q-Ft;return Ue===void 0||je>=I||je<0||Et&&Bt>=ge}function j(){var Q=_();return A(Q)?O(Q):void(he=setTimeout(j,z(Q)))}function O(Q){return he=void 0,ne&&te?U(Q):(te=Le=void 0,ae)}function $(){he!==void 0&&clearTimeout(he),Ft=0,te=Ue=Le=he=void 0}function W(){return he===void 0?ae:O(_())}function fe(){var Q=_(),je=A(Q);if(te=arguments,Le=this,Ue=Q,je){if(he===void 0)return K(Ue);if(Et)return he=setTimeout(j,I),U(Ue)}return he===void 0&&(he=setTimeout(j,I)),ae}var te,Le,ge,ae,he,Ue,Ft=0,zt=!1,Et=!1,ne=!0;if(typeof b!="function")throw new TypeError(f);return I=c(I)||0,a(M)&&(zt=!!M.leading,Et="maxWait"in M,ge=Et?L(c(M.maxWait)||0,I):ge,ne="trailing"in M?!!M.trailing:ne),fe.cancel=$,fe.flush=W,fe}function s(b,I,M){var U=!0,K=!0;if(typeof b!="function")throw new TypeError(f);return a(M)&&(U="leading"in M?!!M.leading:U,K="trailing"in M?!!M.trailing:K),i(b,I,{leading:U,maxWait:I,trailing:K})}function a(b){var I=typeof b>"u"?"undefined":d(b);return!!b&&(I=="object"||I=="function")}function l(b){return!!b&&(typeof b>"u"?"undefined":d(b))=="object"}function u(b){return(typeof b>"u"?"undefined":d(b))=="symbol"||l(b)&&C.call(b)==w}function c(b){if(typeof b=="number")return b;if(u(b))return m;if(a(b)){var I=typeof b.valueOf=="function"?b.valueOf():b;b=a(I)?I+"":I}if(typeof b!="string")return b===0?b:+b;b=b.replace(y,"");var M=h.test(b);return M||p.test(b)?v(b.slice(2),M?2:8):x.test(b)?m:+b}var d=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(b){return typeof b}:function(b){return b&&typeof Symbol=="function"&&b.constructor===Symbol&&b!==Symbol.prototype?"symbol":typeof b},f="Expected a function",m=NaN,w="[object Symbol]",y=/^\s+|\s+$/g,x=/^[-+]0x[0-9a-f]+$/i,h=/^0b[01]+$/i,p=/^0o[0-7]+$/i,v=parseInt,S=(typeof o>"u"?"undefined":d(o))=="object"&&o&&o.Object===Object&&o,E=(typeof self>"u"?"undefined":d(self))=="object"&&self&&self.Object===Object&&self,P=S||E||Function("return this")(),k=Object.prototype,C=k.toString,L=Math.max,N=Math.min,_=function(){return P.Date.now()};n.exports=s}).call(r,function(){return this}())},function(n,r){(function(o){function i(_,b,I){function M(ne){var Q=fe,je=te;return fe=te=void 0,Ue=ne,ge=_.apply(je,Q)}function U(ne){return Ue=ne,ae=setTimeout(A,b),Ft?M(ne):ge}function K(ne){var Q=ne-he,je=ne-Ue,Bt=b-Q;return zt?L(Bt,Le-je):Bt}function z(ne){var Q=ne-he,je=ne-Ue;return he===void 0||Q>=b||Q<0||zt&&je>=Le}function A(){var ne=N();return z(ne)?j(ne):void(ae=setTimeout(A,K(ne)))}function j(ne){return ae=void 0,Et&&fe?M(ne):(fe=te=void 0,ge)}function O(){ae!==void 0&&clearTimeout(ae),Ue=0,fe=he=te=ae=void 0}function $(){return ae===void 0?ge:j(N())}function W(){var ne=N(),Q=z(ne);if(fe=arguments,te=this,he=ne,Q){if(ae===void 0)return U(he);if(zt)return ae=setTimeout(A,b),M(he)}return ae===void 0&&(ae=setTimeout(A,b)),ge}var fe,te,Le,ge,ae,he,Ue=0,Ft=!1,zt=!1,Et=!0;if(typeof _!="function")throw new TypeError(d);return b=u(b)||0,s(I)&&(Ft=!!I.leading,zt="maxWait"in I,Le=zt?C(u(I.maxWait)||0,b):Le,Et="trailing"in I?!!I.trailing:Et),W.cancel=O,W.flush=$,W}function s(_){var b=typeof _>"u"?"undefined":c(_);return!!_&&(b=="object"||b=="function")}function a(_){return!!_&&(typeof _>"u"?"undefined":c(_))=="object"}function l(_){return(typeof _>"u"?"undefined":c(_))=="symbol"||a(_)&&k.call(_)==m}function u(_){if(typeof _=="number")return _;if(l(_))return f;if(s(_)){var b=typeof _.valueOf=="function"?_.valueOf():_;_=s(b)?b+"":b}if(typeof _!="string")return _===0?_:+_;_=_.replace(w,"");var I=x.test(_);return I||h.test(_)?p(_.slice(2),I?2:8):y.test(_)?f:+_}var c=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(_){return typeof _}:function(_){return _&&typeof Symbol=="function"&&_.constructor===Symbol&&_!==Symbol.prototype?"symbol":typeof _},d="Expected a function",f=NaN,m="[object Symbol]",w=/^\s+|\s+$/g,y=/^[-+]0x[0-9a-f]+$/i,x=/^0b[01]+$/i,h=/^0o[0-7]+$/i,p=parseInt,v=(typeof o>"u"?"undefined":c(o))=="object"&&o&&o.Object===Object&&o,S=(typeof self>"u"?"undefined":c(self))=="object"&&self&&self.Object===Object&&self,E=v||S||Function("return this")(),P=Object.prototype,k=P.toString,C=Math.max,L=Math.min,N=function(){return E.Date.now()};n.exports=i}).call(r,function(){return this}())},function(n,r){function o(c){var d=void 0,f=void 0;for(d=0;d<c.length;d+=1)if(f=c[d],f.dataset&&f.dataset.aos||f.children&&o(f.children))return!0;return!1}function i(){return window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver}function s(){return!!i()}function a(c,d){var f=window.document,m=i(),w=new m(l);u=d,w.observe(f.documentElement,{childList:!0,subtree:!0,removedNodes:!0})}function l(c){c&&c.forEach(function(d){var f=Array.prototype.slice.call(d.addedNodes),m=Array.prototype.slice.call(d.removedNodes),w=f.concat(m);if(o(w))return u()})}Object.defineProperty(r,"__esModule",{value:!0});var u=function(){};r.default={isSupported:s,ready:a}},function(n,r){function o(f,m){if(!(f instanceof m))throw new TypeError("Cannot call a class as a function")}function i(){return navigator.userAgent||navigator.vendor||window.opera||""}Object.defineProperty(r,"__esModule",{value:!0});var s=function(){function f(m,w){for(var y=0;y<w.length;y++){var x=w[y];x.enumerable=x.enumerable||!1,x.configurable=!0,"value"in x&&(x.writable=!0),Object.defineProperty(m,x.key,x)}}return function(m,w,y){return w&&f(m.prototype,w),y&&f(m,y),m}}(),a=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i,l=/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i,u=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i,c=/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i,d=function(){function f(){o(this,f)}return s(f,[{key:"phone",value:function(){var m=i();return!(!a.test(m)&&!l.test(m.substr(0,4)))}},{key:"mobile",value:function(){var m=i();return!(!u.test(m)&&!c.test(m.substr(0,4)))}},{key:"tablet",value:function(){return this.mobile()&&!this.phone()}}]),f}();r.default=new d},function(n,r){Object.defineProperty(r,"__esModule",{value:!0});var o=function(s,a,l){var u=s.node.getAttribute("data-aos-once");a>s.position?s.node.classList.add("aos-animate"):typeof u<"u"&&(u==="false"||!l&&u!=="true")&&s.node.classList.remove("aos-animate")},i=function(s,a){var l=window.pageYOffset,u=window.innerHeight;s.forEach(function(c,d){o(c,u+l,a)})};r.default=i},function(n,r,o){function i(u){return u&&u.__esModule?u:{default:u}}Object.defineProperty(r,"__esModule",{value:!0});var s=o(12),a=i(s),l=function(u,c){return u.forEach(function(d,f){d.node.classList.add("aos-init"),d.position=(0,a.default)(d.node,c.offset)}),u};r.default=l},function(n,r,o){function i(u){return u&&u.__esModule?u:{default:u}}Object.defineProperty(r,"__esModule",{value:!0});var s=o(13),a=i(s),l=function(u,c){var d=0,f=0,m=window.innerHeight,w={offset:u.getAttribute("data-aos-offset"),anchor:u.getAttribute("data-aos-anchor"),anchorPlacement:u.getAttribute("data-aos-anchor-placement")};switch(w.offset&&!isNaN(w.offset)&&(f=parseInt(w.offset)),w.anchor&&document.querySelectorAll(w.anchor)&&(u=document.querySelectorAll(w.anchor)[0]),d=(0,a.default)(u).top,w.anchorPlacement){case"top-bottom":break;case"center-bottom":d+=u.offsetHeight/2;break;case"bottom-bottom":d+=u.offsetHeight;break;case"top-center":d+=m/2;break;case"bottom-center":d+=m/2+u.offsetHeight;break;case"center-center":d+=m/2+u.offsetHeight/2;break;case"top-top":d+=m;break;case"bottom-top":d+=u.offsetHeight+m;break;case"center-top":d+=u.offsetHeight/2+m}return w.anchorPlacement||w.offset||isNaN(c)||(f=c),d+f};r.default=l},function(n,r){Object.defineProperty(r,"__esModule",{value:!0});var o=function(i){for(var s=0,a=0;i&&!isNaN(i.offsetLeft)&&!isNaN(i.offsetTop);)s+=i.offsetLeft-(i.tagName!="BODY"?i.scrollLeft:0),a+=i.offsetTop-(i.tagName!="BODY"?i.scrollTop:0),i=i.offsetParent;return{top:a,left:s}};r.default=o},function(n,r){Object.defineProperty(r,"__esModule",{value:!0});var o=function(i){return i=i||document.querySelectorAll("[data-aos]"),Array.prototype.map.call(i,function(s){return{node:s}})};r.default=o}])})})(ay);var mk=ay.exports;const vk=Kl(mk);vk.init();Mm.render(T.jsx(vt.StrictMode,{children:T.jsx(xw,{children:T.jsx(hk,{})})}),document.getElementById("root"));export{mg as $,Kd as A,Tc as B,rC as C,oC as H,Hu as M,vt as R,Ds as V,yg as X,os as _,zm as a,Se as b,cn as c,yk as d,kx as e,J as f,Vo as g,vn as h,Re as i,T as j,Bu as k,Py as l,Po as m,Mm as n,Sk as o,En as p,xk as q,g as r,hg as s,ik as t,Wu as u,gg as v};
function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = []
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
