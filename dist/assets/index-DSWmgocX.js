function Zg(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const i=Object.getOwnPropertyDescriptor(r,o);i&&Object.defineProperty(e,o,i.get?i:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();var qg=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Wl(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var lp={exports:{}},os={},up={exports:{}},$={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ko=Symbol.for("react.element"),Jg=Symbol.for("react.portal"),ey=Symbol.for("react.fragment"),ty=Symbol.for("react.strict_mode"),ny=Symbol.for("react.profiler"),ry=Symbol.for("react.provider"),oy=Symbol.for("react.context"),iy=Symbol.for("react.forward_ref"),sy=Symbol.for("react.suspense"),ay=Symbol.for("react.memo"),ly=Symbol.for("react.lazy"),xc=Symbol.iterator;function uy(e){return e===null||typeof e!="object"?null:(e=xc&&e[xc]||e["@@iterator"],typeof e=="function"?e:null)}var cp={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},dp=Object.assign,fp={};function Sr(e,t,n){this.props=e,this.context=t,this.refs=fp,this.updater=n||cp}Sr.prototype.isReactComponent={};Sr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Sr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function pp(){}pp.prototype=Sr.prototype;function Hl(e,t,n){this.props=e,this.context=t,this.refs=fp,this.updater=n||cp}var Kl=Hl.prototype=new pp;Kl.constructor=Hl;dp(Kl,Sr.prototype);Kl.isPureReactComponent=!0;var Sc=Array.isArray,hp=Object.prototype.hasOwnProperty,Gl={current:null},mp={key:!0,ref:!0,__self:!0,__source:!0};function vp(e,t,n){var r,o={},i=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(i=""+t.key),t)hp.call(t,r)&&!mp.hasOwnProperty(r)&&(o[r]=t[r]);var a=arguments.length-2;if(a===1)o.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];o.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)o[r]===void 0&&(o[r]=a[r]);return{$$typeof:ko,type:e,key:i,ref:s,props:o,_owner:Gl.current}}function cy(e,t){return{$$typeof:ko,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Ql(e){return typeof e=="object"&&e!==null&&e.$$typeof===ko}function dy(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Cc=/\/+/g;function Vs(e,t){return typeof e=="object"&&e!==null&&e.key!=null?dy(""+e.key):t.toString(36)}function si(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case ko:case Jg:s=!0}}if(s)return s=e,o=o(s),e=r===""?"."+Vs(s,0):r,Sc(o)?(n="",e!=null&&(n=e.replace(Cc,"$&/")+"/"),si(o,t,n,"",function(u){return u})):o!=null&&(Ql(o)&&(o=cy(o,n+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(Cc,"$&/")+"/")+e)),t.push(o)),1;if(s=0,r=r===""?".":r+":",Sc(e))for(var a=0;a<e.length;a++){i=e[a];var l=r+Vs(i,a);s+=si(i,t,n,l,o)}else if(l=uy(e),typeof l=="function")for(e=l.call(e),a=0;!(i=e.next()).done;)i=i.value,l=r+Vs(i,a++),s+=si(i,t,n,l,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function Vo(e,t,n){if(e==null)return e;var r=[],o=0;return si(e,r,"","",function(i){return t.call(n,i,o++)}),r}function fy(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ne={current:null},ai={transition:null},py={ReactCurrentDispatcher:Ne,ReactCurrentBatchConfig:ai,ReactCurrentOwner:Gl};$.Children={map:Vo,forEach:function(e,t,n){Vo(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Vo(e,function(){t++}),t},toArray:function(e){return Vo(e,function(t){return t})||[]},only:function(e){if(!Ql(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};$.Component=Sr;$.Fragment=ey;$.Profiler=ny;$.PureComponent=Hl;$.StrictMode=ty;$.Suspense=sy;$.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=py;$.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=dp({},e.props),o=e.key,i=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,s=Gl.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)hp.call(t,l)&&!mp.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:ko,type:e.type,key:o,ref:i,props:r,_owner:s}};$.createContext=function(e){return e={$$typeof:oy,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:ry,_context:e},e.Consumer=e};$.createElement=vp;$.createFactory=function(e){var t=vp.bind(null,e);return t.type=e,t};$.createRef=function(){return{current:null}};$.forwardRef=function(e){return{$$typeof:iy,render:e}};$.isValidElement=Ql;$.lazy=function(e){return{$$typeof:ly,_payload:{_status:-1,_result:e},_init:fy}};$.memo=function(e,t){return{$$typeof:ay,type:e,compare:t===void 0?null:t}};$.startTransition=function(e){var t=ai.transition;ai.transition={};try{e()}finally{ai.transition=t}};$.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")};$.useCallback=function(e,t){return Ne.current.useCallback(e,t)};$.useContext=function(e){return Ne.current.useContext(e)};$.useDebugValue=function(){};$.useDeferredValue=function(e){return Ne.current.useDeferredValue(e)};$.useEffect=function(e,t){return Ne.current.useEffect(e,t)};$.useId=function(){return Ne.current.useId()};$.useImperativeHandle=function(e,t,n){return Ne.current.useImperativeHandle(e,t,n)};$.useInsertionEffect=function(e,t){return Ne.current.useInsertionEffect(e,t)};$.useLayoutEffect=function(e,t){return Ne.current.useLayoutEffect(e,t)};$.useMemo=function(e,t){return Ne.current.useMemo(e,t)};$.useReducer=function(e,t,n){return Ne.current.useReducer(e,t,n)};$.useRef=function(e){return Ne.current.useRef(e)};$.useState=function(e){return Ne.current.useState(e)};$.useSyncExternalStore=function(e,t,n){return Ne.current.useSyncExternalStore(e,t,n)};$.useTransition=function(){return Ne.current.useTransition()};$.version="18.2.0";up.exports=$;var g=up.exports;const ht=Wl(g),hy=Zg({__proto__:null,default:ht},[g]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var my=g,vy=Symbol.for("react.element"),gy=Symbol.for("react.fragment"),yy=Object.prototype.hasOwnProperty,wy=my.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,xy={key:!0,ref:!0,__self:!0,__source:!0};function gp(e,t,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)yy.call(t,r)&&!xy.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:vy,type:e,key:i,ref:s,props:o,_owner:wy.current}}os.Fragment=gy;os.jsx=gp;os.jsxs=gp;lp.exports=os;var C=lp.exports,yp={exports:{}},Ye={},wp={exports:{}},xp={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(M,j){var I=M.length;M.push(j);e:for(;0<I;){var U=I-1>>>1,W=M[U];if(0<o(W,j))M[U]=j,M[I]=W,I=U;else break e}}function n(M){return M.length===0?null:M[0]}function r(M){if(M.length===0)return null;var j=M[0],I=M.pop();if(I!==j){M[0]=I;e:for(var U=0,W=M.length,de=W>>>1;U<de;){var te=2*(U+1)-1,De=M[te],ve=te+1,ae=M[ve];if(0>o(De,I))ve<W&&0>o(ae,De)?(M[U]=ae,M[ve]=I,U=ve):(M[U]=De,M[te]=I,U=te);else if(ve<W&&0>o(ae,I))M[U]=ae,M[ve]=I,U=ve;else break e}}return j}function o(M,j){var I=M.sortIndex-j.sortIndex;return I!==0?I:M.id-j.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var s=Date,a=s.now();e.unstable_now=function(){return s.now()-a}}var l=[],u=[],d=1,c=null,f=3,v=!1,y=!1,w=!1,x=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function m(M){for(var j=n(u);j!==null;){if(j.callback===null)r(u);else if(j.startTime<=M)r(u),j.sortIndex=j.expirationTime,t(l,j);else break;j=n(u)}}function S(M){if(w=!1,m(M),!y)if(n(l)!==null)y=!0,B(T);else{var j=n(u);j!==null&&X(S,j.startTime-M)}}function T(M,j){y=!1,w&&(w=!1,h(P),P=-1),v=!0;var I=f;try{for(m(j),c=n(l);c!==null&&(!(c.expirationTime>j)||M&&!_());){var U=c.callback;if(typeof U=="function"){c.callback=null,f=c.priorityLevel;var W=U(c.expirationTime<=j);j=e.unstable_now(),typeof W=="function"?c.callback=W:c===n(l)&&r(l),m(j)}else r(l);c=n(l)}if(c!==null)var de=!0;else{var te=n(u);te!==null&&X(S,te.startTime-j),de=!1}return de}finally{c=null,f=I,v=!1}}var E=!1,k=null,P=-1,D=5,N=-1;function _(){return!(e.unstable_now()-N<D)}function b(){if(k!==null){var M=e.unstable_now();N=M;var j=!0;try{j=k(!0,M)}finally{j?F():(E=!1,k=null)}}else E=!1}var F;if(typeof p=="function")F=function(){p(b)};else if(typeof MessageChannel<"u"){var A=new MessageChannel,V=A.port2;A.port1.onmessage=b,F=function(){V.postMessage(null)}}else F=function(){x(b,0)};function B(M){k=M,E||(E=!0,F())}function X(M,j){P=x(function(){M(e.unstable_now())},j)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(M){M.callback=null},e.unstable_continueExecution=function(){y||v||(y=!0,B(T))},e.unstable_forceFrameRate=function(M){0>M||125<M?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):D=0<M?Math.floor(1e3/M):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(M){switch(f){case 1:case 2:case 3:var j=3;break;default:j=f}var I=f;f=j;try{return M()}finally{f=I}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(M,j){switch(M){case 1:case 2:case 3:case 4:case 5:break;default:M=3}var I=f;f=M;try{return j()}finally{f=I}},e.unstable_scheduleCallback=function(M,j,I){var U=e.unstable_now();switch(typeof I=="object"&&I!==null?(I=I.delay,I=typeof I=="number"&&0<I?U+I:U):I=U,M){case 1:var W=-1;break;case 2:W=250;break;case 5:W=**********;break;case 4:W=1e4;break;default:W=5e3}return W=I+W,M={id:d++,callback:j,priorityLevel:M,startTime:I,expirationTime:W,sortIndex:-1},I>U?(M.sortIndex=I,t(u,M),n(l)===null&&M===n(u)&&(w?(h(P),P=-1):w=!0,X(S,I-U))):(M.sortIndex=W,t(l,M),y||v||(y=!0,B(T))),M},e.unstable_shouldYield=_,e.unstable_wrapCallback=function(M){var j=f;return function(){var I=f;f=j;try{return M.apply(this,arguments)}finally{f=I}}}})(xp);wp.exports=xp;var Sy=wp.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sp=g,Ge=Sy;function R(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Cp=new Set,ro={};function Fn(e,t){fr(e,t),fr(e+"Capture",t)}function fr(e,t){for(ro[e]=t,e=0;e<t.length;e++)Cp.add(t[e])}var Nt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ka=Object.prototype.hasOwnProperty,Cy=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Tc={},Pc={};function Ty(e){return ka.call(Pc,e)?!0:ka.call(Tc,e)?!1:Cy.test(e)?Pc[e]=!0:(Tc[e]=!0,!1)}function Py(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Ey(e,t,n,r){if(t===null||typeof t>"u"||Py(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Le(e,t,n,r,o,i,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=s}var Ce={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Ce[e]=new Le(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Ce[t]=new Le(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Ce[e]=new Le(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Ce[e]=new Le(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Ce[e]=new Le(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Ce[e]=new Le(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Ce[e]=new Le(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Ce[e]=new Le(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Ce[e]=new Le(e,5,!1,e.toLowerCase(),null,!1,!1)});var Yl=/[\-:]([a-z])/g;function Xl(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Yl,Xl);Ce[t]=new Le(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Yl,Xl);Ce[t]=new Le(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Yl,Xl);Ce[t]=new Le(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Ce[e]=new Le(e,1,!1,e.toLowerCase(),null,!1,!1)});Ce.xlinkHref=new Le("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Ce[e]=new Le(e,1,!1,e.toLowerCase(),null,!0,!0)});function Zl(e,t,n,r){var o=Ce.hasOwnProperty(t)?Ce[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Ey(t,n,o,r)&&(n=null),r||o===null?Ty(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var It=Sp.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Oo=Symbol.for("react.element"),$n=Symbol.for("react.portal"),Un=Symbol.for("react.fragment"),ql=Symbol.for("react.strict_mode"),ba=Symbol.for("react.profiler"),Tp=Symbol.for("react.provider"),Pp=Symbol.for("react.context"),Jl=Symbol.for("react.forward_ref"),Ra=Symbol.for("react.suspense"),Aa=Symbol.for("react.suspense_list"),eu=Symbol.for("react.memo"),Gt=Symbol.for("react.lazy"),Ep=Symbol.for("react.offscreen"),Ec=Symbol.iterator;function kr(e){return e===null||typeof e!="object"?null:(e=Ec&&e[Ec]||e["@@iterator"],typeof e=="function"?e:null)}var se=Object.assign,Os;function Or(e){if(Os===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Os=t&&t[1]||""}return`
`+Os+e}var Is=!1;function Fs(e,t){if(!e||Is)return"";Is=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),s=o.length-1,a=i.length-1;1<=s&&0<=a&&o[s]!==i[a];)a--;for(;1<=s&&0<=a;s--,a--)if(o[s]!==i[a]){if(s!==1||a!==1)do if(s--,a--,0>a||o[s]!==i[a]){var l=`
`+o[s].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=s&&0<=a);break}}}finally{Is=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Or(e):""}function ky(e){switch(e.tag){case 5:return Or(e.type);case 16:return Or("Lazy");case 13:return Or("Suspense");case 19:return Or("SuspenseList");case 0:case 2:case 15:return e=Fs(e.type,!1),e;case 11:return e=Fs(e.type.render,!1),e;case 1:return e=Fs(e.type,!0),e;default:return""}}function Ma(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Un:return"Fragment";case $n:return"Portal";case ba:return"Profiler";case ql:return"StrictMode";case Ra:return"Suspense";case Aa:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Pp:return(e.displayName||"Context")+".Consumer";case Tp:return(e._context.displayName||"Context")+".Provider";case Jl:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case eu:return t=e.displayName||null,t!==null?t:Ma(e.type)||"Memo";case Gt:t=e._payload,e=e._init;try{return Ma(e(t))}catch{}}return null}function by(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Ma(t);case 8:return t===ql?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function un(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function kp(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Ry(e){var t=kp(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(s){r=""+s,i.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Io(e){e._valueTracker||(e._valueTracker=Ry(e))}function bp(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=kp(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Ti(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function _a(e,t){var n=t.checked;return se({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function kc(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=un(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Rp(e,t){t=t.checked,t!=null&&Zl(e,"checked",t,!1)}function Na(e,t){Rp(e,t);var n=un(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?La(e,t.type,n):t.hasOwnProperty("defaultValue")&&La(e,t.type,un(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function bc(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function La(e,t,n){(t!=="number"||Ti(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Ir=Array.isArray;function ir(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+un(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Da(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(R(91));return se({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Rc(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(R(92));if(Ir(n)){if(1<n.length)throw Error(R(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:un(n)}}function Ap(e,t){var n=un(t.value),r=un(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Ac(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Mp(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ja(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Mp(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Fo,_p=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Fo=Fo||document.createElement("div"),Fo.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Fo.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function oo(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Ur={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Ay=["Webkit","ms","Moz","O"];Object.keys(Ur).forEach(function(e){Ay.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Ur[t]=Ur[e]})});function Np(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Ur.hasOwnProperty(e)&&Ur[e]?(""+t).trim():t+"px"}function Lp(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Np(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var My=se({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Va(e,t){if(t){if(My[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(R(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(R(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(R(61))}if(t.style!=null&&typeof t.style!="object")throw Error(R(62))}}function Oa(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ia=null;function tu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Fa=null,sr=null,ar=null;function Mc(e){if(e=Ao(e)){if(typeof Fa!="function")throw Error(R(280));var t=e.stateNode;t&&(t=us(t),Fa(e.stateNode,e.type,t))}}function Dp(e){sr?ar?ar.push(e):ar=[e]:sr=e}function jp(){if(sr){var e=sr,t=ar;if(ar=sr=null,Mc(e),t)for(e=0;e<t.length;e++)Mc(t[e])}}function Vp(e,t){return e(t)}function Op(){}var zs=!1;function Ip(e,t,n){if(zs)return e(t,n);zs=!0;try{return Vp(e,t,n)}finally{zs=!1,(sr!==null||ar!==null)&&(Op(),jp())}}function io(e,t){var n=e.stateNode;if(n===null)return null;var r=us(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(R(231,t,typeof n));return n}var za=!1;if(Nt)try{var br={};Object.defineProperty(br,"passive",{get:function(){za=!0}}),window.addEventListener("test",br,br),window.removeEventListener("test",br,br)}catch{za=!1}function _y(e,t,n,r,o,i,s,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(d){this.onError(d)}}var Wr=!1,Pi=null,Ei=!1,Ba=null,Ny={onError:function(e){Wr=!0,Pi=e}};function Ly(e,t,n,r,o,i,s,a,l){Wr=!1,Pi=null,_y.apply(Ny,arguments)}function Dy(e,t,n,r,o,i,s,a,l){if(Ly.apply(this,arguments),Wr){if(Wr){var u=Pi;Wr=!1,Pi=null}else throw Error(R(198));Ei||(Ei=!0,Ba=u)}}function zn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Fp(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function _c(e){if(zn(e)!==e)throw Error(R(188))}function jy(e){var t=e.alternate;if(!t){if(t=zn(e),t===null)throw Error(R(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return _c(o),e;if(i===r)return _c(o),t;i=i.sibling}throw Error(R(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,a=o.child;a;){if(a===n){s=!0,n=o,r=i;break}if(a===r){s=!0,r=o,n=i;break}a=a.sibling}if(!s){for(a=i.child;a;){if(a===n){s=!0,n=i,r=o;break}if(a===r){s=!0,r=i,n=o;break}a=a.sibling}if(!s)throw Error(R(189))}}if(n.alternate!==r)throw Error(R(190))}if(n.tag!==3)throw Error(R(188));return n.stateNode.current===n?e:t}function zp(e){return e=jy(e),e!==null?Bp(e):null}function Bp(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Bp(e);if(t!==null)return t;e=e.sibling}return null}var $p=Ge.unstable_scheduleCallback,Nc=Ge.unstable_cancelCallback,Vy=Ge.unstable_shouldYield,Oy=Ge.unstable_requestPaint,ce=Ge.unstable_now,Iy=Ge.unstable_getCurrentPriorityLevel,nu=Ge.unstable_ImmediatePriority,Up=Ge.unstable_UserBlockingPriority,ki=Ge.unstable_NormalPriority,Fy=Ge.unstable_LowPriority,Wp=Ge.unstable_IdlePriority,is=null,vt=null;function zy(e){if(vt&&typeof vt.onCommitFiberRoot=="function")try{vt.onCommitFiberRoot(is,e,void 0,(e.current.flags&128)===128)}catch{}}var ut=Math.clz32?Math.clz32:Uy,By=Math.log,$y=Math.LN2;function Uy(e){return e>>>=0,e===0?32:31-(By(e)/$y|0)|0}var zo=64,Bo=4194304;function Fr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function bi(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,s=n&268435455;if(s!==0){var a=s&~o;a!==0?r=Fr(a):(i&=s,i!==0&&(r=Fr(i)))}else s=n&~o,s!==0?r=Fr(s):i!==0&&(r=Fr(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-ut(t),o=1<<n,r|=e[n],t&=~o;return r}function Wy(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Hy(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var s=31-ut(i),a=1<<s,l=o[s];l===-1?(!(a&n)||a&r)&&(o[s]=Wy(a,t)):l<=t&&(e.expiredLanes|=a),i&=~a}}function $a(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Hp(){var e=zo;return zo<<=1,!(zo&4194240)&&(zo=64),e}function Bs(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function bo(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-ut(t),e[t]=n}function Ky(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-ut(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function ru(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-ut(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var G=0;function Kp(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Gp,ou,Qp,Yp,Xp,Ua=!1,$o=[],en=null,tn=null,nn=null,so=new Map,ao=new Map,Yt=[],Gy="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Lc(e,t){switch(e){case"focusin":case"focusout":en=null;break;case"dragenter":case"dragleave":tn=null;break;case"mouseover":case"mouseout":nn=null;break;case"pointerover":case"pointerout":so.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ao.delete(t.pointerId)}}function Rr(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=Ao(t),t!==null&&ou(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function Qy(e,t,n,r,o){switch(t){case"focusin":return en=Rr(en,e,t,n,r,o),!0;case"dragenter":return tn=Rr(tn,e,t,n,r,o),!0;case"mouseover":return nn=Rr(nn,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return so.set(i,Rr(so.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,ao.set(i,Rr(ao.get(i)||null,e,t,n,r,o)),!0}return!1}function Zp(e){var t=En(e.target);if(t!==null){var n=zn(t);if(n!==null){if(t=n.tag,t===13){if(t=Fp(n),t!==null){e.blockedOn=t,Xp(e.priority,function(){Qp(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function li(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Wa(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Ia=r,n.target.dispatchEvent(r),Ia=null}else return t=Ao(n),t!==null&&ou(t),e.blockedOn=n,!1;t.shift()}return!0}function Dc(e,t,n){li(e)&&n.delete(t)}function Yy(){Ua=!1,en!==null&&li(en)&&(en=null),tn!==null&&li(tn)&&(tn=null),nn!==null&&li(nn)&&(nn=null),so.forEach(Dc),ao.forEach(Dc)}function Ar(e,t){e.blockedOn===t&&(e.blockedOn=null,Ua||(Ua=!0,Ge.unstable_scheduleCallback(Ge.unstable_NormalPriority,Yy)))}function lo(e){function t(o){return Ar(o,e)}if(0<$o.length){Ar($o[0],e);for(var n=1;n<$o.length;n++){var r=$o[n];r.blockedOn===e&&(r.blockedOn=null)}}for(en!==null&&Ar(en,e),tn!==null&&Ar(tn,e),nn!==null&&Ar(nn,e),so.forEach(t),ao.forEach(t),n=0;n<Yt.length;n++)r=Yt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Yt.length&&(n=Yt[0],n.blockedOn===null);)Zp(n),n.blockedOn===null&&Yt.shift()}var lr=It.ReactCurrentBatchConfig,Ri=!0;function Xy(e,t,n,r){var o=G,i=lr.transition;lr.transition=null;try{G=1,iu(e,t,n,r)}finally{G=o,lr.transition=i}}function Zy(e,t,n,r){var o=G,i=lr.transition;lr.transition=null;try{G=4,iu(e,t,n,r)}finally{G=o,lr.transition=i}}function iu(e,t,n,r){if(Ri){var o=Wa(e,t,n,r);if(o===null)Zs(e,t,r,Ai,n),Lc(e,r);else if(Qy(o,e,t,n,r))r.stopPropagation();else if(Lc(e,r),t&4&&-1<Gy.indexOf(e)){for(;o!==null;){var i=Ao(o);if(i!==null&&Gp(i),i=Wa(e,t,n,r),i===null&&Zs(e,t,r,Ai,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Zs(e,t,r,null,n)}}var Ai=null;function Wa(e,t,n,r){if(Ai=null,e=tu(r),e=En(e),e!==null)if(t=zn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Fp(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Ai=e,null}function qp(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Iy()){case nu:return 1;case Up:return 4;case ki:case Fy:return 16;case Wp:return 536870912;default:return 16}default:return 16}}var Zt=null,su=null,ui=null;function Jp(){if(ui)return ui;var e,t=su,n=t.length,r,o="value"in Zt?Zt.value:Zt.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===o[i-r];r++);return ui=o.slice(e,1<r?1-r:void 0)}function ci(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Uo(){return!0}function jc(){return!1}function Xe(e){function t(n,r,o,i,s){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Uo:jc,this.isPropagationStopped=jc,this}return se(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Uo)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Uo)},persist:function(){},isPersistent:Uo}),t}var Cr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},au=Xe(Cr),Ro=se({},Cr,{view:0,detail:0}),qy=Xe(Ro),$s,Us,Mr,ss=se({},Ro,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:lu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Mr&&(Mr&&e.type==="mousemove"?($s=e.screenX-Mr.screenX,Us=e.screenY-Mr.screenY):Us=$s=0,Mr=e),$s)},movementY:function(e){return"movementY"in e?e.movementY:Us}}),Vc=Xe(ss),Jy=se({},ss,{dataTransfer:0}),e0=Xe(Jy),t0=se({},Ro,{relatedTarget:0}),Ws=Xe(t0),n0=se({},Cr,{animationName:0,elapsedTime:0,pseudoElement:0}),r0=Xe(n0),o0=se({},Cr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),i0=Xe(o0),s0=se({},Cr,{data:0}),Oc=Xe(s0),a0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},l0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},u0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function c0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=u0[e])?!!t[e]:!1}function lu(){return c0}var d0=se({},Ro,{key:function(e){if(e.key){var t=a0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ci(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?l0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:lu,charCode:function(e){return e.type==="keypress"?ci(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ci(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),f0=Xe(d0),p0=se({},ss,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ic=Xe(p0),h0=se({},Ro,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:lu}),m0=Xe(h0),v0=se({},Cr,{propertyName:0,elapsedTime:0,pseudoElement:0}),g0=Xe(v0),y0=se({},ss,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),w0=Xe(y0),x0=[9,13,27,32],uu=Nt&&"CompositionEvent"in window,Hr=null;Nt&&"documentMode"in document&&(Hr=document.documentMode);var S0=Nt&&"TextEvent"in window&&!Hr,eh=Nt&&(!uu||Hr&&8<Hr&&11>=Hr),Fc=" ",zc=!1;function th(e,t){switch(e){case"keyup":return x0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function nh(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Wn=!1;function C0(e,t){switch(e){case"compositionend":return nh(t);case"keypress":return t.which!==32?null:(zc=!0,Fc);case"textInput":return e=t.data,e===Fc&&zc?null:e;default:return null}}function T0(e,t){if(Wn)return e==="compositionend"||!uu&&th(e,t)?(e=Jp(),ui=su=Zt=null,Wn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return eh&&t.locale!=="ko"?null:t.data;default:return null}}var P0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Bc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!P0[e.type]:t==="textarea"}function rh(e,t,n,r){Dp(r),t=Mi(t,"onChange"),0<t.length&&(n=new au("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Kr=null,uo=null;function E0(e){hh(e,0)}function as(e){var t=Gn(e);if(bp(t))return e}function k0(e,t){if(e==="change")return t}var oh=!1;if(Nt){var Hs;if(Nt){var Ks="oninput"in document;if(!Ks){var $c=document.createElement("div");$c.setAttribute("oninput","return;"),Ks=typeof $c.oninput=="function"}Hs=Ks}else Hs=!1;oh=Hs&&(!document.documentMode||9<document.documentMode)}function Uc(){Kr&&(Kr.detachEvent("onpropertychange",ih),uo=Kr=null)}function ih(e){if(e.propertyName==="value"&&as(uo)){var t=[];rh(t,uo,e,tu(e)),Ip(E0,t)}}function b0(e,t,n){e==="focusin"?(Uc(),Kr=t,uo=n,Kr.attachEvent("onpropertychange",ih)):e==="focusout"&&Uc()}function R0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return as(uo)}function A0(e,t){if(e==="click")return as(t)}function M0(e,t){if(e==="input"||e==="change")return as(t)}function _0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var dt=typeof Object.is=="function"?Object.is:_0;function co(e,t){if(dt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!ka.call(t,o)||!dt(e[o],t[o]))return!1}return!0}function Wc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Hc(e,t){var n=Wc(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Wc(n)}}function sh(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?sh(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function ah(){for(var e=window,t=Ti();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Ti(e.document)}return t}function cu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function N0(e){var t=ah(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&sh(n.ownerDocument.documentElement,n)){if(r!==null&&cu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=Hc(n,i);var s=Hc(n,r);o&&s&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var L0=Nt&&"documentMode"in document&&11>=document.documentMode,Hn=null,Ha=null,Gr=null,Ka=!1;function Kc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ka||Hn==null||Hn!==Ti(r)||(r=Hn,"selectionStart"in r&&cu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Gr&&co(Gr,r)||(Gr=r,r=Mi(Ha,"onSelect"),0<r.length&&(t=new au("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Hn)))}function Wo(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Kn={animationend:Wo("Animation","AnimationEnd"),animationiteration:Wo("Animation","AnimationIteration"),animationstart:Wo("Animation","AnimationStart"),transitionend:Wo("Transition","TransitionEnd")},Gs={},lh={};Nt&&(lh=document.createElement("div").style,"AnimationEvent"in window||(delete Kn.animationend.animation,delete Kn.animationiteration.animation,delete Kn.animationstart.animation),"TransitionEvent"in window||delete Kn.transitionend.transition);function ls(e){if(Gs[e])return Gs[e];if(!Kn[e])return e;var t=Kn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in lh)return Gs[e]=t[n];return e}var uh=ls("animationend"),ch=ls("animationiteration"),dh=ls("animationstart"),fh=ls("transitionend"),ph=new Map,Gc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function pn(e,t){ph.set(e,t),Fn(t,[e])}for(var Qs=0;Qs<Gc.length;Qs++){var Ys=Gc[Qs],D0=Ys.toLowerCase(),j0=Ys[0].toUpperCase()+Ys.slice(1);pn(D0,"on"+j0)}pn(uh,"onAnimationEnd");pn(ch,"onAnimationIteration");pn(dh,"onAnimationStart");pn("dblclick","onDoubleClick");pn("focusin","onFocus");pn("focusout","onBlur");pn(fh,"onTransitionEnd");fr("onMouseEnter",["mouseout","mouseover"]);fr("onMouseLeave",["mouseout","mouseover"]);fr("onPointerEnter",["pointerout","pointerover"]);fr("onPointerLeave",["pointerout","pointerover"]);Fn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Fn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Fn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Fn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Fn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Fn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),V0=new Set("cancel close invalid load scroll toggle".split(" ").concat(zr));function Qc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Dy(r,t,void 0,e),e.currentTarget=null}function hh(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var s=r.length-1;0<=s;s--){var a=r[s],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==i&&o.isPropagationStopped())break e;Qc(o,a,u),i=l}else for(s=0;s<r.length;s++){if(a=r[s],l=a.instance,u=a.currentTarget,a=a.listener,l!==i&&o.isPropagationStopped())break e;Qc(o,a,u),i=l}}}if(Ei)throw e=Ba,Ei=!1,Ba=null,e}function q(e,t){var n=t[Za];n===void 0&&(n=t[Za]=new Set);var r=e+"__bubble";n.has(r)||(mh(t,e,2,!1),n.add(r))}function Xs(e,t,n){var r=0;t&&(r|=4),mh(n,e,r,t)}var Ho="_reactListening"+Math.random().toString(36).slice(2);function fo(e){if(!e[Ho]){e[Ho]=!0,Cp.forEach(function(n){n!=="selectionchange"&&(V0.has(n)||Xs(n,!1,e),Xs(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ho]||(t[Ho]=!0,Xs("selectionchange",!1,t))}}function mh(e,t,n,r){switch(qp(t)){case 1:var o=Xy;break;case 4:o=Zy;break;default:o=iu}n=o.bind(null,t,n,e),o=void 0,!za||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Zs(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var a=r.stateNode.containerInfo;if(a===o||a.nodeType===8&&a.parentNode===o)break;if(s===4)for(s=r.return;s!==null;){var l=s.tag;if((l===3||l===4)&&(l=s.stateNode.containerInfo,l===o||l.nodeType===8&&l.parentNode===o))return;s=s.return}for(;a!==null;){if(s=En(a),s===null)return;if(l=s.tag,l===5||l===6){r=i=s;continue e}a=a.parentNode}}r=r.return}Ip(function(){var u=i,d=tu(n),c=[];e:{var f=ph.get(e);if(f!==void 0){var v=au,y=e;switch(e){case"keypress":if(ci(n)===0)break e;case"keydown":case"keyup":v=f0;break;case"focusin":y="focus",v=Ws;break;case"focusout":y="blur",v=Ws;break;case"beforeblur":case"afterblur":v=Ws;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":v=Vc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":v=e0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":v=m0;break;case uh:case ch:case dh:v=r0;break;case fh:v=g0;break;case"scroll":v=qy;break;case"wheel":v=w0;break;case"copy":case"cut":case"paste":v=i0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":v=Ic}var w=(t&4)!==0,x=!w&&e==="scroll",h=w?f!==null?f+"Capture":null:f;w=[];for(var p=u,m;p!==null;){m=p;var S=m.stateNode;if(m.tag===5&&S!==null&&(m=S,h!==null&&(S=io(p,h),S!=null&&w.push(po(p,S,m)))),x)break;p=p.return}0<w.length&&(f=new v(f,y,null,n,d),c.push({event:f,listeners:w}))}}if(!(t&7)){e:{if(f=e==="mouseover"||e==="pointerover",v=e==="mouseout"||e==="pointerout",f&&n!==Ia&&(y=n.relatedTarget||n.fromElement)&&(En(y)||y[Lt]))break e;if((v||f)&&(f=d.window===d?d:(f=d.ownerDocument)?f.defaultView||f.parentWindow:window,v?(y=n.relatedTarget||n.toElement,v=u,y=y?En(y):null,y!==null&&(x=zn(y),y!==x||y.tag!==5&&y.tag!==6)&&(y=null)):(v=null,y=u),v!==y)){if(w=Vc,S="onMouseLeave",h="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(w=Ic,S="onPointerLeave",h="onPointerEnter",p="pointer"),x=v==null?f:Gn(v),m=y==null?f:Gn(y),f=new w(S,p+"leave",v,n,d),f.target=x,f.relatedTarget=m,S=null,En(d)===u&&(w=new w(h,p+"enter",y,n,d),w.target=m,w.relatedTarget=x,S=w),x=S,v&&y)t:{for(w=v,h=y,p=0,m=w;m;m=Bn(m))p++;for(m=0,S=h;S;S=Bn(S))m++;for(;0<p-m;)w=Bn(w),p--;for(;0<m-p;)h=Bn(h),m--;for(;p--;){if(w===h||h!==null&&w===h.alternate)break t;w=Bn(w),h=Bn(h)}w=null}else w=null;v!==null&&Yc(c,f,v,w,!1),y!==null&&x!==null&&Yc(c,x,y,w,!0)}}e:{if(f=u?Gn(u):window,v=f.nodeName&&f.nodeName.toLowerCase(),v==="select"||v==="input"&&f.type==="file")var T=k0;else if(Bc(f))if(oh)T=M0;else{T=R0;var E=b0}else(v=f.nodeName)&&v.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(T=A0);if(T&&(T=T(e,u))){rh(c,T,n,d);break e}E&&E(e,f,u),e==="focusout"&&(E=f._wrapperState)&&E.controlled&&f.type==="number"&&La(f,"number",f.value)}switch(E=u?Gn(u):window,e){case"focusin":(Bc(E)||E.contentEditable==="true")&&(Hn=E,Ha=u,Gr=null);break;case"focusout":Gr=Ha=Hn=null;break;case"mousedown":Ka=!0;break;case"contextmenu":case"mouseup":case"dragend":Ka=!1,Kc(c,n,d);break;case"selectionchange":if(L0)break;case"keydown":case"keyup":Kc(c,n,d)}var k;if(uu)e:{switch(e){case"compositionstart":var P="onCompositionStart";break e;case"compositionend":P="onCompositionEnd";break e;case"compositionupdate":P="onCompositionUpdate";break e}P=void 0}else Wn?th(e,n)&&(P="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(P="onCompositionStart");P&&(eh&&n.locale!=="ko"&&(Wn||P!=="onCompositionStart"?P==="onCompositionEnd"&&Wn&&(k=Jp()):(Zt=d,su="value"in Zt?Zt.value:Zt.textContent,Wn=!0)),E=Mi(u,P),0<E.length&&(P=new Oc(P,e,null,n,d),c.push({event:P,listeners:E}),k?P.data=k:(k=nh(n),k!==null&&(P.data=k)))),(k=S0?C0(e,n):T0(e,n))&&(u=Mi(u,"onBeforeInput"),0<u.length&&(d=new Oc("onBeforeInput","beforeinput",null,n,d),c.push({event:d,listeners:u}),d.data=k))}hh(c,t)})}function po(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Mi(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=io(e,n),i!=null&&r.unshift(po(e,i,o)),i=io(e,t),i!=null&&r.push(po(e,i,o))),e=e.return}return r}function Bn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Yc(e,t,n,r,o){for(var i=t._reactName,s=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,o?(l=io(n,i),l!=null&&s.unshift(po(n,l,a))):o||(l=io(n,i),l!=null&&s.push(po(n,l,a)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var O0=/\r\n?/g,I0=/\u0000|\uFFFD/g;function Xc(e){return(typeof e=="string"?e:""+e).replace(O0,`
`).replace(I0,"")}function Ko(e,t,n){if(t=Xc(t),Xc(e)!==t&&n)throw Error(R(425))}function _i(){}var Ga=null,Qa=null;function Ya(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Xa=typeof setTimeout=="function"?setTimeout:void 0,F0=typeof clearTimeout=="function"?clearTimeout:void 0,Zc=typeof Promise=="function"?Promise:void 0,z0=typeof queueMicrotask=="function"?queueMicrotask:typeof Zc<"u"?function(e){return Zc.resolve(null).then(e).catch(B0)}:Xa;function B0(e){setTimeout(function(){throw e})}function qs(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),lo(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);lo(t)}function rn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function qc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Tr=Math.random().toString(36).slice(2),mt="__reactFiber$"+Tr,ho="__reactProps$"+Tr,Lt="__reactContainer$"+Tr,Za="__reactEvents$"+Tr,$0="__reactListeners$"+Tr,U0="__reactHandles$"+Tr;function En(e){var t=e[mt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Lt]||n[mt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=qc(e);e!==null;){if(n=e[mt])return n;e=qc(e)}return t}e=n,n=e.parentNode}return null}function Ao(e){return e=e[mt]||e[Lt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Gn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(R(33))}function us(e){return e[ho]||null}var qa=[],Qn=-1;function hn(e){return{current:e}}function J(e){0>Qn||(e.current=qa[Qn],qa[Qn]=null,Qn--)}function Y(e,t){Qn++,qa[Qn]=e.current,e.current=t}var cn={},Ae=hn(cn),Ie=hn(!1),Ln=cn;function pr(e,t){var n=e.type.contextTypes;if(!n)return cn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Fe(e){return e=e.childContextTypes,e!=null}function Ni(){J(Ie),J(Ae)}function Jc(e,t,n){if(Ae.current!==cn)throw Error(R(168));Y(Ae,t),Y(Ie,n)}function vh(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(R(108,by(e)||"Unknown",o));return se({},n,r)}function Li(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||cn,Ln=Ae.current,Y(Ae,e),Y(Ie,Ie.current),!0}function ed(e,t,n){var r=e.stateNode;if(!r)throw Error(R(169));n?(e=vh(e,t,Ln),r.__reactInternalMemoizedMergedChildContext=e,J(Ie),J(Ae),Y(Ae,e)):J(Ie),Y(Ie,n)}var Ct=null,cs=!1,Js=!1;function gh(e){Ct===null?Ct=[e]:Ct.push(e)}function W0(e){cs=!0,gh(e)}function mn(){if(!Js&&Ct!==null){Js=!0;var e=0,t=G;try{var n=Ct;for(G=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Ct=null,cs=!1}catch(o){throw Ct!==null&&(Ct=Ct.slice(e+1)),$p(nu,mn),o}finally{G=t,Js=!1}}return null}var Yn=[],Xn=0,Di=null,ji=0,Je=[],et=0,Dn=null,Tt=1,Pt="";function xn(e,t){Yn[Xn++]=ji,Yn[Xn++]=Di,Di=e,ji=t}function yh(e,t,n){Je[et++]=Tt,Je[et++]=Pt,Je[et++]=Dn,Dn=e;var r=Tt;e=Pt;var o=32-ut(r)-1;r&=~(1<<o),n+=1;var i=32-ut(t)+o;if(30<i){var s=o-o%5;i=(r&(1<<s)-1).toString(32),r>>=s,o-=s,Tt=1<<32-ut(t)+o|n<<o|r,Pt=i+e}else Tt=1<<i|n<<o|r,Pt=e}function du(e){e.return!==null&&(xn(e,1),yh(e,1,0))}function fu(e){for(;e===Di;)Di=Yn[--Xn],Yn[Xn]=null,ji=Yn[--Xn],Yn[Xn]=null;for(;e===Dn;)Dn=Je[--et],Je[et]=null,Pt=Je[--et],Je[et]=null,Tt=Je[--et],Je[et]=null}var Ke=null,He=null,ee=!1,lt=null;function wh(e,t){var n=tt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function td(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ke=e,He=rn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ke=e,He=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Dn!==null?{id:Tt,overflow:Pt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=tt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ke=e,He=null,!0):!1;default:return!1}}function Ja(e){return(e.mode&1)!==0&&(e.flags&128)===0}function el(e){if(ee){var t=He;if(t){var n=t;if(!td(e,t)){if(Ja(e))throw Error(R(418));t=rn(n.nextSibling);var r=Ke;t&&td(e,t)?wh(r,n):(e.flags=e.flags&-4097|2,ee=!1,Ke=e)}}else{if(Ja(e))throw Error(R(418));e.flags=e.flags&-4097|2,ee=!1,Ke=e}}}function nd(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ke=e}function Go(e){if(e!==Ke)return!1;if(!ee)return nd(e),ee=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ya(e.type,e.memoizedProps)),t&&(t=He)){if(Ja(e))throw xh(),Error(R(418));for(;t;)wh(e,t),t=rn(t.nextSibling)}if(nd(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(R(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){He=rn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}He=null}}else He=Ke?rn(e.stateNode.nextSibling):null;return!0}function xh(){for(var e=He;e;)e=rn(e.nextSibling)}function hr(){He=Ke=null,ee=!1}function pu(e){lt===null?lt=[e]:lt.push(e)}var H0=It.ReactCurrentBatchConfig;function st(e,t){if(e&&e.defaultProps){t=se({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}var Vi=hn(null),Oi=null,Zn=null,hu=null;function mu(){hu=Zn=Oi=null}function vu(e){var t=Vi.current;J(Vi),e._currentValue=t}function tl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function ur(e,t){Oi=e,hu=Zn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Oe=!0),e.firstContext=null)}function rt(e){var t=e._currentValue;if(hu!==e)if(e={context:e,memoizedValue:t,next:null},Zn===null){if(Oi===null)throw Error(R(308));Zn=e,Oi.dependencies={lanes:0,firstContext:e}}else Zn=Zn.next=e;return t}var kn=null;function gu(e){kn===null?kn=[e]:kn.push(e)}function Sh(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,gu(t)):(n.next=o.next,o.next=n),t.interleaved=n,Dt(e,r)}function Dt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Qt=!1;function yu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Ch(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function kt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function on(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,H&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Dt(e,n)}return o=r.interleaved,o===null?(t.next=t,gu(r)):(t.next=o.next,o.next=t),r.interleaved=t,Dt(e,n)}function di(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ru(e,n)}}function rd(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=s:i=i.next=s,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ii(e,t,n,r){var o=e.updateQueue;Qt=!1;var i=o.firstBaseUpdate,s=o.lastBaseUpdate,a=o.shared.pending;if(a!==null){o.shared.pending=null;var l=a,u=l.next;l.next=null,s===null?i=u:s.next=u,s=l;var d=e.alternate;d!==null&&(d=d.updateQueue,a=d.lastBaseUpdate,a!==s&&(a===null?d.firstBaseUpdate=u:a.next=u,d.lastBaseUpdate=l))}if(i!==null){var c=o.baseState;s=0,d=u=l=null,a=i;do{var f=a.lane,v=a.eventTime;if((r&f)===f){d!==null&&(d=d.next={eventTime:v,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var y=e,w=a;switch(f=t,v=n,w.tag){case 1:if(y=w.payload,typeof y=="function"){c=y.call(v,c,f);break e}c=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=w.payload,f=typeof y=="function"?y.call(v,c,f):y,f==null)break e;c=se({},c,f);break e;case 2:Qt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,f=o.effects,f===null?o.effects=[a]:f.push(a))}else v={eventTime:v,lane:f,tag:a.tag,payload:a.payload,callback:a.callback,next:null},d===null?(u=d=v,l=c):d=d.next=v,s|=f;if(a=a.next,a===null){if(a=o.shared.pending,a===null)break;f=a,a=f.next,f.next=null,o.lastBaseUpdate=f,o.shared.pending=null}}while(!0);if(d===null&&(l=c),o.baseState=l,o.firstBaseUpdate=u,o.lastBaseUpdate=d,t=o.shared.interleaved,t!==null){o=t;do s|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);Vn|=s,e.lanes=s,e.memoizedState=c}}function od(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(R(191,o));o.call(r)}}}var Th=new Sp.Component().refs;function nl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:se({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var ds={isMounted:function(e){return(e=e._reactInternals)?zn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=_e(),o=an(e),i=kt(r,o);i.payload=t,n!=null&&(i.callback=n),t=on(e,i,o),t!==null&&(ct(t,e,o,r),di(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=_e(),o=an(e),i=kt(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=on(e,i,o),t!==null&&(ct(t,e,o,r),di(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=_e(),r=an(e),o=kt(n,r);o.tag=2,t!=null&&(o.callback=t),t=on(e,o,r),t!==null&&(ct(t,e,r,n),di(t,e,r))}};function id(e,t,n,r,o,i,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,s):t.prototype&&t.prototype.isPureReactComponent?!co(n,r)||!co(o,i):!0}function Ph(e,t,n){var r=!1,o=cn,i=t.contextType;return typeof i=="object"&&i!==null?i=rt(i):(o=Fe(t)?Ln:Ae.current,r=t.contextTypes,i=(r=r!=null)?pr(e,o):cn),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=ds,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function sd(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ds.enqueueReplaceState(t,t.state,null)}function rl(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs=Th,yu(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=rt(i):(i=Fe(t)?Ln:Ae.current,o.context=pr(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(nl(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&ds.enqueueReplaceState(o,o.state,null),Ii(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function _r(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(R(309));var r=n.stateNode}if(!r)throw Error(R(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(s){var a=o.refs;a===Th&&(a=o.refs={}),s===null?delete a[i]:a[i]=s},t._stringRef=i,t)}if(typeof e!="string")throw Error(R(284));if(!n._owner)throw Error(R(290,e))}return e}function Qo(e,t){throw e=Object.prototype.toString.call(t),Error(R(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ad(e){var t=e._init;return t(e._payload)}function Eh(e){function t(h,p){if(e){var m=h.deletions;m===null?(h.deletions=[p],h.flags|=16):m.push(p)}}function n(h,p){if(!e)return null;for(;p!==null;)t(h,p),p=p.sibling;return null}function r(h,p){for(h=new Map;p!==null;)p.key!==null?h.set(p.key,p):h.set(p.index,p),p=p.sibling;return h}function o(h,p){return h=ln(h,p),h.index=0,h.sibling=null,h}function i(h,p,m){return h.index=m,e?(m=h.alternate,m!==null?(m=m.index,m<p?(h.flags|=2,p):m):(h.flags|=2,p)):(h.flags|=1048576,p)}function s(h){return e&&h.alternate===null&&(h.flags|=2),h}function a(h,p,m,S){return p===null||p.tag!==6?(p=sa(m,h.mode,S),p.return=h,p):(p=o(p,m),p.return=h,p)}function l(h,p,m,S){var T=m.type;return T===Un?d(h,p,m.props.children,S,m.key):p!==null&&(p.elementType===T||typeof T=="object"&&T!==null&&T.$$typeof===Gt&&ad(T)===p.type)?(S=o(p,m.props),S.ref=_r(h,p,m),S.return=h,S):(S=gi(m.type,m.key,m.props,null,h.mode,S),S.ref=_r(h,p,m),S.return=h,S)}function u(h,p,m,S){return p===null||p.tag!==4||p.stateNode.containerInfo!==m.containerInfo||p.stateNode.implementation!==m.implementation?(p=aa(m,h.mode,S),p.return=h,p):(p=o(p,m.children||[]),p.return=h,p)}function d(h,p,m,S,T){return p===null||p.tag!==7?(p=_n(m,h.mode,S,T),p.return=h,p):(p=o(p,m),p.return=h,p)}function c(h,p,m){if(typeof p=="string"&&p!==""||typeof p=="number")return p=sa(""+p,h.mode,m),p.return=h,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case Oo:return m=gi(p.type,p.key,p.props,null,h.mode,m),m.ref=_r(h,null,p),m.return=h,m;case $n:return p=aa(p,h.mode,m),p.return=h,p;case Gt:var S=p._init;return c(h,S(p._payload),m)}if(Ir(p)||kr(p))return p=_n(p,h.mode,m,null),p.return=h,p;Qo(h,p)}return null}function f(h,p,m,S){var T=p!==null?p.key:null;if(typeof m=="string"&&m!==""||typeof m=="number")return T!==null?null:a(h,p,""+m,S);if(typeof m=="object"&&m!==null){switch(m.$$typeof){case Oo:return m.key===T?l(h,p,m,S):null;case $n:return m.key===T?u(h,p,m,S):null;case Gt:return T=m._init,f(h,p,T(m._payload),S)}if(Ir(m)||kr(m))return T!==null?null:d(h,p,m,S,null);Qo(h,m)}return null}function v(h,p,m,S,T){if(typeof S=="string"&&S!==""||typeof S=="number")return h=h.get(m)||null,a(p,h,""+S,T);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case Oo:return h=h.get(S.key===null?m:S.key)||null,l(p,h,S,T);case $n:return h=h.get(S.key===null?m:S.key)||null,u(p,h,S,T);case Gt:var E=S._init;return v(h,p,m,E(S._payload),T)}if(Ir(S)||kr(S))return h=h.get(m)||null,d(p,h,S,T,null);Qo(p,S)}return null}function y(h,p,m,S){for(var T=null,E=null,k=p,P=p=0,D=null;k!==null&&P<m.length;P++){k.index>P?(D=k,k=null):D=k.sibling;var N=f(h,k,m[P],S);if(N===null){k===null&&(k=D);break}e&&k&&N.alternate===null&&t(h,k),p=i(N,p,P),E===null?T=N:E.sibling=N,E=N,k=D}if(P===m.length)return n(h,k),ee&&xn(h,P),T;if(k===null){for(;P<m.length;P++)k=c(h,m[P],S),k!==null&&(p=i(k,p,P),E===null?T=k:E.sibling=k,E=k);return ee&&xn(h,P),T}for(k=r(h,k);P<m.length;P++)D=v(k,h,P,m[P],S),D!==null&&(e&&D.alternate!==null&&k.delete(D.key===null?P:D.key),p=i(D,p,P),E===null?T=D:E.sibling=D,E=D);return e&&k.forEach(function(_){return t(h,_)}),ee&&xn(h,P),T}function w(h,p,m,S){var T=kr(m);if(typeof T!="function")throw Error(R(150));if(m=T.call(m),m==null)throw Error(R(151));for(var E=T=null,k=p,P=p=0,D=null,N=m.next();k!==null&&!N.done;P++,N=m.next()){k.index>P?(D=k,k=null):D=k.sibling;var _=f(h,k,N.value,S);if(_===null){k===null&&(k=D);break}e&&k&&_.alternate===null&&t(h,k),p=i(_,p,P),E===null?T=_:E.sibling=_,E=_,k=D}if(N.done)return n(h,k),ee&&xn(h,P),T;if(k===null){for(;!N.done;P++,N=m.next())N=c(h,N.value,S),N!==null&&(p=i(N,p,P),E===null?T=N:E.sibling=N,E=N);return ee&&xn(h,P),T}for(k=r(h,k);!N.done;P++,N=m.next())N=v(k,h,P,N.value,S),N!==null&&(e&&N.alternate!==null&&k.delete(N.key===null?P:N.key),p=i(N,p,P),E===null?T=N:E.sibling=N,E=N);return e&&k.forEach(function(b){return t(h,b)}),ee&&xn(h,P),T}function x(h,p,m,S){if(typeof m=="object"&&m!==null&&m.type===Un&&m.key===null&&(m=m.props.children),typeof m=="object"&&m!==null){switch(m.$$typeof){case Oo:e:{for(var T=m.key,E=p;E!==null;){if(E.key===T){if(T=m.type,T===Un){if(E.tag===7){n(h,E.sibling),p=o(E,m.props.children),p.return=h,h=p;break e}}else if(E.elementType===T||typeof T=="object"&&T!==null&&T.$$typeof===Gt&&ad(T)===E.type){n(h,E.sibling),p=o(E,m.props),p.ref=_r(h,E,m),p.return=h,h=p;break e}n(h,E);break}else t(h,E);E=E.sibling}m.type===Un?(p=_n(m.props.children,h.mode,S,m.key),p.return=h,h=p):(S=gi(m.type,m.key,m.props,null,h.mode,S),S.ref=_r(h,p,m),S.return=h,h=S)}return s(h);case $n:e:{for(E=m.key;p!==null;){if(p.key===E)if(p.tag===4&&p.stateNode.containerInfo===m.containerInfo&&p.stateNode.implementation===m.implementation){n(h,p.sibling),p=o(p,m.children||[]),p.return=h,h=p;break e}else{n(h,p);break}else t(h,p);p=p.sibling}p=aa(m,h.mode,S),p.return=h,h=p}return s(h);case Gt:return E=m._init,x(h,p,E(m._payload),S)}if(Ir(m))return y(h,p,m,S);if(kr(m))return w(h,p,m,S);Qo(h,m)}return typeof m=="string"&&m!==""||typeof m=="number"?(m=""+m,p!==null&&p.tag===6?(n(h,p.sibling),p=o(p,m),p.return=h,h=p):(n(h,p),p=sa(m,h.mode,S),p.return=h,h=p),s(h)):n(h,p)}return x}var mr=Eh(!0),kh=Eh(!1),Mo={},gt=hn(Mo),mo=hn(Mo),vo=hn(Mo);function bn(e){if(e===Mo)throw Error(R(174));return e}function wu(e,t){switch(Y(vo,t),Y(mo,e),Y(gt,Mo),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ja(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=ja(t,e)}J(gt),Y(gt,t)}function vr(){J(gt),J(mo),J(vo)}function bh(e){bn(vo.current);var t=bn(gt.current),n=ja(t,e.type);t!==n&&(Y(mo,e),Y(gt,n))}function xu(e){mo.current===e&&(J(gt),J(mo))}var re=hn(0);function Fi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ea=[];function Su(){for(var e=0;e<ea.length;e++)ea[e]._workInProgressVersionPrimary=null;ea.length=0}var fi=It.ReactCurrentDispatcher,ta=It.ReactCurrentBatchConfig,jn=0,ie=null,he=null,ge=null,zi=!1,Qr=!1,go=0,K0=0;function Te(){throw Error(R(321))}function Cu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!dt(e[n],t[n]))return!1;return!0}function Tu(e,t,n,r,o,i){if(jn=i,ie=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,fi.current=e===null||e.memoizedState===null?X0:Z0,e=n(r,o),Qr){i=0;do{if(Qr=!1,go=0,25<=i)throw Error(R(301));i+=1,ge=he=null,t.updateQueue=null,fi.current=q0,e=n(r,o)}while(Qr)}if(fi.current=Bi,t=he!==null&&he.next!==null,jn=0,ge=he=ie=null,zi=!1,t)throw Error(R(300));return e}function Pu(){var e=go!==0;return go=0,e}function pt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ge===null?ie.memoizedState=ge=e:ge=ge.next=e,ge}function ot(){if(he===null){var e=ie.alternate;e=e!==null?e.memoizedState:null}else e=he.next;var t=ge===null?ie.memoizedState:ge.next;if(t!==null)ge=t,he=e;else{if(e===null)throw Error(R(310));he=e,e={memoizedState:he.memoizedState,baseState:he.baseState,baseQueue:he.baseQueue,queue:he.queue,next:null},ge===null?ie.memoizedState=ge=e:ge=ge.next=e}return ge}function yo(e,t){return typeof t=="function"?t(e):t}function na(e){var t=ot(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=he,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var a=s=null,l=null,u=i;do{var d=u.lane;if((jn&d)===d)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var c={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=c,s=r):l=l.next=c,ie.lanes|=d,Vn|=d}u=u.next}while(u!==null&&u!==i);l===null?s=r:l.next=a,dt(r,t.memoizedState)||(Oe=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,ie.lanes|=i,Vn|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ra(e){var t=ot(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var s=o=o.next;do i=e(i,s.action),s=s.next;while(s!==o);dt(i,t.memoizedState)||(Oe=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Rh(){}function Ah(e,t){var n=ie,r=ot(),o=t(),i=!dt(r.memoizedState,o);if(i&&(r.memoizedState=o,Oe=!0),r=r.queue,Eu(Nh.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||ge!==null&&ge.memoizedState.tag&1){if(n.flags|=2048,wo(9,_h.bind(null,n,r,o,t),void 0,null),ye===null)throw Error(R(349));jn&30||Mh(n,t,o)}return o}function Mh(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ie.updateQueue,t===null?(t={lastEffect:null,stores:null},ie.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function _h(e,t,n,r){t.value=n,t.getSnapshot=r,Lh(t)&&Dh(e)}function Nh(e,t,n){return n(function(){Lh(t)&&Dh(e)})}function Lh(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!dt(e,n)}catch{return!0}}function Dh(e){var t=Dt(e,1);t!==null&&ct(t,e,1,-1)}function ld(e){var t=pt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:yo,lastRenderedState:e},t.queue=e,e=e.dispatch=Y0.bind(null,ie,e),[t.memoizedState,e]}function wo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=ie.updateQueue,t===null?(t={lastEffect:null,stores:null},ie.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function jh(){return ot().memoizedState}function pi(e,t,n,r){var o=pt();ie.flags|=e,o.memoizedState=wo(1|t,n,void 0,r===void 0?null:r)}function fs(e,t,n,r){var o=ot();r=r===void 0?null:r;var i=void 0;if(he!==null){var s=he.memoizedState;if(i=s.destroy,r!==null&&Cu(r,s.deps)){o.memoizedState=wo(t,n,i,r);return}}ie.flags|=e,o.memoizedState=wo(1|t,n,i,r)}function ud(e,t){return pi(8390656,8,e,t)}function Eu(e,t){return fs(2048,8,e,t)}function Vh(e,t){return fs(4,2,e,t)}function Oh(e,t){return fs(4,4,e,t)}function Ih(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Fh(e,t,n){return n=n!=null?n.concat([e]):null,fs(4,4,Ih.bind(null,t,e),n)}function ku(){}function zh(e,t){var n=ot();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Cu(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Bh(e,t){var n=ot();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Cu(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function $h(e,t,n){return jn&21?(dt(n,t)||(n=Hp(),ie.lanes|=n,Vn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Oe=!0),e.memoizedState=n)}function G0(e,t){var n=G;G=n!==0&&4>n?n:4,e(!0);var r=ta.transition;ta.transition={};try{e(!1),t()}finally{G=n,ta.transition=r}}function Uh(){return ot().memoizedState}function Q0(e,t,n){var r=an(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Wh(e))Hh(t,n);else if(n=Sh(e,t,n,r),n!==null){var o=_e();ct(n,e,r,o),Kh(n,t,r)}}function Y0(e,t,n){var r=an(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Wh(e))Hh(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var s=t.lastRenderedState,a=i(s,n);if(o.hasEagerState=!0,o.eagerState=a,dt(a,s)){var l=t.interleaved;l===null?(o.next=o,gu(t)):(o.next=l.next,l.next=o),t.interleaved=o;return}}catch{}finally{}n=Sh(e,t,o,r),n!==null&&(o=_e(),ct(n,e,r,o),Kh(n,t,r))}}function Wh(e){var t=e.alternate;return e===ie||t!==null&&t===ie}function Hh(e,t){Qr=zi=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Kh(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ru(e,n)}}var Bi={readContext:rt,useCallback:Te,useContext:Te,useEffect:Te,useImperativeHandle:Te,useInsertionEffect:Te,useLayoutEffect:Te,useMemo:Te,useReducer:Te,useRef:Te,useState:Te,useDebugValue:Te,useDeferredValue:Te,useTransition:Te,useMutableSource:Te,useSyncExternalStore:Te,useId:Te,unstable_isNewReconciler:!1},X0={readContext:rt,useCallback:function(e,t){return pt().memoizedState=[e,t===void 0?null:t],e},useContext:rt,useEffect:ud,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,pi(4194308,4,Ih.bind(null,t,e),n)},useLayoutEffect:function(e,t){return pi(4194308,4,e,t)},useInsertionEffect:function(e,t){return pi(4,2,e,t)},useMemo:function(e,t){var n=pt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=pt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Q0.bind(null,ie,e),[r.memoizedState,e]},useRef:function(e){var t=pt();return e={current:e},t.memoizedState=e},useState:ld,useDebugValue:ku,useDeferredValue:function(e){return pt().memoizedState=e},useTransition:function(){var e=ld(!1),t=e[0];return e=G0.bind(null,e[1]),pt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ie,o=pt();if(ee){if(n===void 0)throw Error(R(407));n=n()}else{if(n=t(),ye===null)throw Error(R(349));jn&30||Mh(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,ud(Nh.bind(null,r,i,e),[e]),r.flags|=2048,wo(9,_h.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=pt(),t=ye.identifierPrefix;if(ee){var n=Pt,r=Tt;n=(r&~(1<<32-ut(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=go++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=K0++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Z0={readContext:rt,useCallback:zh,useContext:rt,useEffect:Eu,useImperativeHandle:Fh,useInsertionEffect:Vh,useLayoutEffect:Oh,useMemo:Bh,useReducer:na,useRef:jh,useState:function(){return na(yo)},useDebugValue:ku,useDeferredValue:function(e){var t=ot();return $h(t,he.memoizedState,e)},useTransition:function(){var e=na(yo)[0],t=ot().memoizedState;return[e,t]},useMutableSource:Rh,useSyncExternalStore:Ah,useId:Uh,unstable_isNewReconciler:!1},q0={readContext:rt,useCallback:zh,useContext:rt,useEffect:Eu,useImperativeHandle:Fh,useInsertionEffect:Vh,useLayoutEffect:Oh,useMemo:Bh,useReducer:ra,useRef:jh,useState:function(){return ra(yo)},useDebugValue:ku,useDeferredValue:function(e){var t=ot();return he===null?t.memoizedState=e:$h(t,he.memoizedState,e)},useTransition:function(){var e=ra(yo)[0],t=ot().memoizedState;return[e,t]},useMutableSource:Rh,useSyncExternalStore:Ah,useId:Uh,unstable_isNewReconciler:!1};function gr(e,t){try{var n="",r=t;do n+=ky(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function oa(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function ol(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var J0=typeof WeakMap=="function"?WeakMap:Map;function Gh(e,t,n){n=kt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ui||(Ui=!0,hl=r),ol(e,t)},n}function Qh(e,t,n){n=kt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){ol(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){ol(e,t),typeof r!="function"&&(sn===null?sn=new Set([this]):sn.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function cd(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new J0;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=p1.bind(null,e,t,n),t.then(e,e))}function dd(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function fd(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=kt(-1,1),t.tag=2,on(n,t,1))),n.lanes|=1),e)}var e1=It.ReactCurrentOwner,Oe=!1;function Me(e,t,n,r){t.child=e===null?kh(t,null,n,r):mr(t,e.child,n,r)}function pd(e,t,n,r,o){n=n.render;var i=t.ref;return ur(t,o),r=Tu(e,t,n,r,i,o),n=Pu(),e!==null&&!Oe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,jt(e,t,o)):(ee&&n&&du(t),t.flags|=1,Me(e,t,r,o),t.child)}function hd(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!Du(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Yh(e,t,i,r,o)):(e=gi(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var s=i.memoizedProps;if(n=n.compare,n=n!==null?n:co,n(s,r)&&e.ref===t.ref)return jt(e,t,o)}return t.flags|=1,e=ln(i,r),e.ref=t.ref,e.return=t,t.child=e}function Yh(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(co(i,r)&&e.ref===t.ref)if(Oe=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(Oe=!0);else return t.lanes=e.lanes,jt(e,t,o)}return il(e,t,n,r,o)}function Xh(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Y(Jn,Ue),Ue|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Y(Jn,Ue),Ue|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,Y(Jn,Ue),Ue|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,Y(Jn,Ue),Ue|=r;return Me(e,t,o,n),t.child}function Zh(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function il(e,t,n,r,o){var i=Fe(n)?Ln:Ae.current;return i=pr(t,i),ur(t,o),n=Tu(e,t,n,r,i,o),r=Pu(),e!==null&&!Oe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,jt(e,t,o)):(ee&&r&&du(t),t.flags|=1,Me(e,t,n,o),t.child)}function md(e,t,n,r,o){if(Fe(n)){var i=!0;Li(t)}else i=!1;if(ur(t,o),t.stateNode===null)hi(e,t),Ph(t,n,r),rl(t,n,r,o),r=!0;else if(e===null){var s=t.stateNode,a=t.memoizedProps;s.props=a;var l=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=rt(u):(u=Fe(n)?Ln:Ae.current,u=pr(t,u));var d=n.getDerivedStateFromProps,c=typeof d=="function"||typeof s.getSnapshotBeforeUpdate=="function";c||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==r||l!==u)&&sd(t,s,r,u),Qt=!1;var f=t.memoizedState;s.state=f,Ii(t,r,s,o),l=t.memoizedState,a!==r||f!==l||Ie.current||Qt?(typeof d=="function"&&(nl(t,n,d,r),l=t.memoizedState),(a=Qt||id(t,n,a,r,f,l,u))?(c||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),s.props=r,s.state=l,s.context=u,r=a):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,Ch(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:st(t.type,a),s.props=u,c=t.pendingProps,f=s.context,l=n.contextType,typeof l=="object"&&l!==null?l=rt(l):(l=Fe(n)?Ln:Ae.current,l=pr(t,l));var v=n.getDerivedStateFromProps;(d=typeof v=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==c||f!==l)&&sd(t,s,r,l),Qt=!1,f=t.memoizedState,s.state=f,Ii(t,r,s,o);var y=t.memoizedState;a!==c||f!==y||Ie.current||Qt?(typeof v=="function"&&(nl(t,n,v,r),y=t.memoizedState),(u=Qt||id(t,n,u,r,f,y,l)||!1)?(d||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,y,l),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,y,l)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=y),s.props=r,s.state=y,s.context=l,r=u):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return sl(e,t,n,r,i,o)}function sl(e,t,n,r,o,i){Zh(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return o&&ed(t,n,!1),jt(e,t,i);r=t.stateNode,e1.current=t;var a=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=mr(t,e.child,null,i),t.child=mr(t,null,a,i)):Me(e,t,a,i),t.memoizedState=r.state,o&&ed(t,n,!0),t.child}function qh(e){var t=e.stateNode;t.pendingContext?Jc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Jc(e,t.context,!1),wu(e,t.containerInfo)}function vd(e,t,n,r,o){return hr(),pu(o),t.flags|=256,Me(e,t,n,r),t.child}var al={dehydrated:null,treeContext:null,retryLane:0};function ll(e){return{baseLanes:e,cachePool:null,transitions:null}}function Jh(e,t,n){var r=t.pendingProps,o=re.current,i=!1,s=(t.flags&128)!==0,a;if((a=s)||(a=e!==null&&e.memoizedState===null?!1:(o&2)!==0),a?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),Y(re,o&1),e===null)return el(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,i?(r=t.mode,i=t.child,s={mode:"hidden",children:s},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=s):i=ms(s,r,0,null),e=_n(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=ll(n),t.memoizedState=al,e):bu(t,s));if(o=e.memoizedState,o!==null&&(a=o.dehydrated,a!==null))return t1(e,t,s,r,a,o,n);if(i){i=r.fallback,s=t.mode,o=e.child,a=o.sibling;var l={mode:"hidden",children:r.children};return!(s&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=ln(o,l),r.subtreeFlags=o.subtreeFlags&14680064),a!==null?i=ln(a,i):(i=_n(i,s,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,s=e.child.memoizedState,s=s===null?ll(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=al,r}return i=e.child,e=i.sibling,r=ln(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function bu(e,t){return t=ms({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Yo(e,t,n,r){return r!==null&&pu(r),mr(t,e.child,null,n),e=bu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function t1(e,t,n,r,o,i,s){if(n)return t.flags&256?(t.flags&=-257,r=oa(Error(R(422))),Yo(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=ms({mode:"visible",children:r.children},o,0,null),i=_n(i,o,s,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&mr(t,e.child,null,s),t.child.memoizedState=ll(s),t.memoizedState=al,i);if(!(t.mode&1))return Yo(e,t,s,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var a=r.dgst;return r=a,i=Error(R(419)),r=oa(i,r,void 0),Yo(e,t,s,r)}if(a=(s&e.childLanes)!==0,Oe||a){if(r=ye,r!==null){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|s)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,Dt(e,o),ct(r,e,o,-1))}return Lu(),r=oa(Error(R(421))),Yo(e,t,s,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=h1.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,He=rn(o.nextSibling),Ke=t,ee=!0,lt=null,e!==null&&(Je[et++]=Tt,Je[et++]=Pt,Je[et++]=Dn,Tt=e.id,Pt=e.overflow,Dn=t),t=bu(t,r.children),t.flags|=4096,t)}function gd(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),tl(e.return,t,n)}function ia(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function em(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(Me(e,t,r.children,n),r=re.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&gd(e,n,t);else if(e.tag===19)gd(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Y(re,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Fi(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),ia(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Fi(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}ia(t,!0,n,null,i);break;case"together":ia(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function hi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function jt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Vn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(R(153));if(t.child!==null){for(e=t.child,n=ln(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=ln(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function n1(e,t,n){switch(t.tag){case 3:qh(t),hr();break;case 5:bh(t);break;case 1:Fe(t.type)&&Li(t);break;case 4:wu(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;Y(Vi,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(Y(re,re.current&1),t.flags|=128,null):n&t.child.childLanes?Jh(e,t,n):(Y(re,re.current&1),e=jt(e,t,n),e!==null?e.sibling:null);Y(re,re.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return em(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),Y(re,re.current),r)break;return null;case 22:case 23:return t.lanes=0,Xh(e,t,n)}return jt(e,t,n)}var tm,ul,nm,rm;tm=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ul=function(){};nm=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,bn(gt.current);var i=null;switch(n){case"input":o=_a(e,o),r=_a(e,r),i=[];break;case"select":o=se({},o,{value:void 0}),r=se({},r,{value:void 0}),i=[];break;case"textarea":o=Da(e,o),r=Da(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=_i)}Va(n,r);var s;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var a=o[u];for(s in a)a.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(ro.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var l=r[u];if(a=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(s in a)!a.hasOwnProperty(s)||l&&l.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in l)l.hasOwnProperty(s)&&a[s]!==l[s]&&(n||(n={}),n[s]=l[s])}else n||(i||(i=[]),i.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(i=i||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(i=i||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(ro.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&q("scroll",e),i||a===l||(i=[])):(i=i||[]).push(u,l))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};rm=function(e,t,n,r){n!==r&&(t.flags|=4)};function Nr(e,t){if(!ee)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Pe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function r1(e,t,n){var r=t.pendingProps;switch(fu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Pe(t),null;case 1:return Fe(t.type)&&Ni(),Pe(t),null;case 3:return r=t.stateNode,vr(),J(Ie),J(Ae),Su(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Go(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,lt!==null&&(gl(lt),lt=null))),ul(e,t),Pe(t),null;case 5:xu(t);var o=bn(vo.current);if(n=t.type,e!==null&&t.stateNode!=null)nm(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(R(166));return Pe(t),null}if(e=bn(gt.current),Go(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[mt]=t,r[ho]=i,e=(t.mode&1)!==0,n){case"dialog":q("cancel",r),q("close",r);break;case"iframe":case"object":case"embed":q("load",r);break;case"video":case"audio":for(o=0;o<zr.length;o++)q(zr[o],r);break;case"source":q("error",r);break;case"img":case"image":case"link":q("error",r),q("load",r);break;case"details":q("toggle",r);break;case"input":kc(r,i),q("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},q("invalid",r);break;case"textarea":Rc(r,i),q("invalid",r)}Va(n,i),o=null;for(var s in i)if(i.hasOwnProperty(s)){var a=i[s];s==="children"?typeof a=="string"?r.textContent!==a&&(i.suppressHydrationWarning!==!0&&Ko(r.textContent,a,e),o=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&Ko(r.textContent,a,e),o=["children",""+a]):ro.hasOwnProperty(s)&&a!=null&&s==="onScroll"&&q("scroll",r)}switch(n){case"input":Io(r),bc(r,i,!0);break;case"textarea":Io(r),Ac(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=_i)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Mp(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[mt]=t,e[ho]=r,tm(e,t,!1,!1),t.stateNode=e;e:{switch(s=Oa(n,r),n){case"dialog":q("cancel",e),q("close",e),o=r;break;case"iframe":case"object":case"embed":q("load",e),o=r;break;case"video":case"audio":for(o=0;o<zr.length;o++)q(zr[o],e);o=r;break;case"source":q("error",e),o=r;break;case"img":case"image":case"link":q("error",e),q("load",e),o=r;break;case"details":q("toggle",e),o=r;break;case"input":kc(e,r),o=_a(e,r),q("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=se({},r,{value:void 0}),q("invalid",e);break;case"textarea":Rc(e,r),o=Da(e,r),q("invalid",e);break;default:o=r}Va(n,o),a=o;for(i in a)if(a.hasOwnProperty(i)){var l=a[i];i==="style"?Lp(e,l):i==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&_p(e,l)):i==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&oo(e,l):typeof l=="number"&&oo(e,""+l):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(ro.hasOwnProperty(i)?l!=null&&i==="onScroll"&&q("scroll",e):l!=null&&Zl(e,i,l,s))}switch(n){case"input":Io(e),bc(e,r,!1);break;case"textarea":Io(e),Ac(e);break;case"option":r.value!=null&&e.setAttribute("value",""+un(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?ir(e,!!r.multiple,i,!1):r.defaultValue!=null&&ir(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=_i)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Pe(t),null;case 6:if(e&&t.stateNode!=null)rm(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(R(166));if(n=bn(vo.current),bn(gt.current),Go(t)){if(r=t.stateNode,n=t.memoizedProps,r[mt]=t,(i=r.nodeValue!==n)&&(e=Ke,e!==null))switch(e.tag){case 3:Ko(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ko(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[mt]=t,t.stateNode=r}return Pe(t),null;case 13:if(J(re),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ee&&He!==null&&t.mode&1&&!(t.flags&128))xh(),hr(),t.flags|=98560,i=!1;else if(i=Go(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(R(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(R(317));i[mt]=t}else hr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Pe(t),i=!1}else lt!==null&&(gl(lt),lt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||re.current&1?me===0&&(me=3):Lu())),t.updateQueue!==null&&(t.flags|=4),Pe(t),null);case 4:return vr(),ul(e,t),e===null&&fo(t.stateNode.containerInfo),Pe(t),null;case 10:return vu(t.type._context),Pe(t),null;case 17:return Fe(t.type)&&Ni(),Pe(t),null;case 19:if(J(re),i=t.memoizedState,i===null)return Pe(t),null;if(r=(t.flags&128)!==0,s=i.rendering,s===null)if(r)Nr(i,!1);else{if(me!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=Fi(e),s!==null){for(t.flags|=128,Nr(i,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,s=i.alternate,s===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Y(re,re.current&1|2),t.child}e=e.sibling}i.tail!==null&&ce()>yr&&(t.flags|=128,r=!0,Nr(i,!1),t.lanes=4194304)}else{if(!r)if(e=Fi(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Nr(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!ee)return Pe(t),null}else 2*ce()-i.renderingStartTime>yr&&n!==1073741824&&(t.flags|=128,r=!0,Nr(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(n=i.last,n!==null?n.sibling=s:t.child=s,i.last=s)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=ce(),t.sibling=null,n=re.current,Y(re,r?n&1|2:n&1),t):(Pe(t),null);case 22:case 23:return Nu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ue&1073741824&&(Pe(t),t.subtreeFlags&6&&(t.flags|=8192)):Pe(t),null;case 24:return null;case 25:return null}throw Error(R(156,t.tag))}function o1(e,t){switch(fu(t),t.tag){case 1:return Fe(t.type)&&Ni(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return vr(),J(Ie),J(Ae),Su(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return xu(t),null;case 13:if(J(re),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(R(340));hr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return J(re),null;case 4:return vr(),null;case 10:return vu(t.type._context),null;case 22:case 23:return Nu(),null;case 24:return null;default:return null}}var Xo=!1,ke=!1,i1=typeof WeakSet=="function"?WeakSet:Set,L=null;function qn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){le(e,t,r)}else n.current=null}function cl(e,t,n){try{n()}catch(r){le(e,t,r)}}var yd=!1;function s1(e,t){if(Ga=Ri,e=ah(),cu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var s=0,a=-1,l=-1,u=0,d=0,c=e,f=null;t:for(;;){for(var v;c!==n||o!==0&&c.nodeType!==3||(a=s+o),c!==i||r!==0&&c.nodeType!==3||(l=s+r),c.nodeType===3&&(s+=c.nodeValue.length),(v=c.firstChild)!==null;)f=c,c=v;for(;;){if(c===e)break t;if(f===n&&++u===o&&(a=s),f===i&&++d===r&&(l=s),(v=c.nextSibling)!==null)break;c=f,f=c.parentNode}c=v}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(Qa={focusedElem:e,selectionRange:n},Ri=!1,L=t;L!==null;)if(t=L,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,L=e;else for(;L!==null;){t=L;try{var y=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var w=y.memoizedProps,x=y.memoizedState,h=t.stateNode,p=h.getSnapshotBeforeUpdate(t.elementType===t.type?w:st(t.type,w),x);h.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var m=t.stateNode.containerInfo;m.nodeType===1?m.textContent="":m.nodeType===9&&m.documentElement&&m.removeChild(m.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(R(163))}}catch(S){le(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,L=e;break}L=t.return}return y=yd,yd=!1,y}function Yr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&cl(t,n,i)}o=o.next}while(o!==r)}}function ps(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function dl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function om(e){var t=e.alternate;t!==null&&(e.alternate=null,om(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[mt],delete t[ho],delete t[Za],delete t[$0],delete t[U0])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function im(e){return e.tag===5||e.tag===3||e.tag===4}function wd(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||im(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function fl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=_i));else if(r!==4&&(e=e.child,e!==null))for(fl(e,t,n),e=e.sibling;e!==null;)fl(e,t,n),e=e.sibling}function pl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(pl(e,t,n),e=e.sibling;e!==null;)pl(e,t,n),e=e.sibling}var we=null,at=!1;function Ut(e,t,n){for(n=n.child;n!==null;)sm(e,t,n),n=n.sibling}function sm(e,t,n){if(vt&&typeof vt.onCommitFiberUnmount=="function")try{vt.onCommitFiberUnmount(is,n)}catch{}switch(n.tag){case 5:ke||qn(n,t);case 6:var r=we,o=at;we=null,Ut(e,t,n),we=r,at=o,we!==null&&(at?(e=we,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):we.removeChild(n.stateNode));break;case 18:we!==null&&(at?(e=we,n=n.stateNode,e.nodeType===8?qs(e.parentNode,n):e.nodeType===1&&qs(e,n),lo(e)):qs(we,n.stateNode));break;case 4:r=we,o=at,we=n.stateNode.containerInfo,at=!0,Ut(e,t,n),we=r,at=o;break;case 0:case 11:case 14:case 15:if(!ke&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,s=i.destroy;i=i.tag,s!==void 0&&(i&2||i&4)&&cl(n,t,s),o=o.next}while(o!==r)}Ut(e,t,n);break;case 1:if(!ke&&(qn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){le(n,t,a)}Ut(e,t,n);break;case 21:Ut(e,t,n);break;case 22:n.mode&1?(ke=(r=ke)||n.memoizedState!==null,Ut(e,t,n),ke=r):Ut(e,t,n);break;default:Ut(e,t,n)}}function xd(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new i1),t.forEach(function(r){var o=m1.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function it(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,s=t,a=s;e:for(;a!==null;){switch(a.tag){case 5:we=a.stateNode,at=!1;break e;case 3:we=a.stateNode.containerInfo,at=!0;break e;case 4:we=a.stateNode.containerInfo,at=!0;break e}a=a.return}if(we===null)throw Error(R(160));sm(i,s,o),we=null,at=!1;var l=o.alternate;l!==null&&(l.return=null),o.return=null}catch(u){le(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)am(t,e),t=t.sibling}function am(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(it(t,e),ft(e),r&4){try{Yr(3,e,e.return),ps(3,e)}catch(w){le(e,e.return,w)}try{Yr(5,e,e.return)}catch(w){le(e,e.return,w)}}break;case 1:it(t,e),ft(e),r&512&&n!==null&&qn(n,n.return);break;case 5:if(it(t,e),ft(e),r&512&&n!==null&&qn(n,n.return),e.flags&32){var o=e.stateNode;try{oo(o,"")}catch(w){le(e,e.return,w)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,s=n!==null?n.memoizedProps:i,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&Rp(o,i),Oa(a,s);var u=Oa(a,i);for(s=0;s<l.length;s+=2){var d=l[s],c=l[s+1];d==="style"?Lp(o,c):d==="dangerouslySetInnerHTML"?_p(o,c):d==="children"?oo(o,c):Zl(o,d,c,u)}switch(a){case"input":Na(o,i);break;case"textarea":Ap(o,i);break;case"select":var f=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var v=i.value;v!=null?ir(o,!!i.multiple,v,!1):f!==!!i.multiple&&(i.defaultValue!=null?ir(o,!!i.multiple,i.defaultValue,!0):ir(o,!!i.multiple,i.multiple?[]:"",!1))}o[ho]=i}catch(w){le(e,e.return,w)}}break;case 6:if(it(t,e),ft(e),r&4){if(e.stateNode===null)throw Error(R(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(w){le(e,e.return,w)}}break;case 3:if(it(t,e),ft(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{lo(t.containerInfo)}catch(w){le(e,e.return,w)}break;case 4:it(t,e),ft(e);break;case 13:it(t,e),ft(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(Mu=ce())),r&4&&xd(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(ke=(u=ke)||d,it(t,e),ke=u):it(t,e),ft(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!d&&e.mode&1)for(L=e,d=e.child;d!==null;){for(c=L=d;L!==null;){switch(f=L,v=f.child,f.tag){case 0:case 11:case 14:case 15:Yr(4,f,f.return);break;case 1:qn(f,f.return);var y=f.stateNode;if(typeof y.componentWillUnmount=="function"){r=f,n=f.return;try{t=r,y.props=t.memoizedProps,y.state=t.memoizedState,y.componentWillUnmount()}catch(w){le(r,n,w)}}break;case 5:qn(f,f.return);break;case 22:if(f.memoizedState!==null){Cd(c);continue}}v!==null?(v.return=f,L=v):Cd(c)}d=d.sibling}e:for(d=null,c=e;;){if(c.tag===5){if(d===null){d=c;try{o=c.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=c.stateNode,l=c.memoizedProps.style,s=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=Np("display",s))}catch(w){le(e,e.return,w)}}}else if(c.tag===6){if(d===null)try{c.stateNode.nodeValue=u?"":c.memoizedProps}catch(w){le(e,e.return,w)}}else if((c.tag!==22&&c.tag!==23||c.memoizedState===null||c===e)&&c.child!==null){c.child.return=c,c=c.child;continue}if(c===e)break e;for(;c.sibling===null;){if(c.return===null||c.return===e)break e;d===c&&(d=null),c=c.return}d===c&&(d=null),c.sibling.return=c.return,c=c.sibling}}break;case 19:it(t,e),ft(e),r&4&&xd(e);break;case 21:break;default:it(t,e),ft(e)}}function ft(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(im(n)){var r=n;break e}n=n.return}throw Error(R(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(oo(o,""),r.flags&=-33);var i=wd(e);pl(e,i,o);break;case 3:case 4:var s=r.stateNode.containerInfo,a=wd(e);fl(e,a,s);break;default:throw Error(R(161))}}catch(l){le(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function a1(e,t,n){L=e,lm(e)}function lm(e,t,n){for(var r=(e.mode&1)!==0;L!==null;){var o=L,i=o.child;if(o.tag===22&&r){var s=o.memoizedState!==null||Xo;if(!s){var a=o.alternate,l=a!==null&&a.memoizedState!==null||ke;a=Xo;var u=ke;if(Xo=s,(ke=l)&&!u)for(L=o;L!==null;)s=L,l=s.child,s.tag===22&&s.memoizedState!==null?Td(o):l!==null?(l.return=s,L=l):Td(o);for(;i!==null;)L=i,lm(i),i=i.sibling;L=o,Xo=a,ke=u}Sd(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,L=i):Sd(e)}}function Sd(e){for(;L!==null;){var t=L;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ke||ps(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!ke)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:st(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&od(t,i,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}od(t,s,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var c=d.dehydrated;c!==null&&lo(c)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(R(163))}ke||t.flags&512&&dl(t)}catch(f){le(t,t.return,f)}}if(t===e){L=null;break}if(n=t.sibling,n!==null){n.return=t.return,L=n;break}L=t.return}}function Cd(e){for(;L!==null;){var t=L;if(t===e){L=null;break}var n=t.sibling;if(n!==null){n.return=t.return,L=n;break}L=t.return}}function Td(e){for(;L!==null;){var t=L;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ps(4,t)}catch(l){le(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(l){le(t,o,l)}}var i=t.return;try{dl(t)}catch(l){le(t,i,l)}break;case 5:var s=t.return;try{dl(t)}catch(l){le(t,s,l)}}}catch(l){le(t,t.return,l)}if(t===e){L=null;break}var a=t.sibling;if(a!==null){a.return=t.return,L=a;break}L=t.return}}var l1=Math.ceil,$i=It.ReactCurrentDispatcher,Ru=It.ReactCurrentOwner,nt=It.ReactCurrentBatchConfig,H=0,ye=null,fe=null,Se=0,Ue=0,Jn=hn(0),me=0,xo=null,Vn=0,hs=0,Au=0,Xr=null,Ve=null,Mu=0,yr=1/0,St=null,Ui=!1,hl=null,sn=null,Zo=!1,qt=null,Wi=0,Zr=0,ml=null,mi=-1,vi=0;function _e(){return H&6?ce():mi!==-1?mi:mi=ce()}function an(e){return e.mode&1?H&2&&Se!==0?Se&-Se:H0.transition!==null?(vi===0&&(vi=Hp()),vi):(e=G,e!==0||(e=window.event,e=e===void 0?16:qp(e.type)),e):1}function ct(e,t,n,r){if(50<Zr)throw Zr=0,ml=null,Error(R(185));bo(e,n,r),(!(H&2)||e!==ye)&&(e===ye&&(!(H&2)&&(hs|=n),me===4&&Xt(e,Se)),ze(e,r),n===1&&H===0&&!(t.mode&1)&&(yr=ce()+500,cs&&mn()))}function ze(e,t){var n=e.callbackNode;Hy(e,t);var r=bi(e,e===ye?Se:0);if(r===0)n!==null&&Nc(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Nc(n),t===1)e.tag===0?W0(Pd.bind(null,e)):gh(Pd.bind(null,e)),z0(function(){!(H&6)&&mn()}),n=null;else{switch(Kp(r)){case 1:n=nu;break;case 4:n=Up;break;case 16:n=ki;break;case 536870912:n=Wp;break;default:n=ki}n=vm(n,um.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function um(e,t){if(mi=-1,vi=0,H&6)throw Error(R(327));var n=e.callbackNode;if(cr()&&e.callbackNode!==n)return null;var r=bi(e,e===ye?Se:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Hi(e,r);else{t=r;var o=H;H|=2;var i=dm();(ye!==e||Se!==t)&&(St=null,yr=ce()+500,Mn(e,t));do try{d1();break}catch(a){cm(e,a)}while(!0);mu(),$i.current=i,H=o,fe!==null?t=0:(ye=null,Se=0,t=me)}if(t!==0){if(t===2&&(o=$a(e),o!==0&&(r=o,t=vl(e,o))),t===1)throw n=xo,Mn(e,0),Xt(e,r),ze(e,ce()),n;if(t===6)Xt(e,r);else{if(o=e.current.alternate,!(r&30)&&!u1(o)&&(t=Hi(e,r),t===2&&(i=$a(e),i!==0&&(r=i,t=vl(e,i))),t===1))throw n=xo,Mn(e,0),Xt(e,r),ze(e,ce()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(R(345));case 2:Sn(e,Ve,St);break;case 3:if(Xt(e,r),(r&130023424)===r&&(t=Mu+500-ce(),10<t)){if(bi(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){_e(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Xa(Sn.bind(null,e,Ve,St),t);break}Sn(e,Ve,St);break;case 4:if(Xt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var s=31-ut(r);i=1<<s,s=t[s],s>o&&(o=s),r&=~i}if(r=o,r=ce()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*l1(r/1960))-r,10<r){e.timeoutHandle=Xa(Sn.bind(null,e,Ve,St),r);break}Sn(e,Ve,St);break;case 5:Sn(e,Ve,St);break;default:throw Error(R(329))}}}return ze(e,ce()),e.callbackNode===n?um.bind(null,e):null}function vl(e,t){var n=Xr;return e.current.memoizedState.isDehydrated&&(Mn(e,t).flags|=256),e=Hi(e,t),e!==2&&(t=Ve,Ve=n,t!==null&&gl(t)),e}function gl(e){Ve===null?Ve=e:Ve.push.apply(Ve,e)}function u1(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!dt(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Xt(e,t){for(t&=~Au,t&=~hs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ut(t),r=1<<n;e[n]=-1,t&=~r}}function Pd(e){if(H&6)throw Error(R(327));cr();var t=bi(e,0);if(!(t&1))return ze(e,ce()),null;var n=Hi(e,t);if(e.tag!==0&&n===2){var r=$a(e);r!==0&&(t=r,n=vl(e,r))}if(n===1)throw n=xo,Mn(e,0),Xt(e,t),ze(e,ce()),n;if(n===6)throw Error(R(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Sn(e,Ve,St),ze(e,ce()),null}function _u(e,t){var n=H;H|=1;try{return e(t)}finally{H=n,H===0&&(yr=ce()+500,cs&&mn())}}function On(e){qt!==null&&qt.tag===0&&!(H&6)&&cr();var t=H;H|=1;var n=nt.transition,r=G;try{if(nt.transition=null,G=1,e)return e()}finally{G=r,nt.transition=n,H=t,!(H&6)&&mn()}}function Nu(){Ue=Jn.current,J(Jn)}function Mn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,F0(n)),fe!==null)for(n=fe.return;n!==null;){var r=n;switch(fu(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Ni();break;case 3:vr(),J(Ie),J(Ae),Su();break;case 5:xu(r);break;case 4:vr();break;case 13:J(re);break;case 19:J(re);break;case 10:vu(r.type._context);break;case 22:case 23:Nu()}n=n.return}if(ye=e,fe=e=ln(e.current,null),Se=Ue=t,me=0,xo=null,Au=hs=Vn=0,Ve=Xr=null,kn!==null){for(t=0;t<kn.length;t++)if(n=kn[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var s=i.next;i.next=o,r.next=s}n.pending=r}kn=null}return e}function cm(e,t){do{var n=fe;try{if(mu(),fi.current=Bi,zi){for(var r=ie.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}zi=!1}if(jn=0,ge=he=ie=null,Qr=!1,go=0,Ru.current=null,n===null||n.return===null){me=1,xo=t,fe=null;break}e:{var i=e,s=n.return,a=n,l=t;if(t=Se,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,d=a,c=d.tag;if(!(d.mode&1)&&(c===0||c===11||c===15)){var f=d.alternate;f?(d.updateQueue=f.updateQueue,d.memoizedState=f.memoizedState,d.lanes=f.lanes):(d.updateQueue=null,d.memoizedState=null)}var v=dd(s);if(v!==null){v.flags&=-257,fd(v,s,a,i,t),v.mode&1&&cd(i,u,t),t=v,l=u;var y=t.updateQueue;if(y===null){var w=new Set;w.add(l),t.updateQueue=w}else y.add(l);break e}else{if(!(t&1)){cd(i,u,t),Lu();break e}l=Error(R(426))}}else if(ee&&a.mode&1){var x=dd(s);if(x!==null){!(x.flags&65536)&&(x.flags|=256),fd(x,s,a,i,t),pu(gr(l,a));break e}}i=l=gr(l,a),me!==4&&(me=2),Xr===null?Xr=[i]:Xr.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var h=Gh(i,l,t);rd(i,h);break e;case 1:a=l;var p=i.type,m=i.stateNode;if(!(i.flags&128)&&(typeof p.getDerivedStateFromError=="function"||m!==null&&typeof m.componentDidCatch=="function"&&(sn===null||!sn.has(m)))){i.flags|=65536,t&=-t,i.lanes|=t;var S=Qh(i,a,t);rd(i,S);break e}}i=i.return}while(i!==null)}pm(n)}catch(T){t=T,fe===n&&n!==null&&(fe=n=n.return);continue}break}while(!0)}function dm(){var e=$i.current;return $i.current=Bi,e===null?Bi:e}function Lu(){(me===0||me===3||me===2)&&(me=4),ye===null||!(Vn&268435455)&&!(hs&268435455)||Xt(ye,Se)}function Hi(e,t){var n=H;H|=2;var r=dm();(ye!==e||Se!==t)&&(St=null,Mn(e,t));do try{c1();break}catch(o){cm(e,o)}while(!0);if(mu(),H=n,$i.current=r,fe!==null)throw Error(R(261));return ye=null,Se=0,me}function c1(){for(;fe!==null;)fm(fe)}function d1(){for(;fe!==null&&!Vy();)fm(fe)}function fm(e){var t=mm(e.alternate,e,Ue);e.memoizedProps=e.pendingProps,t===null?pm(e):fe=t,Ru.current=null}function pm(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=o1(n,t),n!==null){n.flags&=32767,fe=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{me=6,fe=null;return}}else if(n=r1(n,t,Ue),n!==null){fe=n;return}if(t=t.sibling,t!==null){fe=t;return}fe=t=e}while(t!==null);me===0&&(me=5)}function Sn(e,t,n){var r=G,o=nt.transition;try{nt.transition=null,G=1,f1(e,t,n,r)}finally{nt.transition=o,G=r}return null}function f1(e,t,n,r){do cr();while(qt!==null);if(H&6)throw Error(R(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(R(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(Ky(e,i),e===ye&&(fe=ye=null,Se=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Zo||(Zo=!0,vm(ki,function(){return cr(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=nt.transition,nt.transition=null;var s=G;G=1;var a=H;H|=4,Ru.current=null,s1(e,n),am(n,e),N0(Qa),Ri=!!Ga,Qa=Ga=null,e.current=n,a1(n),Oy(),H=a,G=s,nt.transition=i}else e.current=n;if(Zo&&(Zo=!1,qt=e,Wi=o),i=e.pendingLanes,i===0&&(sn=null),zy(n.stateNode),ze(e,ce()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Ui)throw Ui=!1,e=hl,hl=null,e;return Wi&1&&e.tag!==0&&cr(),i=e.pendingLanes,i&1?e===ml?Zr++:(Zr=0,ml=e):Zr=0,mn(),null}function cr(){if(qt!==null){var e=Kp(Wi),t=nt.transition,n=G;try{if(nt.transition=null,G=16>e?16:e,qt===null)var r=!1;else{if(e=qt,qt=null,Wi=0,H&6)throw Error(R(331));var o=H;for(H|=4,L=e.current;L!==null;){var i=L,s=i.child;if(L.flags&16){var a=i.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(L=u;L!==null;){var d=L;switch(d.tag){case 0:case 11:case 15:Yr(8,d,i)}var c=d.child;if(c!==null)c.return=d,L=c;else for(;L!==null;){d=L;var f=d.sibling,v=d.return;if(om(d),d===u){L=null;break}if(f!==null){f.return=v,L=f;break}L=v}}}var y=i.alternate;if(y!==null){var w=y.child;if(w!==null){y.child=null;do{var x=w.sibling;w.sibling=null,w=x}while(w!==null)}}L=i}}if(i.subtreeFlags&2064&&s!==null)s.return=i,L=s;else e:for(;L!==null;){if(i=L,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Yr(9,i,i.return)}var h=i.sibling;if(h!==null){h.return=i.return,L=h;break e}L=i.return}}var p=e.current;for(L=p;L!==null;){s=L;var m=s.child;if(s.subtreeFlags&2064&&m!==null)m.return=s,L=m;else e:for(s=p;L!==null;){if(a=L,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:ps(9,a)}}catch(T){le(a,a.return,T)}if(a===s){L=null;break e}var S=a.sibling;if(S!==null){S.return=a.return,L=S;break e}L=a.return}}if(H=o,mn(),vt&&typeof vt.onPostCommitFiberRoot=="function")try{vt.onPostCommitFiberRoot(is,e)}catch{}r=!0}return r}finally{G=n,nt.transition=t}}return!1}function Ed(e,t,n){t=gr(n,t),t=Gh(e,t,1),e=on(e,t,1),t=_e(),e!==null&&(bo(e,1,t),ze(e,t))}function le(e,t,n){if(e.tag===3)Ed(e,e,n);else for(;t!==null;){if(t.tag===3){Ed(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(sn===null||!sn.has(r))){e=gr(n,e),e=Qh(t,e,1),t=on(t,e,1),e=_e(),t!==null&&(bo(t,1,e),ze(t,e));break}}t=t.return}}function p1(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=_e(),e.pingedLanes|=e.suspendedLanes&n,ye===e&&(Se&n)===n&&(me===4||me===3&&(Se&130023424)===Se&&500>ce()-Mu?Mn(e,0):Au|=n),ze(e,t)}function hm(e,t){t===0&&(e.mode&1?(t=Bo,Bo<<=1,!(Bo&130023424)&&(Bo=4194304)):t=1);var n=_e();e=Dt(e,t),e!==null&&(bo(e,t,n),ze(e,n))}function h1(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),hm(e,n)}function m1(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(R(314))}r!==null&&r.delete(t),hm(e,n)}var mm;mm=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ie.current)Oe=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Oe=!1,n1(e,t,n);Oe=!!(e.flags&131072)}else Oe=!1,ee&&t.flags&1048576&&yh(t,ji,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;hi(e,t),e=t.pendingProps;var o=pr(t,Ae.current);ur(t,n),o=Tu(null,t,r,e,o,n);var i=Pu();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Fe(r)?(i=!0,Li(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,yu(t),o.updater=ds,t.stateNode=o,o._reactInternals=t,rl(t,r,e,n),t=sl(null,t,r,!0,i,n)):(t.tag=0,ee&&i&&du(t),Me(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(hi(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=g1(r),e=st(r,e),o){case 0:t=il(null,t,r,e,n);break e;case 1:t=md(null,t,r,e,n);break e;case 11:t=pd(null,t,r,e,n);break e;case 14:t=hd(null,t,r,st(r.type,e),n);break e}throw Error(R(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:st(r,o),il(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:st(r,o),md(e,t,r,o,n);case 3:e:{if(qh(t),e===null)throw Error(R(387));r=t.pendingProps,i=t.memoizedState,o=i.element,Ch(e,t),Ii(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=gr(Error(R(423)),t),t=vd(e,t,r,n,o);break e}else if(r!==o){o=gr(Error(R(424)),t),t=vd(e,t,r,n,o);break e}else for(He=rn(t.stateNode.containerInfo.firstChild),Ke=t,ee=!0,lt=null,n=kh(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(hr(),r===o){t=jt(e,t,n);break e}Me(e,t,r,n)}t=t.child}return t;case 5:return bh(t),e===null&&el(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,s=o.children,Ya(r,o)?s=null:i!==null&&Ya(r,i)&&(t.flags|=32),Zh(e,t),Me(e,t,s,n),t.child;case 6:return e===null&&el(t),null;case 13:return Jh(e,t,n);case 4:return wu(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=mr(t,null,r,n):Me(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:st(r,o),pd(e,t,r,o,n);case 7:return Me(e,t,t.pendingProps,n),t.child;case 8:return Me(e,t,t.pendingProps.children,n),t.child;case 12:return Me(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,s=o.value,Y(Vi,r._currentValue),r._currentValue=s,i!==null)if(dt(i.value,s)){if(i.children===o.children&&!Ie.current){t=jt(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var a=i.dependencies;if(a!==null){s=i.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(i.tag===1){l=kt(-1,n&-n),l.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?l.next=l:(l.next=d.next,d.next=l),u.pending=l}}i.lanes|=n,l=i.alternate,l!==null&&(l.lanes|=n),tl(i.return,n,t),a.lanes|=n;break}l=l.next}}else if(i.tag===10)s=i.type===t.type?null:i.child;else if(i.tag===18){if(s=i.return,s===null)throw Error(R(341));s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),tl(s,n,t),s=i.sibling}else s=i.child;if(s!==null)s.return=i;else for(s=i;s!==null;){if(s===t){s=null;break}if(i=s.sibling,i!==null){i.return=s.return,s=i;break}s=s.return}i=s}Me(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,ur(t,n),o=rt(o),r=r(o),t.flags|=1,Me(e,t,r,n),t.child;case 14:return r=t.type,o=st(r,t.pendingProps),o=st(r.type,o),hd(e,t,r,o,n);case 15:return Yh(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:st(r,o),hi(e,t),t.tag=1,Fe(r)?(e=!0,Li(t)):e=!1,ur(t,n),Ph(t,r,o),rl(t,r,o,n),sl(null,t,r,!0,e,n);case 19:return em(e,t,n);case 22:return Xh(e,t,n)}throw Error(R(156,t.tag))};function vm(e,t){return $p(e,t)}function v1(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function tt(e,t,n,r){return new v1(e,t,n,r)}function Du(e){return e=e.prototype,!(!e||!e.isReactComponent)}function g1(e){if(typeof e=="function")return Du(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Jl)return 11;if(e===eu)return 14}return 2}function ln(e,t){var n=e.alternate;return n===null?(n=tt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function gi(e,t,n,r,o,i){var s=2;if(r=e,typeof e=="function")Du(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case Un:return _n(n.children,o,i,t);case ql:s=8,o|=8;break;case ba:return e=tt(12,n,t,o|2),e.elementType=ba,e.lanes=i,e;case Ra:return e=tt(13,n,t,o),e.elementType=Ra,e.lanes=i,e;case Aa:return e=tt(19,n,t,o),e.elementType=Aa,e.lanes=i,e;case Ep:return ms(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Tp:s=10;break e;case Pp:s=9;break e;case Jl:s=11;break e;case eu:s=14;break e;case Gt:s=16,r=null;break e}throw Error(R(130,e==null?e:typeof e,""))}return t=tt(s,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function _n(e,t,n,r){return e=tt(7,e,r,t),e.lanes=n,e}function ms(e,t,n,r){return e=tt(22,e,r,t),e.elementType=Ep,e.lanes=n,e.stateNode={isHidden:!1},e}function sa(e,t,n){return e=tt(6,e,null,t),e.lanes=n,e}function aa(e,t,n){return t=tt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function y1(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Bs(0),this.expirationTimes=Bs(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Bs(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function ju(e,t,n,r,o,i,s,a,l){return e=new y1(e,t,n,a,l),t===1?(t=1,i===!0&&(t|=8)):t=0,i=tt(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},yu(i),e}function w1(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:$n,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function gm(e){if(!e)return cn;e=e._reactInternals;e:{if(zn(e)!==e||e.tag!==1)throw Error(R(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Fe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(R(171))}if(e.tag===1){var n=e.type;if(Fe(n))return vh(e,n,t)}return t}function ym(e,t,n,r,o,i,s,a,l){return e=ju(n,r,!0,e,o,i,s,a,l),e.context=gm(null),n=e.current,r=_e(),o=an(n),i=kt(r,o),i.callback=t??null,on(n,i,o),e.current.lanes=o,bo(e,o,r),ze(e,r),e}function vs(e,t,n,r){var o=t.current,i=_e(),s=an(o);return n=gm(n),t.context===null?t.context=n:t.pendingContext=n,t=kt(i,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=on(o,t,s),e!==null&&(ct(e,o,s,i),di(e,o,s)),s}function Ki(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function kd(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Vu(e,t){kd(e,t),(e=e.alternate)&&kd(e,t)}function x1(){return null}var wm=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ou(e){this._internalRoot=e}gs.prototype.render=Ou.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(R(409));vs(e,t,null,null)};gs.prototype.unmount=Ou.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;On(function(){vs(null,e,null,null)}),t[Lt]=null}};function gs(e){this._internalRoot=e}gs.prototype.unstable_scheduleHydration=function(e){if(e){var t=Yp();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Yt.length&&t!==0&&t<Yt[n].priority;n++);Yt.splice(n,0,e),n===0&&Zp(e)}};function Iu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ys(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function bd(){}function S1(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=Ki(s);i.call(u)}}var s=ym(t,r,e,0,null,!1,!1,"",bd);return e._reactRootContainer=s,e[Lt]=s.current,fo(e.nodeType===8?e.parentNode:e),On(),s}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var a=r;r=function(){var u=Ki(l);a.call(u)}}var l=ju(e,0,!1,null,null,!1,!1,"",bd);return e._reactRootContainer=l,e[Lt]=l.current,fo(e.nodeType===8?e.parentNode:e),On(function(){vs(t,l,n,r)}),l}function ws(e,t,n,r,o){var i=n._reactRootContainer;if(i){var s=i;if(typeof o=="function"){var a=o;o=function(){var l=Ki(s);a.call(l)}}vs(t,s,e,o)}else s=S1(n,t,e,o,r);return Ki(s)}Gp=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Fr(t.pendingLanes);n!==0&&(ru(t,n|1),ze(t,ce()),!(H&6)&&(yr=ce()+500,mn()))}break;case 13:On(function(){var r=Dt(e,1);if(r!==null){var o=_e();ct(r,e,1,o)}}),Vu(e,1)}};ou=function(e){if(e.tag===13){var t=Dt(e,134217728);if(t!==null){var n=_e();ct(t,e,134217728,n)}Vu(e,134217728)}};Qp=function(e){if(e.tag===13){var t=an(e),n=Dt(e,t);if(n!==null){var r=_e();ct(n,e,t,r)}Vu(e,t)}};Yp=function(){return G};Xp=function(e,t){var n=G;try{return G=e,t()}finally{G=n}};Fa=function(e,t,n){switch(t){case"input":if(Na(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=us(r);if(!o)throw Error(R(90));bp(r),Na(r,o)}}}break;case"textarea":Ap(e,n);break;case"select":t=n.value,t!=null&&ir(e,!!n.multiple,t,!1)}};Vp=_u;Op=On;var C1={usingClientEntryPoint:!1,Events:[Ao,Gn,us,Dp,jp,_u]},Lr={findFiberByHostInstance:En,bundleType:0,version:"18.2.0",rendererPackageName:"react-dom"},T1={bundleType:Lr.bundleType,version:Lr.version,rendererPackageName:Lr.rendererPackageName,rendererConfig:Lr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:It.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=zp(e),e===null?null:e.stateNode},findFiberByHostInstance:Lr.findFiberByHostInstance||x1,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.2.0-next-9e3b772b8-20220608"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var qo=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!qo.isDisabled&&qo.supportsFiber)try{is=qo.inject(T1),vt=qo}catch{}}Ye.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=C1;Ye.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Iu(t))throw Error(R(200));return w1(e,t,null,n)};Ye.createRoot=function(e,t){if(!Iu(e))throw Error(R(299));var n=!1,r="",o=wm;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=ju(e,1,!1,null,null,n,!1,r,o),e[Lt]=t.current,fo(e.nodeType===8?e.parentNode:e),new Ou(t)};Ye.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(R(188)):(e=Object.keys(e).join(","),Error(R(268,e)));return e=zp(t),e=e===null?null:e.stateNode,e};Ye.flushSync=function(e){return On(e)};Ye.hydrate=function(e,t,n){if(!ys(t))throw Error(R(200));return ws(null,e,t,!0,n)};Ye.hydrateRoot=function(e,t,n){if(!Iu(e))throw Error(R(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",s=wm;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=ym(t,null,e,1,n??null,o,!1,i,s),e[Lt]=t.current,fo(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new gs(t)};Ye.render=function(e,t,n){if(!ys(t))throw Error(R(200));return ws(null,e,t,!1,n)};Ye.unmountComponentAtNode=function(e){if(!ys(e))throw Error(R(40));return e._reactRootContainer?(On(function(){ws(null,null,e,!1,function(){e._reactRootContainer=null,e[Lt]=null})}),!0):!1};Ye.unstable_batchedUpdates=_u;Ye.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!ys(n))throw Error(R(200));if(e==null||e._reactInternals===void 0)throw Error(R(38));return ws(e,t,n,!1,r)};Ye.version="18.2.0-next-9e3b772b8-20220608";function xm(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(xm)}catch(e){console.error(e)}}xm(),yp.exports=Ye;var Fu=yp.exports;const Sm=Wl(Fu);/**
 * @remix-run/router v1.15.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Gi(){return Gi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Gi.apply(this,arguments)}var Jt;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Jt||(Jt={}));const Rd="popstate";function P1(e){e===void 0&&(e={});function t(r,o){let{pathname:i,search:s,hash:a}=r.location;return yl("",{pathname:i,search:s,hash:a},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function n(r,o){return typeof o=="string"?o:Tm(o)}return k1(t,n,null,e)}function Be(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Cm(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function E1(){return Math.random().toString(36).substr(2,8)}function Ad(e,t){return{usr:e.state,key:e.key,idx:t}}function yl(e,t,n,r){return n===void 0&&(n=null),Gi({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?xs(t):t,{state:n,key:t&&t.key||r||E1()})}function Tm(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function xs(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function k1(e,t,n,r){r===void 0&&(r={});let{window:o=document.defaultView,v5Compat:i=!1}=r,s=o.history,a=Jt.Pop,l=null,u=d();u==null&&(u=0,s.replaceState(Gi({},s.state,{idx:u}),""));function d(){return(s.state||{idx:null}).idx}function c(){a=Jt.Pop;let x=d(),h=x==null?null:x-u;u=x,l&&l({action:a,location:w.location,delta:h})}function f(x,h){a=Jt.Push;let p=yl(w.location,x,h);n&&n(p,x),u=d()+1;let m=Ad(p,u),S=w.createHref(p);try{s.pushState(m,"",S)}catch(T){if(T instanceof DOMException&&T.name==="DataCloneError")throw T;o.location.assign(S)}i&&l&&l({action:a,location:w.location,delta:1})}function v(x,h){a=Jt.Replace;let p=yl(w.location,x,h);n&&n(p,x),u=d();let m=Ad(p,u),S=w.createHref(p);s.replaceState(m,"",S),i&&l&&l({action:a,location:w.location,delta:0})}function y(x){let h=o.location.origin!=="null"?o.location.origin:o.location.href,p=typeof x=="string"?x:Tm(x);return p=p.replace(/ $/,"%20"),Be(h,"No window.location.(origin|href) available to create URL for href: "+p),new URL(p,h)}let w={get action(){return a},get location(){return e(o,s)},listen(x){if(l)throw new Error("A history only accepts one active listener");return o.addEventListener(Rd,c),l=x,()=>{o.removeEventListener(Rd,c),l=null}},createHref(x){return t(o,x)},createURL:y,encodeLocation(x){let h=y(x);return{pathname:h.pathname,search:h.search,hash:h.hash}},push:f,replace:v,go(x){return s.go(x)}};return w}var Md;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Md||(Md={}));function b1(e,t,n){n===void 0&&(n="/");let r=typeof t=="string"?xs(t):t,o=km(r.pathname||"/",n);if(o==null)return null;let i=Pm(e);R1(i);let s=null;for(let a=0;s==null&&a<i.length;++a){let l=z1(o);s=O1(i[a],l)}return s}function Pm(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let o=(i,s,a)=>{let l={relativePath:a===void 0?i.path||"":a,caseSensitive:i.caseSensitive===!0,childrenIndex:s,route:i};l.relativePath.startsWith("/")&&(Be(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let u=dr([r,l.relativePath]),d=n.concat(l);i.children&&i.children.length>0&&(Be(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),Pm(i.children,t,d,u)),!(i.path==null&&!i.index)&&t.push({path:u,score:j1(u,i.index),routesMeta:d})};return e.forEach((i,s)=>{var a;if(i.path===""||!((a=i.path)!=null&&a.includes("?")))o(i,s);else for(let l of Em(i.path))o(i,s,l)}),t}function Em(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,o=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return o?[i,""]:[i];let s=Em(r.join("/")),a=[];return a.push(...s.map(l=>l===""?i:[i,l].join("/"))),o&&a.push(...s),a.map(l=>e.startsWith("/")&&l===""?"/":l)}function R1(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:V1(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const A1=/^:[\w-]+$/,M1=3,_1=2,N1=1,L1=10,D1=-2,_d=e=>e==="*";function j1(e,t){let n=e.split("/"),r=n.length;return n.some(_d)&&(r+=D1),t&&(r+=_1),n.filter(o=>!_d(o)).reduce((o,i)=>o+(A1.test(i)?M1:i===""?N1:L1),r)}function V1(e,t){return e.length===t.length&&e.slice(0,-1).every((r,o)=>r===t[o])?e[e.length-1]-t[t.length-1]:0}function O1(e,t){let{routesMeta:n}=e,r={},o="/",i=[];for(let s=0;s<n.length;++s){let a=n[s],l=s===n.length-1,u=o==="/"?t:t.slice(o.length)||"/",d=I1({path:a.relativePath,caseSensitive:a.caseSensitive,end:l},u);if(!d)return null;Object.assign(r,d.params);let c=a.route;i.push({params:r,pathname:dr([o,d.pathname]),pathnameBase:B1(dr([o,d.pathnameBase])),route:c}),d.pathnameBase!=="/"&&(o=dr([o,d.pathnameBase]))}return i}function I1(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=F1(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let i=o[0],s=i.replace(/(.)\/+$/,"$1"),a=o.slice(1);return{params:r.reduce((u,d,c)=>{let{paramName:f,isOptional:v}=d;if(f==="*"){let w=a[c]||"";s=i.slice(0,i.length-w.length).replace(/(.)\/+$/,"$1")}const y=a[c];return v&&!y?u[f]=void 0:u[f]=(y||"").replace(/%2F/g,"/"),u},{}),pathname:i,pathnameBase:s,pattern:e}}function F1(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Cm(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(s,a,l)=>(r.push({paramName:a,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function z1(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Cm(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function km(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}const dr=e=>e.join("/").replace(/\/\/+/g,"/"),B1=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/");function $1(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const bm=["post","put","patch","delete"];new Set(bm);const U1=["get",...bm];new Set(U1);/**
 * React Router v6.22.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Qi(){return Qi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Qi.apply(this,arguments)}const W1=g.createContext(null),H1=g.createContext(null),Rm=g.createContext(null),Ss=g.createContext(null),Cs=g.createContext({outlet:null,matches:[],isDataRoute:!1}),Am=g.createContext(null);function zu(){return g.useContext(Ss)!=null}function K1(){return zu()||Be(!1),g.useContext(Ss).location}function G1(e,t){return Q1(e,t)}function Q1(e,t,n,r){zu()||Be(!1);let{navigator:o}=g.useContext(Rm),{matches:i}=g.useContext(Cs),s=i[i.length-1],a=s?s.params:{};s&&s.pathname;let l=s?s.pathnameBase:"/";s&&s.route;let u=K1(),d;if(t){var c;let x=typeof t=="string"?xs(t):t;l==="/"||(c=x.pathname)!=null&&c.startsWith(l)||Be(!1),d=x}else d=u;let f=d.pathname||"/",v=f;if(l!=="/"){let x=l.replace(/^\//,"").split("/");v="/"+f.replace(/^\//,"").split("/").slice(x.length).join("/")}let y=b1(e,{pathname:v}),w=J1(y&&y.map(x=>Object.assign({},x,{params:Object.assign({},a,x.params),pathname:dr([l,o.encodeLocation?o.encodeLocation(x.pathname).pathname:x.pathname]),pathnameBase:x.pathnameBase==="/"?l:dr([l,o.encodeLocation?o.encodeLocation(x.pathnameBase).pathname:x.pathnameBase])})),i,n,r);return t&&w?g.createElement(Ss.Provider,{value:{location:Qi({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:Jt.Pop}},w):w}function Y1(){let e=rw(),t=$1(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return g.createElement(g.Fragment,null,g.createElement("h2",null,"Unexpected Application Error!"),g.createElement("h3",{style:{fontStyle:"italic"}},t),n?g.createElement("pre",{style:o},n):null,null)}const X1=g.createElement(Y1,null);class Z1 extends g.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?g.createElement(Cs.Provider,{value:this.props.routeContext},g.createElement(Am.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function q1(e){let{routeContext:t,match:n,children:r}=e,o=g.useContext(W1);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),g.createElement(Cs.Provider,{value:t},r)}function J1(e,t,n,r){var o;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var i;if((i=n)!=null&&i.errors)e=n.matches;else return null}let s=e,a=(o=n)==null?void 0:o.errors;if(a!=null){let d=s.findIndex(c=>c.route.id&&(a==null?void 0:a[c.route.id]));d>=0||Be(!1),s=s.slice(0,Math.min(s.length,d+1))}let l=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let d=0;d<s.length;d++){let c=s[d];if((c.route.HydrateFallback||c.route.hydrateFallbackElement)&&(u=d),c.route.id){let{loaderData:f,errors:v}=n,y=c.route.loader&&f[c.route.id]===void 0&&(!v||v[c.route.id]===void 0);if(c.route.lazy||y){l=!0,u>=0?s=s.slice(0,u+1):s=[s[0]];break}}}return s.reduceRight((d,c,f)=>{let v,y=!1,w=null,x=null;n&&(v=a&&c.route.id?a[c.route.id]:void 0,w=c.route.errorElement||X1,l&&(u<0&&f===0?(ow("route-fallback",!1),y=!0,x=null):u===f&&(y=!0,x=c.route.hydrateFallbackElement||null)));let h=t.concat(s.slice(0,f+1)),p=()=>{let m;return v?m=w:y?m=x:c.route.Component?m=g.createElement(c.route.Component,null):c.route.element?m=c.route.element:m=d,g.createElement(q1,{match:c,routeContext:{outlet:d,matches:h,isDataRoute:n!=null},children:m})};return n&&(c.route.ErrorBoundary||c.route.errorElement||f===0)?g.createElement(Z1,{location:n.location,revalidation:n.revalidation,component:w,error:v,children:p(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):p()},null)}var wl=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(wl||{});function ew(e){let t=g.useContext(H1);return t||Be(!1),t}function tw(e){let t=g.useContext(Cs);return t||Be(!1),t}function nw(e){let t=tw(),n=t.matches[t.matches.length-1];return n.route.id||Be(!1),n.route.id}function rw(){var e;let t=g.useContext(Am),n=ew(wl.UseRouteError),r=nw(wl.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}const Nd={};function ow(e,t,n){!t&&!Nd[e]&&(Nd[e]=!0)}function xl(e){Be(!1)}function iw(e){let{basename:t="/",children:n=null,location:r,navigationType:o=Jt.Pop,navigator:i,static:s=!1,future:a}=e;zu()&&Be(!1);let l=t.replace(/^\/*/,"/"),u=g.useMemo(()=>({basename:l,navigator:i,static:s,future:Qi({v7_relativeSplatPath:!1},a)}),[l,a,i,s]);typeof r=="string"&&(r=xs(r));let{pathname:d="/",search:c="",hash:f="",state:v=null,key:y="default"}=r,w=g.useMemo(()=>{let x=km(d,l);return x==null?null:{location:{pathname:x,search:c,hash:f,state:v,key:y},navigationType:o}},[l,d,c,f,v,y,o]);return w==null?null:g.createElement(Rm.Provider,{value:u},g.createElement(Ss.Provider,{children:n,value:w}))}function sw(e){let{children:t,location:n}=e;return G1(Sl(t),n)}new Promise(()=>{});function Sl(e,t){t===void 0&&(t=[]);let n=[];return g.Children.forEach(e,(r,o)=>{if(!g.isValidElement(r))return;let i=[...t,o];if(r.type===g.Fragment){n.push.apply(n,Sl(r.props.children,i));return}r.type!==xl&&Be(!1),!r.props.index||!r.props.children||Be(!1);let s={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(s.children=Sl(r.props.children,i)),n.push(s)}),n}/**
 * React Router DOM v6.22.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const aw="6";try{window.__reactRouterVersion=aw}catch{}const lw="startTransition",Ld=hy[lw];function uw(e){let{basename:t,children:n,future:r,window:o}=e,i=g.useRef();i.current==null&&(i.current=P1({window:o,v5Compat:!0}));let s=i.current,[a,l]=g.useState({action:s.action,location:s.location}),{v7_startTransition:u}=r||{},d=g.useCallback(c=>{u&&Ld?Ld(()=>l(c)):l(c)},[l,u]);return g.useLayoutEffect(()=>s.listen(d),[s,d]),g.createElement(iw,{basename:t,children:n,location:a.location,navigationType:a.action,navigator:s,future:r})}var Dd;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Dd||(Dd={}));var jd;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(jd||(jd={}));const cw="modulepreload",dw=function(e){return"/"+e},Vd={},fw=function(t,n,r){let o=Promise.resolve();if(n&&n.length>0){const i=document.getElementsByTagName("link");o=Promise.all(n.map(s=>{if(s=dw(s),s in Vd)return;Vd[s]=!0;const a=s.endsWith(".css"),l=a?'[rel="stylesheet"]':"";if(!!r)for(let c=i.length-1;c>=0;c--){const f=i[c];if(f.href===s&&(!a||f.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${s}"]${l}`))return;const d=document.createElement("link");if(d.rel=a?"stylesheet":cw,a||(d.as="script",d.crossOrigin=""),d.href=s,document.head.appendChild(d),a)return new Promise((c,f)=>{d.addEventListener("load",c),d.addEventListener("error",()=>f(new Error(`Unable to preload CSS for ${s}`)))})}))}return o.then(()=>t()).catch(i=>{const s=new Event("vite:preloadError",{cancelable:!0});if(s.payload=i,window.dispatchEvent(s),!s.defaultPrevented)throw i})};function pw(e){if(typeof Proxy>"u")return e;const t=new Map,n=(...r)=>e(...r);return new Proxy(n,{get:(r,o)=>o==="create"?e:(t.has(o)||t.set(o,e(o)),t.get(o))})}function So(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Cl=e=>Array.isArray(e);function Mm(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function Co(e){return typeof e=="string"||Array.isArray(e)}function Od(e){const t=[{},{}];return e==null||e.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function Bu(e,t,n,r){if(typeof t=="function"){const[o,i]=Od(r);t=t(n!==void 0?n:e.custom,o,i)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[o,i]=Od(r);t=t(n!==void 0?n:e.custom,o,i)}return t}function Ts(e,t,n){const r=e.getProps();return Bu(r,t,n!==void 0?n:r.custom,e)}const $u=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Uu=["initial",...$u],_o=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],vn=new Set(_o),bt=e=>e*1e3,Rt=e=>e/1e3,hw={type:"spring",stiffness:500,damping:25,restSpeed:10},mw=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),vw={type:"keyframes",duration:.8},gw={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},yw=(e,{keyframes:t})=>t.length>2?vw:vn.has(e)?e.startsWith("scale")?mw(t[1]):hw:gw;function ww({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:o,repeat:i,repeatType:s,repeatDelay:a,from:l,elapsed:u,...d}){return!!Object.keys(d).length}function Wu(e,t){return e[t]||e.default||e}const xw={skipAnimations:!1,useManualTiming:!1},Sw=e=>e!==null;function Ps(e,{repeat:t,repeatType:n="loop"},r){const o=e.filter(Sw),i=t&&n!=="loop"&&t%2===1?0:o.length-1;return!i||r===void 0?o[i]:r}const Re=e=>e;function Cw(e){let t=new Set,n=new Set,r=!1,o=!1;const i=new WeakSet;let s={delta:0,timestamp:0,isProcessing:!1};function a(u){i.has(u)&&(l.schedule(u),e()),u(s)}const l={schedule:(u,d=!1,c=!1)=>{const v=c&&r?t:n;return d&&i.add(u),v.has(u)||v.add(u),u},cancel:u=>{n.delete(u),i.delete(u)},process:u=>{if(s=u,r){o=!0;return}r=!0,[t,n]=[n,t],n.clear(),t.forEach(a),r=!1,o&&(o=!1,l.process(u))}};return l}const Jo=["read","resolveKeyframes","update","preRender","render","postRender"],Tw=40;function _m(e,t){let n=!1,r=!0;const o={delta:0,timestamp:0,isProcessing:!1},i=()=>n=!0,s=Jo.reduce((h,p)=>(h[p]=Cw(i),h),{}),{read:a,resolveKeyframes:l,update:u,preRender:d,render:c,postRender:f}=s,v=()=>{const h=performance.now();n=!1,o.delta=r?1e3/60:Math.max(Math.min(h-o.timestamp,Tw),1),o.timestamp=h,o.isProcessing=!0,a.process(o),l.process(o),u.process(o),d.process(o),c.process(o),f.process(o),o.isProcessing=!1,n&&t&&(r=!1,e(v))},y=()=>{n=!0,r=!0,o.isProcessing||e(v)};return{schedule:Jo.reduce((h,p)=>{const m=s[p];return h[p]=(S,T=!1,E=!1)=>(n||y(),m.schedule(S,T,E)),h},{}),cancel:h=>{for(let p=0;p<Jo.length;p++)s[Jo[p]].cancel(h)},state:o,steps:s}}const{schedule:K,cancel:Vt,state:xe,steps:la}=_m(typeof requestAnimationFrame<"u"?requestAnimationFrame:Re,!0),Nm=e=>/^0[^.\s]+$/u.test(e);function Pw(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||Nm(e):!0}let Lm=Re;const Dm=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),jm=e=>t=>typeof t=="string"&&t.startsWith(e),Vm=jm("--"),Ew=jm("var(--"),Hu=e=>Ew(e)?kw.test(e.split("/*")[0].trim()):!1,kw=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,bw=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Rw(e){const t=bw.exec(e);if(!t)return[,];const[,n,r,o]=t;return[`--${n??r}`,o]}function Om(e,t,n=1){const[r,o]=Rw(e);if(!r)return;const i=window.getComputedStyle(t).getPropertyValue(r);if(i){const s=i.trim();return Dm(s)?parseFloat(s):s}return Hu(o)?Om(o,t,n+1):o}const dn=(e,t,n)=>n>t?t:n<e?e:n,Pr={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},qr={...Pr,transform:e=>dn(0,1,e)},ei={...Pr,default:1},Jr=e=>Math.round(e*1e5)/1e5,Ku=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,Aw=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,Mw=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu;function No(e){return typeof e=="string"}function _w(e){return e==null}const Lo=e=>({test:t=>No(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),Kt=Lo("deg"),yt=Lo("%"),O=Lo("px"),Nw=Lo("vh"),Lw=Lo("vw"),Id={...yt,parse:e=>yt.parse(e)/100,transform:e=>yt.transform(e*100)},Dw=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),Fd=e=>e===Pr||e===O,zd=(e,t)=>parseFloat(e.split(", ")[t]),Bd=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const o=r.match(/^matrix3d\((.+)\)$/u);if(o)return zd(o[1],t);{const i=r.match(/^matrix\((.+)\)$/u);return i?zd(i[1],e):0}},jw=new Set(["x","y","z"]),Vw=_o.filter(e=>!jw.has(e));function Ow(e){const t=[];return Vw.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t}const wr={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:Bd(4,13),y:Bd(5,14)};wr.translateX=wr.x;wr.translateY=wr.y;const Im=e=>t=>t.test(e),Iw={test:e=>e==="auto",parse:e=>e},Fm=[Pr,O,yt,Kt,Lw,Nw,Iw],$d=e=>Fm.find(Im(e)),Nn=new Set;let Tl=!1,Pl=!1;function zm(){if(Pl){const e=Array.from(Nn).filter(r=>r.needsMeasurement),t=new Set(e.map(r=>r.element)),n=new Map;t.forEach(r=>{const o=Ow(r);o.length&&(n.set(r,o),r.render())}),e.forEach(r=>r.measureInitialState()),t.forEach(r=>{r.render();const o=n.get(r);o&&o.forEach(([i,s])=>{var a;(a=r.getValue(i))===null||a===void 0||a.set(s)})}),e.forEach(r=>r.measureEndState()),e.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}Pl=!1,Tl=!1,Nn.forEach(e=>e.complete()),Nn.clear()}function Bm(){Nn.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(Pl=!0)})}function Fw(){Bm(),zm()}class Gu{constructor(t,n,r,o,i,s=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=n,this.name=r,this.motionValue=o,this.element=i,this.isAsync=s}scheduleResolve(){this.isScheduled=!0,this.isAsync?(Nn.add(this),Tl||(Tl=!0,K.read(Bm),K.resolveKeyframes(zm))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:n,element:r,motionValue:o}=this;for(let i=0;i<t.length;i++)if(t[i]===null)if(i===0){const s=o==null?void 0:o.get(),a=t[t.length-1];if(s!==void 0)t[0]=s;else if(r&&n){const l=r.readValue(n,a);l!=null&&(t[0]=l)}t[0]===void 0&&(t[0]=a),o&&s===void 0&&o.set(t[0])}else t[i]=t[i-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),Nn.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,Nn.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const Qu=(e,t)=>n=>!!(No(n)&&Mw.test(n)&&n.startsWith(e)||t&&!_w(n)&&Object.prototype.hasOwnProperty.call(n,t)),$m=(e,t,n)=>r=>{if(!No(r))return r;const[o,i,s,a]=r.match(Ku);return{[e]:parseFloat(o),[t]:parseFloat(i),[n]:parseFloat(s),alpha:a!==void 0?parseFloat(a):1}},zw=e=>dn(0,255,e),ua={...Pr,transform:e=>Math.round(zw(e))},Rn={test:Qu("rgb","red"),parse:$m("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+ua.transform(e)+", "+ua.transform(t)+", "+ua.transform(n)+", "+Jr(qr.transform(r))+")"};function Bw(e){let t="",n="",r="",o="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),o=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),o=e.substring(4,5),t+=t,n+=n,r+=r,o+=o),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:o?parseInt(o,16)/255:1}}const El={test:Qu("#"),parse:Bw,transform:Rn.transform},er={test:Qu("hsl","hue"),parse:$m("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+yt.transform(Jr(t))+", "+yt.transform(Jr(n))+", "+Jr(qr.transform(r))+")"},Ee={test:e=>Rn.test(e)||El.test(e)||er.test(e),parse:e=>Rn.test(e)?Rn.parse(e):er.test(e)?er.parse(e):El.parse(e),transform:e=>No(e)?e:e.hasOwnProperty("red")?Rn.transform(e):er.transform(e)};function $w(e){var t,n;return isNaN(e)&&No(e)&&(((t=e.match(Ku))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(Aw))===null||n===void 0?void 0:n.length)||0)>0}const Um="number",Wm="color",Uw="var",Ww="var(",Ud="${}",Hw=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function To(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},o=[];let i=0;const a=t.replace(Hw,l=>(Ee.test(l)?(r.color.push(i),o.push(Wm),n.push(Ee.parse(l))):l.startsWith(Ww)?(r.var.push(i),o.push(Uw),n.push(l)):(r.number.push(i),o.push(Um),n.push(parseFloat(l))),++i,Ud)).split(Ud);return{values:n,split:a,indexes:r,types:o}}function Hm(e){return To(e).values}function Km(e){const{split:t,types:n}=To(e),r=t.length;return o=>{let i="";for(let s=0;s<r;s++)if(i+=t[s],o[s]!==void 0){const a=n[s];a===Um?i+=Jr(o[s]):a===Wm?i+=Ee.transform(o[s]):i+=o[s]}return i}}const Kw=e=>typeof e=="number"?0:e;function Gw(e){const t=Hm(e);return Km(e)(t.map(Kw))}const fn={test:$w,parse:Hm,createTransformer:Km,getAnimatableNone:Gw},Qw=new Set(["brightness","contrast","saturate","opacity"]);function Yw(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(Ku)||[];if(!r)return e;const o=n.replace(r,"");let i=Qw.has(t)?1:0;return r!==n&&(i*=100),t+"("+i+o+")"}const Xw=/\b([a-z-]*)\(.*?\)/gu,kl={...fn,getAnimatableNone:e=>{const t=e.match(Xw);return t?t.map(Yw).join(" "):e}},Wd={...Pr,transform:Math.round},Yu={borderWidth:O,borderTopWidth:O,borderRightWidth:O,borderBottomWidth:O,borderLeftWidth:O,borderRadius:O,radius:O,borderTopLeftRadius:O,borderTopRightRadius:O,borderBottomRightRadius:O,borderBottomLeftRadius:O,width:O,maxWidth:O,height:O,maxHeight:O,size:O,top:O,right:O,bottom:O,left:O,padding:O,paddingTop:O,paddingRight:O,paddingBottom:O,paddingLeft:O,margin:O,marginTop:O,marginRight:O,marginBottom:O,marginLeft:O,rotate:Kt,rotateX:Kt,rotateY:Kt,rotateZ:Kt,scale:ei,scaleX:ei,scaleY:ei,scaleZ:ei,skew:Kt,skewX:Kt,skewY:Kt,distance:O,translateX:O,translateY:O,translateZ:O,x:O,y:O,z:O,perspective:O,transformPerspective:O,opacity:qr,originX:Id,originY:Id,originZ:O,zIndex:Wd,backgroundPositionX:O,backgroundPositionY:O,fillOpacity:qr,strokeOpacity:qr,numOctaves:Wd},Zw={...Yu,color:Ee,backgroundColor:Ee,outlineColor:Ee,fill:Ee,stroke:Ee,borderColor:Ee,borderTopColor:Ee,borderRightColor:Ee,borderBottomColor:Ee,borderLeftColor:Ee,filter:kl,WebkitFilter:kl},Xu=e=>Zw[e];function Gm(e,t){let n=Xu(e);return n!==kl&&(n=fn),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const qw=new Set(["auto","none","0"]);function Jw(e,t,n){let r=0,o;for(;r<e.length&&!o;){const i=e[r];typeof i=="string"&&!qw.has(i)&&To(i).values.length&&(o=e[r]),r++}if(o&&n)for(const i of t)e[i]=Gm(n,o)}class Qm extends Gu{constructor(t,n,r,o,i){super(t,n,r,o,i,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:n,name:r}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<t.length;l++){let u=t[l];if(typeof u=="string"&&(u=u.trim(),Hu(u))){const d=Om(u,n.current);d!==void 0&&(t[l]=d),l===t.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!Dw.has(r)||t.length!==2)return;const[o,i]=t,s=$d(o),a=$d(i);if(s!==a)if(Fd(s)&&Fd(a))for(let l=0;l<t.length;l++){const u=t[l];typeof u=="string"&&(t[l]=parseFloat(u))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,r=[];for(let o=0;o<t.length;o++)Pw(t[o])&&r.push(o);r.length&&Jw(t,r,n)}measureInitialState(){const{element:t,unresolvedKeyframes:n,name:r}=this;if(!t||!t.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=wr[r](t.measureViewportBox(),window.getComputedStyle(t.current)),n[0]=this.measuredOrigin;const o=n[n.length-1];o!==void 0&&t.getValue(r,o).jump(o,!1)}measureEndState(){var t;const{element:n,name:r,unresolvedKeyframes:o}=this;if(!n||!n.current)return;const i=n.getValue(r);i&&i.jump(this.measuredOrigin,!1);const s=o.length-1,a=o[s];o[s]=wr[r](n.measureViewportBox(),window.getComputedStyle(n.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),!((t=this.removedTransforms)===null||t===void 0)&&t.length&&this.removedTransforms.forEach(([l,u])=>{n.getValue(l).set(u)}),this.resolveNoneKeyframes()}}function Ym(e){let t;return()=>(t===void 0&&(t=e()),t)}let yi;function ex(){yi=void 0}const At={now:()=>(yi===void 0&&At.set(xe.isProcessing||xw.useManualTiming?xe.timestamp:performance.now()),yi),set:e=>{yi=e,queueMicrotask(ex)}},Hd=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(fn.test(e)||e==="0")&&!e.startsWith("url("));function tx(e){const t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function nx(e,t,n,r){const o=e[0];if(o===null)return!1;if(t==="display"||t==="visibility")return!0;const i=e[e.length-1],s=Hd(o,t),a=Hd(i,t);return!s||!a?!1:tx(e)||n==="spring"&&r}const rx=40;class Xm{constructor({autoplay:t=!0,delay:n=0,type:r="keyframes",repeat:o=0,repeatDelay:i=0,repeatType:s="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=At.now(),this.options={autoplay:t,delay:n,type:r,repeat:o,repeatDelay:i,repeatType:s,...a},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>rx?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&Fw(),this._resolved}onKeyframesResolved(t,n){this.resolvedAt=At.now(),this.hasAttemptedResolve=!0;const{name:r,type:o,velocity:i,delay:s,onComplete:a,onUpdate:l,isGenerator:u}=this.options;if(!u&&!nx(t,r,o,i))if(s)this.options.duration=0;else{l==null||l(Ps(t,this.options,n)),a==null||a(),this.resolveFinishedPromise();return}const d=this.initPlayback(t,n);d!==!1&&(this._resolved={keyframes:t,finalKeyframe:n,...d},this.onPostResolved())}onPostResolved(){}then(t,n){return this.currentFinishedPromise.then(t,n)}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}function Zm(e,t){return t?e*(1e3/t):0}const ox=5;function qm(e,t,n){const r=Math.max(t-ox,0);return Zm(n-e(r),t-r)}const ca=.001,ix=.01,sx=10,ax=.05,lx=1;function ux({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let o,i,s=1-t;s=dn(ax,lx,s),e=dn(ix,sx,Rt(e)),s<1?(o=u=>{const d=u*s,c=d*e,f=d-n,v=bl(u,s),y=Math.exp(-c);return ca-f/v*y},i=u=>{const c=u*s*e,f=c*n+n,v=Math.pow(s,2)*Math.pow(u,2)*e,y=Math.exp(-c),w=bl(Math.pow(u,2),s);return(-o(u)+ca>0?-1:1)*((f-v)*y)/w}):(o=u=>{const d=Math.exp(-u*e),c=(u-n)*e+1;return-ca+d*c},i=u=>{const d=Math.exp(-u*e),c=(n-u)*(e*e);return d*c});const a=5/e,l=dx(o,i,a);if(e=bt(e),isNaN(l))return{stiffness:100,damping:10,duration:e};{const u=Math.pow(l,2)*r;return{stiffness:u,damping:s*2*Math.sqrt(r*u),duration:e}}}const cx=12;function dx(e,t,n){let r=n;for(let o=1;o<cx;o++)r=r-e(r)/t(r);return r}function bl(e,t){return e*Math.sqrt(1-t*t)}const fx=["duration","bounce"],px=["stiffness","damping","mass"];function Kd(e,t){return t.some(n=>e[n]!==void 0)}function hx(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!Kd(e,px)&&Kd(e,fx)){const n=ux(e);t={...t,...n,mass:1},t.isResolvedFromDuration=!0}return t}function Jm({keyframes:e,restDelta:t,restSpeed:n,...r}){const o=e[0],i=e[e.length-1],s={done:!1,value:o},{stiffness:a,damping:l,mass:u,duration:d,velocity:c,isResolvedFromDuration:f}=hx({...r,velocity:-Rt(r.velocity||0)}),v=c||0,y=l/(2*Math.sqrt(a*u)),w=i-o,x=Rt(Math.sqrt(a/u)),h=Math.abs(w)<5;n||(n=h?.01:2),t||(t=h?.005:.5);let p;if(y<1){const m=bl(x,y);p=S=>{const T=Math.exp(-y*x*S);return i-T*((v+y*x*w)/m*Math.sin(m*S)+w*Math.cos(m*S))}}else if(y===1)p=m=>i-Math.exp(-x*m)*(w+(v+x*w)*m);else{const m=x*Math.sqrt(y*y-1);p=S=>{const T=Math.exp(-y*x*S),E=Math.min(m*S,300);return i-T*((v+y*x*w)*Math.sinh(E)+m*w*Math.cosh(E))/m}}return{calculatedDuration:f&&d||null,next:m=>{const S=p(m);if(f)s.done=m>=d;else{let T=0;y<1&&(T=m===0?bt(v):qm(p,m,S));const E=Math.abs(T)<=n,k=Math.abs(i-S)<=t;s.done=E&&k}return s.value=s.done?i:S,s}}}function Gd({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:o=10,bounceStiffness:i=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:d}){const c=e[0],f={done:!1,value:c},v=P=>a!==void 0&&P<a||l!==void 0&&P>l,y=P=>a===void 0?l:l===void 0||Math.abs(a-P)<Math.abs(l-P)?a:l;let w=n*t;const x=c+w,h=s===void 0?x:s(x);h!==x&&(w=h-c);const p=P=>-w*Math.exp(-P/r),m=P=>h+p(P),S=P=>{const D=p(P),N=m(P);f.done=Math.abs(D)<=u,f.value=f.done?h:N};let T,E;const k=P=>{v(f.value)&&(T=P,E=Jm({keyframes:[f.value,y(f.value)],velocity:qm(m,P,f.value),damping:o,stiffness:i,restDelta:u,restSpeed:d}))};return k(0),{calculatedDuration:null,next:P=>{let D=!1;return!E&&T===void 0&&(D=!0,S(P),k(P)),T!==void 0&&P>=T?E.next(P-T):(!D&&S(P),f)}}}const ev=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,mx=1e-7,vx=12;function gx(e,t,n,r,o){let i,s,a=0;do s=t+(n-t)/2,i=ev(s,r,o)-e,i>0?n=s:t=s;while(Math.abs(i)>mx&&++a<vx);return s}function Do(e,t,n,r){if(e===t&&n===r)return Re;const o=i=>gx(i,0,1,e,n);return i=>i===0||i===1?i:ev(o(i),t,r)}const yx=Do(.42,0,1,1),wx=Do(0,0,.58,1),tv=Do(.42,0,.58,1),xx=e=>Array.isArray(e)&&typeof e[0]!="number",nv=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,rv=e=>t=>1-e(1-t),Zu=e=>1-Math.sin(Math.acos(e)),ov=rv(Zu),Sx=nv(Zu),iv=Do(.33,1.53,.69,.99),qu=rv(iv),Cx=nv(qu),Tx=e=>(e*=2)<1?.5*qu(e):.5*(2-Math.pow(2,-10*(e-1))),Px={linear:Re,easeIn:yx,easeInOut:tv,easeOut:wx,circIn:Zu,circInOut:Sx,circOut:ov,backIn:qu,backInOut:Cx,backOut:iv,anticipate:Tx},Qd=e=>{if(Array.isArray(e)){Lm(e.length===4);const[t,n,r,o]=e;return Do(t,n,r,o)}else if(typeof e=="string")return Px[e];return e},Ex=(e,t)=>n=>t(e(n)),Mt=(...e)=>e.reduce(Ex),Po=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},oe=(e,t,n)=>e+(t-e)*n;function da(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function kx({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let o=0,i=0,s=0;if(!t)o=i=s=n;else{const a=n<.5?n*(1+t):n+t-n*t,l=2*n-a;o=da(l,a,e+1/3),i=da(l,a,e),s=da(l,a,e-1/3)}return{red:Math.round(o*255),green:Math.round(i*255),blue:Math.round(s*255),alpha:r}}function Yi(e,t){return n=>n>0?t:e}const fa=(e,t,n)=>{const r=e*e,o=n*(t*t-r)+r;return o<0?0:Math.sqrt(o)},bx=[El,Rn,er],Rx=e=>bx.find(t=>t.test(e));function Yd(e){const t=Rx(e);if(!t)return!1;let n=t.parse(e);return t===er&&(n=kx(n)),n}const Xd=(e,t)=>{const n=Yd(e),r=Yd(t);if(!n||!r)return Yi(e,t);const o={...n};return i=>(o.red=fa(n.red,r.red,i),o.green=fa(n.green,r.green,i),o.blue=fa(n.blue,r.blue,i),o.alpha=oe(n.alpha,r.alpha,i),Rn.transform(o))},Rl=new Set(["none","hidden"]);function Ax(e,t){return Rl.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function Mx(e,t){return n=>oe(e,t,n)}function Ju(e){return typeof e=="number"?Mx:typeof e=="string"?Hu(e)?Yi:Ee.test(e)?Xd:Lx:Array.isArray(e)?sv:typeof e=="object"?Ee.test(e)?Xd:_x:Yi}function sv(e,t){const n=[...e],r=n.length,o=e.map((i,s)=>Ju(i)(i,t[s]));return i=>{for(let s=0;s<r;s++)n[s]=o[s](i);return n}}function _x(e,t){const n={...e,...t},r={};for(const o in n)e[o]!==void 0&&t[o]!==void 0&&(r[o]=Ju(e[o])(e[o],t[o]));return o=>{for(const i in r)n[i]=r[i](o);return n}}function Nx(e,t){var n;const r=[],o={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){const s=t.types[i],a=e.indexes[s][o[s]],l=(n=e.values[a])!==null&&n!==void 0?n:0;r[i]=l,o[s]++}return r}const Lx=(e,t)=>{const n=fn.createTransformer(t),r=To(e),o=To(t);return r.indexes.var.length===o.indexes.var.length&&r.indexes.color.length===o.indexes.color.length&&r.indexes.number.length>=o.indexes.number.length?Rl.has(e)&&!o.values.length||Rl.has(t)&&!r.values.length?Ax(e,t):Mt(sv(Nx(r,o),o.values),n):Yi(e,t)};function av(e,t,n){return typeof e=="number"&&typeof t=="number"&&typeof n=="number"?oe(e,t,n):Ju(e)(e,t)}function Dx(e,t,n){const r=[],o=n||av,i=e.length-1;for(let s=0;s<i;s++){let a=o(e[s],e[s+1]);if(t){const l=Array.isArray(t)?t[s]||Re:t;a=Mt(l,a)}r.push(a)}return r}function jx(e,t,{clamp:n=!0,ease:r,mixer:o}={}){const i=e.length;if(Lm(i===t.length),i===1)return()=>t[0];if(i===2&&e[0]===e[1])return()=>t[1];e[0]>e[i-1]&&(e=[...e].reverse(),t=[...t].reverse());const s=Dx(t,r,o),a=s.length,l=u=>{let d=0;if(a>1)for(;d<e.length-2&&!(u<e[d+1]);d++);const c=Po(e[d],e[d+1],u);return s[d](c)};return n?u=>l(dn(e[0],e[i-1],u)):l}function Vx(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const o=Po(0,t,r);e.push(oe(n,1,o))}}function Ox(e){const t=[0];return Vx(t,e.length-1),t}function Ix(e,t){return e.map(n=>n*t)}function Fx(e,t){return e.map(()=>t||tv).splice(0,e.length-1)}function Xi({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const o=xx(r)?r.map(Qd):Qd(r),i={done:!1,value:t[0]},s=Ix(n&&n.length===t.length?n:Ox(t),e),a=jx(s,t,{ease:Array.isArray(o)?o:Fx(t,o)});return{calculatedDuration:e,next:l=>(i.value=a(l),i.done=l>=e,i)}}const Zd=2e4;function zx(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<Zd;)t+=n,r=e.next(t);return t>=Zd?1/0:t}const Bx=e=>{const t=({timestamp:n})=>e(n);return{start:()=>K.update(t,!0),stop:()=>Vt(t),now:()=>xe.isProcessing?xe.timestamp:At.now()}},$x={decay:Gd,inertia:Gd,tween:Xi,keyframes:Xi,spring:Jm},Ux=e=>e/100;class Es extends Xm{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:l}=this.options;l&&l()};const{name:n,motionValue:r,element:o,keyframes:i}=this.options,s=(o==null?void 0:o.KeyframeResolver)||Gu,a=(l,u)=>this.onKeyframesResolved(l,u);this.resolver=new s(i,a,n,r,o),this.resolver.scheduleResolve()}initPlayback(t){const{type:n="keyframes",repeat:r=0,repeatDelay:o=0,repeatType:i,velocity:s=0}=this.options,a=$x[n]||Xi;let l,u;a!==Xi&&typeof t[0]!="number"&&(l=Mt(Ux,av(t[0],t[1])),t=[0,100]);const d=a({...this.options,keyframes:t});i==="mirror"&&(u=a({...this.options,keyframes:[...t].reverse(),velocity:-s})),d.calculatedDuration===null&&(d.calculatedDuration=zx(d));const{calculatedDuration:c}=d,f=c+o,v=f*(r+1)-o;return{generator:d,mirroredGenerator:u,mapPercentToKeyframes:l,calculatedDuration:c,resolvedDuration:f,totalDuration:v}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!t?this.pause():this.state=this.pendingPlayState}tick(t,n=!1){const{resolved:r}=this;if(!r){const{keyframes:P}=this.options;return{done:!0,value:P[P.length-1]}}const{finalKeyframe:o,generator:i,mirroredGenerator:s,mapPercentToKeyframes:a,keyframes:l,calculatedDuration:u,totalDuration:d,resolvedDuration:c}=r;if(this.startTime===null)return i.next(0);const{delay:f,repeat:v,repeatType:y,repeatDelay:w,onUpdate:x}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-d/this.speed,this.startTime)),n?this.currentTime=t:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const h=this.currentTime-f*(this.speed>=0?1:-1),p=this.speed>=0?h<0:h>d;this.currentTime=Math.max(h,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=d);let m=this.currentTime,S=i;if(v){const P=Math.min(this.currentTime,d)/c;let D=Math.floor(P),N=P%1;!N&&P>=1&&(N=1),N===1&&D--,D=Math.min(D,v+1),!!(D%2)&&(y==="reverse"?(N=1-N,w&&(N-=w/c)):y==="mirror"&&(S=s)),m=dn(0,1,N)*c}const T=p?{done:!1,value:l[0]}:S.next(m);a&&(T.value=a(T.value));let{done:E}=T;!p&&u!==null&&(E=this.speed>=0?this.currentTime>=d:this.currentTime<=0);const k=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&E);return k&&o!==void 0&&(T.value=Ps(l,this.options,o)),x&&x(T.value),k&&this.finish(),T}get duration(){const{resolved:t}=this;return t?Rt(t.calculatedDuration):0}get time(){return Rt(this.currentTime)}set time(t){t=bt(t),this.currentTime=t,this.holdTime!==null||this.speed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=Rt(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:t=Bx,onPlay:n,startTime:r}=this.options;this.driver||(this.driver=t(i=>this.tick(i))),n&&n();const o=this.driver.now();this.holdTime!==null?this.startTime=o-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=o):this.startTime=r??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(t=this.currentTime)!==null&&t!==void 0?t:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}function ik(e){return new Es(e)}const lv=new Set(["opacity","clipPath","filter","transform"]),uv=e=>Array.isArray(e)&&typeof e[0]=="number";function cv(e){return!!(!e||typeof e=="string"&&e in ec||uv(e)||Array.isArray(e)&&e.every(cv))}const Br=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,ec={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Br([0,.65,.55,1]),circOut:Br([.55,0,1,.45]),backIn:Br([.31,.01,.66,-.59]),backOut:Br([.33,1.53,.69,.99])};function Wx(e){return dv(e)||ec.easeOut}function dv(e){if(e)return uv(e)?Br(e):Array.isArray(e)?e.map(Wx):ec[e]}function Hx(e,t,n,{delay:r=0,duration:o=300,repeat:i=0,repeatType:s="loop",ease:a,times:l}={}){const u={[t]:n};l&&(u.offset=l);const d=dv(a);return Array.isArray(d)&&(u.easing=d),e.animate(u,{delay:r,duration:o,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:i+1,direction:s==="reverse"?"alternate":"normal"})}const Kx=Ym(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Zi=10,Gx=2e4;function Qx(e){return e.type==="spring"||!cv(e.ease)}function Yx(e,t){const n=new Es({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0});let r={done:!1,value:e[0]};const o=[];let i=0;for(;!r.done&&i<Gx;)r=n.sample(i),o.push(r.value),i+=Zi;return{times:void 0,keyframes:o,duration:i-Zi,ease:"linear"}}class qd extends Xm{constructor(t){super(t);const{name:n,motionValue:r,element:o,keyframes:i}=this.options;this.resolver=new Qm(i,(s,a)=>this.onKeyframesResolved(s,a),n,r,o),this.resolver.scheduleResolve()}initPlayback(t,n){var r;let{duration:o=300,times:i,ease:s,type:a,motionValue:l,name:u,startTime:d}=this.options;if(!(!((r=l.owner)===null||r===void 0)&&r.current))return!1;if(Qx(this.options)){const{onComplete:f,onUpdate:v,motionValue:y,element:w,...x}=this.options,h=Yx(t,x);t=h.keyframes,t.length===1&&(t[1]=t[0]),o=h.duration,i=h.times,s=h.ease,a="keyframes"}const c=Hx(l.owner.current,u,t,{...this.options,duration:o,times:i,ease:s});return c.startTime=d??this.calcStartTime(),this.pendingTimeline?(c.timeline=this.pendingTimeline,this.pendingTimeline=void 0):c.onfinish=()=>{const{onComplete:f}=this.options;l.set(Ps(t,this.options,n)),f&&f(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:o,times:i,type:a,ease:s,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:n}=t;return Rt(n)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:n}=t;return Rt(n.currentTime||0)}set time(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.currentTime=bt(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:n}=t;return n.playbackRate}set speed(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:n}=t;return n.playState}get startTime(){const{resolved:t}=this;if(!t)return null;const{animation:n}=t;return n.startTime}attachTimeline(t){if(!this._resolved)this.pendingTimeline=t;else{const{resolved:n}=this;if(!n)return Re;const{animation:r}=n;r.timeline=t,r.onfinish=null}return Re}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:t}=this;if(!t)return;const{animation:n,keyframes:r,duration:o,type:i,ease:s,times:a}=t;if(n.playState==="idle"||n.playState==="finished")return;if(this.time){const{motionValue:u,onUpdate:d,onComplete:c,element:f,...v}=this.options,y=new Es({...v,keyframes:r,duration:o,type:i,ease:s,times:a,isGenerator:!0}),w=bt(this.time);u.setWithVelocity(y.sample(w-Zi).value,y.sample(w).value,Zi)}const{onStop:l}=this.options;l&&l(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:n,name:r,repeatDelay:o,repeatType:i,damping:s,type:a}=t;return Kx()&&r&&lv.has(r)&&n&&n.owner&&n.owner.current instanceof HTMLElement&&!n.owner.getProps().onUpdate&&!o&&i!=="mirror"&&s!==0&&a!=="inertia"}}function Xx(e,t){let n;const r=()=>{const{currentTime:o}=t,s=(o===null?0:o.value)/100;n!==s&&e(s),n=s};return K.update(r,!0),()=>Vt(r)}const Zx=Ym(()=>window.ScrollTimeline!==void 0);class qx{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}then(t,n){return Promise.all(this.animations).then(t).catch(n)}getAll(t){return this.animations[0][t]}setAll(t,n){for(let r=0;r<this.animations.length;r++)this.animations[r][t]=n}attachTimeline(t){const n=this.animations.map(r=>{if(Zx()&&r.attachTimeline)r.attachTimeline(t);else return r.pause(),Xx(o=>{r.time=r.duration*o},t)});return()=>{n.forEach((r,o)=>{r&&r(),this.animations[o].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let n=0;n<this.animations.length;n++)t=Math.max(t,this.animations[n].duration);return t}runAll(t){this.animations.forEach(n=>n[t]())}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}const tc=(e,t,n,r={},o,i,s)=>a=>{const l=Wu(r,e)||{},u=l.delay||r.delay||0;let{elapsed:d=0}=r;d=d-bt(u);let c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...l,delay:-d,onUpdate:v=>{t.set(v),l.onUpdate&&l.onUpdate(v)},onComplete:()=>{a(),l.onComplete&&l.onComplete(),s&&s()},onStop:s,name:e,motionValue:t,element:i?void 0:o};ww(l)||(c={...c,...yw(e,c)}),c.duration&&(c.duration=bt(c.duration)),c.repeatDelay&&(c.repeatDelay=bt(c.repeatDelay)),c.from!==void 0&&(c.keyframes[0]=c.from);let f=!1;if((c.type===!1||c.duration===0&&!c.repeatDelay)&&(c.duration=0,c.delay===0&&(f=!0)),f&&!i&&t.get()!==void 0){const v=Ps(c.keyframes,l);if(v!==void 0)return K.update(()=>{c.onUpdate(v),c.onComplete()}),new qx([])}return!i&&qd.supports(c)?new qd(c):new Es(c)},Jx=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),eS=e=>Cl(e)?e[e.length-1]||0:e;function ks(e,t){e.indexOf(t)===-1&&e.push(t)}function bs(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class nc{constructor(){this.subscriptions=[]}add(t){return ks(this.subscriptions,t),()=>bs(this.subscriptions,t)}notify(t,n,r){const o=this.subscriptions.length;if(o)if(o===1)this.subscriptions[0](t,n,r);else for(let i=0;i<o;i++){const s=this.subscriptions[i];s&&s(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Jd=30,tS=e=>!isNaN(parseFloat(e));class fv{constructor(t,n={}){this.version="11.5.4",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(r,o=!0)=>{const i=At.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),o&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=n.owner}setCurrent(t){this.current=t,this.updatedAt=At.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=tS(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new nc);const r=this.events[t].add(n);return t==="change"?()=>{r(),K.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,n=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=At.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>Jd)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Jd);return Zm(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Eo(e,t){return new fv(e,t)}function nS(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Eo(n))}function rS(e,t){const n=Ts(e,t);let{transitionEnd:r={},transition:o={},...i}=n||{};i={...i,...r};for(const s in i){const a=eS(i[s]);nS(e,s,a)}}const Rs=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),oS="framerAppearId",pv="data-"+Rs(oS);function hv(e){return e.props[pv]}function mv(e){if(vn.has(e))return"transform";if(lv.has(e))return Rs(e)}class iS extends fv{constructor(){super(...arguments),this.output=[],this.counts=new Map}add(t){const n=mv(t);if(!n)return;const r=this.counts.get(n)||0;this.counts.set(n,r+1),r===0&&(this.output.push(n),this.update());let o=!1;return()=>{if(o)return;o=!0;const i=this.counts.get(n)-1;this.counts.set(n,i),i===0&&(bs(this.output,n),this.update())}}update(){this.set(this.output.length?this.output.join(", "):"auto")}}const be=e=>!!(e&&e.getVelocity);function sS(e){return!!(be(e)&&e.add)}function Al(e,t){var n;if(!e.applyWillChange)return;let r=e.getValue("willChange");if(!r&&!(!((n=e.props.style)===null||n===void 0)&&n.willChange)&&(r=new iS("auto"),e.addValue("willChange",r)),sS(r))return r.add(t)}function aS({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function vv(e,t,{delay:n=0,transitionOverride:r,type:o}={}){var i;let{transition:s=e.getDefaultTransition(),transitionEnd:a,...l}=t;r&&(s=r);const u=[],d=o&&e.animationState&&e.animationState.getState()[o];for(const c in l){const f=e.getValue(c,(i=e.latestValues[c])!==null&&i!==void 0?i:null),v=l[c];if(v===void 0||d&&aS(d,c))continue;const y={delay:n,...Wu(s||{},c)};let w=!1;if(window.MotionHandoffAnimation){const h=hv(e);if(h){const p=window.MotionHandoffAnimation(h,c,K);p!==null&&(y.startTime=p,w=!0)}}f.start(tc(c,f,v,e.shouldReduceMotion&&vn.has(c)?{type:!1}:y,e,w,Al(e,c)));const x=f.animation;x&&u.push(x)}return a&&Promise.all(u).then(()=>{K.update(()=>{a&&rS(e,a)})}),u}function Ml(e,t,n={}){var r;const o=Ts(e,t,n.type==="exit"?(r=e.presenceContext)===null||r===void 0?void 0:r.custom:void 0);let{transition:i=e.getDefaultTransition()||{}}=o||{};n.transitionOverride&&(i=n.transitionOverride);const s=o?()=>Promise.all(vv(e,o,n)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(u=0)=>{const{delayChildren:d=0,staggerChildren:c,staggerDirection:f}=i;return lS(e,t,d+u,c,f,n)}:()=>Promise.resolve(),{when:l}=i;if(l){const[u,d]=l==="beforeChildren"?[s,a]:[a,s];return u().then(()=>d())}else return Promise.all([s(),a(n.delay)])}function lS(e,t,n=0,r=0,o=1,i){const s=[],a=(e.variantChildren.size-1)*r,l=o===1?(u=0)=>u*r:(u=0)=>a-u*r;return Array.from(e.variantChildren).sort(uS).forEach((u,d)=>{u.notify("AnimationStart",t),s.push(Ml(u,t,{...i,delay:n+l(d)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(s)}function uS(e,t){return e.sortNodePosition(t)}function cS(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const o=t.map(i=>Ml(e,i,n));r=Promise.all(o)}else if(typeof t=="string")r=Ml(e,t,n);else{const o=typeof t=="function"?Ts(e,t,n.custom):t;r=Promise.all(vv(e,o,n))}return r.then(()=>{e.notify("AnimationComplete",t)})}const dS=[...$u].reverse(),fS=$u.length;function pS(e){return t=>Promise.all(t.map(({animation:n,options:r})=>cS(e,n,r)))}function hS(e){let t=pS(e),n=ef(),r=!0;const o=l=>(u,d)=>{var c;const f=Ts(e,d,l==="exit"?(c=e.presenceContext)===null||c===void 0?void 0:c.custom:void 0);if(f){const{transition:v,transitionEnd:y,...w}=f;u={...u,...w,...y}}return u};function i(l){t=l(e)}function s(l){const u=e.getProps(),d=e.getVariantContext(!0)||{},c=[],f=new Set;let v={},y=1/0;for(let x=0;x<fS;x++){const h=dS[x],p=n[h],m=u[h]!==void 0?u[h]:d[h],S=Co(m),T=h===l?p.isActive:null;T===!1&&(y=x);let E=m===d[h]&&m!==u[h]&&S;if(E&&r&&e.manuallyAnimateOnMount&&(E=!1),p.protectedKeys={...v},!p.isActive&&T===null||!m&&!p.prevProp||So(m)||typeof m=="boolean")continue;let P=mS(p.prevProp,m)||h===l&&p.isActive&&!E&&S||x>y&&S,D=!1;const N=Array.isArray(m)?m:[m];let _=N.reduce(o(h),{});T===!1&&(_={});const{prevResolvedValues:b={}}=p,F={...b,..._},A=V=>{P=!0,f.has(V)&&(D=!0,f.delete(V)),p.needsAnimating[V]=!0;const B=e.getValue(V);B&&(B.liveStyle=!1)};for(const V in F){const B=_[V],X=b[V];if(v.hasOwnProperty(V))continue;let M=!1;Cl(B)&&Cl(X)?M=!Mm(B,X):M=B!==X,M?B!=null?A(V):f.add(V):B!==void 0&&f.has(V)?A(V):p.protectedKeys[V]=!0}p.prevProp=m,p.prevResolvedValues=_,p.isActive&&(v={...v,..._}),r&&e.blockInitialAnimation&&(P=!1),P&&(!E||D)&&c.push(...N.map(V=>({animation:V,options:{type:h}})))}if(f.size){const x={};f.forEach(h=>{const p=e.getBaseTarget(h),m=e.getValue(h);m&&(m.liveStyle=!0),x[h]=p??null}),c.push({animation:x})}let w=!!c.length;return r&&(u.initial===!1||u.initial===u.animate)&&!e.manuallyAnimateOnMount&&(w=!1),r=!1,w?t(c):Promise.resolve()}function a(l,u){var d;if(n[l].isActive===u)return Promise.resolve();(d=e.variantChildren)===null||d===void 0||d.forEach(f=>{var v;return(v=f.animationState)===null||v===void 0?void 0:v.setActive(l,u)}),n[l].isActive=u;const c=s(l);for(const f in n)n[f].protectedKeys={};return c}return{animateChanges:s,setActive:a,setAnimateFunction:i,getState:()=>n,reset:()=>{n=ef(),r=!0}}}function mS(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Mm(t,e):!1}function wn(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ef(){return{animate:wn(!0),whileInView:wn(),whileHover:wn(),whileTap:wn(),whileDrag:wn(),whileFocus:wn(),exit:wn()}}class gn{constructor(t){this.isMounted=!1,this.node=t}update(){}}class vS extends gn{constructor(t){super(t),t.animationState||(t.animationState=hS(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();So(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),(t=this.unmountControls)===null||t===void 0||t.call(this)}}let gS=0;class yS extends gn{constructor(){super(...arguments),this.id=gS++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;const o=this.node.animationState.setActive("exit",!t);n&&!t&&o.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const wS={animation:{Feature:vS},exit:{Feature:yS}},gv=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function As(e,t="page"){return{point:{x:e[`${t}X`],y:e[`${t}Y`]}}}const xS=e=>t=>gv(t)&&e(t,As(t));function Et(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function _t(e,t,n,r){return Et(e,t,xS(n),r)}const tf=(e,t)=>Math.abs(e-t);function SS(e,t){const n=tf(e.x,t.x),r=tf(e.y,t.y);return Math.sqrt(n**2+r**2)}class yv{constructor(t,n,{transformPagePoint:r,contextWindow:o,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const c=ha(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,v=SS(c.offset,{x:0,y:0})>=3;if(!f&&!v)return;const{point:y}=c,{timestamp:w}=xe;this.history.push({...y,timestamp:w});const{onStart:x,onMove:h}=this.handlers;f||(x&&x(this.lastMoveEvent,c),this.startEvent=this.lastMoveEvent),h&&h(this.lastMoveEvent,c)},this.handlePointerMove=(c,f)=>{this.lastMoveEvent=c,this.lastMoveEventInfo=pa(f,this.transformPagePoint),K.update(this.updatePoint,!0)},this.handlePointerUp=(c,f)=>{this.end();const{onEnd:v,onSessionEnd:y,resumeAnimation:w}=this.handlers;if(this.dragSnapToOrigin&&w&&w(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const x=ha(c.type==="pointercancel"?this.lastMoveEventInfo:pa(f,this.transformPagePoint),this.history);this.startEvent&&v&&v(c,x),y&&y(c,x)},!gv(t))return;this.dragSnapToOrigin=i,this.handlers=n,this.transformPagePoint=r,this.contextWindow=o||window;const s=As(t),a=pa(s,this.transformPagePoint),{point:l}=a,{timestamp:u}=xe;this.history=[{...l,timestamp:u}];const{onSessionStart:d}=n;d&&d(t,ha(a,this.history)),this.removeListeners=Mt(_t(this.contextWindow,"pointermove",this.handlePointerMove),_t(this.contextWindow,"pointerup",this.handlePointerUp),_t(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Vt(this.updatePoint)}}function pa(e,t){return t?{point:t(e.point)}:e}function nf(e,t){return{x:e.x-t.x,y:e.y-t.y}}function ha({point:e},t){return{point:e,delta:nf(e,wv(t)),offset:nf(e,CS(t)),velocity:TS(t,.1)}}function CS(e){return e[0]}function wv(e){return e[e.length-1]}function TS(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const o=wv(e);for(;n>=0&&(r=e[n],!(o.timestamp-r.timestamp>bt(t)));)n--;if(!r)return{x:0,y:0};const i=Rt(o.timestamp-r.timestamp);if(i===0)return{x:0,y:0};const s={x:(o.x-r.x)/i,y:(o.y-r.y)/i};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}function xv(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const rf=xv("dragHorizontal"),of=xv("dragVertical");function Sv(e){let t=!1;if(e==="y")t=of();else if(e==="x")t=rf();else{const n=rf(),r=of();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function Cv(){const e=Sv(!0);return e?(e(),!1):!0}function tr(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}const Tv=1e-4,PS=1-Tv,ES=1+Tv,Pv=.01,kS=0-Pv,bS=0+Pv;function Qe(e){return e.max-e.min}function RS(e,t,n){return Math.abs(e-t)<=n}function sf(e,t,n,r=.5){e.origin=r,e.originPoint=oe(t.min,t.max,e.origin),e.scale=Qe(n)/Qe(t),e.translate=oe(n.min,n.max,e.origin)-e.originPoint,(e.scale>=PS&&e.scale<=ES||isNaN(e.scale))&&(e.scale=1),(e.translate>=kS&&e.translate<=bS||isNaN(e.translate))&&(e.translate=0)}function eo(e,t,n,r){sf(e.x,t.x,n.x,r?r.originX:void 0),sf(e.y,t.y,n.y,r?r.originY:void 0)}function af(e,t,n){e.min=n.min+t.min,e.max=e.min+Qe(t)}function AS(e,t,n){af(e.x,t.x,n.x),af(e.y,t.y,n.y)}function lf(e,t,n){e.min=t.min-n.min,e.max=e.min+Qe(t)}function to(e,t,n){lf(e.x,t.x,n.x),lf(e.y,t.y,n.y)}function MS(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?oe(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?oe(n,e,r.max):Math.min(e,n)),e}function uf(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function _S(e,{top:t,left:n,bottom:r,right:o}){return{x:uf(e.x,n,o),y:uf(e.y,t,r)}}function cf(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function NS(e,t){return{x:cf(e.x,t.x),y:cf(e.y,t.y)}}function LS(e,t){let n=.5;const r=Qe(e),o=Qe(t);return o>r?n=Po(t.min,t.max-r,e.min):r>o&&(n=Po(e.min,e.max-o,t.min)),dn(0,1,n)}function DS(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const _l=.35;function jS(e=_l){return e===!1?e=0:e===!0&&(e=_l),{x:df(e,"left","right"),y:df(e,"top","bottom")}}function df(e,t,n){return{min:ff(e,t),max:ff(e,n)}}function ff(e,t){return typeof e=="number"?e:e[t]||0}const pf=()=>({translate:0,scale:1,origin:0,originPoint:0}),nr=()=>({x:pf(),y:pf()}),hf=()=>({min:0,max:0}),ue=()=>({x:hf(),y:hf()});function qe(e){return[e("x"),e("y")]}function Ev({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function VS({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function OS(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function ma(e){return e===void 0||e===1}function Nl({scale:e,scaleX:t,scaleY:n}){return!ma(e)||!ma(t)||!ma(n)}function Cn(e){return Nl(e)||kv(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function kv(e){return mf(e.x)||mf(e.y)}function mf(e){return e&&e!=="0%"}function qi(e,t,n){const r=e-n,o=t*r;return n+o}function vf(e,t,n,r,o){return o!==void 0&&(e=qi(e,o,r)),qi(e,n,r)+t}function Ll(e,t=0,n=1,r,o){e.min=vf(e.min,t,n,r,o),e.max=vf(e.max,t,n,r,o)}function bv(e,{x:t,y:n}){Ll(e.x,t.translate,t.scale,t.originPoint),Ll(e.y,n.translate,n.scale,n.originPoint)}const gf=.999999999999,yf=1.0000000000001;function IS(e,t,n,r=!1){const o=n.length;if(!o)return;t.x=t.y=1;let i,s;for(let a=0;a<o;a++){i=n[a],s=i.projectionDelta;const{visualElement:l}=i.options;l&&l.props.style&&l.props.style.display==="contents"||(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&or(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,bv(e,s)),r&&Cn(i.latestValues)&&or(e,i.latestValues))}t.x<yf&&t.x>gf&&(t.x=1),t.y<yf&&t.y>gf&&(t.y=1)}function rr(e,t){e.min=e.min+t,e.max=e.max+t}function wf(e,t,n,r,o=.5){const i=oe(e.min,e.max,o);Ll(e,t,n,i,r)}function or(e,t){wf(e.x,t.x,t.scaleX,t.scale,t.originX),wf(e.y,t.y,t.scaleY,t.scale,t.originY)}function Rv(e,t){return Ev(OS(e.getBoundingClientRect(),t))}function FS(e,t,n){const r=Rv(e,n),{scroll:o}=t;return o&&(rr(r.x,o.offset.x),rr(r.y,o.offset.y)),r}const Av=({current:e})=>e?e.ownerDocument.defaultView:null,zS=new WeakMap;class BS{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ue(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const o=d=>{const{dragSnapToOrigin:c}=this.getProps();c?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(As(d,"page").point)},i=(d,c)=>{var f;const{drag:v,dragPropagation:y,onDragStart:w}=this.getProps();if(v&&!y&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=Sv(v),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),qe(h=>{let p=this.getAxisMotionValue(h).get()||0;if(yt.test(p)){const{projection:m}=this.visualElement;if(m&&m.layout){const S=m.layout.layoutBox[h];S&&(p=Qe(S)*(parseFloat(p)/100))}}this.originPoint[h]=p}),w&&K.postRender(()=>w(d,c)),(f=this.removeWillChange)===null||f===void 0||f.call(this),this.removeWillChange=Al(this.visualElement,"transform");const{animationState:x}=this.visualElement;x&&x.setActive("whileDrag",!0)},s=(d,c)=>{const{dragPropagation:f,dragDirectionLock:v,onDirectionLock:y,onDrag:w}=this.getProps();if(!f&&!this.openGlobalLock)return;const{offset:x}=c;if(v&&this.currentDirection===null){this.currentDirection=$S(x),this.currentDirection!==null&&y&&y(this.currentDirection);return}this.updateAxis("x",c.point,x),this.updateAxis("y",c.point,x),this.visualElement.render(),w&&w(d,c)},a=(d,c)=>this.stop(d,c),l=()=>qe(d=>{var c;return this.getAnimationState(d)==="paused"&&((c=this.getAxisMotionValue(d).animation)===null||c===void 0?void 0:c.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new yv(t,{onSessionStart:o,onStart:i,onMove:s,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:Av(this.visualElement)})}stop(t,n){var r;(r=this.removeWillChange)===null||r===void 0||r.call(this);const o=this.isDragging;if(this.cancel(),!o)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&K.postRender(()=>s(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:o}=this.getProps();if(!r||!ti(t,o,this.currentDirection))return;const i=this.getAxisMotionValue(t);let s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=MS(s,this.constraints[t],this.elastic[t])),i.set(s)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),o=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,i=this.constraints;n&&tr(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&o?this.constraints=_S(o.layoutBox,n):this.constraints=!1,this.elastic=jS(r),i!==this.constraints&&o&&this.constraints&&!this.hasMutatedConstraints&&qe(s=>{this.constraints!==!1&&this.getAxisMotionValue(s)&&(this.constraints[s]=DS(o.layoutBox[s],this.constraints[s]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!tr(t))return!1;const r=t.current,{projection:o}=this.visualElement;if(!o||!o.layout)return!1;const i=FS(r,o.root,this.visualElement.getTransformPagePoint());let s=NS(o.layout.layoutBox,i);if(n){const a=n(VS(s));this.hasMutatedConstraints=!!a,a&&(s=Ev(a))}return s}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:o,dragTransition:i,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},u=qe(d=>{if(!ti(d,n,this.currentDirection))return;let c=l&&l[d]||{};s&&(c={min:0,max:0});const f=o?200:1e6,v=o?40:1e7,y={type:"inertia",velocity:r?t[d]:0,bounceStiffness:f,bounceDamping:v,timeConstant:750,restDelta:1,restSpeed:10,...i,...c};return this.startAxisValueAnimation(d,y)});return Promise.all(u).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return r.start(tc(t,r,0,n,this.visualElement,!1,Al(this.visualElement,t)))}stopAnimation(){qe(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){qe(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n=`_drag${t.toUpperCase()}`,r=this.visualElement.getProps(),o=r[n];return o||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){qe(n=>{const{drag:r}=this.getProps();if(!ti(n,r,this.currentDirection))return;const{projection:o}=this.visualElement,i=this.getAxisMotionValue(n);if(o&&o.layout){const{min:s,max:a}=o.layout.layoutBox[n];i.set(t[n]-oe(s,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!tr(n)||!r||!this.constraints)return;this.stopAnimation();const o={x:0,y:0};qe(s=>{const a=this.getAxisMotionValue(s);if(a&&this.constraints!==!1){const l=a.get();o[s]=LS({min:l,max:l},this.constraints[s])}});const{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),qe(s=>{if(!ti(s,t,null))return;const a=this.getAxisMotionValue(s),{min:l,max:u}=this.constraints[s];a.set(oe(l,u,o[s]))})}addListeners(){if(!this.visualElement.current)return;zS.set(this.visualElement,this);const t=this.visualElement.current,n=_t(t,"pointerdown",l=>{const{drag:u,dragListener:d=!0}=this.getProps();u&&d&&this.start(l)}),r=()=>{const{dragConstraints:l}=this.getProps();tr(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:o}=this.visualElement,i=o.addEventListener("measure",r);o&&!o.layout&&(o.root&&o.root.updateScroll(),o.updateLayout()),K.read(r);const s=Et(window,"resize",()=>this.scalePositionWithinConstraints()),a=o.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(qe(d=>{const c=this.getAxisMotionValue(d);c&&(this.originPoint[d]+=l[d].translate,c.set(c.get()+l[d].translate))}),this.visualElement.render())});return()=>{s(),n(),i(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:o=!1,dragConstraints:i=!1,dragElastic:s=_l,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:o,dragConstraints:i,dragElastic:s,dragMomentum:a}}}function ti(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function $S(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class US extends gn{constructor(t){super(t),this.removeGroupControls=Re,this.removeListeners=Re,this.controls=new BS(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Re}unmount(){this.removeGroupControls(),this.removeListeners()}}const xf=e=>(t,n)=>{e&&K.postRender(()=>e(t,n))};class WS extends gn{constructor(){super(...arguments),this.removePointerDownListener=Re}onPointerDown(t){this.session=new yv(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Av(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:o}=this.node.getProps();return{onSessionStart:xf(t),onStart:xf(n),onMove:r,onEnd:(i,s)=>{delete this.session,o&&K.postRender(()=>o(i,s))}}}mount(){this.removePointerDownListener=_t(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Ms=g.createContext(null);function HS(){const e=g.useContext(Ms);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,o=g.useId();g.useEffect(()=>r(o),[]);const i=g.useCallback(()=>n&&n(o),[o,n]);return!t&&n?[!1,i]:[!0]}const rc=g.createContext({}),Mv=g.createContext({}),wi={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Sf(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const Dr={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(O.test(e))e=parseFloat(e);else return e;const n=Sf(e,t.target.x),r=Sf(e,t.target.y);return`${n}% ${r}%`}},KS={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,o=fn.parse(e);if(o.length>5)return r;const i=fn.createTransformer(e),s=typeof o[0]!="number"?1:0,a=n.x.scale*t.x,l=n.y.scale*t.y;o[0+s]/=a,o[1+s]/=l;const u=oe(a,l,.5);return typeof o[2+s]=="number"&&(o[2+s]/=u),typeof o[3+s]=="number"&&(o[3+s]/=u),i(o)}},Ji={};function GS(e){Object.assign(Ji,e)}const{schedule:oc,cancel:sk}=_m(queueMicrotask,!1);class QS extends g.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:o}=this.props,{projection:i}=t;GS(YS),i&&(n.group&&n.group.add(i),r&&r.register&&o&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),wi.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:o,isPresent:i}=this.props,s=r.projection;return s&&(s.isPresent=i,o||t.layoutDependency!==n||n===void 0?s.willUpdate():this.safeToRemove(),t.isPresent!==i&&(i?s.promote():s.relegate()||K.postRender(()=>{const a=s.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),oc.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:o}=t;o&&(o.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(o),r&&r.deregister&&r.deregister(o))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function _v(e){const[t,n]=HS(),r=g.useContext(rc);return C.jsx(QS,{...e,layoutGroup:r,switchLayoutGroup:g.useContext(Mv),isPresent:t,safeToRemove:n})}const YS={borderRadius:{...Dr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Dr,borderTopRightRadius:Dr,borderBottomLeftRadius:Dr,borderBottomRightRadius:Dr,boxShadow:KS},Nv=["TopLeft","TopRight","BottomLeft","BottomRight"],XS=Nv.length,Cf=e=>typeof e=="string"?parseFloat(e):e,Tf=e=>typeof e=="number"||O.test(e);function ZS(e,t,n,r,o,i){o?(e.opacity=oe(0,n.opacity!==void 0?n.opacity:1,qS(r)),e.opacityExit=oe(t.opacity!==void 0?t.opacity:1,0,JS(r))):i&&(e.opacity=oe(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let s=0;s<XS;s++){const a=`border${Nv[s]}Radius`;let l=Pf(t,a),u=Pf(n,a);if(l===void 0&&u===void 0)continue;l||(l=0),u||(u=0),l===0||u===0||Tf(l)===Tf(u)?(e[a]=Math.max(oe(Cf(l),Cf(u),r),0),(yt.test(u)||yt.test(l))&&(e[a]+="%")):e[a]=u}(t.rotate||n.rotate)&&(e.rotate=oe(t.rotate||0,n.rotate||0,r))}function Pf(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const qS=Lv(0,.5,ov),JS=Lv(.5,.95,Re);function Lv(e,t,n){return r=>r<e?0:r>t?1:n(Po(e,t,r))}function Ef(e,t){e.min=t.min,e.max=t.max}function Ze(e,t){Ef(e.x,t.x),Ef(e.y,t.y)}function kf(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function bf(e,t,n,r,o){return e-=t,e=qi(e,1/n,r),o!==void 0&&(e=qi(e,1/o,r)),e}function eC(e,t=0,n=1,r=.5,o,i=e,s=e){if(yt.test(t)&&(t=parseFloat(t),t=oe(s.min,s.max,t/100)-s.min),typeof t!="number")return;let a=oe(i.min,i.max,r);e===i&&(a-=t),e.min=bf(e.min,t,n,a,o),e.max=bf(e.max,t,n,a,o)}function Rf(e,t,[n,r,o],i,s){eC(e,t[n],t[r],t[o],t.scale,i,s)}const tC=["x","scaleX","originX"],nC=["y","scaleY","originY"];function Af(e,t,n,r){Rf(e.x,t,tC,n?n.x:void 0,r?r.x:void 0),Rf(e.y,t,nC,n?n.y:void 0,r?r.y:void 0)}function Mf(e){return e.translate===0&&e.scale===1}function Dv(e){return Mf(e.x)&&Mf(e.y)}function _f(e,t){return e.min===t.min&&e.max===t.max}function rC(e,t){return _f(e.x,t.x)&&_f(e.y,t.y)}function Nf(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function jv(e,t){return Nf(e.x,t.x)&&Nf(e.y,t.y)}function Lf(e){return Qe(e.x)/Qe(e.y)}function Df(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class oC{constructor(){this.members=[]}add(t){ks(this.members,t),t.scheduleRender()}remove(t){if(bs(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(o=>t===o);if(n===0)return!1;let r;for(let o=n;o>=0;o--){const i=this.members[o];if(i.isPresent!==!1){r=i;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:o}=t.options;o===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function iC(e,t,n){let r="";const o=e.x.translate/t.x,i=e.y.translate/t.y,s=(n==null?void 0:n.z)||0;if((o||i||s)&&(r=`translate3d(${o}px, ${i}px, ${s}px) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{transformPerspective:u,rotate:d,rotateX:c,rotateY:f,skewX:v,skewY:y}=n;u&&(r=`perspective(${u}px) ${r}`),d&&(r+=`rotate(${d}deg) `),c&&(r+=`rotateX(${c}deg) `),f&&(r+=`rotateY(${f}deg) `),v&&(r+=`skewX(${v}deg) `),y&&(r+=`skewY(${y}deg) `)}const a=e.x.scale*t.x,l=e.y.scale*t.y;return(a!==1||l!==1)&&(r+=`scale(${a}, ${l})`),r||"none"}const sC=(e,t)=>e.depth-t.depth;class aC{constructor(){this.children=[],this.isDirty=!1}add(t){ks(this.children,t),this.isDirty=!0}remove(t){bs(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(sC),this.isDirty=!1,this.children.forEach(t)}}function xi(e){const t=be(e)?e.get():e;return Jx(t)?t.toValue():t}function lC(e,t){const n=At.now(),r=({timestamp:o})=>{const i=o-n;i>=t&&(Vt(r),e(i-t))};return K.read(r,!0),()=>Vt(r)}function uC(e){return e instanceof SVGElement&&e.tagName!=="svg"}function cC(e,t,n){const r=be(e)?e:Eo(e);return r.start(tc("",r,t,n)),r.animation}const Tn={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},$r=typeof window<"u"&&window.MotionDebug!==void 0,va=["","X","Y","Z"],dC={visibility:"hidden"},jf=1e3;let fC=0;function ga(e,t,n,r){const{latestValues:o}=t;o[e]&&(n[e]=o[e],t.setStaticValue(e,0),r&&(r[e]=0))}function Vv(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const n=hv(t);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:o,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",K,!(o||i))}const{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&Vv(r)}function Ov({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:o}){return class{constructor(s={},a=t==null?void 0:t()){this.id=fC++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,$r&&(Tn.totalNodes=Tn.resolvedTargetDeltas=Tn.recalculatedProjection=0),this.nodes.forEach(mC),this.nodes.forEach(xC),this.nodes.forEach(SC),this.nodes.forEach(vC),$r&&window.MotionDebug.record(Tn)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=s,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new aC)}addEventListener(s,a){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new nc),this.eventHandlers.get(s).add(a)}notifyListeners(s,...a){const l=this.eventHandlers.get(s);l&&l.notify(...a)}hasListeners(s){return this.eventHandlers.has(s)}mount(s,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=uC(s),this.instance=s;const{layoutId:l,layout:u,visualElement:d}=this.options;if(d&&!d.current&&d.mount(s),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(u||l)&&(this.isLayoutDirty=!0),e){let c;const f=()=>this.root.updateBlockedByResize=!1;e(s,()=>{this.root.updateBlockedByResize=!0,c&&c(),c=lC(f,250),wi.hasAnimatedSinceResize&&(wi.hasAnimatedSinceResize=!1,this.nodes.forEach(Of))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&d&&(l||u)&&this.addEventListener("didUpdate",({delta:c,hasLayoutChanged:f,hasRelativeTargetChanged:v,layout:y})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const w=this.options.transition||d.getDefaultTransition()||kC,{onLayoutAnimationStart:x,onLayoutAnimationComplete:h}=d.getProps(),p=!this.targetLayout||!jv(this.targetLayout,y)||v,m=!f&&v;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||m||f&&(p||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(c,m);const S={...Wu(w,"layout"),onPlay:x,onComplete:h};(d.shouldReduceMotion||this.options.layoutRoot)&&(S.delay=0,S.type=!1),this.startAnimation(S)}else f||Of(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=y})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const s=this.getStack();s&&s.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Vt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(CC),this.animationId++)}getTransformTemplate(){const{visualElement:s}=this.options;return s&&s.getProps().transformTemplate}willUpdate(s=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Vv(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let d=0;d<this.path.length;d++){const c=this.path[d];c.shouldResetTransform=!0,c.updateScroll("snapshot"),c.options.layoutRoot&&c.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Vf);return}this.isUpdating||this.nodes.forEach(yC),this.isUpdating=!1,this.nodes.forEach(wC),this.nodes.forEach(pC),this.nodes.forEach(hC),this.clearAllSnapshots();const a=At.now();xe.delta=dn(0,1e3/60,a-xe.timestamp),xe.timestamp=a,xe.isProcessing=!0,la.update.process(xe),la.preRender.process(xe),la.render.process(xe),xe.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,oc.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(gC),this.sharedNodes.forEach(TC)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,K.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){K.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const s=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ue(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,s?s.layoutBox:void 0)}updateScroll(s="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(a=!1),a){const l=r(this.instance);this.scroll={animationId:this.root.animationId,phase:s,isRoot:l,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!o)return;const s=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!Dv(this.projectionDelta),l=this.getTransformTemplate(),u=l?l(this.latestValues,""):void 0,d=u!==this.prevTransformTemplateValue;s&&(a||Cn(this.latestValues)||d)&&(o(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return s&&(l=this.removeTransform(l)),bC(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var s;const{visualElement:a}=this.options;if(!a)return ue();const l=a.measureViewportBox();if(!(((s=this.scroll)===null||s===void 0?void 0:s.wasRoot)||this.path.some(RC))){const{scroll:d}=this.root;d&&(rr(l.x,d.offset.x),rr(l.y,d.offset.y))}return l}removeElementScroll(s){var a;const l=ue();if(Ze(l,s),!((a=this.scroll)===null||a===void 0)&&a.wasRoot)return l;for(let u=0;u<this.path.length;u++){const d=this.path[u],{scroll:c,options:f}=d;d!==this.root&&c&&f.layoutScroll&&(c.wasRoot&&Ze(l,s),rr(l.x,c.offset.x),rr(l.y,c.offset.y))}return l}applyTransform(s,a=!1){const l=ue();Ze(l,s);for(let u=0;u<this.path.length;u++){const d=this.path[u];!a&&d.options.layoutScroll&&d.scroll&&d!==d.root&&or(l,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),Cn(d.latestValues)&&or(l,d.latestValues)}return Cn(this.latestValues)&&or(l,this.latestValues),l}removeTransform(s){const a=ue();Ze(a,s);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!Cn(u.latestValues))continue;Nl(u.latestValues)&&u.updateSnapshot();const d=ue(),c=u.measurePageBox();Ze(d,c),Af(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,d)}return Cn(this.latestValues)&&Af(a,this.latestValues),a}setTargetDelta(s){this.targetDelta=s,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==xe.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(s=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==l;if(!(s||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:c,layoutId:f}=this.options;if(!(!this.layout||!(c||f))){if(this.resolvedRelativeTargetAt=xe.timestamp,!this.targetDelta&&!this.relativeTarget){const v=this.getClosestProjectingParent();v&&v.layout&&this.animationProgress!==1?(this.relativeParent=v,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ue(),this.relativeTargetOrigin=ue(),to(this.relativeTargetOrigin,this.layout.layoutBox,v.layout.layoutBox),Ze(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=ue(),this.targetWithTransforms=ue()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),AS(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Ze(this.target,this.layout.layoutBox),bv(this.target,this.targetDelta)):Ze(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const v=this.getClosestProjectingParent();v&&!!v.resumingFrom==!!this.resumingFrom&&!v.options.layoutScroll&&v.target&&this.animationProgress!==1?(this.relativeParent=v,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ue(),this.relativeTargetOrigin=ue(),to(this.relativeTargetOrigin,this.target,v.target),Ze(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}$r&&Tn.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Nl(this.parent.latestValues)||kv(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var s;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let u=!0;if((this.isProjectionDirty||!((s=this.parent)===null||s===void 0)&&s.isProjectionDirty)&&(u=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===xe.timestamp&&(u=!1),u)return;const{layout:d,layoutId:c}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(d||c))return;Ze(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,v=this.treeScale.y;IS(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=ue());const{target:y}=a;if(!y){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(kf(this.prevProjectionDelta.x,this.projectionDelta.x),kf(this.prevProjectionDelta.y,this.projectionDelta.y)),eo(this.projectionDelta,this.layoutCorrected,y,this.latestValues),(this.treeScale.x!==f||this.treeScale.y!==v||!Df(this.projectionDelta.x,this.prevProjectionDelta.x)||!Df(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",y)),$r&&Tn.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){var a;if((a=this.options.visualElement)===null||a===void 0||a.scheduleRender(),s){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=nr(),this.projectionDelta=nr(),this.projectionDeltaWithTransform=nr()}setAnimationOrigin(s,a=!1){const l=this.snapshot,u=l?l.latestValues:{},d={...this.latestValues},c=nr();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=ue(),v=l?l.source:void 0,y=this.layout?this.layout.source:void 0,w=v!==y,x=this.getStack(),h=!x||x.members.length<=1,p=!!(w&&!h&&this.options.crossfade===!0&&!this.path.some(EC));this.animationProgress=0;let m;this.mixTargetDelta=S=>{const T=S/1e3;If(c.x,s.x,T),If(c.y,s.y,T),this.setTargetDelta(c),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(to(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),PC(this.relativeTarget,this.relativeTargetOrigin,f,T),m&&rC(this.relativeTarget,m)&&(this.isProjectionDirty=!1),m||(m=ue()),Ze(m,this.relativeTarget)),w&&(this.animationValues=d,ZS(d,u,this.latestValues,T,p,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=T},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(s){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Vt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=K.update(()=>{wi.hasAnimatedSinceResize=!0,this.currentAnimation=cC(0,jf,{...s,onUpdate:a=>{this.mixTargetDelta(a),s.onUpdate&&s.onUpdate(a)},onComplete:()=>{s.onComplete&&s.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const s=this.getStack();s&&s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(jf),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:a,target:l,layout:u,latestValues:d}=s;if(!(!a||!l||!u)){if(this!==s&&this.layout&&u&&Iv(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||ue();const c=Qe(this.layout.layoutBox.x);l.x.min=s.target.x.min,l.x.max=l.x.min+c;const f=Qe(this.layout.layoutBox.y);l.y.min=s.target.y.min,l.y.max=l.y.min+f}Ze(a,l),or(a,d),eo(this.projectionDeltaWithTransform,this.layoutCorrected,a,d)}}registerSharedNode(s,a){this.sharedNodes.has(s)||this.sharedNodes.set(s,new oC),this.sharedNodes.get(s).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:a}=this.options;return a?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:a}=this.options;return a?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:a,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),s&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetSkewAndRotation(){const{visualElement:s}=this.options;if(!s)return;let a=!1;const{latestValues:l}=s;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const u={};l.z&&ga("z",s,u,this.animationValues);for(let d=0;d<va.length;d++)ga(`rotate${va[d]}`,s,u,this.animationValues),ga(`skew${va[d]}`,s,u,this.animationValues);s.render();for(const d in u)s.setStaticValue(d,u[d]),this.animationValues&&(this.animationValues[d]=u[d]);s.scheduleRender()}getProjectionStyles(s){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return dC;const u={visibility:""},d=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=xi(s==null?void 0:s.pointerEvents)||"",u.transform=d?d(this.latestValues,""):"none",u;const c=this.getLead();if(!this.projectionDelta||!this.layout||!c.target){const w={};return this.options.layoutId&&(w.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,w.pointerEvents=xi(s==null?void 0:s.pointerEvents)||""),this.hasProjected&&!Cn(this.latestValues)&&(w.transform=d?d({},""):"none",this.hasProjected=!1),w}const f=c.animationValues||c.latestValues;this.applyTransformsToTarget(),u.transform=iC(this.projectionDeltaWithTransform,this.treeScale,f),d&&(u.transform=d(f,u.transform));const{x:v,y}=this.projectionDelta;u.transformOrigin=`${v.origin*100}% ${y.origin*100}% 0`,c.animationValues?u.opacity=c===this?(l=(a=f.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:u.opacity=c===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const w in Ji){if(f[w]===void 0)continue;const{correct:x,applyTo:h}=Ji[w],p=u.transform==="none"?f[w]:x(f[w],c);if(h){const m=h.length;for(let S=0;S<m;S++)u[h[S]]=p}else u[w]=p}return this.options.layoutId&&(u.pointerEvents=c===this?xi(s==null?void 0:s.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var a;return(a=s.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(Vf),this.root.sharedNodes.clear()}}}function pC(e){e.updateLayout()}function hC(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:o}=e.layout,{animationType:i}=e.options,s=n.source!==e.layout.source;i==="size"?qe(c=>{const f=s?n.measuredBox[c]:n.layoutBox[c],v=Qe(f);f.min=r[c].min,f.max=f.min+v}):Iv(i,n.layoutBox,r)&&qe(c=>{const f=s?n.measuredBox[c]:n.layoutBox[c],v=Qe(r[c]);f.max=f.min+v,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[c].max=e.relativeTarget[c].min+v)});const a=nr();eo(a,r,n.layoutBox);const l=nr();s?eo(l,e.applyTransform(o,!0),n.measuredBox):eo(l,r,n.layoutBox);const u=!Dv(a);let d=!1;if(!e.resumeFrom){const c=e.getClosestProjectingParent();if(c&&!c.resumeFrom){const{snapshot:f,layout:v}=c;if(f&&v){const y=ue();to(y,n.layoutBox,f.layoutBox);const w=ue();to(w,r,v.layoutBox),jv(y,w)||(d=!0),c.options.layoutRoot&&(e.relativeTarget=w,e.relativeTargetOrigin=y,e.relativeParent=c)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:u,hasRelativeTargetChanged:d})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function mC(e){$r&&Tn.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function vC(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function gC(e){e.clearSnapshot()}function Vf(e){e.clearMeasurements()}function yC(e){e.isLayoutDirty=!1}function wC(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Of(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function xC(e){e.resolveTargetDelta()}function SC(e){e.calcProjection()}function CC(e){e.resetSkewAndRotation()}function TC(e){e.removeLeadSnapshot()}function If(e,t,n){e.translate=oe(t.translate,0,n),e.scale=oe(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function Ff(e,t,n,r){e.min=oe(t.min,n.min,r),e.max=oe(t.max,n.max,r)}function PC(e,t,n,r){Ff(e.x,t.x,n.x,r),Ff(e.y,t.y,n.y,r)}function EC(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const kC={duration:.45,ease:[.4,0,.1,1]},zf=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),Bf=zf("applewebkit/")&&!zf("chrome/")?Math.round:Re;function $f(e){e.min=Bf(e.min),e.max=Bf(e.max)}function bC(e){$f(e.x),$f(e.y)}function Iv(e,t,n){return e==="position"||e==="preserve-aspect"&&!RS(Lf(t),Lf(n),.2)}function RC(e){var t;return e!==e.root&&((t=e.scroll)===null||t===void 0?void 0:t.wasRoot)}const AC=Ov({attachResizeListener:(e,t)=>Et(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ya={current:void 0},Fv=Ov({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!ya.current){const e=new AC({});e.mount(window),e.setOptions({layoutScroll:!0}),ya.current=e}return ya.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),MC={pan:{Feature:WS},drag:{Feature:US,ProjectionNode:Fv,MeasureLayout:_v}};function Uf(e,t){const n=t?"pointerenter":"pointerleave",r=t?"onHoverStart":"onHoverEnd",o=(i,s)=>{if(i.pointerType==="touch"||Cv())return;const a=e.getProps();e.animationState&&a.whileHover&&e.animationState.setActive("whileHover",t);const l=a[r];l&&K.postRender(()=>l(i,s))};return _t(e.current,n,o,{passive:!e.getProps()[r]})}class _C extends gn{mount(){this.unmount=Mt(Uf(this.node,!0),Uf(this.node,!1))}unmount(){}}class NC extends gn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Mt(Et(this.node.current,"focus",()=>this.onFocus()),Et(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const zv=(e,t)=>t?e===t?!0:zv(e,t.parentElement):!1;function wa(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,As(n))}class LC extends gn{constructor(){super(...arguments),this.removeStartListeners=Re,this.removeEndListeners=Re,this.removeAccessibleListeners=Re,this.startPointerPress=(t,n)=>{if(this.isPressing)return;this.removeEndListeners();const r=this.node.getProps(),i=_t(window,"pointerup",(a,l)=>{if(!this.checkPressEnd())return;const{onTap:u,onTapCancel:d,globalTapTarget:c}=this.node.getProps(),f=!c&&!zv(this.node.current,a.target)?d:u;f&&K.update(()=>f(a,l))},{passive:!(r.onTap||r.onPointerUp)}),s=_t(window,"pointercancel",(a,l)=>this.cancelPress(a,l),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=Mt(i,s),this.startPress(t,n)},this.startAccessiblePress=()=>{const t=i=>{if(i.key!=="Enter"||this.isPressing)return;const s=a=>{a.key!=="Enter"||!this.checkPressEnd()||wa("up",(l,u)=>{const{onTap:d}=this.node.getProps();d&&K.postRender(()=>d(l,u))})};this.removeEndListeners(),this.removeEndListeners=Et(this.node.current,"keyup",s),wa("down",(a,l)=>{this.startPress(a,l)})},n=Et(this.node.current,"keydown",t),r=()=>{this.isPressing&&wa("cancel",(i,s)=>this.cancelPress(i,s))},o=Et(this.node.current,"blur",r);this.removeAccessibleListeners=Mt(n,o)}}startPress(t,n){this.isPressing=!0;const{onTapStart:r,whileTap:o}=this.node.getProps();o&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&K.postRender(()=>r(t,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!Cv()}cancelPress(t,n){if(!this.checkPressEnd())return;const{onTapCancel:r}=this.node.getProps();r&&K.postRender(()=>r(t,n))}mount(){const t=this.node.getProps(),n=_t(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=Et(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=Mt(n,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const Dl=new WeakMap,xa=new WeakMap,DC=e=>{const t=Dl.get(e.target);t&&t(e)},jC=e=>{e.forEach(DC)};function VC({root:e,...t}){const n=e||document;xa.has(n)||xa.set(n,{});const r=xa.get(n),o=JSON.stringify(t);return r[o]||(r[o]=new IntersectionObserver(jC,{root:e,...t})),r[o]}function OC(e,t,n){const r=VC(t);return Dl.set(e,n),r.observe(e),()=>{Dl.delete(e),r.unobserve(e)}}const IC={some:0,all:1};class FC extends gn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:o="some",once:i}=t,s={root:n?n.current:void 0,rootMargin:r,threshold:typeof o=="number"?o:IC[o]},a=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,i&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:d,onViewportLeave:c}=this.node.getProps(),f=u?d:c;f&&f(l)};return OC(this.node.current,s,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(zC(t,n))&&this.startObserver()}unmount(){}}function zC({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const BC={inView:{Feature:FC},tap:{Feature:LC},focus:{Feature:NC},hover:{Feature:_C}},$C={layout:{ProjectionNode:Fv,MeasureLayout:_v}},ic=g.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),_s=g.createContext({}),sc=typeof window<"u",Bv=sc?g.useLayoutEffect:g.useEffect,$v=g.createContext({strict:!1});let Wf=!1;function UC(e,t,n,r,o){var i;const{visualElement:s}=g.useContext(_s),a=g.useContext($v),l=g.useContext(Ms),u=g.useContext(ic).reducedMotion,d=g.useRef();r=r||a.renderer,!d.current&&r&&(d.current=r(e,{visualState:t,parent:s,props:n,presenceContext:l,blockInitialAnimation:l?l.initial===!1:!1,reducedMotionConfig:u}));const c=d.current,f=g.useContext(Mv);c&&!c.projection&&o&&(c.type==="html"||c.type==="svg")&&HC(d.current,n,o,f),g.useInsertionEffect(()=>{c&&c.update(n,l)});const v=n[pv],y=g.useRef(!!v&&!window.MotionHandoffIsComplete&&((i=window.MotionHasOptimisedAnimation)===null||i===void 0?void 0:i.call(window,v)));return Bv(()=>{c&&(c.updateFeatures(),oc.render(c.render),y.current&&c.animationState&&c.animationState.animateChanges())}),g.useEffect(()=>{c&&(!y.current&&c.animationState&&c.animationState.animateChanges(),y.current=!1,Wf||(Wf=!0,queueMicrotask(WC)))}),c}function WC(){window.MotionHandoffIsComplete=!0}function HC(e,t,n,r){const{layoutId:o,layout:i,drag:s,dragConstraints:a,layoutScroll:l,layoutRoot:u}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:Uv(e.parent)),e.projection.setOptions({layoutId:o,layout:i,alwaysMeasureLayout:!!s||a&&tr(a),visualElement:e,animationType:typeof i=="string"?i:"both",initialPromotionConfig:r,layoutScroll:l,layoutRoot:u})}function Uv(e){if(e)return e.options.allowProjection!==!1?e.projection:Uv(e.parent)}function KC(e,t,n){return g.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):tr(n)&&(n.current=r))},[t])}function Ns(e){return So(e.animate)||Uu.some(t=>Co(e[t]))}function Wv(e){return!!(Ns(e)||e.variants)}function GC(e,t){if(Ns(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Co(n)?n:void 0,animate:Co(r)?r:void 0}}return e.inherit!==!1?t:{}}function QC(e){const{initial:t,animate:n}=GC(e,g.useContext(_s));return g.useMemo(()=>({initial:t,animate:n}),[Hf(t),Hf(n)])}function Hf(e){return Array.isArray(e)?e.join(" "):e}const Kf={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},xr={};for(const e in Kf)xr[e]={isEnabled:t=>Kf[e].some(n=>!!t[n])};function YC(e){for(const t in e)xr[t]={...xr[t],...e[t]}}const XC=Symbol.for("motionComponentSymbol");function ZC({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:o}){e&&YC(e);function i(a,l){let u;const d={...g.useContext(ic),...a,layoutId:qC(a)},{isStatic:c}=d,f=QC(a),v=r(a,c);if(!c&&sc){JC();const y=eT(d);u=y.MeasureLayout,f.visualElement=UC(o,v,d,t,y.ProjectionNode)}return C.jsxs(_s.Provider,{value:f,children:[u&&f.visualElement?C.jsx(u,{visualElement:f.visualElement,...d}):null,n(o,a,KC(v,f.visualElement,l),v,c,f.visualElement)]})}const s=g.forwardRef(i);return s[XC]=o,s}function qC({layoutId:e}){const t=g.useContext(rc).id;return t&&e!==void 0?t+"-"+e:e}function JC(e,t){g.useContext($v).strict}function eT(e){const{drag:t,layout:n}=xr;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t!=null&&t.isEnabled(e)||n!=null&&n.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const tT=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function ac(e){return typeof e!="string"||e.includes("-")?!1:!!(tT.indexOf(e)>-1||/[A-Z]/u.test(e))}function Hv(e,{style:t,vars:n},r,o){Object.assign(e.style,t,o&&o.getProjectionStyles(r));for(const i in n)e.style.setProperty(i,n[i])}const Kv=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Gv(e,t,n,r){Hv(e,t,void 0,r);for(const o in t.attrs)e.setAttribute(Kv.has(o)?o:Rs(o),t.attrs[o])}function Qv(e,{layout:t,layoutId:n}){return vn.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!Ji[e]||e==="opacity")}function lc(e,t,n){var r;const{style:o}=e,i={};for(const s in o)(be(o[s])||t.style&&be(t.style[s])||Qv(s,e)||((r=n==null?void 0:n.getValue(s))===null||r===void 0?void 0:r.liveStyle)!==void 0)&&(i[s]=o[s]);return n&&o&&typeof o.willChange=="string"&&(n.applyWillChange=!1),i}function Yv(e,t,n){const r=lc(e,t,n);for(const o in e)if(be(e[o])||be(t[o])){const i=_o.indexOf(o)!==-1?"attr"+o.charAt(0).toUpperCase()+o.substring(1):o;r[i]=e[o]}return r}function uc(e){const t=g.useRef(null);return t.current===null&&(t.current=e()),t.current}function nT({applyWillChange:e=!1,scrapeMotionValuesFromProps:t,createRenderState:n,onMount:r},o,i,s,a){const l={latestValues:oT(o,i,s,a?!1:e,t),renderState:n()};return r&&(l.mount=u=>r(o,u,l)),l}const Xv=e=>(t,n)=>{const r=g.useContext(_s),o=g.useContext(Ms),i=()=>nT(e,t,r,o,n);return n?i():uc(i)};function rT(e,t){const n=mv(t);n&&ks(e,n)}function Gf(e,t,n){const r=Array.isArray(t)?t:[t];for(let o=0;o<r.length;o++){const i=Bu(e,r[o]);if(i){const{transitionEnd:s,transition:a,...l}=i;n(l,s)}}}function oT(e,t,n,r,o){var i;const s={},a=[],l=r&&((i=e.style)===null||i===void 0?void 0:i.willChange)===void 0,u=o(e,{});for(const x in u)s[x]=xi(u[x]);let{initial:d,animate:c}=e;const f=Ns(e),v=Wv(e);t&&v&&!f&&e.inherit!==!1&&(d===void 0&&(d=t.initial),c===void 0&&(c=t.animate));let y=n?n.initial===!1:!1;y=y||d===!1;const w=y?c:d;return w&&typeof w!="boolean"&&!So(w)&&Gf(e,w,(x,h)=>{for(const p in x){let m=x[p];if(Array.isArray(m)){const S=y?m.length-1:0;m=m[S]}m!==null&&(s[p]=m)}for(const p in h)s[p]=h[p]}),l&&(c&&d!==!1&&!So(c)&&Gf(e,c,x=>{for(const h in x)rT(a,h)}),a.length&&(s.willChange=a.join(","))),s}const cc=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),Zv=()=>({...cc(),attrs:{}}),qv=(e,t)=>t&&typeof e=="number"?t.transform(e):e,iT={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},sT=_o.length;function aT(e,t,n){let r="",o=!0;for(let i=0;i<sT;i++){const s=_o[i],a=e[s];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(s.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||n){const u=qv(a,Yu[s]);if(!l){o=!1;const d=iT[s]||s;r+=`${d}(${u}) `}n&&(t[s]=u)}}return r=r.trim(),n?r=n(t,o?"":r):o&&(r="none"),r}function dc(e,t,n){const{style:r,vars:o,transformOrigin:i}=e;let s=!1,a=!1;for(const l in t){const u=t[l];if(vn.has(l)){s=!0;continue}else if(Vm(l)){o[l]=u;continue}else{const d=qv(u,Yu[l]);l.startsWith("origin")?(a=!0,i[l]=d):r[l]=d}}if(t.transform||(s||n?r.transform=aT(t,e.transform,n):r.transform&&(r.transform="none")),a){const{originX:l="50%",originY:u="50%",originZ:d=0}=i;r.transformOrigin=`${l} ${u} ${d}`}}function Qf(e,t,n){return typeof e=="string"?e:O.transform(t+n*e)}function lT(e,t,n){const r=Qf(t,e.x,e.width),o=Qf(n,e.y,e.height);return`${r} ${o}`}const uT={offset:"stroke-dashoffset",array:"stroke-dasharray"},cT={offset:"strokeDashoffset",array:"strokeDasharray"};function dT(e,t,n=1,r=0,o=!0){e.pathLength=1;const i=o?uT:cT;e[i.offset]=O.transform(-r);const s=O.transform(t),a=O.transform(n);e[i.array]=`${s} ${a}`}function fc(e,{attrX:t,attrY:n,attrScale:r,originX:o,originY:i,pathLength:s,pathSpacing:a=1,pathOffset:l=0,...u},d,c){if(dc(e,u,c),d){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:f,style:v,dimensions:y}=e;f.transform&&(y&&(v.transform=f.transform),delete f.transform),y&&(o!==void 0||i!==void 0||v.transform)&&(v.transformOrigin=lT(y,o!==void 0?o:.5,i!==void 0?i:.5)),t!==void 0&&(f.x=t),n!==void 0&&(f.y=n),r!==void 0&&(f.scale=r),s!==void 0&&dT(f,s,a,l,!1)}const pc=e=>typeof e=="string"&&e.toLowerCase()==="svg",fT={useVisualState:Xv({scrapeMotionValuesFromProps:Yv,createRenderState:Zv,onMount:(e,t,{renderState:n,latestValues:r})=>{K.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),K.render(()=>{fc(n,r,pc(t.tagName),e.transformTemplate),Gv(t,n)})}})},pT={useVisualState:Xv({applyWillChange:!0,scrapeMotionValuesFromProps:lc,createRenderState:cc})};function Jv(e,t,n){for(const r in t)!be(t[r])&&!Qv(r,n)&&(e[r]=t[r])}function hT({transformTemplate:e},t){return g.useMemo(()=>{const n=cc();return dc(n,t,e),Object.assign({},n.vars,n.style)},[t])}function mT(e,t){const n=e.style||{},r={};return Jv(r,n,e),Object.assign(r,hT(e,t)),r}function vT(e,t){const n={},r=mT(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}const gT=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function es(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||gT.has(e)}let eg=e=>!es(e);function yT(e){e&&(eg=t=>t.startsWith("on")?!es(t):e(t))}try{yT(require("@emotion/is-prop-valid").default)}catch{}function wT(e,t,n){const r={};for(const o in e)o==="values"&&typeof e.values=="object"||(eg(o)||n===!0&&es(o)||!t&&!es(o)||e.draggable&&o.startsWith("onDrag"))&&(r[o]=e[o]);return r}function xT(e,t,n,r){const o=g.useMemo(()=>{const i=Zv();return fc(i,t,pc(r),e.transformTemplate),{...i.attrs,style:{...i.style}}},[t]);if(e.style){const i={};Jv(i,e.style,e),o.style={...i,...o.style}}return o}function ST(e=!1){return(n,r,o,{latestValues:i},s)=>{const l=(ac(n)?xT:vT)(r,i,s,n),u=wT(r,typeof n=="string",e),d=n!==g.Fragment?{...u,...l,ref:o}:{},{children:c}=r,f=g.useMemo(()=>be(c)?c.get():c,[c]);return g.createElement(n,{...d,children:f})}}function CT(e,t){return function(r,{forwardMotionProps:o}={forwardMotionProps:!1}){const s={...ac(r)?fT:pT,preloadedFeatures:e,useRender:ST(o),createVisualElement:t,Component:r};return ZC(s)}}const jl={current:null},tg={current:!1};function TT(){if(tg.current=!0,!!sc)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>jl.current=e.matches;e.addListener(t),t()}else jl.current=!1}function PT(e,t,n){for(const r in t){const o=t[r],i=n[r];if(be(o))e.addValue(r,o);else if(be(i))e.addValue(r,Eo(o,{owner:e}));else if(i!==o)if(e.hasValue(r)){const s=e.getValue(r);s.liveStyle===!0?s.jump(o):s.hasAnimated||s.set(o)}else{const s=e.getStaticValue(r);e.addValue(r,Eo(s!==void 0?s:o,{owner:e}))}}for(const r in n)t[r]===void 0&&e.removeValue(r);return t}const Yf=new WeakMap,ET=[...Fm,Ee,fn],kT=e=>ET.find(Im(e)),Xf=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],bT=Uu.length;class RT{scrapeMotionValuesFromProps(t,n,r){return{}}constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:o,blockInitialAnimation:i,visualState:s},a={}){this.applyWillChange=!1,this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Gu,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.isRenderScheduled=!1,this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.isRenderScheduled=!1,this.scheduleRender=()=>{this.isRenderScheduled||(this.isRenderScheduled=!0,K.render(this.render,!1,!0))};const{latestValues:l,renderState:u}=s;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=u,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=o,this.options=a,this.blockInitialAnimation=!!i,this.isControllingVariants=Ns(n),this.isVariantNode=Wv(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:d,...c}=this.scrapeMotionValuesFromProps(n,{},this);for(const f in c){const v=c[f];l[f]!==void 0&&be(v)&&v.set(l[f],!1)}}mount(t){this.current=t,Yf.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),tg.current||TT(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:jl.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Yf.delete(this.current),this.projection&&this.projection.unmount(),Vt(this.notifyUpdate),Vt(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const n=this.features[t];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(t,n){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const r=vn.has(t),o=n.on("change",a=>{this.latestValues[t]=a,this.props.onUpdate&&K.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),i=n.on("renderRequest",this.scheduleRender);let s;window.MotionCheckAppearSync&&(s=window.MotionCheckAppearSync(this,t,n)),this.valueSubscriptions.set(t,()=>{o(),i(),s&&s(),n.owner&&n.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in xr){const n=xr[t];if(!n)continue;const{isEnabled:r,Feature:o}=n;if(!this.features[t]&&o&&r(this.props)&&(this.features[t]=new o(this)),this.features[t]){const i=this.features[t];i.isMounted?i.update():(i.mount(),i.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ue()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<Xf.length;r++){const o=Xf[r];this.propEventSubscriptions[o]&&(this.propEventSubscriptions[o](),delete this.propEventSubscriptions[o]);const i="on"+o,s=t[i];s&&(this.propEventSubscriptions[o]=this.on(o,s))}this.prevMotionValues=PT(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const r=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}const n={};for(let r=0;r<bT;r++){const o=Uu[r],i=this.props[o];(Co(i)||i===!1)&&(n[o]=i)}return n}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){const r=this.values.get(t);n!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,n),this.values.set(t,n),this.latestValues[t]=n.get())}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=Eo(n===null?void 0:n,{owner:this}),this.addValue(t,r)),r}readValue(t,n){var r;let o=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(r=this.getBaseTargetFromProps(this.props,t))!==null&&r!==void 0?r:this.readValueFromInstance(this.current,t,this.options);return o!=null&&(typeof o=="string"&&(Dm(o)||Nm(o))?o=parseFloat(o):!kT(o)&&fn.test(n)&&(o=Gm(t,n)),this.setBaseTarget(t,be(o)?o.get():o)),be(o)?o.get():o}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props;let o;if(typeof r=="string"||typeof r=="object"){const s=Bu(this.props,r,(n=this.presenceContext)===null||n===void 0?void 0:n.custom);s&&(o=s[t])}if(r&&o!==void 0)return o;const i=this.getBaseTargetFromProps(this.props,t);return i!==void 0&&!be(i)?i:this.initialValues[t]!==void 0&&o===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new nc),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class ng extends RT{constructor(){super(...arguments),this.KeyframeResolver=Qm}sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}}function AT(e){return window.getComputedStyle(e)}class MT extends ng{constructor(){super(...arguments),this.type="html",this.applyWillChange=!0,this.renderInstance=Hv}readValueFromInstance(t,n){if(vn.has(n)){const r=Xu(n);return r&&r.default||0}else{const r=AT(t),o=(Vm(n)?r.getPropertyValue(n):r[n])||0;return typeof o=="string"?o.trim():o}}measureInstanceViewportBox(t,{transformPagePoint:n}){return Rv(t,n)}build(t,n,r){dc(t,n,r.transformTemplate)}scrapeMotionValuesFromProps(t,n,r){return lc(t,n,r)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;be(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}class _T extends ng{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ue}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(vn.has(n)){const r=Xu(n);return r&&r.default||0}return n=Kv.has(n)?n:Rs(n),t.getAttribute(n)}scrapeMotionValuesFromProps(t,n,r){return Yv(t,n,r)}build(t,n,r){fc(t,n,this.isSVGTag,r.transformTemplate)}renderInstance(t,n,r,o){Gv(t,n,r,o)}mount(t){this.isSVGTag=pc(t.tagName),super.mount(t)}}const NT=(e,t)=>ac(e)?new _T(t):new MT(t,{allowProjection:e!==g.Fragment}),LT=CT({...wS,...BC,...MC,...$C},NT),Pn=pw(LT);class DT extends g.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function jT({children:e,isPresent:t}){const n=g.useId(),r=g.useRef(null),o=g.useRef({width:0,height:0,top:0,left:0}),{nonce:i}=g.useContext(ic);return g.useInsertionEffect(()=>{const{width:s,height:a,top:l,left:u}=o.current;if(t||!r.current||!s||!a)return;r.current.dataset.motionPopId=n;const d=document.createElement("style");return i&&(d.nonce=i),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${s}px !important;
            height: ${a}px !important;
            top: ${l}px !important;
            left: ${u}px !important;
          }
        `),()=>{document.head.removeChild(d)}},[t]),C.jsx(DT,{isPresent:t,childRef:r,sizeRef:o,children:g.cloneElement(e,{ref:r})})}const VT=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:o,presenceAffectsLayout:i,mode:s})=>{const a=uc(OT),l=g.useId(),u=g.useMemo(()=>({id:l,initial:t,isPresent:n,custom:o,onExitComplete:d=>{a.set(d,!0);for(const c of a.values())if(!c)return;r&&r()},register:d=>(a.set(d,!1),()=>a.delete(d))}),i?[Math.random()]:[n]);return g.useMemo(()=>{a.forEach((d,c)=>a.set(c,!1))},[n]),g.useEffect(()=>{!n&&!a.size&&r&&r()},[n]),s==="popLayout"&&(e=C.jsx(jT,{isPresent:n,children:e})),C.jsx(Ms.Provider,{value:u,children:e})};function OT(){return new Map}const ni=e=>e.key||"";function Zf(e){const t=[];return g.Children.forEach(e,n=>{g.isValidElement(n)&&t.push(n)}),t}const qf=({children:e,exitBeforeEnter:t,custom:n,initial:r=!0,onExitComplete:o,presenceAffectsLayout:i=!0,mode:s="sync"})=>{const a=g.useMemo(()=>Zf(e),[e]),l=a.map(ni),u=g.useRef(!0),d=g.useRef(a),c=uc(()=>new Map),[f,v]=g.useState(a),[y,w]=g.useState(a);Bv(()=>{u.current=!1,d.current=a;for(let p=0;p<y.length;p++){const m=ni(y[p]);l.includes(m)?c.delete(m):c.get(m)!==!0&&c.set(m,!1)}},[y,l.length,l.join("-")]);const x=[];if(a!==f){let p=[...a];for(let m=0;m<y.length;m++){const S=y[m],T=ni(S);l.includes(T)||(p.splice(m,0,S),x.push(S))}s==="wait"&&x.length&&(p=x),w(Zf(p)),v(a);return}const{forceRender:h}=g.useContext(rc);return C.jsx(C.Fragment,{children:y.map(p=>{const m=ni(p),S=a===y||l.includes(m),T=()=>{if(c.has(m))c.set(m,!0);else return;let E=!0;c.forEach(k=>{k||(E=!1)}),E&&(h==null||h(),w(d.current),o&&o())};return C.jsx(VT,{isPresent:S,initial:!u.current||r?void 0:!1,custom:S?void 0:n,presenceAffectsLayout:i,mode:s,onExitComplete:S?void 0:T,children:p},m)})})};function ts(){return ts=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ts.apply(this,arguments)}function IT(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function rg(...e){return t=>e.forEach(n=>IT(n,t))}function lk(...e){return g.useCallback(rg(...e),e)}const og=g.forwardRef((e,t)=>{const{children:n,...r}=e,o=g.Children.toArray(n),i=o.find(zT);if(i){const s=i.props.children,a=o.map(l=>l===i?g.Children.count(s)>1?g.Children.only(null):g.isValidElement(s)?s.props.children:null:l);return g.createElement(Vl,ts({},r,{ref:t}),g.isValidElement(s)?g.cloneElement(s,void 0,a):null)}return g.createElement(Vl,ts({},r,{ref:t}),n)});og.displayName="Slot";const Vl=g.forwardRef((e,t)=>{const{children:n,...r}=e;return g.isValidElement(n)?g.cloneElement(n,{...BT(r,n.props),ref:t?rg(t,n.ref):n.ref}):g.Children.count(n)>1?g.Children.only(null):null});Vl.displayName="SlotClone";const FT=({children:e})=>g.createElement(g.Fragment,null,e);function zT(e){return g.isValidElement(e)&&e.type===FT}function BT(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...a)=>{i(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function ig(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=ig(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}function $T(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=ig(e))&&(r&&(r+=" "),r+=t);return r}const Jf=e=>typeof e=="boolean"?"".concat(e):e===0?"0":e,ep=$T,sg=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return ep(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:i}=t,s=Object.keys(o).map(u=>{const d=n==null?void 0:n[u],c=i==null?void 0:i[u];if(d===null)return null;const f=Jf(d)||Jf(c);return o[u][f]}),a=n&&Object.entries(n).reduce((u,d)=>{let[c,f]=d;return f===void 0||(u[c]=f),u},{}),l=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,d)=>{let{class:c,className:f,...v}=d;return Object.entries(v).every(y=>{let[w,x]=y;return Array.isArray(x)?x.includes({...i,...a}[w]):{...i,...a}[w]===x})?[...u,c,f]:u},[]);return ep(e,s,l,n==null?void 0:n.class,n==null?void 0:n.className)};/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var UT={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const WT=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),jo=(e,t)=>{const n=g.forwardRef(({color:r="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:s,className:a="",children:l,...u},d)=>g.createElement("svg",{ref:d,...UT,width:o,height:o,stroke:r,strokeWidth:s?Number(i)*24/Number(o):i,className:["lucide",`lucide-${WT(e)}`,a].join(" "),...u},[...t.map(([c,f])=>g.createElement(c,f)),...Array.isArray(l)?l:[l]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const HT=jo("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const KT=jo("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const GT=jo("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const QT=jo("Wallet",[["path",{d:"M21 12V7H5a2 2 0 0 1 0-4h14v4",key:"195gfw"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h16v-5",key:"195n9w"}],["path",{d:"M18 12a2 2 0 0 0 0 4h4v-4Z",key:"vllfpd"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ag=jo("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function lg(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=lg(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function YT(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=lg(e))&&(r&&(r+=" "),r+=t);return r}const hc="-";function XT(e){const t=qT(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;function o(s){const a=s.split(hc);return a[0]===""&&a.length!==1&&a.shift(),ug(a,t)||ZT(s)}function i(s,a){const l=n[s]||[];return a&&r[s]?[...l,...r[s]]:l}return{getClassGroupId:o,getConflictingClassGroupIds:i}}function ug(e,t){var s;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?ug(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const i=e.join(hc);return(s=t.validators.find(({validator:a})=>a(i)))==null?void 0:s.classGroupId}const tp=/^\[(.+)\]$/;function ZT(e){if(tp.test(e)){const t=tp.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}}function qT(e){const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return eP(Object.entries(e.classGroups),n).forEach(([i,s])=>{Ol(s,r,i,t)}),r}function Ol(e,t,n,r){e.forEach(o=>{if(typeof o=="string"){const i=o===""?t:np(t,o);i.classGroupId=n;return}if(typeof o=="function"){if(JT(o)){Ol(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([i,s])=>{Ol(s,np(t,i),n,r)})})}function np(e,t){let n=e;return t.split(hc).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n}function JT(e){return e.isThemeGetter}function eP(e,t){return t?e.map(([n,r])=>{const o=r.map(i=>typeof i=="string"?t+i:typeof i=="object"?Object.fromEntries(Object.entries(i).map(([s,a])=>[t+s,a])):i);return[n,o]}):e}function tP(e){if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;function o(i,s){n.set(i,s),t++,t>e&&(t=0,r=n,n=new Map)}return{get(i){let s=n.get(i);if(s!==void 0)return s;if((s=r.get(i))!==void 0)return o(i,s),s},set(i,s){n.has(i)?n.set(i,s):o(i,s)}}}const cg="!";function nP(e){const t=e.separator,n=t.length===1,r=t[0],o=t.length;return function(s){const a=[];let l=0,u=0,d;for(let w=0;w<s.length;w++){let x=s[w];if(l===0){if(x===r&&(n||s.slice(w,w+o)===t)){a.push(s.slice(u,w)),u=w+o;continue}if(x==="/"){d=w;continue}}x==="["?l++:x==="]"&&l--}const c=a.length===0?s:s.substring(u),f=c.startsWith(cg),v=f?c.substring(1):c,y=d&&d>u?d-u:void 0;return{modifiers:a,hasImportantModifier:f,baseClassName:v,maybePostfixModifierPosition:y}}}function rP(e){if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t}function oP(e){return{cache:tP(e.cacheSize),splitModifiers:nP(e),...XT(e)}}const iP=/\s+/;function sP(e,t){const{splitModifiers:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,i=new Set;return e.trim().split(iP).map(s=>{const{modifiers:a,hasImportantModifier:l,baseClassName:u,maybePostfixModifierPosition:d}=n(s);let c=r(d?u.substring(0,d):u),f=!!d;if(!c){if(!d)return{isTailwindClass:!1,originalClassName:s};if(c=r(u),!c)return{isTailwindClass:!1,originalClassName:s};f=!1}const v=rP(a).join(":");return{isTailwindClass:!0,modifierId:l?v+cg:v,classGroupId:c,originalClassName:s,hasPostfixModifier:f}}).reverse().filter(s=>{if(!s.isTailwindClass)return!0;const{modifierId:a,classGroupId:l,hasPostfixModifier:u}=s,d=a+l;return i.has(d)?!1:(i.add(d),o(l,u).forEach(c=>i.add(a+c)),!0)}).reverse().map(s=>s.originalClassName).join(" ")}function aP(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=dg(t))&&(r&&(r+=" "),r+=n);return r}function dg(e){if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=dg(e[r]))&&(n&&(n+=" "),n+=t);return n}function lP(e,...t){let n,r,o,i=s;function s(l){const u=t.reduce((d,c)=>c(d),e());return n=oP(u),r=n.cache.get,o=n.cache.set,i=a,a(l)}function a(l){const u=r(l);if(u)return u;const d=sP(l,n);return o(l,d),d}return function(){return i(aP.apply(null,arguments))}}function Z(e){const t=n=>n[e]||[];return t.isThemeGetter=!0,t}const fg=/^\[(?:([a-z-]+):)?(.+)\]$/i,uP=/^\d+\/\d+$/,cP=new Set(["px","full","screen"]),dP=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,fP=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,pP=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,hP=/^-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,mP=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/;function xt(e){return An(e)||cP.has(e)||uP.test(e)}function Wt(e){return Er(e,"length",TP)}function An(e){return!!e&&!Number.isNaN(Number(e))}function ri(e){return Er(e,"number",An)}function jr(e){return!!e&&Number.isInteger(Number(e))}function vP(e){return e.endsWith("%")&&An(e.slice(0,-1))}function z(e){return fg.test(e)}function Ht(e){return dP.test(e)}const gP=new Set(["length","size","percentage"]);function yP(e){return Er(e,gP,pg)}function wP(e){return Er(e,"position",pg)}const xP=new Set(["image","url"]);function SP(e){return Er(e,xP,EP)}function CP(e){return Er(e,"",PP)}function Vr(){return!0}function Er(e,t,n){const r=fg.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1}function TP(e){return fP.test(e)&&!pP.test(e)}function pg(){return!1}function PP(e){return hP.test(e)}function EP(e){return mP.test(e)}function kP(){const e=Z("colors"),t=Z("spacing"),n=Z("blur"),r=Z("brightness"),o=Z("borderColor"),i=Z("borderRadius"),s=Z("borderSpacing"),a=Z("borderWidth"),l=Z("contrast"),u=Z("grayscale"),d=Z("hueRotate"),c=Z("invert"),f=Z("gap"),v=Z("gradientColorStops"),y=Z("gradientColorStopPositions"),w=Z("inset"),x=Z("margin"),h=Z("opacity"),p=Z("padding"),m=Z("saturate"),S=Z("scale"),T=Z("sepia"),E=Z("skew"),k=Z("space"),P=Z("translate"),D=()=>["auto","contain","none"],N=()=>["auto","hidden","clip","visible","scroll"],_=()=>["auto",z,t],b=()=>[z,t],F=()=>["",xt,Wt],A=()=>["auto",An,z],V=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],B=()=>["solid","dashed","dotted","double","none"],X=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter"],M=()=>["start","end","center","between","around","evenly","stretch"],j=()=>["","0",z],I=()=>["auto","avoid","all","avoid-page","page","left","right","column"],U=()=>[An,ri],W=()=>[An,z];return{cacheSize:500,separator:":",theme:{colors:[Vr],spacing:[xt,Wt],blur:["none","",Ht,z],brightness:U(),borderColor:[e],borderRadius:["none","","full",Ht,z],borderSpacing:b(),borderWidth:F(),contrast:U(),grayscale:j(),hueRotate:W(),invert:j(),gap:b(),gradientColorStops:[e],gradientColorStopPositions:[vP,Wt],inset:_(),margin:_(),opacity:U(),padding:b(),saturate:U(),scale:U(),sepia:j(),skew:W(),space:b(),translate:b()},classGroups:{aspect:[{aspect:["auto","square","video",z]}],container:["container"],columns:[{columns:[Ht]}],"break-after":[{"break-after":I()}],"break-before":[{"break-before":I()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...V(),z]}],overflow:[{overflow:N()}],"overflow-x":[{"overflow-x":N()}],"overflow-y":[{"overflow-y":N()}],overscroll:[{overscroll:D()}],"overscroll-x":[{"overscroll-x":D()}],"overscroll-y":[{"overscroll-y":D()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[w]}],"inset-x":[{"inset-x":[w]}],"inset-y":[{"inset-y":[w]}],start:[{start:[w]}],end:[{end:[w]}],top:[{top:[w]}],right:[{right:[w]}],bottom:[{bottom:[w]}],left:[{left:[w]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",jr,z]}],basis:[{basis:_()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",z]}],grow:[{grow:j()}],shrink:[{shrink:j()}],order:[{order:["first","last","none",jr,z]}],"grid-cols":[{"grid-cols":[Vr]}],"col-start-end":[{col:["auto",{span:["full",jr,z]},z]}],"col-start":[{"col-start":A()}],"col-end":[{"col-end":A()}],"grid-rows":[{"grid-rows":[Vr]}],"row-start-end":[{row:["auto",{span:[jr,z]},z]}],"row-start":[{"row-start":A()}],"row-end":[{"row-end":A()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",z]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",z]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...M()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...M(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...M(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[p]}],px:[{px:[p]}],py:[{py:[p]}],ps:[{ps:[p]}],pe:[{pe:[p]}],pt:[{pt:[p]}],pr:[{pr:[p]}],pb:[{pb:[p]}],pl:[{pl:[p]}],m:[{m:[x]}],mx:[{mx:[x]}],my:[{my:[x]}],ms:[{ms:[x]}],me:[{me:[x]}],mt:[{mt:[x]}],mr:[{mr:[x]}],mb:[{mb:[x]}],ml:[{ml:[x]}],"space-x":[{"space-x":[k]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[k]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",z,t]}],"min-w":[{"min-w":[z,t,"min","max","fit"]}],"max-w":[{"max-w":[z,t,"none","full","min","max","fit","prose",{screen:[Ht]},Ht]}],h:[{h:[z,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[z,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[z,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[z,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Ht,Wt]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",ri]}],"font-family":[{font:[Vr]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",z]}],"line-clamp":[{"line-clamp":["none",An,ri]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",xt,z]}],"list-image":[{"list-image":["none",z]}],"list-style-type":[{list:["none","disc","decimal",z]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[h]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[h]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...B(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",xt,Wt]}],"underline-offset":[{"underline-offset":["auto",xt,z]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:b()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",z]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",z]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[h]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...V(),wP]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",yP]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},SP]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[y]}],"gradient-via-pos":[{via:[y]}],"gradient-to-pos":[{to:[y]}],"gradient-from":[{from:[v]}],"gradient-via":[{via:[v]}],"gradient-to":[{to:[v]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[h]}],"border-style":[{border:[...B(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[h]}],"divide-style":[{divide:B()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...B()]}],"outline-offset":[{"outline-offset":[xt,z]}],"outline-w":[{outline:[xt,Wt]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:F()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[h]}],"ring-offset-w":[{"ring-offset":[xt,Wt]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Ht,CP]}],"shadow-color":[{shadow:[Vr]}],opacity:[{opacity:[h]}],"mix-blend":[{"mix-blend":X()}],"bg-blend":[{"bg-blend":X()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",Ht,z]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[c]}],saturate:[{saturate:[m]}],sepia:[{sepia:[T]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[c]}],"backdrop-opacity":[{"backdrop-opacity":[h]}],"backdrop-saturate":[{"backdrop-saturate":[m]}],"backdrop-sepia":[{"backdrop-sepia":[T]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",z]}],duration:[{duration:W()}],ease:[{ease:["linear","in","out","in-out",z]}],delay:[{delay:W()}],animate:[{animate:["none","spin","ping","pulse","bounce",z]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[S]}],"scale-x":[{"scale-x":[S]}],"scale-y":[{"scale-y":[S]}],rotate:[{rotate:[jr,z]}],"translate-x":[{"translate-x":[P]}],"translate-y":[{"translate-y":[P]}],"skew-x":[{"skew-x":[E]}],"skew-y":[{"skew-y":[E]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",z]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",z]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":b()}],"scroll-mx":[{"scroll-mx":b()}],"scroll-my":[{"scroll-my":b()}],"scroll-ms":[{"scroll-ms":b()}],"scroll-me":[{"scroll-me":b()}],"scroll-mt":[{"scroll-mt":b()}],"scroll-mr":[{"scroll-mr":b()}],"scroll-mb":[{"scroll-mb":b()}],"scroll-ml":[{"scroll-ml":b()}],"scroll-p":[{"scroll-p":b()}],"scroll-px":[{"scroll-px":b()}],"scroll-py":[{"scroll-py":b()}],"scroll-ps":[{"scroll-ps":b()}],"scroll-pe":[{"scroll-pe":b()}],"scroll-pt":[{"scroll-pt":b()}],"scroll-pr":[{"scroll-pr":b()}],"scroll-pb":[{"scroll-pb":b()}],"scroll-pl":[{"scroll-pl":b()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",z]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[xt,Wt,ri]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}const bP=lP(kP);function yn(...e){return bP(YT(e))}const RP=sg("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"h-full gap-1 flex bg-[var(--link)] max-md:h-12 capitalize font-bold items-center hover:shadow-xl justify-center rounded-lg cursor-pointer px-6 py-3 transition duration-100 transform bg-[var(--button)] border-2 border-[var(--button-border)] text-[var(--button-text)] hoverd hover:bg-[var(--button-hover)] hover:text-[var(--button-text-hover)]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"h-full gap-1 flex max-md:h-12 capitalize font-bold items-center justify-center  bg-transparent rounded-lg cursor-pointer px-6 py-3 transition duration-100 transform border-[var(--button)] border-2 border-[var(--button-border)] text-[var(--outline-button-text)] hoverd hover:bg-[var(--button-hover)] hover:text-[var(--button-text-hover)]",secondary:"h-full gap-1 flex max-md:h-12 capitalize font-bold items-center justify-center rounded-lg cursor-pointer px-6 py-3 transition duration-100 transform bg-[var(--button)] border-2 border-[var(--button-border)] text-[var(--button-text)] hoverd hover:bg-[var(--button-hover)] hover:text-[var(--button-text-hover)]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",active:"transition duration-100 transform bg-[var(--active)] text-[var(--active-text)]"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),mc=g.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,icon:o,isLoading:i,children:s,...a},l)=>{const u=r?og:"button";return C.jsxs(u,{className:yn(RP({variant:t,size:n,className:e})),ref:l,disabled:i||a.disabled,...a,children:[i&&C.jsx(GT,{className:"mr-2 h-4 w-4 animate-spin"}),o&&!i&&C.jsx("span",{className:"mr-2",children:o}),s]})});mc.displayName="Button";function AP({className:e,angle:t=65,cellSize:n=60,opacity:r=.5,lightLineColor:o="gray",darkLineColor:i="gray",...s}){const a={"--grid-angle":`${t}deg`,"--cell-size":`${n}px`,"--opacity":r,"--light-line":o,"--dark-line":i};return C.jsxs("div",{className:yn("pointer-events-none absolute size-full overflow-hidden [perspective:200px]","opacity-[30%]",e),style:a,...s,children:[C.jsx("div",{className:"notfoundeffect absolute inset-0 [transform:rotateX(var(--grid-angle))]",children:C.jsx("div",{className:"animate-grid [background-image:linear-gradient(to_right,var(--light-line)_1px,transparent_0),linear-gradient(to_bottom,var(--light-line)_1px,transparent_0)] [background-repeat:repeat] [background-size:var(--cell-size)_var(--cell-size)] [height:300vh] [inset:0%_0px] [margin-left:-200%] [transform-origin:100%_0_0] [width:600vw] dark:[background-image:linear-gradient(to_right,var(--dark-line)_1px,transparent_0),linear-gradient(to_bottom,var(--dark-line)_1px,transparent_0)]"})}),C.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-white to-transparent to-90% dark:from-black"})]})}function MP(){return C.jsxs("div",{className:"relative flex h-[100vh] w-full flex-col items-center justify-center overflow-hidden rounded-lg bg-[var(--background)] md:shadow-xl",children:[C.jsx(Pn.span,{initial:{opacity:0},animate:{opacity:1},transition:{duration:1},className:"pointer-events-none z-10 whitespace-pre-wrap bg-gradient-to-b from-[#ffd319] via-[#ff2975] to-[#8c1eff] bg-clip-text text-center text-7xl font-bold leading-none tracking-tighter text-transparent",children:"404 Not Found"}),C.jsx(AP,{})]})}const _P=ht.lazy(()=>fw(()=>import("./HomePage-BqnHRsU4.js"),__vite__mapDeps([]))),NP=()=>{const e=()=>C.jsx(Pn.div,{className:"flex h-screen items-center justify-center bg-[var(--background)]",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:C.jsx("div",{className:"text-base",children:C.jsx(mc,{isLoading:!0,className:"flex cursor-text gap-0 border-none bg-transparent",children:"Loading..."})})});return C.jsx(g.Suspense,{fallback:C.jsx(e,{}),children:C.jsxs(sw,{children:[C.jsx(xl,{path:"/",element:C.jsx(_P,{})}),C.jsx(xl,{path:"*",element:C.jsx(MP,{})})]})})};var LP="@vercel/analytics",DP="1.3.1",jP=()=>{window.va||(window.va=function(...t){(window.vaq=window.vaq||[]).push(t)})};function hg(){return typeof window<"u"}function mg(){try{const e="production"}catch{}return"production"}function VP(e="auto"){if(e==="auto"){window.vam=mg();return}window.vam=e}function OP(){return(hg()?window.vam:mg())||"production"}function Sa(){return OP()==="development"}var IP="https://va.vercel-scripts.com/v1/script.debug.js",FP="/_vercel/insights/script.js";function zP(e={debug:!0}){var t;if(!hg())return;VP(e.mode),jP(),e.beforeSend&&((t=window.va)==null||t.call(window,"beforeSend",e.beforeSend));const n=e.scriptSrc||(Sa()?IP:FP);if(document.head.querySelector(`script[src*="${n}"]`))return;const r=document.createElement("script");r.src=n,r.defer=!0,r.dataset.sdkn=LP+(e.framework?`/${e.framework}`:""),r.dataset.sdkv=DP,e.disableAutoTrack&&(r.dataset.disableAutoTrack="1"),e.endpoint&&(r.dataset.endpoint=e.endpoint),e.dsn&&(r.dataset.dsn=e.dsn),r.onerror=()=>{const o=Sa()?"Please check if any ad blockers are enabled and try again.":"Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.";console.log(`[Vercel Web Analytics] Failed to load script from ${n}. ${o}`)},Sa()&&e.debug===!1&&(r.dataset.debug="false"),document.head.appendChild(r)}function We(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function BP(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function vg(...e){return t=>e.forEach(n=>BP(n,t))}function In(...e){return g.useCallback(vg(...e),e)}function $P(e,t=[]){let n=[];function r(i,s){const a=g.createContext(s),l=n.length;n=[...n,s];function u(c){const{scope:f,children:v,...y}=c,w=(f==null?void 0:f[e][l])||a,x=g.useMemo(()=>y,Object.values(y));return C.jsx(w.Provider,{value:x,children:v})}function d(c,f){const v=(f==null?void 0:f[e][l])||a,y=g.useContext(v);if(y)return y;if(s!==void 0)return s;throw new Error(`\`${c}\` must be used within \`${i}\``)}return u.displayName=i+"Provider",[u,d]}const o=()=>{const i=n.map(s=>g.createContext(s));return function(a){const l=(a==null?void 0:a[e])||i;return g.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,UP(o,...t)]}function UP(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:l,scopeName:u})=>{const c=l(i)[`__scope${u}`];return{...a,...c}},{});return g.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var ns=g.forwardRef((e,t)=>{const{children:n,...r}=e,o=g.Children.toArray(n),i=o.find(HP);if(i){const s=i.props.children,a=o.map(l=>l===i?g.Children.count(s)>1?g.Children.only(null):g.isValidElement(s)?s.props.children:null:l);return C.jsx(Il,{...r,ref:t,children:g.isValidElement(s)?g.cloneElement(s,void 0,a):null})}return C.jsx(Il,{...r,ref:t,children:n})});ns.displayName="Slot";var Il=g.forwardRef((e,t)=>{const{children:n,...r}=e;if(g.isValidElement(n)){const o=GP(n);return g.cloneElement(n,{...KP(r,n.props),ref:t?vg(t,o):o})}return g.Children.count(n)>1?g.Children.only(null):null});Il.displayName="SlotClone";var WP=({children:e})=>C.jsx(C.Fragment,{children:e});function HP(e){return g.isValidElement(e)&&e.type===WP}function KP(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...a)=>{i(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function GP(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function QP(e){const t=e+"CollectionProvider",[n,r]=$P(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=v=>{const{scope:y,children:w}=v,x=ht.useRef(null),h=ht.useRef(new Map).current;return C.jsx(o,{scope:y,itemMap:h,collectionRef:x,children:w})};s.displayName=t;const a=e+"CollectionSlot",l=ht.forwardRef((v,y)=>{const{scope:w,children:x}=v,h=i(a,w),p=In(y,h.collectionRef);return C.jsx(ns,{ref:p,children:x})});l.displayName=a;const u=e+"CollectionItemSlot",d="data-radix-collection-item",c=ht.forwardRef((v,y)=>{const{scope:w,children:x,...h}=v,p=ht.useRef(null),m=In(y,p),S=i(u,w);return ht.useEffect(()=>(S.itemMap.set(p,{ref:p,...h}),()=>void S.itemMap.delete(p))),C.jsx(ns,{[d]:"",ref:m,children:x})});c.displayName=u;function f(v){const y=i(e+"CollectionConsumer",v);return ht.useCallback(()=>{const x=y.collectionRef.current;if(!x)return[];const h=Array.from(x.querySelectorAll(`[${d}]`));return Array.from(y.itemMap.values()).sort((S,T)=>h.indexOf(S.ref.current)-h.indexOf(T.ref.current))},[y.collectionRef,y.itemMap])}return[{Provider:s,Slot:l,ItemSlot:c},f,r]}function YP(e,t=[]){let n=[];function r(i,s){const a=g.createContext(s),l=n.length;n=[...n,s];const u=c=>{var h;const{scope:f,children:v,...y}=c,w=((h=f==null?void 0:f[e])==null?void 0:h[l])||a,x=g.useMemo(()=>y,Object.values(y));return C.jsx(w.Provider,{value:x,children:v})};u.displayName=i+"Provider";function d(c,f){var w;const v=((w=f==null?void 0:f[e])==null?void 0:w[l])||a,y=g.useContext(v);if(y)return y;if(s!==void 0)return s;throw new Error(`\`${c}\` must be used within \`${i}\``)}return[u,d]}const o=()=>{const i=n.map(s=>g.createContext(s));return function(a){const l=(a==null?void 0:a[e])||i;return g.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,XP(o,...t)]}function XP(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:l,scopeName:u})=>{const c=l(i)[`__scope${u}`];return{...a,...c}},{});return g.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var ZP=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],Ft=ZP.reduce((e,t)=>{const n=g.forwardRef((r,o)=>{const{asChild:i,...s}=r,a=i?ns:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),C.jsx(a,{...s,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function gg(e,t){e&&Fu.flushSync(()=>e.dispatchEvent(t))}function Ot(e){const t=g.useRef(e);return g.useEffect(()=>{t.current=e}),g.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function qP(e,t=globalThis==null?void 0:globalThis.document){const n=Ot(e);g.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var JP="DismissableLayer",Fl="dismissableLayer.update",eE="dismissableLayer.pointerDownOutside",tE="dismissableLayer.focusOutside",rp,yg=g.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),wg=g.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:s,onDismiss:a,...l}=e,u=g.useContext(yg),[d,c]=g.useState(null),f=(d==null?void 0:d.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,v]=g.useState({}),y=In(t,k=>c(k)),w=Array.from(u.layers),[x]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),h=w.indexOf(x),p=d?w.indexOf(d):-1,m=u.layersWithOutsidePointerEventsDisabled.size>0,S=p>=h,T=rE(k=>{const P=k.target,D=[...u.branches].some(N=>N.contains(P));!S||D||(o==null||o(k),s==null||s(k),k.defaultPrevented||a==null||a())},f),E=oE(k=>{const P=k.target;[...u.branches].some(N=>N.contains(P))||(i==null||i(k),s==null||s(k),k.defaultPrevented||a==null||a())},f);return qP(k=>{p===u.layers.size-1&&(r==null||r(k),!k.defaultPrevented&&a&&(k.preventDefault(),a()))},f),g.useEffect(()=>{if(d)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(rp=f.body.style.pointerEvents,f.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(d)),u.layers.add(d),op(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(f.body.style.pointerEvents=rp)}},[d,f,n,u]),g.useEffect(()=>()=>{d&&(u.layers.delete(d),u.layersWithOutsidePointerEventsDisabled.delete(d),op())},[d,u]),g.useEffect(()=>{const k=()=>v({});return document.addEventListener(Fl,k),()=>document.removeEventListener(Fl,k)},[]),C.jsx(Ft.div,{...l,ref:y,style:{pointerEvents:m?S?"auto":"none":void 0,...e.style},onFocusCapture:We(e.onFocusCapture,E.onFocusCapture),onBlurCapture:We(e.onBlurCapture,E.onBlurCapture),onPointerDownCapture:We(e.onPointerDownCapture,T.onPointerDownCapture)})});wg.displayName=JP;var nE="DismissableLayerBranch",xg=g.forwardRef((e,t)=>{const n=g.useContext(yg),r=g.useRef(null),o=In(t,r);return g.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),C.jsx(Ft.div,{...e,ref:o})});xg.displayName=nE;function rE(e,t=globalThis==null?void 0:globalThis.document){const n=Ot(e),r=g.useRef(!1),o=g.useRef(()=>{});return g.useEffect(()=>{const i=a=>{if(a.target&&!r.current){let l=function(){Sg(eE,n,u,{discrete:!0})};const u={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=l,t.addEventListener("click",o.current,{once:!0})):l()}else t.removeEventListener("click",o.current);r.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function oE(e,t=globalThis==null?void 0:globalThis.document){const n=Ot(e),r=g.useRef(!1);return g.useEffect(()=>{const o=i=>{i.target&&!r.current&&Sg(tE,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function op(){const e=new CustomEvent(Fl);document.dispatchEvent(e)}function Sg(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?gg(o,i):o.dispatchEvent(i)}var iE=wg,sE=xg,rs=globalThis!=null&&globalThis.document?g.useLayoutEffect:()=>{},aE="Portal",Cg=g.forwardRef((e,t)=>{var a;const{container:n,...r}=e,[o,i]=g.useState(!1);rs(()=>i(!0),[]);const s=n||o&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return s?Sm.createPortal(C.jsx(Ft.div,{...r,ref:t}),s):null});Cg.displayName=aE;function lE(e,t){return g.useReducer((n,r)=>t[n][r]??n,e)}var Tg=e=>{const{present:t,children:n}=e,r=uE(t),o=typeof n=="function"?n({present:r.isPresent}):g.Children.only(n),i=In(r.ref,cE(o));return typeof n=="function"||r.isPresent?g.cloneElement(o,{ref:i}):null};Tg.displayName="Presence";function uE(e){const[t,n]=g.useState(),r=g.useRef({}),o=g.useRef(e),i=g.useRef("none"),s=e?"mounted":"unmounted",[a,l]=lE(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return g.useEffect(()=>{const u=oi(r.current);i.current=a==="mounted"?u:"none"},[a]),rs(()=>{const u=r.current,d=o.current;if(d!==e){const f=i.current,v=oi(u);e?l("MOUNT"):v==="none"||(u==null?void 0:u.display)==="none"?l("UNMOUNT"):l(d&&f!==v?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),rs(()=>{if(t){let u;const d=t.ownerDocument.defaultView??window,c=v=>{const w=oi(r.current).includes(v.animationName);if(v.target===t&&w&&(l("ANIMATION_END"),!o.current)){const x=t.style.animationFillMode;t.style.animationFillMode="forwards",u=d.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=x)})}},f=v=>{v.target===t&&(i.current=oi(r.current))};return t.addEventListener("animationstart",f),t.addEventListener("animationcancel",c),t.addEventListener("animationend",c),()=>{d.clearTimeout(u),t.removeEventListener("animationstart",f),t.removeEventListener("animationcancel",c),t.removeEventListener("animationend",c)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:g.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function oi(e){return(e==null?void 0:e.animationName)||"none"}function cE(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function dE({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=fE({defaultProp:t,onChange:n}),i=e!==void 0,s=i?e:r,a=Ot(n),l=g.useCallback(u=>{if(i){const c=typeof u=="function"?u(e):u;c!==e&&a(c)}else o(u)},[i,e,o,a]);return[s,l]}function fE({defaultProp:e,onChange:t}){const n=g.useState(e),[r]=n,o=g.useRef(r),i=Ot(t);return g.useEffect(()=>{o.current!==r&&(i(r),o.current=r)},[r,o,i]),n}function pE(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function hE(...e){return t=>e.forEach(n=>pE(n,t))}var Pg=g.forwardRef((e,t)=>{const{children:n,...r}=e,o=g.Children.toArray(n),i=o.find(vE);if(i){const s=i.props.children,a=o.map(l=>l===i?g.Children.count(s)>1?g.Children.only(null):g.isValidElement(s)?s.props.children:null:l);return C.jsx(zl,{...r,ref:t,children:g.isValidElement(s)?g.cloneElement(s,void 0,a):null})}return C.jsx(zl,{...r,ref:t,children:n})});Pg.displayName="Slot";var zl=g.forwardRef((e,t)=>{const{children:n,...r}=e;if(g.isValidElement(n)){const o=yE(n);return g.cloneElement(n,{...gE(r,n.props),ref:t?hE(t,o):o})}return g.Children.count(n)>1?g.Children.only(null):null});zl.displayName="SlotClone";var mE=({children:e})=>C.jsx(C.Fragment,{children:e});function vE(e){return g.isValidElement(e)&&e.type===mE}function gE(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...a)=>{i(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function yE(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var wE=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],xE=wE.reduce((e,t)=>{const n=g.forwardRef((r,o)=>{const{asChild:i,...s}=r,a=i?Pg:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),C.jsx(a,{...s,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),SE="VisuallyHidden",Ls=g.forwardRef((e,t)=>C.jsx(xE.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));Ls.displayName=SE;var uk=Ls,vc="ToastProvider",[gc,CE,TE]=QP("Toast"),[Eg,ck]=YP("Toast",[TE]),[PE,Ds]=Eg(vc),kg=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:i=50,children:s}=e,[a,l]=g.useState(null),[u,d]=g.useState(0),c=g.useRef(!1),f=g.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${vc}\`. Expected non-empty \`string\`.`),C.jsx(gc.Provider,{scope:t,children:C.jsx(PE,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:i,toastCount:u,viewport:a,onViewportChange:l,onToastAdd:g.useCallback(()=>d(v=>v+1),[]),onToastRemove:g.useCallback(()=>d(v=>v-1),[]),isFocusedToastEscapeKeyDownRef:c,isClosePausedRef:f,children:s})})};kg.displayName=vc;var bg="ToastViewport",EE=["F8"],Bl="toast.viewportPause",$l="toast.viewportResume",Rg=g.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=EE,label:o="Notifications ({hotkey})",...i}=e,s=Ds(bg,n),a=CE(n),l=g.useRef(null),u=g.useRef(null),d=g.useRef(null),c=g.useRef(null),f=In(t,c,s.onViewportChange),v=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),y=s.toastCount>0;g.useEffect(()=>{const x=h=>{var m;r.length!==0&&r.every(S=>h[S]||h.code===S)&&((m=c.current)==null||m.focus())};return document.addEventListener("keydown",x),()=>document.removeEventListener("keydown",x)},[r]),g.useEffect(()=>{const x=l.current,h=c.current;if(y&&x&&h){const p=()=>{if(!s.isClosePausedRef.current){const E=new CustomEvent(Bl);h.dispatchEvent(E),s.isClosePausedRef.current=!0}},m=()=>{if(s.isClosePausedRef.current){const E=new CustomEvent($l);h.dispatchEvent(E),s.isClosePausedRef.current=!1}},S=E=>{!x.contains(E.relatedTarget)&&m()},T=()=>{x.contains(document.activeElement)||m()};return x.addEventListener("focusin",p),x.addEventListener("focusout",S),x.addEventListener("pointermove",p),x.addEventListener("pointerleave",T),window.addEventListener("blur",p),window.addEventListener("focus",m),()=>{x.removeEventListener("focusin",p),x.removeEventListener("focusout",S),x.removeEventListener("pointermove",p),x.removeEventListener("pointerleave",T),window.removeEventListener("blur",p),window.removeEventListener("focus",m)}}},[y,s.isClosePausedRef]);const w=g.useCallback(({tabbingDirection:x})=>{const p=a().map(m=>{const S=m.ref.current,T=[S,...IE(S)];return x==="forwards"?T:T.reverse()});return(x==="forwards"?p.reverse():p).flat()},[a]);return g.useEffect(()=>{const x=c.current;if(x){const h=p=>{var T,E,k;const m=p.altKey||p.ctrlKey||p.metaKey;if(p.key==="Tab"&&!m){const P=document.activeElement,D=p.shiftKey;if(p.target===x&&D){(T=u.current)==null||T.focus();return}const b=w({tabbingDirection:D?"backwards":"forwards"}),F=b.findIndex(A=>A===P);Ca(b.slice(F+1))?p.preventDefault():D?(E=u.current)==null||E.focus():(k=d.current)==null||k.focus()}};return x.addEventListener("keydown",h),()=>x.removeEventListener("keydown",h)}},[a,w]),C.jsxs(sE,{ref:l,role:"region","aria-label":o.replace("{hotkey}",v),tabIndex:-1,style:{pointerEvents:y?void 0:"none"},children:[y&&C.jsx(Ul,{ref:u,onFocusFromOutsideViewport:()=>{const x=w({tabbingDirection:"forwards"});Ca(x)}}),C.jsx(gc.Slot,{scope:n,children:C.jsx(Ft.ol,{tabIndex:-1,...i,ref:f})}),y&&C.jsx(Ul,{ref:d,onFocusFromOutsideViewport:()=>{const x=w({tabbingDirection:"backwards"});Ca(x)}})]})});Rg.displayName=bg;var Ag="ToastFocusProxy",Ul=g.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,i=Ds(Ag,n);return C.jsx(Ls,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:s=>{var u;const a=s.relatedTarget;!((u=i.viewport)!=null&&u.contains(a))&&r()}})});Ul.displayName=Ag;var js="Toast",kE="toast.swipeStart",bE="toast.swipeMove",RE="toast.swipeCancel",AE="toast.swipeEnd",Mg=g.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:i,...s}=e,[a=!0,l]=dE({prop:r,defaultProp:o,onChange:i});return C.jsx(Tg,{present:n||a,children:C.jsx(NE,{open:a,...s,ref:t,onClose:()=>l(!1),onPause:Ot(e.onPause),onResume:Ot(e.onResume),onSwipeStart:We(e.onSwipeStart,u=>{u.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:We(e.onSwipeMove,u=>{const{x:d,y:c}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","move"),u.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${d}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${c}px`)}),onSwipeCancel:We(e.onSwipeCancel,u=>{u.currentTarget.setAttribute("data-swipe","cancel"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:We(e.onSwipeEnd,u=>{const{x:d,y:c}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","end"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${d}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${c}px`),l(!1)})})})});Mg.displayName=js;var[ME,_E]=Eg(js,{onClose(){}}),NE=g.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:o,open:i,onClose:s,onEscapeKeyDown:a,onPause:l,onResume:u,onSwipeStart:d,onSwipeMove:c,onSwipeCancel:f,onSwipeEnd:v,...y}=e,w=Ds(js,n),[x,h]=g.useState(null),p=In(t,A=>h(A)),m=g.useRef(null),S=g.useRef(null),T=o||w.duration,E=g.useRef(0),k=g.useRef(T),P=g.useRef(0),{onToastAdd:D,onToastRemove:N}=w,_=Ot(()=>{var V;(x==null?void 0:x.contains(document.activeElement))&&((V=w.viewport)==null||V.focus()),s()}),b=g.useCallback(A=>{!A||A===1/0||(window.clearTimeout(P.current),E.current=new Date().getTime(),P.current=window.setTimeout(_,A))},[_]);g.useEffect(()=>{const A=w.viewport;if(A){const V=()=>{b(k.current),u==null||u()},B=()=>{const X=new Date().getTime()-E.current;k.current=k.current-X,window.clearTimeout(P.current),l==null||l()};return A.addEventListener(Bl,B),A.addEventListener($l,V),()=>{A.removeEventListener(Bl,B),A.removeEventListener($l,V)}}},[w.viewport,T,l,u,b]),g.useEffect(()=>{i&&!w.isClosePausedRef.current&&b(T)},[i,T,w.isClosePausedRef,b]),g.useEffect(()=>(D(),()=>N()),[D,N]);const F=g.useMemo(()=>x?Og(x):null,[x]);return w.viewport?C.jsxs(C.Fragment,{children:[F&&C.jsx(LE,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:F}),C.jsx(ME,{scope:n,onClose:_,children:Fu.createPortal(C.jsx(gc.ItemSlot,{scope:n,children:C.jsx(iE,{asChild:!0,onEscapeKeyDown:We(a,()=>{w.isFocusedToastEscapeKeyDownRef.current||_(),w.isFocusedToastEscapeKeyDownRef.current=!1}),children:C.jsx(Ft.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":i?"open":"closed","data-swipe-direction":w.swipeDirection,...y,ref:p,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:We(e.onKeyDown,A=>{A.key==="Escape"&&(a==null||a(A.nativeEvent),A.nativeEvent.defaultPrevented||(w.isFocusedToastEscapeKeyDownRef.current=!0,_()))}),onPointerDown:We(e.onPointerDown,A=>{A.button===0&&(m.current={x:A.clientX,y:A.clientY})}),onPointerMove:We(e.onPointerMove,A=>{if(!m.current)return;const V=A.clientX-m.current.x,B=A.clientY-m.current.y,X=!!S.current,M=["left","right"].includes(w.swipeDirection),j=["left","up"].includes(w.swipeDirection)?Math.min:Math.max,I=M?j(0,V):0,U=M?0:j(0,B),W=A.pointerType==="touch"?10:2,de={x:I,y:U},te={originalEvent:A,delta:de};X?(S.current=de,ii(bE,c,te,{discrete:!1})):ip(de,w.swipeDirection,W)?(S.current=de,ii(kE,d,te,{discrete:!1}),A.target.setPointerCapture(A.pointerId)):(Math.abs(V)>W||Math.abs(B)>W)&&(m.current=null)}),onPointerUp:We(e.onPointerUp,A=>{const V=S.current,B=A.target;if(B.hasPointerCapture(A.pointerId)&&B.releasePointerCapture(A.pointerId),S.current=null,m.current=null,V){const X=A.currentTarget,M={originalEvent:A,delta:V};ip(V,w.swipeDirection,w.swipeThreshold)?ii(AE,v,M,{discrete:!0}):ii(RE,f,M,{discrete:!0}),X.addEventListener("click",j=>j.preventDefault(),{once:!0})}})})})}),w.viewport)})]}):null}),LE=e=>{const{__scopeToast:t,children:n,...r}=e,o=Ds(js,t),[i,s]=g.useState(!1),[a,l]=g.useState(!1);return VE(()=>s(!0)),g.useEffect(()=>{const u=window.setTimeout(()=>l(!0),1e3);return()=>window.clearTimeout(u)},[]),a?null:C.jsx(Cg,{asChild:!0,children:C.jsx(Ls,{...r,children:i&&C.jsxs(C.Fragment,{children:[o.label," ",n]})})})},DE="ToastTitle",_g=g.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return C.jsx(Ft.div,{...r,ref:t})});_g.displayName=DE;var jE="ToastDescription",Ng=g.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return C.jsx(Ft.div,{...r,ref:t})});Ng.displayName=jE;var Lg="ToastAction",Dg=g.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?C.jsx(Vg,{altText:n,asChild:!0,children:C.jsx(yc,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${Lg}\`. Expected non-empty \`string\`.`),null)});Dg.displayName=Lg;var jg="ToastClose",yc=g.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,o=_E(jg,n);return C.jsx(Vg,{asChild:!0,children:C.jsx(Ft.button,{type:"button",...r,ref:t,onClick:We(e.onClick,o.onClose)})})});yc.displayName=jg;var Vg=g.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return C.jsx(Ft.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function Og(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),OE(r)){const o=r.ariaHidden||r.hidden||r.style.display==="none",i=r.dataset.radixToastAnnounceExclude==="";if(!o)if(i){const s=r.dataset.radixToastAnnounceAlt;s&&t.push(s)}else t.push(...Og(r))}}),t}function ii(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?gg(o,i):o.dispatchEvent(i)}var ip=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),i=r>o;return t==="left"||t==="right"?i&&r>n:!i&&o>n};function VE(e=()=>{}){const t=Ot(e);rs(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function OE(e){return e.nodeType===e.ELEMENT_NODE}function IE(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Ca(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var FE=kg,Ig=Rg,Fg=Mg,zg=_g,Bg=Ng,$g=Dg,Ug=yc;const zE=FE,Wg=g.forwardRef(({className:e,...t},n)=>C.jsx(Ig,{ref:n,className:yn("fixed bottom-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));Wg.displayName=Ig.displayName;const BE=sg("group pointer-events-auto relative  flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full ",{variants:{variant:{default:"bg-white rounded-md text-stone-700 shadow-sm border p-2 py-4",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),Hg=g.forwardRef(({className:e,variant:t,...n},r)=>C.jsx(Fg,{ref:r,className:yn(BE({variant:t}),e),...n}));Hg.displayName=Fg.displayName;const $E=g.forwardRef(({className:e,...t},n)=>C.jsx($g,{ref:n,className:yn("group-[.destructive]:focus:ring-destructiv group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground",e),...t}));$E.displayName=$g.displayName;const Kg=g.forwardRef(({className:e,...t},n)=>C.jsx(Ug,{ref:n,className:yn("text-foreground/50 absolute right-2 top-2 rounded-md p-1 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:C.jsx(ag,{className:"h-4 w-4"})}));Kg.displayName=Ug.displayName;const Gg=g.forwardRef(({className:e,...t},n)=>C.jsx(zg,{ref:n,className:yn("text-sm font-semibold",e),...t}));Gg.displayName=zg.displayName;const Qg=g.forwardRef(({className:e,...t},n)=>C.jsx(Bg,{ref:n,className:yn("text-sm opacity-90",e),...t}));Qg.displayName=Bg.displayName;const UE=20,WE=1e3;let Ta=0;function HE(){return Ta=(Ta+1)%Number.MAX_SAFE_INTEGER,Ta.toString()}const Pa=new Map,sp=e=>{if(Pa.has(e))return;const t=setTimeout(()=>{Pa.delete(e),no({type:"REMOVE_TOAST",toastId:e})},WE);Pa.set(e,t)},KE=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,UE)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(n=>n.id===t.toast.id?{...n,...t.toast}:n)};case"DISMISS_TOAST":{const{toastId:n}=t;return n?sp(n):e.toasts.forEach(r=>{sp(r.id)}),{...e,toasts:e.toasts.map(r=>r.id===n||n===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(n=>n.id!==t.toastId)}}},Si=[];let Ci={toasts:[]};function no(e){Ci=KE(Ci,e),Si.forEach(t=>{t(Ci)})}function GE({...e}){const t=HE(),n=o=>no({type:"UPDATE_TOAST",toast:{...o,id:t}}),r=()=>no({type:"DISMISS_TOAST",toastId:t});return no({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:o=>{o||r()}}}),{id:t,dismiss:r,update:n}}function Yg(){const[e,t]=g.useState(Ci);return g.useEffect(()=>(Si.push(t),()=>{const n=Si.indexOf(t);n>-1&&Si.splice(n,1)}),[e]),{...e,toast:GE,dismiss:n=>no({type:"DISMISS_TOAST",toastId:n})}}function QE(){const{toasts:e}=Yg();return C.jsxs(zE,{children:[e.map(function({id:t,title:n,description:r,action:o,...i}){return C.jsxs(Hg,{className:"my-1 bg-gray-50",...i,children:[C.jsxs("div",{className:"grid gap-1",children:[n&&C.jsx(Gg,{children:n}),r&&C.jsx(Qg,{children:r})]}),o,C.jsx(Kg,{})]},t)}),C.jsx(Wg,{})]})}const YE=()=>C.jsx("div",{className:"absolute inset-0 -z-10 h-full w-full bg-[var(--background)] bg-[linear-gradient(to_right,var(--grid-color,rgba(139,92,246,0.025))_1px,transparent_1px),linear-gradient(to_bottom,var(--grid-color,rgba(139,92,246,0.025))_1px,transparent_1px)] bg-[size:14px_24px]"});function XE(){return C.jsx("div",{children:C.jsx("script",{type:"text/javascript",src:"https://cdnjs.buymeacoffee.com/1.0.0/button.prod.min.js","data-name":"bmc-button","data-slug":"alshaercond","data-color":"#BD5FFF","data-emoji":"☕","data-font":"Poppins","data-text":"Buy me a coffee","data-outline-color":"#000000","data-font-color":"#ffffff","data-coffee-color":"#FFDD00",children:"fdd"})})}const Ea="TUpNv8wRALuuKuDpH9PbbCatSFHVtqeByk",ZE="https://link.trustwallet.com/send?coin=195&address=TUpNv8wRALuuKuDpH9PbbCatSFHVtqeByk&token_id=TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",qE="/app/assets/qrcode.jpg",JE={initial:{scale:1,rotate:0},animate:{scale:1.08,rotate:8},exit:{scale:1,rotate:0}},ap={hidden:{scale:.4,opacity:0,rotate:-30},visible:{scale:1,opacity:1,rotate:0,transition:{type:"spring",duration:.22}},exit:{scale:.4,opacity:0,rotate:-30,transition:{duration:.12}}},ek={closed:{opacity:0,y:30,transition:{duration:.13}},open:{opacity:1,y:0,transition:{duration:.2}}};function tk(){const[e,t]=g.useState(!1),[n,r]=g.useState(!1),{toast:o}=Yg(),i=g.useCallback(async()=>{var s;try{if((s=navigator==null?void 0:navigator.clipboard)!=null&&s.writeText)await navigator.clipboard.writeText(Ea);else{const a=document.createElement("textarea");a.value=Ea,document.body.appendChild(a),a.select(),document.execCommand("copy"),document.body.removeChild(a)}r(!0),o==null||o({title:"Address copied!",description:"Thank you for supporting my work.",duration:1200}),setTimeout(()=>r(!1),1200)}catch{r(!1)}},[o]);return g.useEffect(()=>{if(!e)return;const s=a=>{a.key==="Escape"&&t(!1)};return window.addEventListener("keydown",s),()=>window.removeEventListener("keydown",s)},[e]),C.jsxs(C.Fragment,{children:[C.jsx(Pn.button,{type:"button","aria-pressed":e,title:"Support Me","aria-label":"Support Me",onClick:()=>t(s=>!s),tabIndex:0,initial:"initial",animate:e?"animate":"initial",variants:JE,whileTap:{scale:.93},className:`
          fixed z-50 bottom-10 right-10 max-md:bottom-4 max-md:right-4
          flex items-center justify-center
          w-14 h-14 max-md:w-12 max-md:h-12
          rounded-full shadow-xl border border-[var(--border)]
          bg-[var(--background)] text-[var(--primary)]
          hover:bg-[var(--muted)] hover:text-[var(--primary-foreground)]
          focus-visible:ring-4 focus-visible:ring-[var(--focus)]
          transition-all duration-200 outline-none
        `,children:C.jsx(qf,{mode:"wait",initial:!1,children:e?C.jsx(Pn.span,{variants:ap,initial:"hidden",animate:"visible",exit:"exit",className:"flex",children:C.jsx(ag,{className:"w-6 h-6 text-[var(--primary)]","aria-hidden":"true"})},"x"):C.jsx(Pn.span,{variants:ap,initial:"hidden",animate:"visible",exit:"exit",className:"flex",children:C.jsx(KT,{className:"w-6 h-6 text-[var(--destructive)]","aria-hidden":"true"})},"heart")})}),C.jsx(qf,{children:e&&C.jsxs(C.Fragment,{children:[C.jsx(Pn.div,{initial:{opacity:0},animate:{opacity:.48},exit:{opacity:0},onClick:()=>t(!1),className:"fixed inset-0 z-40",style:{background:"var(--background)",opacity:.48},"aria-label":"Close support modal",tabIndex:-1,role:"presentation"}),C.jsx(Pn.div,{initial:"closed",animate:"open",exit:"closed",variants:ek,style:{position:"fixed",bottom:"6rem",right:"1.5rem",zIndex:50,minWidth:"312px",maxWidth:"95vw"},className:`
                bg-[var(--card)] text-[var(--card-foreground)] border border-[var(--border)]
                rounded-[var(--radius)] shadow-lg px-0
              `,tabIndex:-1,"aria-modal":"true",role:"dialog",children:C.jsxs("div",{className:"flex flex-col items-center gap-4 py-4 px-6",children:[C.jsxs("span",{className:`flex items-center gap-1 px-2 py-1 bg-[var(--background)]
                    text-[var(--foreground)] text-[11px] rounded-[calc(var(--radius)*0.7)]
                    uppercase font-semibold tracking-wide border border-[var(--border)]
                    opacity-95 select-none mb-1`,children:[C.jsx(QT,{className:"w-3.5 h-3.5"}),"USDT (TRC20)"]}),C.jsx("div",{className:"flex items-center justify-center bg-[var(--background)] p-3 rounded-[var(--radius)] border border-[var(--border)]","aria-label":"QR code for USDT payment",role:"img",tabIndex:-1,children:C.jsx("img",{src:qE,alt:"QR code to send USDT (TRC20)",width:104,height:104,style:{display:"block"}})}),C.jsxs("div",{className:`
                    flex items-center gap-2 w-max bg-[var(--muted)] text-[var(--muted-foreground)]
                    px-2.5 py-2 rounded-[var(--radius)] border border-[var(--border)] font-mono
                    text-xs break-all focus-within:ring-2 focus-within:ring-[var(--focus)] transition
                  `,tabIndex:-1,"aria-label":"USDT TRC20 Wallet Address",role:"group",children:[C.jsx("span",{className:"truncate",children:Ea}),C.jsx("button",{type:"button","aria-label":"Copy wallet address","aria-pressed":n,onClick:i,className:"flex items-center hover:text-blue-600 focus:text-blue-700 transition",children:C.jsx(HT,{size:14})})]}),C.jsx("a",{href:ZE,target:"_blank",rel:"noopener noreferrer","aria-label":"Pay via Trust Wallet",tabIndex:0,children:C.jsxs(mc,{className:"bg-gray-700 gap-0 hover:bg-gray-800 border text-white border-none",children:[C.jsx("span",{children:"Trust Wallet"}),C.jsx("span",{className:"ml-2 h-[20px] w-[20px]",children:C.jsxs("svg",{width:"20",height:"22",viewBox:"0 0 444 501",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[C.jsx("path",{d:"M0.710022 72.41L222.16 0.109985V500.63C63.98 433.89 0.710022 305.98 0.710022 233.69V72.41Z",fill:"#0500FF"}),C.jsx("path",{d:"M443.62 72.41L222.17 0.109985V500.63C380.35 433.89 443.62 305.98 443.62 233.69V72.41Z",fill:"url(#trust__paint0_linear_3_10)"}),C.jsx("defs",{children:C.jsxs("linearGradient",{id:"trust__paint0_linear_3_10",x1:"385.26",y1:"-34.78",x2:"216.61",y2:"493.5",gradientUnits:"userSpaceOnUse",children:[C.jsx("stop",{offset:"0.02",stopColor:"#0000FF"}),C.jsx("stop",{offset:"0.08",stopColor:"#0094FF"}),C.jsx("stop",{offset:"0.16",stopColor:"#48FF91"}),C.jsx("stop",{offset:"0.42",stopColor:"#0094FF"}),C.jsx("stop",{offset:"0.68",stopColor:"#0038FF"}),C.jsx("stop",{offset:"0.9",stopColor:"#0500FF"})]})})]})})]})})]})},"supportMenu")]})})]})}const nk=()=>(zP(),C.jsxs("div",{dir:"ltr",className:"App pb-[30px] relative min-h-screen overflow-x-hidden",children:[C.jsx(tk,{}),C.jsx(XE,{}),C.jsx(YE,{}),C.jsx(QE,{}),C.jsx(NP,{})]}));var Xg={exports:{}};(function(e,t){(function(n,r){e.exports=r()})(qg,function(){return function(n){function r(i){if(o[i])return o[i].exports;var s=o[i]={exports:{},id:i,loaded:!1};return n[i].call(s.exports,s,s.exports,r),s.loaded=!0,s.exports}var o={};return r.m=n,r.c=o,r.p="dist/",r(0)}([function(n,r,o){function i(A){return A&&A.__esModule?A:{default:A}}var s=Object.assign||function(A){for(var V=1;V<arguments.length;V++){var B=arguments[V];for(var X in B)Object.prototype.hasOwnProperty.call(B,X)&&(A[X]=B[X])}return A},a=o(1),l=(i(a),o(6)),u=i(l),d=o(7),c=i(d),f=o(8),v=i(f),y=o(9),w=i(y),x=o(10),h=i(x),p=o(11),m=i(p),S=o(14),T=i(S),E=[],k=!1,P={offset:120,delay:0,easing:"ease",duration:400,disable:!1,once:!1,startEvent:"DOMContentLoaded",throttleDelay:99,debounceDelay:50,disableMutationObserver:!1},D=function(){var A=arguments.length>0&&arguments[0]!==void 0&&arguments[0];if(A&&(k=!0),k)return E=(0,m.default)(E,P),(0,h.default)(E,P.once),E},N=function(){E=(0,T.default)(),D()},_=function(){E.forEach(function(A,V){A.node.removeAttribute("data-aos"),A.node.removeAttribute("data-aos-easing"),A.node.removeAttribute("data-aos-duration"),A.node.removeAttribute("data-aos-delay")})},b=function(A){return A===!0||A==="mobile"&&w.default.mobile()||A==="phone"&&w.default.phone()||A==="tablet"&&w.default.tablet()||typeof A=="function"&&A()===!0},F=function(A){P=s(P,A),E=(0,T.default)();var V=document.all&&!window.atob;return b(P.disable)||V?_():(P.disableMutationObserver||v.default.isSupported()||(console.info(`
      aos: MutationObserver is not supported on this browser,
      code mutations observing has been disabled.
      You may have to call "refreshHard()" by yourself.
    `),P.disableMutationObserver=!0),document.querySelector("body").setAttribute("data-aos-easing",P.easing),document.querySelector("body").setAttribute("data-aos-duration",P.duration),document.querySelector("body").setAttribute("data-aos-delay",P.delay),P.startEvent==="DOMContentLoaded"&&["complete","interactive"].indexOf(document.readyState)>-1?D(!0):P.startEvent==="load"?window.addEventListener(P.startEvent,function(){D(!0)}):document.addEventListener(P.startEvent,function(){D(!0)}),window.addEventListener("resize",(0,c.default)(D,P.debounceDelay,!0)),window.addEventListener("orientationchange",(0,c.default)(D,P.debounceDelay,!0)),window.addEventListener("scroll",(0,u.default)(function(){(0,h.default)(E,P.once)},P.throttleDelay)),P.disableMutationObserver||v.default.ready("[data-aos]",N),E)};n.exports={init:F,refresh:D,refreshHard:N}},function(n,r){},,,,,function(n,r){(function(o){function i(b,F,A){function V(Q){var je=te,$t=De;return te=De=void 0,zt=Q,ae=b.apply($t,je)}function B(Q){return zt=Q,pe=setTimeout(j,F),Bt?V(Q):ae}function X(Q){var je=Q-$e,$t=Q-zt,wc=F-je;return wt?N(wc,ve-$t):wc}function M(Q){var je=Q-$e,$t=Q-zt;return $e===void 0||je>=F||je<0||wt&&$t>=ve}function j(){var Q=_();return M(Q)?I(Q):void(pe=setTimeout(j,X(Q)))}function I(Q){return pe=void 0,ne&&te?V(Q):(te=De=void 0,ae)}function U(){pe!==void 0&&clearTimeout(pe),zt=0,te=$e=De=pe=void 0}function W(){return pe===void 0?ae:I(_())}function de(){var Q=_(),je=M(Q);if(te=arguments,De=this,$e=Q,je){if(pe===void 0)return B($e);if(wt)return pe=setTimeout(j,F),V($e)}return pe===void 0&&(pe=setTimeout(j,F)),ae}var te,De,ve,ae,pe,$e,zt=0,Bt=!1,wt=!1,ne=!0;if(typeof b!="function")throw new TypeError(f);return F=d(F)||0,a(A)&&(Bt=!!A.leading,wt="maxWait"in A,ve=wt?D(d(A.maxWait)||0,F):ve,ne="trailing"in A?!!A.trailing:ne),de.cancel=U,de.flush=W,de}function s(b,F,A){var V=!0,B=!0;if(typeof b!="function")throw new TypeError(f);return a(A)&&(V="leading"in A?!!A.leading:V,B="trailing"in A?!!A.trailing:B),i(b,F,{leading:V,maxWait:F,trailing:B})}function a(b){var F=typeof b>"u"?"undefined":c(b);return!!b&&(F=="object"||F=="function")}function l(b){return!!b&&(typeof b>"u"?"undefined":c(b))=="object"}function u(b){return(typeof b>"u"?"undefined":c(b))=="symbol"||l(b)&&P.call(b)==y}function d(b){if(typeof b=="number")return b;if(u(b))return v;if(a(b)){var F=typeof b.valueOf=="function"?b.valueOf():b;b=a(F)?F+"":F}if(typeof b!="string")return b===0?b:+b;b=b.replace(w,"");var A=h.test(b);return A||p.test(b)?m(b.slice(2),A?2:8):x.test(b)?v:+b}var c=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(b){return typeof b}:function(b){return b&&typeof Symbol=="function"&&b.constructor===Symbol&&b!==Symbol.prototype?"symbol":typeof b},f="Expected a function",v=NaN,y="[object Symbol]",w=/^\s+|\s+$/g,x=/^[-+]0x[0-9a-f]+$/i,h=/^0b[01]+$/i,p=/^0o[0-7]+$/i,m=parseInt,S=(typeof o>"u"?"undefined":c(o))=="object"&&o&&o.Object===Object&&o,T=(typeof self>"u"?"undefined":c(self))=="object"&&self&&self.Object===Object&&self,E=S||T||Function("return this")(),k=Object.prototype,P=k.toString,D=Math.max,N=Math.min,_=function(){return E.Date.now()};n.exports=s}).call(r,function(){return this}())},function(n,r){(function(o){function i(_,b,F){function A(ne){var Q=de,je=te;return de=te=void 0,$e=ne,ve=_.apply(je,Q)}function V(ne){return $e=ne,ae=setTimeout(M,b),zt?A(ne):ve}function B(ne){var Q=ne-pe,je=ne-$e,$t=b-Q;return Bt?D($t,De-je):$t}function X(ne){var Q=ne-pe,je=ne-$e;return pe===void 0||Q>=b||Q<0||Bt&&je>=De}function M(){var ne=N();return X(ne)?j(ne):void(ae=setTimeout(M,B(ne)))}function j(ne){return ae=void 0,wt&&de?A(ne):(de=te=void 0,ve)}function I(){ae!==void 0&&clearTimeout(ae),$e=0,de=pe=te=ae=void 0}function U(){return ae===void 0?ve:j(N())}function W(){var ne=N(),Q=X(ne);if(de=arguments,te=this,pe=ne,Q){if(ae===void 0)return V(pe);if(Bt)return ae=setTimeout(M,b),A(pe)}return ae===void 0&&(ae=setTimeout(M,b)),ve}var de,te,De,ve,ae,pe,$e=0,zt=!1,Bt=!1,wt=!0;if(typeof _!="function")throw new TypeError(c);return b=u(b)||0,s(F)&&(zt=!!F.leading,Bt="maxWait"in F,De=Bt?P(u(F.maxWait)||0,b):De,wt="trailing"in F?!!F.trailing:wt),W.cancel=I,W.flush=U,W}function s(_){var b=typeof _>"u"?"undefined":d(_);return!!_&&(b=="object"||b=="function")}function a(_){return!!_&&(typeof _>"u"?"undefined":d(_))=="object"}function l(_){return(typeof _>"u"?"undefined":d(_))=="symbol"||a(_)&&k.call(_)==v}function u(_){if(typeof _=="number")return _;if(l(_))return f;if(s(_)){var b=typeof _.valueOf=="function"?_.valueOf():_;_=s(b)?b+"":b}if(typeof _!="string")return _===0?_:+_;_=_.replace(y,"");var F=x.test(_);return F||h.test(_)?p(_.slice(2),F?2:8):w.test(_)?f:+_}var d=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(_){return typeof _}:function(_){return _&&typeof Symbol=="function"&&_.constructor===Symbol&&_!==Symbol.prototype?"symbol":typeof _},c="Expected a function",f=NaN,v="[object Symbol]",y=/^\s+|\s+$/g,w=/^[-+]0x[0-9a-f]+$/i,x=/^0b[01]+$/i,h=/^0o[0-7]+$/i,p=parseInt,m=(typeof o>"u"?"undefined":d(o))=="object"&&o&&o.Object===Object&&o,S=(typeof self>"u"?"undefined":d(self))=="object"&&self&&self.Object===Object&&self,T=m||S||Function("return this")(),E=Object.prototype,k=E.toString,P=Math.max,D=Math.min,N=function(){return T.Date.now()};n.exports=i}).call(r,function(){return this}())},function(n,r){function o(d){var c=void 0,f=void 0;for(c=0;c<d.length;c+=1)if(f=d[c],f.dataset&&f.dataset.aos||f.children&&o(f.children))return!0;return!1}function i(){return window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver}function s(){return!!i()}function a(d,c){var f=window.document,v=i(),y=new v(l);u=c,y.observe(f.documentElement,{childList:!0,subtree:!0,removedNodes:!0})}function l(d){d&&d.forEach(function(c){var f=Array.prototype.slice.call(c.addedNodes),v=Array.prototype.slice.call(c.removedNodes),y=f.concat(v);if(o(y))return u()})}Object.defineProperty(r,"__esModule",{value:!0});var u=function(){};r.default={isSupported:s,ready:a}},function(n,r){function o(f,v){if(!(f instanceof v))throw new TypeError("Cannot call a class as a function")}function i(){return navigator.userAgent||navigator.vendor||window.opera||""}Object.defineProperty(r,"__esModule",{value:!0});var s=function(){function f(v,y){for(var w=0;w<y.length;w++){var x=y[w];x.enumerable=x.enumerable||!1,x.configurable=!0,"value"in x&&(x.writable=!0),Object.defineProperty(v,x.key,x)}}return function(v,y,w){return y&&f(v.prototype,y),w&&f(v,w),v}}(),a=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i,l=/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i,u=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i,d=/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i,c=function(){function f(){o(this,f)}return s(f,[{key:"phone",value:function(){var v=i();return!(!a.test(v)&&!l.test(v.substr(0,4)))}},{key:"mobile",value:function(){var v=i();return!(!u.test(v)&&!d.test(v.substr(0,4)))}},{key:"tablet",value:function(){return this.mobile()&&!this.phone()}}]),f}();r.default=new c},function(n,r){Object.defineProperty(r,"__esModule",{value:!0});var o=function(s,a,l){var u=s.node.getAttribute("data-aos-once");a>s.position?s.node.classList.add("aos-animate"):typeof u<"u"&&(u==="false"||!l&&u!=="true")&&s.node.classList.remove("aos-animate")},i=function(s,a){var l=window.pageYOffset,u=window.innerHeight;s.forEach(function(d,c){o(d,u+l,a)})};r.default=i},function(n,r,o){function i(u){return u&&u.__esModule?u:{default:u}}Object.defineProperty(r,"__esModule",{value:!0});var s=o(12),a=i(s),l=function(u,d){return u.forEach(function(c,f){c.node.classList.add("aos-init"),c.position=(0,a.default)(c.node,d.offset)}),u};r.default=l},function(n,r,o){function i(u){return u&&u.__esModule?u:{default:u}}Object.defineProperty(r,"__esModule",{value:!0});var s=o(13),a=i(s),l=function(u,d){var c=0,f=0,v=window.innerHeight,y={offset:u.getAttribute("data-aos-offset"),anchor:u.getAttribute("data-aos-anchor"),anchorPlacement:u.getAttribute("data-aos-anchor-placement")};switch(y.offset&&!isNaN(y.offset)&&(f=parseInt(y.offset)),y.anchor&&document.querySelectorAll(y.anchor)&&(u=document.querySelectorAll(y.anchor)[0]),c=(0,a.default)(u).top,y.anchorPlacement){case"top-bottom":break;case"center-bottom":c+=u.offsetHeight/2;break;case"bottom-bottom":c+=u.offsetHeight;break;case"top-center":c+=v/2;break;case"bottom-center":c+=v/2+u.offsetHeight;break;case"center-center":c+=v/2+u.offsetHeight/2;break;case"top-top":c+=v;break;case"bottom-top":c+=u.offsetHeight+v;break;case"center-top":c+=u.offsetHeight/2+v}return y.anchorPlacement||y.offset||isNaN(d)||(f=d),c+f};r.default=l},function(n,r){Object.defineProperty(r,"__esModule",{value:!0});var o=function(i){for(var s=0,a=0;i&&!isNaN(i.offsetLeft)&&!isNaN(i.offsetTop);)s+=i.offsetLeft-(i.tagName!="BODY"?i.scrollLeft:0),a+=i.offsetTop-(i.tagName!="BODY"?i.scrollTop:0),i=i.offsetParent;return{top:a,left:s}};r.default=o},function(n,r){Object.defineProperty(r,"__esModule",{value:!0});var o=function(i){return i=i||document.querySelectorAll("[data-aos]"),Array.prototype.map.call(i,function(s){return{node:s}})};r.default=o}])})})(Xg);var rk=Xg.exports;const ok=Wl(rk);ok.init();Sm.render(C.jsx(ht.StrictMode,{children:C.jsx(uw,{children:C.jsx(nk,{})})}),document.getElementById("root"));export{og as $,qf as A,mc as B,HT as C,KT as H,ic as M,ht as R,Ls as V,ag as X,ts as _,Bv as a,xe as b,Vt as c,ik as d,Lm as e,K as f,jo as g,yn as h,be as i,C as j,Fu as k,hy as l,Eo as m,Sm as n,uk as o,lk as p,rg as q,g as r,Pn as s,GE as t,uc as u,sg as v};
function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = []
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
