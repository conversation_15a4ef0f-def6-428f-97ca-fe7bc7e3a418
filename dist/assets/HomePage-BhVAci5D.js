import{u as yd,m as vd,r as s,M as Za,a as Ja,c as wd,f as Qa,i as ur,b as xd,d as kd,e as Cd,g as re,j as u,h as L,R as W,k as Ae,l as Bt,n as oo,V as Ed,o as Sd,p as te,H as $d,_ as B,$ as fr,q as me,s as es,A as ts,C as Ad,t as yn,X as Pd,v as Rd,B as Ze}from"./index-C_YRHkyC.js";function Mt(e){const t=yd(()=>vd(e)),{isStatic:r}=s.useContext(Za);if(r){const[,n]=s.useState(e);s.useEffect(()=>t.on("change",n),[])}return t}function Nd(e,t){const r=Mt(t()),n=()=>r.set(t());return n(),Ja(()=>{const o=()=>Qa.preRender(n,!1,!0),a=e.map(c=>c.on("change",o));return()=>{a.forEach(c=>c()),wd(n)}}),r}function Fd(e,...t){const r=e.length;function n(){let o="";for(let a=0;a<r;a++){o+=e[a];const c=t[a];c&&(o+=ur(c)?c.get():c)}return o}return Nd(t.filter(ur),n)}function Zo(e){return typeof e=="number"?e:parseFloat(e)}function Dd(e,t={}){const{isStatic:r}=s.useContext(Za),n=s.useRef(null),o=Mt(ur(e)?Zo(e.get()):e),a=s.useRef(o.get()),c=s.useRef(()=>{}),i=()=>{const d=n.current;d&&d.time===0&&d.sample(xd.delta),l(),n.current=kd({keyframes:[o.get(),a.current],velocity:o.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...t,onUpdate:c.current})},l=()=>{n.current&&n.current.stop()};return s.useInsertionEffect(()=>o.attach((d,h)=>r?h(d):(a.current=d,c.current=h,Qa.update(i),o.get()),l),[JSON.stringify(t)]),Ja(()=>{if(ur(e))return e.on("change",d=>o.set(Zo(d)))},[o]),o}const Td={some:0,all:1};function Md(e,t,{root:r,margin:n,amount:o="some"}={}){const a=Cd(e),c=new WeakMap,i=d=>{d.forEach(h=>{const f=c.get(h.target);if(h.isIntersecting!==!!f)if(h.isIntersecting){const g=t(h);typeof g=="function"?c.set(h.target,g):l.unobserve(h.target)}else typeof f=="function"&&(f(h),c.delete(h.target))})},l=new IntersectionObserver(i,{root:r,rootMargin:n,threshold:typeof o=="number"?o:Td[o]});return a.forEach(d=>l.observe(d)),()=>l.disconnect()}function Od(e,{root:t,margin:r,amount:n,once:o=!1}={}){const[a,c]=s.useState(!1);return s.useEffect(()=>{if(!e.current||o&&a)return;const i=()=>(c(!0),o?void 0:()=>c(!1)),l={root:t&&t.current||void 0,margin:r,amount:n};return Md(e.current,i,l)},[t,e,r,o,n]),a}/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jd=re("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nr=re("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wt=re("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _d=re("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rs=re("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Id=re("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ld=re("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bd=re("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ns=re("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wd=re("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vd=re("FileImage",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["circle",{cx:"10",cy:"13",r:"2",key:"6v46hv"}],["path",{d:"m20 17-1.09-1.09a2 2 0 0 0-2.82 0L10 22",key:"17vly1"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zd=re("FileWarning",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hd=re("Gauge",[["path",{d:"m12 14 4-4",key:"9kzdfg"}],["path",{d:"M3.34 19a10 10 0 1 1 17.32 0",key:"19p75a"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jo=re("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ud=re("Layers",[["path",{d:"m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z",key:"8b97xw"}],["path",{d:"m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65",key:"dd6zsq"}],["path",{d:"m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65",key:"ep9fru"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gd=re("Monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qo=re("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",key:"1xcu5"}],["circle",{cx:"17.5",cy:"10.5",r:".5",key:"736e4u"}],["circle",{cx:"8.5",cy:"7.5",r:".5",key:"clrty"}],["circle",{cx:"6.5",cy:"12.5",r:".5",key:"1s4xz9"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kd=re("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yd=re("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.302.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xd=re("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]),os=s.forwardRef(({className:e,type:t,...r},n)=>u.jsx("input",{type:t,className:L("flex h-10 w-full rounded-md border-2 border-[#7b7b7b7b] bg-[var(--input-background)] px-3 py-2 text-sm text-[var(--input-text)] placeholder-[var(--paragraph)] ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus:border-[var(--input-border-color)] focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:n,...r}));os.displayName="Input";function hr(e,[t,r]){return Math.min(r,Math.max(t,e))}function oe(e,t,{checkForDefaultPrevented:r=!0}={}){return function(o){if(e==null||e(o),r===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function ao(e,t=[]){let r=[];function n(a,c){const i=s.createContext(c),l=r.length;r=[...r,c];function d(f){const{scope:g,children:b,...p}=f,m=(g==null?void 0:g[e][l])||i,y=s.useMemo(()=>p,Object.values(p));return u.jsx(m.Provider,{value:y,children:b})}function h(f,g){const b=(g==null?void 0:g[e][l])||i,p=s.useContext(b);if(p)return p;if(c!==void 0)return c;throw new Error(`\`${f}\` must be used within \`${a}\``)}return d.displayName=a+"Provider",[d,h]}const o=()=>{const a=r.map(c=>s.createContext(c));return function(i){const l=(i==null?void 0:i[e])||a;return s.useMemo(()=>({[`__scope${e}`]:{...i,[e]:l}}),[i,l])}};return o.scopeName=e,[n,qd(o,...t)]}function qd(...e){const t=e[0];if(e.length===1)return t;const r=()=>{const n=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(a){const c=n.reduce((i,{useScope:l,scopeName:d})=>{const f=l(a)[`__scope${d}`];return{...i,...f}},{});return s.useMemo(()=>({[`__scope${t.scopeName}`]:c}),[c])}};return r.scopeName=t.scopeName,r}function Zd(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function as(...e){return t=>e.forEach(r=>Zd(r,t))}function se(...e){return s.useCallback(as(...e),e)}var Ot=s.forwardRef((e,t)=>{const{children:r,...n}=e,o=s.Children.toArray(r),a=o.find(Qd);if(a){const c=a.props.children,i=o.map(l=>l===a?s.Children.count(c)>1?s.Children.only(null):s.isValidElement(c)?c.props.children:null:l);return u.jsx(vn,{...n,ref:t,children:s.isValidElement(c)?s.cloneElement(c,void 0,i):null})}return u.jsx(vn,{...n,ref:t,children:r})});Ot.displayName="Slot";var vn=s.forwardRef((e,t)=>{const{children:r,...n}=e;if(s.isValidElement(r)){const o=tu(r);return s.cloneElement(r,{...eu(n,r.props),ref:t?as(t,o):o})}return s.Children.count(r)>1?s.Children.only(null):null});vn.displayName="SlotClone";var Jd=({children:e})=>u.jsx(u.Fragment,{children:e});function Qd(e){return s.isValidElement(e)&&e.type===Jd}function eu(e,t){const r={...t};for(const n in t){const o=e[n],a=t[n];/^on[A-Z]/.test(n)?o&&a?r[n]=(...i)=>{a(...i),o(...i)}:o&&(r[n]=o):n==="style"?r[n]={...o,...a}:n==="className"&&(r[n]=[o,a].filter(Boolean).join(" "))}return{...e,...r}}function tu(e){var n,o;let t=(n=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:n.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,r=t&&"isReactWarning"in t&&t.isReactWarning,r?e.props.ref:e.props.ref||e.ref)}function ru(e){const t=e+"CollectionProvider",[r,n]=ao(t),[o,a]=r(t,{collectionRef:{current:null},itemMap:new Map}),c=b=>{const{scope:p,children:m}=b,y=W.useRef(null),v=W.useRef(new Map).current;return u.jsx(o,{scope:p,itemMap:v,collectionRef:y,children:m})};c.displayName=t;const i=e+"CollectionSlot",l=W.forwardRef((b,p)=>{const{scope:m,children:y}=b,v=a(i,m),w=se(p,v.collectionRef);return u.jsx(Ot,{ref:w,children:y})});l.displayName=i;const d=e+"CollectionItemSlot",h="data-radix-collection-item",f=W.forwardRef((b,p)=>{const{scope:m,children:y,...v}=b,w=W.useRef(null),x=se(p,w),k=a(d,m);return W.useEffect(()=>(k.itemMap.set(w,{ref:w,...v}),()=>void k.itemMap.delete(w))),u.jsx(Ot,{[h]:"",ref:x,children:y})});f.displayName=d;function g(b){const p=a(e+"CollectionConsumer",b);return W.useCallback(()=>{const y=p.collectionRef.current;if(!y)return[];const v=Array.from(y.querySelectorAll(`[${h}]`));return Array.from(p.itemMap.values()).sort((k,E)=>v.indexOf(k.ref.current)-v.indexOf(E.ref.current))},[p.collectionRef,p.itemMap])}return[{Provider:c,Slot:l,ItemSlot:f},g,n]}var nu=s.createContext(void 0);function ou(e){const t=s.useContext(nu);return e||t||"ltr"}var au=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],ne=au.reduce((e,t)=>{const r=s.forwardRef((n,o)=>{const{asChild:a,...c}=n,i=a?Ot:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),u.jsx(i,{...c,ref:o})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function su(e,t){e&&Ae.flushSync(()=>e.dispatchEvent(t))}function Te(e){const t=s.useRef(e);return s.useEffect(()=>{t.current=e}),s.useMemo(()=>(...r)=>{var n;return(n=t.current)==null?void 0:n.call(t,...r)},[])}function iu(e,t=globalThis==null?void 0:globalThis.document){const r=Te(e);s.useEffect(()=>{const n=o=>{o.key==="Escape"&&r(o)};return t.addEventListener("keydown",n,{capture:!0}),()=>t.removeEventListener("keydown",n,{capture:!0})},[r,t])}var cu="DismissableLayer",wn="dismissableLayer.update",lu="dismissableLayer.pointerDownOutside",du="dismissableLayer.focusOutside",ea,ss=s.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),is=s.forwardRef((e,t)=>{const{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:n,onPointerDownOutside:o,onFocusOutside:a,onInteractOutside:c,onDismiss:i,...l}=e,d=s.useContext(ss),[h,f]=s.useState(null),g=(h==null?void 0:h.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,b]=s.useState({}),p=se(t,C=>f(C)),m=Array.from(d.layers),[y]=[...d.layersWithOutsidePointerEventsDisabled].slice(-1),v=m.indexOf(y),w=h?m.indexOf(h):-1,x=d.layersWithOutsidePointerEventsDisabled.size>0,k=w>=v,E=hu(C=>{const $=C.target,N=[...d.branches].some(D=>D.contains($));!k||N||(o==null||o(C),c==null||c(C),C.defaultPrevented||i==null||i())},g),S=mu(C=>{const $=C.target;[...d.branches].some(D=>D.contains($))||(a==null||a(C),c==null||c(C),C.defaultPrevented||i==null||i())},g);return iu(C=>{w===d.layers.size-1&&(n==null||n(C),!C.defaultPrevented&&i&&(C.preventDefault(),i()))},g),s.useEffect(()=>{if(h)return r&&(d.layersWithOutsidePointerEventsDisabled.size===0&&(ea=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),d.layersWithOutsidePointerEventsDisabled.add(h)),d.layers.add(h),ta(),()=>{r&&d.layersWithOutsidePointerEventsDisabled.size===1&&(g.body.style.pointerEvents=ea)}},[h,g,r,d]),s.useEffect(()=>()=>{h&&(d.layers.delete(h),d.layersWithOutsidePointerEventsDisabled.delete(h),ta())},[h,d]),s.useEffect(()=>{const C=()=>b({});return document.addEventListener(wn,C),()=>document.removeEventListener(wn,C)},[]),u.jsx(ne.div,{...l,ref:p,style:{pointerEvents:x?k?"auto":"none":void 0,...e.style},onFocusCapture:oe(e.onFocusCapture,S.onFocusCapture),onBlurCapture:oe(e.onBlurCapture,S.onBlurCapture),onPointerDownCapture:oe(e.onPointerDownCapture,E.onPointerDownCapture)})});is.displayName=cu;var uu="DismissableLayerBranch",fu=s.forwardRef((e,t)=>{const r=s.useContext(ss),n=s.useRef(null),o=se(t,n);return s.useEffect(()=>{const a=n.current;if(a)return r.branches.add(a),()=>{r.branches.delete(a)}},[r.branches]),u.jsx(ne.div,{...e,ref:o})});fu.displayName=uu;function hu(e,t=globalThis==null?void 0:globalThis.document){const r=Te(e),n=s.useRef(!1),o=s.useRef(()=>{});return s.useEffect(()=>{const a=i=>{if(i.target&&!n.current){let l=function(){cs(lu,r,d,{discrete:!0})};const d={originalEvent:i};i.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=l,t.addEventListener("click",o.current,{once:!0})):l()}else t.removeEventListener("click",o.current);n.current=!1},c=window.setTimeout(()=>{t.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(c),t.removeEventListener("pointerdown",a),t.removeEventListener("click",o.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}function mu(e,t=globalThis==null?void 0:globalThis.document){const r=Te(e),n=s.useRef(!1);return s.useEffect(()=>{const o=a=>{a.target&&!n.current&&cs(du,r,{originalEvent:a},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}function ta(){const e=new CustomEvent(wn);document.dispatchEvent(e)}function cs(e,t,r,{discrete:n}){const o=r.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),n?su(o,a):o.dispatchEvent(a)}var Jr=0;function gu(){s.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??ra()),document.body.insertAdjacentElement("beforeend",e[1]??ra()),Jr++,()=>{Jr===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Jr--}},[])}function ra(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}var Qr="focusScope.autoFocusOnMount",en="focusScope.autoFocusOnUnmount",na={bubbles:!1,cancelable:!0},pu="FocusScope",ls=s.forwardRef((e,t)=>{const{loop:r=!1,trapped:n=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...c}=e,[i,l]=s.useState(null),d=Te(o),h=Te(a),f=s.useRef(null),g=se(t,m=>l(m)),b=s.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;s.useEffect(()=>{if(n){let m=function(x){if(b.paused||!i)return;const k=x.target;i.contains(k)?f.current=k:Be(f.current,{select:!0})},y=function(x){if(b.paused||!i)return;const k=x.relatedTarget;k!==null&&(i.contains(k)||Be(f.current,{select:!0}))},v=function(x){if(document.activeElement===document.body)for(const E of x)E.removedNodes.length>0&&Be(i)};document.addEventListener("focusin",m),document.addEventListener("focusout",y);const w=new MutationObserver(v);return i&&w.observe(i,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",m),document.removeEventListener("focusout",y),w.disconnect()}}},[n,i,b.paused]),s.useEffect(()=>{if(i){aa.add(b);const m=document.activeElement;if(!i.contains(m)){const v=new CustomEvent(Qr,na);i.addEventListener(Qr,d),i.dispatchEvent(v),v.defaultPrevented||(bu(ku(ds(i)),{select:!0}),document.activeElement===m&&Be(i))}return()=>{i.removeEventListener(Qr,d),setTimeout(()=>{const v=new CustomEvent(en,na);i.addEventListener(en,h),i.dispatchEvent(v),v.defaultPrevented||Be(m??document.body,{select:!0}),i.removeEventListener(en,h),aa.remove(b)},0)}}},[i,d,h,b]);const p=s.useCallback(m=>{if(!r&&!n||b.paused)return;const y=m.key==="Tab"&&!m.altKey&&!m.ctrlKey&&!m.metaKey,v=document.activeElement;if(y&&v){const w=m.currentTarget,[x,k]=yu(w);x&&k?!m.shiftKey&&v===k?(m.preventDefault(),r&&Be(x,{select:!0})):m.shiftKey&&v===x&&(m.preventDefault(),r&&Be(k,{select:!0})):v===w&&m.preventDefault()}},[r,n,b.paused]);return u.jsx(ne.div,{tabIndex:-1,...c,ref:g,onKeyDown:p})});ls.displayName=pu;function bu(e,{select:t=!1}={}){const r=document.activeElement;for(const n of e)if(Be(n,{select:t}),document.activeElement!==r)return}function yu(e){const t=ds(e),r=oa(t,e),n=oa(t.reverse(),e);return[r,n]}function ds(e){const t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:n=>{const o=n.tagName==="INPUT"&&n.type==="hidden";return n.disabled||n.hidden||o?NodeFilter.FILTER_SKIP:n.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function oa(e,t){for(const r of e)if(!vu(r,{upTo:t}))return r}function vu(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function wu(e){return e instanceof HTMLInputElement&&"select"in e}function Be(e,{select:t=!1}={}){if(e&&e.focus){const r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&wu(e)&&t&&e.select()}}var aa=xu();function xu(){let e=[];return{add(t){const r=e[0];t!==r&&(r==null||r.pause()),e=sa(e,t),e.unshift(t)},remove(t){var r;e=sa(e,t),(r=e[0])==null||r.resume()}}}function sa(e,t){const r=[...e],n=r.indexOf(t);return n!==-1&&r.splice(n,1),r}function ku(e){return e.filter(t=>t.tagName!=="A")}var ge=globalThis!=null&&globalThis.document?s.useLayoutEffect:()=>{},Cu=Bt.useId||(()=>{}),Eu=0;function so(e){const[t,r]=s.useState(Cu());return ge(()=>{e||r(n=>n??String(Eu++))},[e]),e||(t?`radix-${t}`:"")}const Su=["top","right","bottom","left"],Ue=Math.min,fe=Math.max,mr=Math.round,qt=Math.floor,Ge=e=>({x:e,y:e}),$u={left:"right",right:"left",bottom:"top",top:"bottom"},Au={start:"end",end:"start"};function xn(e,t,r){return fe(e,Ue(t,r))}function Me(e,t){return typeof e=="function"?e(t):e}function Oe(e){return e.split("-")[0]}function St(e){return e.split("-")[1]}function io(e){return e==="x"?"y":"x"}function co(e){return e==="y"?"height":"width"}function $t(e){return["top","bottom"].includes(Oe(e))?"y":"x"}function lo(e){return io($t(e))}function Pu(e,t,r){r===void 0&&(r=!1);const n=St(e),o=lo(e),a=co(o);let c=o==="x"?n===(r?"end":"start")?"right":"left":n==="start"?"bottom":"top";return t.reference[a]>t.floating[a]&&(c=gr(c)),[c,gr(c)]}function Ru(e){const t=gr(e);return[kn(e),t,kn(t)]}function kn(e){return e.replace(/start|end/g,t=>Au[t])}function Nu(e,t,r){const n=["left","right"],o=["right","left"],a=["top","bottom"],c=["bottom","top"];switch(e){case"top":case"bottom":return r?t?o:n:t?n:o;case"left":case"right":return t?a:c;default:return[]}}function Fu(e,t,r,n){const o=St(e);let a=Nu(Oe(e),r==="start",n);return o&&(a=a.map(c=>c+"-"+o),t&&(a=a.concat(a.map(kn)))),a}function gr(e){return e.replace(/left|right|bottom|top/g,t=>$u[t])}function Du(e){return{top:0,right:0,bottom:0,left:0,...e}}function us(e){return typeof e!="number"?Du(e):{top:e,right:e,bottom:e,left:e}}function pr(e){return{...e,top:e.y,left:e.x,right:e.x+e.width,bottom:e.y+e.height}}function ia(e,t,r){let{reference:n,floating:o}=e;const a=$t(t),c=lo(t),i=co(c),l=Oe(t),d=a==="y",h=n.x+n.width/2-o.width/2,f=n.y+n.height/2-o.height/2,g=n[i]/2-o[i]/2;let b;switch(l){case"top":b={x:h,y:n.y-o.height};break;case"bottom":b={x:h,y:n.y+n.height};break;case"right":b={x:n.x+n.width,y:f};break;case"left":b={x:n.x-o.width,y:f};break;default:b={x:n.x,y:n.y}}switch(St(t)){case"start":b[c]-=g*(r&&d?-1:1);break;case"end":b[c]+=g*(r&&d?-1:1);break}return b}const Tu=async(e,t,r)=>{const{placement:n="bottom",strategy:o="absolute",middleware:a=[],platform:c}=r,i=a.filter(Boolean),l=await(c.isRTL==null?void 0:c.isRTL(t));let d=await c.getElementRects({reference:e,floating:t,strategy:o}),{x:h,y:f}=ia(d,n,l),g=n,b={},p=0;for(let m=0;m<i.length;m++){const{name:y,fn:v}=i[m],{x:w,y:x,data:k,reset:E}=await v({x:h,y:f,initialPlacement:n,placement:g,strategy:o,middlewareData:b,rects:d,platform:c,elements:{reference:e,floating:t}});h=w??h,f=x??f,b={...b,[y]:{...b[y],...k}},E&&p<=50&&(p++,typeof E=="object"&&(E.placement&&(g=E.placement),E.rects&&(d=E.rects===!0?await c.getElementRects({reference:e,floating:t,strategy:o}):E.rects),{x:h,y:f}=ia(d,g,l)),m=-1)}return{x:h,y:f,placement:g,strategy:o,middlewareData:b}};async function jt(e,t){var r;t===void 0&&(t={});const{x:n,y:o,platform:a,rects:c,elements:i,strategy:l}=e,{boundary:d="clippingAncestors",rootBoundary:h="viewport",elementContext:f="floating",altBoundary:g=!1,padding:b=0}=Me(t,e),p=us(b),y=i[g?f==="floating"?"reference":"floating":f],v=pr(await a.getClippingRect({element:(r=await(a.isElement==null?void 0:a.isElement(y)))==null||r?y:y.contextElement||await(a.getDocumentElement==null?void 0:a.getDocumentElement(i.floating)),boundary:d,rootBoundary:h,strategy:l})),w=f==="floating"?{...c.floating,x:n,y:o}:c.reference,x=await(a.getOffsetParent==null?void 0:a.getOffsetParent(i.floating)),k=await(a.isElement==null?void 0:a.isElement(x))?await(a.getScale==null?void 0:a.getScale(x))||{x:1,y:1}:{x:1,y:1},E=pr(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:i,rect:w,offsetParent:x,strategy:l}):w);return{top:(v.top-E.top+p.top)/k.y,bottom:(E.bottom-v.bottom+p.bottom)/k.y,left:(v.left-E.left+p.left)/k.x,right:(E.right-v.right+p.right)/k.x}}const Mu=e=>({name:"arrow",options:e,async fn(t){const{x:r,y:n,placement:o,rects:a,platform:c,elements:i,middlewareData:l}=t,{element:d,padding:h=0}=Me(e,t)||{};if(d==null)return{};const f=us(h),g={x:r,y:n},b=lo(o),p=co(b),m=await c.getDimensions(d),y=b==="y",v=y?"top":"left",w=y?"bottom":"right",x=y?"clientHeight":"clientWidth",k=a.reference[p]+a.reference[b]-g[b]-a.floating[p],E=g[b]-a.reference[b],S=await(c.getOffsetParent==null?void 0:c.getOffsetParent(d));let C=S?S[x]:0;(!C||!await(c.isElement==null?void 0:c.isElement(S)))&&(C=i.floating[x]||a.floating[p]);const $=k/2-E/2,N=C/2-m[p]/2-1,D=Ue(f[v],N),T=Ue(f[w],N),P=D,j=C-m[p]-T,I=C/2-m[p]/2+$,M=xn(P,I,j),F=!l.arrow&&St(o)!=null&&I!==M&&a.reference[p]/2-(I<P?D:T)-m[p]/2<0,R=F?I<P?I-P:I-j:0;return{[b]:g[b]+R,data:{[b]:M,centerOffset:I-M-R,...F&&{alignmentOffset:R}},reset:F}}}),Ou=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var r,n;const{placement:o,middlewareData:a,rects:c,initialPlacement:i,platform:l,elements:d}=t,{mainAxis:h=!0,crossAxis:f=!0,fallbackPlacements:g,fallbackStrategy:b="bestFit",fallbackAxisSideDirection:p="none",flipAlignment:m=!0,...y}=Me(e,t);if((r=a.arrow)!=null&&r.alignmentOffset)return{};const v=Oe(o),w=Oe(i)===i,x=await(l.isRTL==null?void 0:l.isRTL(d.floating)),k=g||(w||!m?[gr(i)]:Ru(i));!g&&p!=="none"&&k.push(...Fu(i,m,p,x));const E=[i,...k],S=await jt(t,y),C=[];let $=((n=a.flip)==null?void 0:n.overflows)||[];if(h&&C.push(S[v]),f){const P=Pu(o,c,x);C.push(S[P[0]],S[P[1]])}if($=[...$,{placement:o,overflows:C}],!C.every(P=>P<=0)){var N,D;const P=(((N=a.flip)==null?void 0:N.index)||0)+1,j=E[P];if(j)return{data:{index:P,overflows:$},reset:{placement:j}};let I=(D=$.filter(M=>M.overflows[0]<=0).sort((M,F)=>M.overflows[1]-F.overflows[1])[0])==null?void 0:D.placement;if(!I)switch(b){case"bestFit":{var T;const M=(T=$.map(F=>[F.placement,F.overflows.filter(R=>R>0).reduce((R,O)=>R+O,0)]).sort((F,R)=>F[1]-R[1])[0])==null?void 0:T[0];M&&(I=M);break}case"initialPlacement":I=i;break}if(o!==I)return{reset:{placement:I}}}return{}}}};function ca(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function la(e){return Su.some(t=>e[t]>=0)}const ju=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:r}=t,{strategy:n="referenceHidden",...o}=Me(e,t);switch(n){case"referenceHidden":{const a=await jt(t,{...o,elementContext:"reference"}),c=ca(a,r.reference);return{data:{referenceHiddenOffsets:c,referenceHidden:la(c)}}}case"escaped":{const a=await jt(t,{...o,altBoundary:!0}),c=ca(a,r.floating);return{data:{escapedOffsets:c,escaped:la(c)}}}default:return{}}}}};async function _u(e,t){const{placement:r,platform:n,elements:o}=e,a=await(n.isRTL==null?void 0:n.isRTL(o.floating)),c=Oe(r),i=St(r),l=$t(r)==="y",d=["left","top"].includes(c)?-1:1,h=a&&l?-1:1,f=Me(t,e);let{mainAxis:g,crossAxis:b,alignmentAxis:p}=typeof f=="number"?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...f};return i&&typeof p=="number"&&(b=i==="end"?p*-1:p),l?{x:b*h,y:g*d}:{x:g*d,y:b*h}}const uo=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var r,n;const{x:o,y:a,placement:c,middlewareData:i}=t,l=await _u(t,e);return c===((r=i.offset)==null?void 0:r.placement)&&(n=i.arrow)!=null&&n.alignmentOffset?{}:{x:o+l.x,y:a+l.y,data:{...l,placement:c}}}}},Iu=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:r,y:n,placement:o}=t,{mainAxis:a=!0,crossAxis:c=!1,limiter:i={fn:y=>{let{x:v,y:w}=y;return{x:v,y:w}}},...l}=Me(e,t),d={x:r,y:n},h=await jt(t,l),f=$t(Oe(o)),g=io(f);let b=d[g],p=d[f];if(a){const y=g==="y"?"top":"left",v=g==="y"?"bottom":"right",w=b+h[y],x=b-h[v];b=xn(w,b,x)}if(c){const y=f==="y"?"top":"left",v=f==="y"?"bottom":"right",w=p+h[y],x=p-h[v];p=xn(w,p,x)}const m=i.fn({...t,[g]:b,[f]:p});return{...m,data:{x:m.x-r,y:m.y-n}}}}},Lu=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:r,y:n,placement:o,rects:a,middlewareData:c}=t,{offset:i=0,mainAxis:l=!0,crossAxis:d=!0}=Me(e,t),h={x:r,y:n},f=$t(o),g=io(f);let b=h[g],p=h[f];const m=Me(i,t),y=typeof m=="number"?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(l){const x=g==="y"?"height":"width",k=a.reference[g]-a.floating[x]+y.mainAxis,E=a.reference[g]+a.reference[x]-y.mainAxis;b<k?b=k:b>E&&(b=E)}if(d){var v,w;const x=g==="y"?"width":"height",k=["top","left"].includes(Oe(o)),E=a.reference[f]-a.floating[x]+(k&&((v=c.offset)==null?void 0:v[f])||0)+(k?0:y.crossAxis),S=a.reference[f]+a.reference[x]+(k?0:((w=c.offset)==null?void 0:w[f])||0)-(k?y.crossAxis:0);p<E?p=E:p>S&&(p=S)}return{[g]:b,[f]:p}}}},Bu=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){const{placement:r,rects:n,platform:o,elements:a}=t,{apply:c=()=>{},...i}=Me(e,t),l=await jt(t,i),d=Oe(r),h=St(r),f=$t(r)==="y",{width:g,height:b}=n.floating;let p,m;d==="top"||d==="bottom"?(p=d,m=h===(await(o.isRTL==null?void 0:o.isRTL(a.floating))?"start":"end")?"left":"right"):(m=d,p=h==="end"?"top":"bottom");const y=b-l[p],v=g-l[m],w=!t.middlewareData.shift;let x=y,k=v;if(f){const S=g-l.left-l.right;k=h||w?Ue(v,S):S}else{const S=b-l.top-l.bottom;x=h||w?Ue(y,S):S}if(w&&!h){const S=fe(l.left,0),C=fe(l.right,0),$=fe(l.top,0),N=fe(l.bottom,0);f?k=g-2*(S!==0||C!==0?S+C:fe(l.left,l.right)):x=b-2*($!==0||N!==0?$+N:fe(l.top,l.bottom))}await c({...t,availableWidth:k,availableHeight:x});const E=await o.getDimensions(a.floating);return g!==E.width||b!==E.height?{reset:{rects:!0}}:{}}}};function Ke(e){return fs(e)?(e.nodeName||"").toLowerCase():"#document"}function he(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function _e(e){var t;return(t=(fs(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function fs(e){return e instanceof Node||e instanceof he(e).Node}function je(e){return e instanceof Element||e instanceof he(e).Element}function Se(e){return e instanceof HTMLElement||e instanceof he(e).HTMLElement}function da(e){return typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof he(e).ShadowRoot}function Vt(e){const{overflow:t,overflowX:r,overflowY:n,display:o}=pe(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!["inline","contents"].includes(o)}function Wu(e){return["table","td","th"].includes(Ke(e))}function fo(e){const t=ho(),r=pe(e);return r.transform!=="none"||r.perspective!=="none"||(r.containerType?r.containerType!=="normal":!1)||!t&&(r.backdropFilter?r.backdropFilter!=="none":!1)||!t&&(r.filter?r.filter!=="none":!1)||["transform","perspective","filter"].some(n=>(r.willChange||"").includes(n))||["paint","layout","strict","content"].some(n=>(r.contain||"").includes(n))}function Vu(e){let t=xt(e);for(;Se(t)&&!Fr(t);){if(fo(t))return t;t=xt(t)}return null}function ho(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Fr(e){return["html","body","#document"].includes(Ke(e))}function pe(e){return he(e).getComputedStyle(e)}function Dr(e){return je(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function xt(e){if(Ke(e)==="html")return e;const t=e.assignedSlot||e.parentNode||da(e)&&e.host||_e(e);return da(t)?t.host:t}function hs(e){const t=xt(e);return Fr(t)?e.ownerDocument?e.ownerDocument.body:e.body:Se(t)&&Vt(t)?t:hs(t)}function _t(e,t,r){var n;t===void 0&&(t=[]),r===void 0&&(r=!0);const o=hs(e),a=o===((n=e.ownerDocument)==null?void 0:n.body),c=he(o);return a?t.concat(c,c.visualViewport||[],Vt(o)?o:[],c.frameElement&&r?_t(c.frameElement):[]):t.concat(o,_t(o,[],r))}function ms(e){const t=pe(e);let r=parseFloat(t.width)||0,n=parseFloat(t.height)||0;const o=Se(e),a=o?e.offsetWidth:r,c=o?e.offsetHeight:n,i=mr(r)!==a||mr(n)!==c;return i&&(r=a,n=c),{width:r,height:n,$:i}}function mo(e){return je(e)?e:e.contextElement}function gt(e){const t=mo(e);if(!Se(t))return Ge(1);const r=t.getBoundingClientRect(),{width:n,height:o,$:a}=ms(t);let c=(a?mr(r.width):r.width)/n,i=(a?mr(r.height):r.height)/o;return(!c||!Number.isFinite(c))&&(c=1),(!i||!Number.isFinite(i))&&(i=1),{x:c,y:i}}const zu=Ge(0);function gs(e){const t=he(e);return!ho()||!t.visualViewport?zu:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Hu(e,t,r){return t===void 0&&(t=!1),!r||t&&r!==he(e)?!1:t}function Je(e,t,r,n){t===void 0&&(t=!1),r===void 0&&(r=!1);const o=e.getBoundingClientRect(),a=mo(e);let c=Ge(1);t&&(n?je(n)&&(c=gt(n)):c=gt(e));const i=Hu(a,r,n)?gs(a):Ge(0);let l=(o.left+i.x)/c.x,d=(o.top+i.y)/c.y,h=o.width/c.x,f=o.height/c.y;if(a){const g=he(a),b=n&&je(n)?he(n):n;let p=g,m=p.frameElement;for(;m&&n&&b!==p;){const y=gt(m),v=m.getBoundingClientRect(),w=pe(m),x=v.left+(m.clientLeft+parseFloat(w.paddingLeft))*y.x,k=v.top+(m.clientTop+parseFloat(w.paddingTop))*y.y;l*=y.x,d*=y.y,h*=y.x,f*=y.y,l+=x,d+=k,p=he(m),m=p.frameElement}}return pr({width:h,height:f,x:l,y:d})}const Uu=[":popover-open",":modal"];function ps(e){return Uu.some(t=>{try{return e.matches(t)}catch{return!1}})}function Gu(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e;const a=o==="fixed",c=_e(n),i=t?ps(t.floating):!1;if(n===c||i&&a)return r;let l={scrollLeft:0,scrollTop:0},d=Ge(1);const h=Ge(0),f=Se(n);if((f||!f&&!a)&&((Ke(n)!=="body"||Vt(c))&&(l=Dr(n)),Se(n))){const g=Je(n);d=gt(n),h.x=g.x+n.clientLeft,h.y=g.y+n.clientTop}return{width:r.width*d.x,height:r.height*d.y,x:r.x*d.x-l.scrollLeft*d.x+h.x,y:r.y*d.y-l.scrollTop*d.y+h.y}}function Ku(e){return Array.from(e.getClientRects())}function bs(e){return Je(_e(e)).left+Dr(e).scrollLeft}function Yu(e){const t=_e(e),r=Dr(e),n=e.ownerDocument.body,o=fe(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),a=fe(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight);let c=-r.scrollLeft+bs(e);const i=-r.scrollTop;return pe(n).direction==="rtl"&&(c+=fe(t.clientWidth,n.clientWidth)-o),{width:o,height:a,x:c,y:i}}function Xu(e,t){const r=he(e),n=_e(e),o=r.visualViewport;let a=n.clientWidth,c=n.clientHeight,i=0,l=0;if(o){a=o.width,c=o.height;const d=ho();(!d||d&&t==="fixed")&&(i=o.offsetLeft,l=o.offsetTop)}return{width:a,height:c,x:i,y:l}}function qu(e,t){const r=Je(e,!0,t==="fixed"),n=r.top+e.clientTop,o=r.left+e.clientLeft,a=Se(e)?gt(e):Ge(1),c=e.clientWidth*a.x,i=e.clientHeight*a.y,l=o*a.x,d=n*a.y;return{width:c,height:i,x:l,y:d}}function ua(e,t,r){let n;if(t==="viewport")n=Xu(e,r);else if(t==="document")n=Yu(_e(e));else if(je(t))n=qu(t,r);else{const o=gs(e);n={...t,x:t.x-o.x,y:t.y-o.y}}return pr(n)}function ys(e,t){const r=xt(e);return r===t||!je(r)||Fr(r)?!1:pe(r).position==="fixed"||ys(r,t)}function Zu(e,t){const r=t.get(e);if(r)return r;let n=_t(e,[],!1).filter(i=>je(i)&&Ke(i)!=="body"),o=null;const a=pe(e).position==="fixed";let c=a?xt(e):e;for(;je(c)&&!Fr(c);){const i=pe(c),l=fo(c);!l&&i.position==="fixed"&&(o=null),(a?!l&&!o:!l&&i.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||Vt(c)&&!l&&ys(e,c))?n=n.filter(h=>h!==c):o=i,c=xt(c)}return t.set(e,n),n}function Ju(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e;const c=[...r==="clippingAncestors"?Zu(t,this._c):[].concat(r),n],i=c[0],l=c.reduce((d,h)=>{const f=ua(t,h,o);return d.top=fe(f.top,d.top),d.right=Ue(f.right,d.right),d.bottom=Ue(f.bottom,d.bottom),d.left=fe(f.left,d.left),d},ua(t,i,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function Qu(e){const{width:t,height:r}=ms(e);return{width:t,height:r}}function ef(e,t,r){const n=Se(t),o=_e(t),a=r==="fixed",c=Je(e,!0,a,t);let i={scrollLeft:0,scrollTop:0};const l=Ge(0);if(n||!n&&!a)if((Ke(t)!=="body"||Vt(o))&&(i=Dr(t)),n){const f=Je(t,!0,a,t);l.x=f.x+t.clientLeft,l.y=f.y+t.clientTop}else o&&(l.x=bs(o));const d=c.left+i.scrollLeft-l.x,h=c.top+i.scrollTop-l.y;return{x:d,y:h,width:c.width,height:c.height}}function fa(e,t){return!Se(e)||pe(e).position==="fixed"?null:t?t(e):e.offsetParent}function vs(e,t){const r=he(e);if(!Se(e)||ps(e))return r;let n=fa(e,t);for(;n&&Wu(n)&&pe(n).position==="static";)n=fa(n,t);return n&&(Ke(n)==="html"||Ke(n)==="body"&&pe(n).position==="static"&&!fo(n))?r:n||Vu(e)||r}const tf=async function(e){const t=this.getOffsetParent||vs,r=this.getDimensions;return{reference:ef(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,...await r(e.floating)}}};function rf(e){return pe(e).direction==="rtl"}const nf={convertOffsetParentRelativeRectToViewportRelativeRect:Gu,getDocumentElement:_e,getClippingRect:Ju,getOffsetParent:vs,getElementRects:tf,getClientRects:Ku,getDimensions:Qu,getScale:gt,isElement:je,isRTL:rf};function of(e,t){let r=null,n;const o=_e(e);function a(){var i;clearTimeout(n),(i=r)==null||i.disconnect(),r=null}function c(i,l){i===void 0&&(i=!1),l===void 0&&(l=1),a();const{left:d,top:h,width:f,height:g}=e.getBoundingClientRect();if(i||t(),!f||!g)return;const b=qt(h),p=qt(o.clientWidth-(d+f)),m=qt(o.clientHeight-(h+g)),y=qt(d),w={rootMargin:-b+"px "+-p+"px "+-m+"px "+-y+"px",threshold:fe(0,Ue(1,l))||1};let x=!0;function k(E){const S=E[0].intersectionRatio;if(S!==l){if(!x)return c();S?c(!1,S):n=setTimeout(()=>{c(!1,1e-7)},100)}x=!1}try{r=new IntersectionObserver(k,{...w,root:o.ownerDocument})}catch{r=new IntersectionObserver(k,w)}r.observe(e)}return c(!0),a}function go(e,t,r,n){n===void 0&&(n={});const{ancestorScroll:o=!0,ancestorResize:a=!0,elementResize:c=typeof ResizeObserver=="function",layoutShift:i=typeof IntersectionObserver=="function",animationFrame:l=!1}=n,d=mo(e),h=o||a?[...d?_t(d):[],..._t(t)]:[];h.forEach(v=>{o&&v.addEventListener("scroll",r,{passive:!0}),a&&v.addEventListener("resize",r)});const f=d&&i?of(d,r):null;let g=-1,b=null;c&&(b=new ResizeObserver(v=>{let[w]=v;w&&w.target===d&&b&&(b.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var x;(x=b)==null||x.observe(t)})),r()}),d&&!l&&b.observe(d),b.observe(t));let p,m=l?Je(e):null;l&&y();function y(){const v=Je(e);m&&(v.x!==m.x||v.y!==m.y||v.width!==m.width||v.height!==m.height)&&r(),m=v,p=requestAnimationFrame(y)}return r(),()=>{var v;h.forEach(w=>{o&&w.removeEventListener("scroll",r),a&&w.removeEventListener("resize",r)}),f==null||f(),(v=b)==null||v.disconnect(),b=null,l&&cancelAnimationFrame(p)}}const po=Iu,bo=Ou,yo=Bu,vo=ju,ha=Mu,wo=Lu,af=(e,t,r)=>{const n=new Map,o={platform:nf,...r},a={...o.platform,_c:n};return Tu(e,t,{...o,platform:a})},xo=e=>{function t(r){return{}.hasOwnProperty.call(r,"current")}return{name:"arrow",options:e,fn(r){const{element:n,padding:o}=typeof e=="function"?e(r):e;return n&&t(n)?n.current!=null?ha({element:n.current,padding:o}).fn(r):{}:n?ha({element:n,padding:o}).fn(r):{}}}};var dr=typeof document<"u"?s.useLayoutEffect:s.useEffect;function br(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let r,n,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(r=e.length,r!==t.length)return!1;for(n=r;n--!==0;)if(!br(e[n],t[n]))return!1;return!0}if(o=Object.keys(e),r=o.length,r!==Object.keys(t).length)return!1;for(n=r;n--!==0;)if(!{}.hasOwnProperty.call(t,o[n]))return!1;for(n=r;n--!==0;){const a=o[n];if(!(a==="_owner"&&e.$$typeof)&&!br(e[a],t[a]))return!1}return!0}return e!==e&&t!==t}function ws(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ma(e,t){const r=ws(e);return Math.round(t*r)/r}function ga(e){const t=s.useRef(e);return dr(()=>{t.current=e}),t}function ko(e){e===void 0&&(e={});const{placement:t="bottom",strategy:r="absolute",middleware:n=[],platform:o,elements:{reference:a,floating:c}={},transform:i=!0,whileElementsMounted:l,open:d}=e,[h,f]=s.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[g,b]=s.useState(n);br(g,n)||b(n);const[p,m]=s.useState(null),[y,v]=s.useState(null),w=s.useCallback(R=>{R!==S.current&&(S.current=R,m(R))},[]),x=s.useCallback(R=>{R!==C.current&&(C.current=R,v(R))},[]),k=a||p,E=c||y,S=s.useRef(null),C=s.useRef(null),$=s.useRef(h),N=l!=null,D=ga(l),T=ga(o),P=s.useCallback(()=>{if(!S.current||!C.current)return;const R={placement:t,strategy:r,middleware:g};T.current&&(R.platform=T.current),af(S.current,C.current,R).then(O=>{const A={...O,isPositioned:!0};j.current&&!br($.current,A)&&($.current=A,Ae.flushSync(()=>{f(A)}))})},[g,t,r,T]);dr(()=>{d===!1&&$.current.isPositioned&&($.current.isPositioned=!1,f(R=>({...R,isPositioned:!1})))},[d]);const j=s.useRef(!1);dr(()=>(j.current=!0,()=>{j.current=!1}),[]),dr(()=>{if(k&&(S.current=k),E&&(C.current=E),k&&E){if(D.current)return D.current(k,E,P);P()}},[k,E,P,D,N]);const I=s.useMemo(()=>({reference:S,floating:C,setReference:w,setFloating:x}),[w,x]),M=s.useMemo(()=>({reference:k,floating:E}),[k,E]),F=s.useMemo(()=>{const R={position:r,left:0,top:0};if(!M.floating)return R;const O=ma(M.floating,h.x),A=ma(M.floating,h.y);return i?{...R,transform:"translate("+O+"px, "+A+"px)",...ws(M.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:O,top:A}},[r,i,M.floating,h.x,h.y]);return s.useMemo(()=>({...h,update:P,refs:I,elements:M,floatingStyles:F}),[h,P,I,M,F])}var sf="Arrow",xs=s.forwardRef((e,t)=>{const{children:r,width:n=10,height:o=5,...a}=e;return u.jsx(ne.svg,{...a,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:u.jsx("polygon",{points:"0,0 30,0 15,10"})})});xs.displayName=sf;var cf=xs;function lf(e){const[t,r]=s.useState(void 0);return ge(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});const n=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const a=o[0];let c,i;if("borderBoxSize"in a){const l=a.borderBoxSize,d=Array.isArray(l)?l[0]:l;c=d.inlineSize,i=d.blockSize}else c=e.offsetWidth,i=e.offsetHeight;r({width:c,height:i})});return n.observe(e,{box:"border-box"}),()=>n.unobserve(e)}else r(void 0)},[e]),t}var Co="Popper",[ks,Cs]=ao(Co),[df,Es]=ks(Co),Ss=e=>{const{__scopePopper:t,children:r}=e,[n,o]=s.useState(null);return u.jsx(df,{scope:t,anchor:n,onAnchorChange:o,children:r})};Ss.displayName=Co;var $s="PopperAnchor",As=s.forwardRef((e,t)=>{const{__scopePopper:r,virtualRef:n,...o}=e,a=Es($s,r),c=s.useRef(null),i=se(t,c);return s.useEffect(()=>{a.onAnchorChange((n==null?void 0:n.current)||c.current)}),n?null:u.jsx(ne.div,{...o,ref:i})});As.displayName=$s;var Eo="PopperContent",[uf,ff]=ks(Eo),Ps=s.forwardRef((e,t)=>{var _,K,z,H,Y,X;const{__scopePopper:r,side:n="bottom",sideOffset:o=0,align:a="center",alignOffset:c=0,arrowPadding:i=0,avoidCollisions:l=!0,collisionBoundary:d=[],collisionPadding:h=0,sticky:f="partial",hideWhenDetached:g=!1,updatePositionStrategy:b="optimized",onPlaced:p,...m}=e,y=Es(Eo,r),[v,w]=s.useState(null),x=se(t,Q=>w(Q)),[k,E]=s.useState(null),S=lf(k),C=(S==null?void 0:S.width)??0,$=(S==null?void 0:S.height)??0,N=n+(a!=="center"?"-"+a:""),D=typeof h=="number"?h:{top:0,right:0,bottom:0,left:0,...h},T=Array.isArray(d)?d:[d],P=T.length>0,j={padding:D,boundary:T.filter(mf),altBoundary:P},{refs:I,floatingStyles:M,placement:F,isPositioned:R,middlewareData:O}=ko({strategy:"fixed",placement:N,whileElementsMounted:(...Q)=>go(...Q,{animationFrame:b==="always"}),elements:{reference:y.anchor},middleware:[uo({mainAxis:o+$,alignmentAxis:c}),l&&po({mainAxis:!0,crossAxis:!1,limiter:f==="partial"?wo():void 0,...j}),l&&bo({...j}),yo({...j,apply:({elements:Q,rects:de,availableWidth:ue,availableHeight:Ce})=>{const{width:Pe,height:Dt}=de.reference,ve=Q.floating.style;ve.setProperty("--radix-popper-available-width",`${ue}px`),ve.setProperty("--radix-popper-available-height",`${Ce}px`),ve.setProperty("--radix-popper-anchor-width",`${Pe}px`),ve.setProperty("--radix-popper-anchor-height",`${Dt}px`)}}),k&&xo({element:k,padding:i}),gf({arrowWidth:C,arrowHeight:$}),g&&vo({strategy:"referenceHidden",...j})]}),[A,G]=Fs(F),V=Te(p);ge(()=>{R&&(V==null||V())},[R,V]);const ae=(_=O.arrow)==null?void 0:_.x,ie=(K=O.arrow)==null?void 0:K.y,ce=((z=O.arrow)==null?void 0:z.centerOffset)!==0,[le,J]=s.useState();return ge(()=>{v&&J(window.getComputedStyle(v).zIndex)},[v]),u.jsx("div",{ref:I.setFloating,"data-radix-popper-content-wrapper":"",style:{...M,transform:R?M.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:le,"--radix-popper-transform-origin":[(H=O.transformOrigin)==null?void 0:H.x,(Y=O.transformOrigin)==null?void 0:Y.y].join(" "),...((X=O.hide)==null?void 0:X.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:u.jsx(uf,{scope:r,placedSide:A,onArrowChange:E,arrowX:ae,arrowY:ie,shouldHideArrow:ce,children:u.jsx(ne.div,{"data-side":A,"data-align":G,...m,ref:x,style:{...m.style,animation:R?void 0:"none"}})})})});Ps.displayName=Eo;var Rs="PopperArrow",hf={top:"bottom",right:"left",bottom:"top",left:"right"},Ns=s.forwardRef(function(t,r){const{__scopePopper:n,...o}=t,a=ff(Rs,n),c=hf[a.placedSide];return u.jsx("span",{ref:a.onArrowChange,style:{position:"absolute",left:a.arrowX,top:a.arrowY,[c]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[a.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[a.placedSide],visibility:a.shouldHideArrow?"hidden":void 0},children:u.jsx(cf,{...o,ref:r,style:{...o.style,display:"block"}})})});Ns.displayName=Rs;function mf(e){return e!==null}var gf=e=>({name:"transformOrigin",options:e,fn(t){var y,v,w;const{placement:r,rects:n,middlewareData:o}=t,c=((y=o.arrow)==null?void 0:y.centerOffset)!==0,i=c?0:e.arrowWidth,l=c?0:e.arrowHeight,[d,h]=Fs(r),f={start:"0%",center:"50%",end:"100%"}[h],g=(((v=o.arrow)==null?void 0:v.x)??0)+i/2,b=(((w=o.arrow)==null?void 0:w.y)??0)+l/2;let p="",m="";return d==="bottom"?(p=c?f:`${g}px`,m=`${-l}px`):d==="top"?(p=c?f:`${g}px`,m=`${n.floating.height+l}px`):d==="right"?(p=`${-l}px`,m=c?f:`${b}px`):d==="left"&&(p=`${n.floating.width+l}px`,m=c?f:`${b}px`),{data:{x:p,y:m}}}});function Fs(e){const[t,r="center"]=e.split("-");return[t,r]}var pf=Ss,bf=As,yf=Ps,vf=Ns,wf="Portal",Ds=s.forwardRef((e,t)=>{var i;const{container:r,...n}=e,[o,a]=s.useState(!1);ge(()=>a(!0),[]);const c=r||o&&((i=globalThis==null?void 0:globalThis.document)==null?void 0:i.body);return c?oo.createPortal(u.jsx(ne.div,{...n,ref:t}),c):null});Ds.displayName=wf;function pa({prop:e,defaultProp:t,onChange:r=()=>{}}){const[n,o]=xf({defaultProp:t,onChange:r}),a=e!==void 0,c=a?e:n,i=Te(r),l=s.useCallback(d=>{if(a){const f=typeof d=="function"?d(e):d;f!==e&&i(f)}else o(d)},[a,e,o,i]);return[c,l]}function xf({defaultProp:e,onChange:t}){const r=s.useState(e),[n]=r,o=s.useRef(n),a=Te(t);return s.useEffect(()=>{o.current!==n&&(a(n),o.current=n)},[n,o,a]),r}function Ts(e){const t=s.useRef({value:e,previous:e});return s.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var kf=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},it=new WeakMap,Zt=new WeakMap,Jt={},tn=0,Ms=function(e){return e&&(e.host||Ms(e.parentNode))},Cf=function(e,t){return t.map(function(r){if(e.contains(r))return r;var n=Ms(r);return n&&e.contains(n)?n:(console.error("aria-hidden",r,"in not contained inside",e,". Doing nothing"),null)}).filter(function(r){return!!r})},Ef=function(e,t,r,n){var o=Cf(t,Array.isArray(e)?e:[e]);Jt[r]||(Jt[r]=new WeakMap);var a=Jt[r],c=[],i=new Set,l=new Set(o),d=function(f){!f||i.has(f)||(i.add(f),d(f.parentNode))};o.forEach(d);var h=function(f){!f||l.has(f)||Array.prototype.forEach.call(f.children,function(g){if(i.has(g))h(g);else{var b=g.getAttribute(n),p=b!==null&&b!=="false",m=(it.get(g)||0)+1,y=(a.get(g)||0)+1;it.set(g,m),a.set(g,y),c.push(g),m===1&&p&&Zt.set(g,!0),y===1&&g.setAttribute(r,"true"),p||g.setAttribute(n,"true")}})};return h(t),i.clear(),tn++,function(){c.forEach(function(f){var g=it.get(f)-1,b=a.get(f)-1;it.set(f,g),a.set(f,b),g||(Zt.has(f)||f.removeAttribute(n),Zt.delete(f)),b||f.removeAttribute(r)}),tn--,tn||(it=new WeakMap,it=new WeakMap,Zt=new WeakMap,Jt={})}},So=function(e,t,r){r===void 0&&(r="data-aria-hidden");var n=Array.from(Array.isArray(e)?e:[e]),o=t||kf(e);return o?(n.push.apply(n,Array.from(o.querySelectorAll("[aria-live]"))),Ef(n,o,r,"aria-hidden")):function(){return null}},Z=function(){return Z=Object.assign||function(t){for(var r,n=1,o=arguments.length;n<o;n++){r=arguments[n];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(t[a]=r[a])}return t},Z.apply(this,arguments)};function Tr(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}function $o(e,t,r){if(r||arguments.length===2)for(var n=0,o=t.length,a;n<o;n++)(a||!(n in t))&&(a||(a=Array.prototype.slice.call(t,0,n)),a[n]=t[n]);return e.concat(a||Array.prototype.slice.call(t))}var pt="right-scroll-bar-position",bt="width-before-scroll-bar",Sf="with-scroll-bars-hidden",$f="--removed-body-scroll-bar-size";function rn(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function Af(e,t){var r=s.useState(function(){return{value:e,callback:t,facade:{get current(){return r.value},set current(n){var o=r.value;o!==n&&(r.value=n,r.callback(n,o))}}}})[0];return r.callback=t,r.facade}var Pf=typeof window<"u"?s.useLayoutEffect:s.useEffect,ba=new WeakMap;function Ao(e,t){var r=Af(t||null,function(n){return e.forEach(function(o){return rn(o,n)})});return Pf(function(){var n=ba.get(r);if(n){var o=new Set(n),a=new Set(e),c=r.current;o.forEach(function(i){a.has(i)||rn(i,null)}),a.forEach(function(i){o.has(i)||rn(i,c)})}ba.set(r,e)},[e]),r}function Rf(e){return e}function Nf(e,t){t===void 0&&(t=Rf);var r=[],n=!1,o={read:function(){if(n)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:e},useMedium:function(a){var c=t(a,n);return r.push(c),function(){r=r.filter(function(i){return i!==c})}},assignSyncMedium:function(a){for(n=!0;r.length;){var c=r;r=[],c.forEach(a)}r={push:function(i){return a(i)},filter:function(){return r}}},assignMedium:function(a){n=!0;var c=[];if(r.length){var i=r;r=[],i.forEach(a),c=r}var l=function(){var h=c;c=[],h.forEach(a)},d=function(){return Promise.resolve().then(l)};d(),r={push:function(h){c.push(h),d()},filter:function(h){return c=c.filter(h),r}}}};return o}function Po(e){e===void 0&&(e={});var t=Nf(null);return t.options=Z({async:!0,ssr:!1},e),t}var Os=function(e){var t=e.sideCar,r=Tr(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw new Error("Sidecar medium not found");return s.createElement(n,Z({},r))};Os.isSideCarExport=!0;function Ro(e,t){return e.useMedium(t),Os}var js=Po(),nn=function(){},Mr=s.forwardRef(function(e,t){var r=s.useRef(null),n=s.useState({onScrollCapture:nn,onWheelCapture:nn,onTouchMoveCapture:nn}),o=n[0],a=n[1],c=e.forwardProps,i=e.children,l=e.className,d=e.removeScrollBar,h=e.enabled,f=e.shards,g=e.sideCar,b=e.noIsolation,p=e.inert,m=e.allowPinchZoom,y=e.as,v=y===void 0?"div":y,w=e.gapMode,x=Tr(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),k=g,E=Ao([r,t]),S=Z(Z({},x),o);return s.createElement(s.Fragment,null,h&&s.createElement(k,{sideCar:js,removeScrollBar:d,shards:f,noIsolation:b,inert:p,setCallbacks:a,allowPinchZoom:!!m,lockRef:r,gapMode:w}),c?s.cloneElement(s.Children.only(i),Z(Z({},S),{ref:E})):s.createElement(v,Z({},S,{className:l,ref:E}),i))});Mr.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Mr.classNames={fullWidth:bt,zeroRight:pt};var Ff=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Df(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Ff();return t&&e.setAttribute("nonce",t),e}function Tf(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Mf(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var Of=function(){var e=0,t=null;return{add:function(r){e==0&&(t=Df())&&(Tf(t,r),Mf(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},jf=function(){var e=Of();return function(t,r){s.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},Or=function(){var e=jf(),t=function(r){var n=r.styles,o=r.dynamic;return e(n,o),null};return t},_f={left:0,top:0,right:0,gap:0},on=function(e){return parseInt(e||"",10)||0},If=function(e){var t=window.getComputedStyle(document.body),r=t[e==="padding"?"paddingLeft":"marginLeft"],n=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[on(r),on(n),on(o)]},Lf=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return _f;var t=If(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},Bf=Or(),yt="data-scroll-locked",Wf=function(e,t,r,n){var o=e.left,a=e.top,c=e.right,i=e.gap;return r===void 0&&(r="margin"),`
  .`.concat(Sf,` {
   overflow: hidden `).concat(n,`;
   padding-right: `).concat(i,"px ").concat(n,`;
  }
  body[`).concat(yt,`] {
    overflow: hidden `).concat(n,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(n,";"),r==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(a,`px;
    padding-right: `).concat(c,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(i,"px ").concat(n,`;
    `),r==="padding"&&"padding-right: ".concat(i,"px ").concat(n,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(pt,` {
    right: `).concat(i,"px ").concat(n,`;
  }
  
  .`).concat(bt,` {
    margin-right: `).concat(i,"px ").concat(n,`;
  }
  
  .`).concat(pt," .").concat(pt,` {
    right: 0 `).concat(n,`;
  }
  
  .`).concat(bt," .").concat(bt,` {
    margin-right: 0 `).concat(n,`;
  }
  
  body[`).concat(yt,`] {
    `).concat($f,": ").concat(i,`px;
  }
`)},ya=function(){var e=parseInt(document.body.getAttribute(yt)||"0",10);return isFinite(e)?e:0},Vf=function(){s.useEffect(function(){return document.body.setAttribute(yt,(ya()+1).toString()),function(){var e=ya()-1;e<=0?document.body.removeAttribute(yt):document.body.setAttribute(yt,e.toString())}},[])},No=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,o=n===void 0?"margin":n;Vf();var a=s.useMemo(function(){return Lf(o)},[o]);return s.createElement(Bf,{styles:Wf(a,!t,o,r?"":"!important")})},Cn=!1;if(typeof window<"u")try{var Qt=Object.defineProperty({},"passive",{get:function(){return Cn=!0,!0}});window.addEventListener("test",Qt,Qt),window.removeEventListener("test",Qt,Qt)}catch{Cn=!1}var ct=Cn?{passive:!1}:!1,zf=function(e){return e.tagName==="TEXTAREA"},_s=function(e,t){var r=window.getComputedStyle(e);return r[t]!=="hidden"&&!(r.overflowY===r.overflowX&&!zf(e)&&r[t]==="visible")},Hf=function(e){return _s(e,"overflowY")},Uf=function(e){return _s(e,"overflowX")},va=function(e,t){var r=t.ownerDocument,n=t;do{typeof ShadowRoot<"u"&&n instanceof ShadowRoot&&(n=n.host);var o=Is(e,n);if(o){var a=Ls(e,n),c=a[1],i=a[2];if(c>i)return!0}n=n.parentNode}while(n&&n!==r.body);return!1},Gf=function(e){var t=e.scrollTop,r=e.scrollHeight,n=e.clientHeight;return[t,r,n]},Kf=function(e){var t=e.scrollLeft,r=e.scrollWidth,n=e.clientWidth;return[t,r,n]},Is=function(e,t){return e==="v"?Hf(t):Uf(t)},Ls=function(e,t){return e==="v"?Gf(t):Kf(t)},Yf=function(e,t){return e==="h"&&t==="rtl"?-1:1},Xf=function(e,t,r,n,o){var a=Yf(e,window.getComputedStyle(t).direction),c=a*n,i=r.target,l=t.contains(i),d=!1,h=c>0,f=0,g=0;do{var b=Ls(e,i),p=b[0],m=b[1],y=b[2],v=m-y-a*p;(p||v)&&Is(e,i)&&(f+=v,g+=p),i instanceof ShadowRoot?i=i.host:i=i.parentNode}while(!l&&i!==document.body||l&&(t.contains(i)||t===i));return(h&&(o&&Math.abs(f)<1||!o&&c>f)||!h&&(o&&Math.abs(g)<1||!o&&-c>g))&&(d=!0),d},er=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},wa=function(e){return[e.deltaX,e.deltaY]},xa=function(e){return e&&"current"in e?e.current:e},qf=function(e,t){return e[0]===t[0]&&e[1]===t[1]},Zf=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},Jf=0,lt=[];function Qf(e){var t=s.useRef([]),r=s.useRef([0,0]),n=s.useRef(),o=s.useState(Jf++)[0],a=s.useState(Or)[0],c=s.useRef(e);s.useEffect(function(){c.current=e},[e]),s.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var m=$o([e.lockRef.current],(e.shards||[]).map(xa),!0).filter(Boolean);return m.forEach(function(y){return y.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),m.forEach(function(y){return y.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var i=s.useCallback(function(m,y){if("touches"in m&&m.touches.length===2)return!c.current.allowPinchZoom;var v=er(m),w=r.current,x="deltaX"in m?m.deltaX:w[0]-v[0],k="deltaY"in m?m.deltaY:w[1]-v[1],E,S=m.target,C=Math.abs(x)>Math.abs(k)?"h":"v";if("touches"in m&&C==="h"&&S.type==="range")return!1;var $=va(C,S);if(!$)return!0;if($?E=C:(E=C==="v"?"h":"v",$=va(C,S)),!$)return!1;if(!n.current&&"changedTouches"in m&&(x||k)&&(n.current=E),!E)return!0;var N=n.current||E;return Xf(N,y,m,N==="h"?x:k,!0)},[]),l=s.useCallback(function(m){var y=m;if(!(!lt.length||lt[lt.length-1]!==a)){var v="deltaY"in y?wa(y):er(y),w=t.current.filter(function(E){return E.name===y.type&&(E.target===y.target||y.target===E.shadowParent)&&qf(E.delta,v)})[0];if(w&&w.should){y.cancelable&&y.preventDefault();return}if(!w){var x=(c.current.shards||[]).map(xa).filter(Boolean).filter(function(E){return E.contains(y.target)}),k=x.length>0?i(y,x[0]):!c.current.noIsolation;k&&y.cancelable&&y.preventDefault()}}},[]),d=s.useCallback(function(m,y,v,w){var x={name:m,delta:y,target:v,should:w,shadowParent:eh(v)};t.current.push(x),setTimeout(function(){t.current=t.current.filter(function(k){return k!==x})},1)},[]),h=s.useCallback(function(m){r.current=er(m),n.current=void 0},[]),f=s.useCallback(function(m){d(m.type,wa(m),m.target,i(m,e.lockRef.current))},[]),g=s.useCallback(function(m){d(m.type,er(m),m.target,i(m,e.lockRef.current))},[]);s.useEffect(function(){return lt.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:g}),document.addEventListener("wheel",l,ct),document.addEventListener("touchmove",l,ct),document.addEventListener("touchstart",h,ct),function(){lt=lt.filter(function(m){return m!==a}),document.removeEventListener("wheel",l,ct),document.removeEventListener("touchmove",l,ct),document.removeEventListener("touchstart",h,ct)}},[]);var b=e.removeScrollBar,p=e.inert;return s.createElement(s.Fragment,null,p?s.createElement(a,{styles:Zf(o)}):null,b?s.createElement(No,{gapMode:e.gapMode}):null)}function eh(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const th=Ro(js,Qf);var Bs=s.forwardRef(function(e,t){return s.createElement(Mr,Z({},e,{ref:t,sideCar:th}))});Bs.classNames=Mr.classNames;const rh=Bs;var nh=[" ","Enter","ArrowUp","ArrowDown"],oh=[" ","Enter"],zt="Select",[jr,_r,ah]=ru(zt),[At,rw]=ao(zt,[ah,Cs]),Ir=Cs(),[sh,Ye]=At(zt),[ih,ch]=At(zt),Ws=e=>{const{__scopeSelect:t,children:r,open:n,defaultOpen:o,onOpenChange:a,value:c,defaultValue:i,onValueChange:l,dir:d,name:h,autoComplete:f,disabled:g,required:b}=e,p=Ir(t),[m,y]=s.useState(null),[v,w]=s.useState(null),[x,k]=s.useState(!1),E=ou(d),[S=!1,C]=pa({prop:n,defaultProp:o,onChange:a}),[$,N]=pa({prop:c,defaultProp:i,onChange:l}),D=s.useRef(null),T=m?!!m.closest("form"):!0,[P,j]=s.useState(new Set),I=Array.from(P).map(M=>M.props.value).join(";");return u.jsx(pf,{...p,children:u.jsxs(sh,{required:b,scope:t,trigger:m,onTriggerChange:y,valueNode:v,onValueNodeChange:w,valueNodeHasChildren:x,onValueNodeHasChildrenChange:k,contentId:so(),value:$,onValueChange:N,open:S,onOpenChange:C,dir:E,triggerPointerDownPosRef:D,disabled:g,children:[u.jsx(jr.Provider,{scope:t,children:u.jsx(ih,{scope:e.__scopeSelect,onNativeOptionAdd:s.useCallback(M=>{j(F=>new Set(F).add(M))},[]),onNativeOptionRemove:s.useCallback(M=>{j(F=>{const R=new Set(F);return R.delete(M),R})},[]),children:r})}),T?u.jsxs(fi,{"aria-hidden":!0,required:b,tabIndex:-1,name:h,autoComplete:f,value:$,onChange:M=>N(M.target.value),disabled:g,children:[$===void 0?u.jsx("option",{value:""}):null,Array.from(P)]},I):null]})})};Ws.displayName=zt;var Vs="SelectTrigger",zs=s.forwardRef((e,t)=>{const{__scopeSelect:r,disabled:n=!1,...o}=e,a=Ir(r),c=Ye(Vs,r),i=c.disabled||n,l=se(t,c.onTriggerChange),d=_r(r),[h,f,g]=hi(p=>{const m=d().filter(w=>!w.disabled),y=m.find(w=>w.value===c.value),v=mi(m,p,y);v!==void 0&&c.onValueChange(v.value)}),b=()=>{i||(c.onOpenChange(!0),g())};return u.jsx(bf,{asChild:!0,...a,children:u.jsx(ne.button,{type:"button",role:"combobox","aria-controls":c.contentId,"aria-expanded":c.open,"aria-required":c.required,"aria-autocomplete":"none",dir:c.dir,"data-state":c.open?"open":"closed",disabled:i,"data-disabled":i?"":void 0,"data-placeholder":ui(c.value)?"":void 0,...o,ref:l,onClick:oe(o.onClick,p=>{p.currentTarget.focus()}),onPointerDown:oe(o.onPointerDown,p=>{const m=p.target;m.hasPointerCapture(p.pointerId)&&m.releasePointerCapture(p.pointerId),p.button===0&&p.ctrlKey===!1&&(b(),c.triggerPointerDownPosRef.current={x:Math.round(p.pageX),y:Math.round(p.pageY)},p.preventDefault())}),onKeyDown:oe(o.onKeyDown,p=>{const m=h.current!=="";!(p.ctrlKey||p.altKey||p.metaKey)&&p.key.length===1&&f(p.key),!(m&&p.key===" ")&&nh.includes(p.key)&&(b(),p.preventDefault())})})})});zs.displayName=Vs;var Hs="SelectValue",Us=s.forwardRef((e,t)=>{const{__scopeSelect:r,className:n,style:o,children:a,placeholder:c="",...i}=e,l=Ye(Hs,r),{onValueNodeHasChildrenChange:d}=l,h=a!==void 0,f=se(t,l.onValueNodeChange);return ge(()=>{d(h)},[d,h]),u.jsx(ne.span,{...i,ref:f,style:{pointerEvents:"none"},children:ui(l.value)?u.jsx(u.Fragment,{children:c}):a})});Us.displayName=Hs;var lh="SelectIcon",Gs=s.forwardRef((e,t)=>{const{__scopeSelect:r,children:n,...o}=e;return u.jsx(ne.span,{"aria-hidden":!0,...o,ref:t,children:n||"▼"})});Gs.displayName=lh;var dh="SelectPortal",Ks=e=>u.jsx(Ds,{asChild:!0,...e});Ks.displayName=dh;var Qe="SelectContent",Ys=s.forwardRef((e,t)=>{const r=Ye(Qe,e.__scopeSelect),[n,o]=s.useState();if(ge(()=>{o(new DocumentFragment)},[]),!r.open){const a=n;return a?Ae.createPortal(u.jsx(Xs,{scope:e.__scopeSelect,children:u.jsx(jr.Slot,{scope:e.__scopeSelect,children:u.jsx("div",{children:e.children})})}),a):null}return u.jsx(qs,{...e,ref:t})});Ys.displayName=Qe;var Re=10,[Xs,Xe]=At(Qe),uh="SelectContentImpl",qs=s.forwardRef((e,t)=>{const{__scopeSelect:r,position:n="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:a,onPointerDownOutside:c,side:i,sideOffset:l,align:d,alignOffset:h,arrowPadding:f,collisionBoundary:g,collisionPadding:b,sticky:p,hideWhenDetached:m,avoidCollisions:y,...v}=e,w=Ye(Qe,r),[x,k]=s.useState(null),[E,S]=s.useState(null),C=se(t,_=>k(_)),[$,N]=s.useState(null),[D,T]=s.useState(null),P=_r(r),[j,I]=s.useState(!1),M=s.useRef(!1);s.useEffect(()=>{if(x)return So(x)},[x]),gu();const F=s.useCallback(_=>{const[K,...z]=P().map(X=>X.ref.current),[H]=z.slice(-1),Y=document.activeElement;for(const X of _)if(X===Y||(X==null||X.scrollIntoView({block:"nearest"}),X===K&&E&&(E.scrollTop=0),X===H&&E&&(E.scrollTop=E.scrollHeight),X==null||X.focus(),document.activeElement!==Y))return},[P,E]),R=s.useCallback(()=>F([$,x]),[F,$,x]);s.useEffect(()=>{j&&R()},[j,R]);const{onOpenChange:O,triggerPointerDownPosRef:A}=w;s.useEffect(()=>{if(x){let _={x:0,y:0};const K=H=>{var Y,X;_={x:Math.abs(Math.round(H.pageX)-(((Y=A.current)==null?void 0:Y.x)??0)),y:Math.abs(Math.round(H.pageY)-(((X=A.current)==null?void 0:X.y)??0))}},z=H=>{_.x<=10&&_.y<=10?H.preventDefault():x.contains(H.target)||O(!1),document.removeEventListener("pointermove",K),A.current=null};return A.current!==null&&(document.addEventListener("pointermove",K),document.addEventListener("pointerup",z,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",K),document.removeEventListener("pointerup",z,{capture:!0})}}},[x,O,A]),s.useEffect(()=>{const _=()=>O(!1);return window.addEventListener("blur",_),window.addEventListener("resize",_),()=>{window.removeEventListener("blur",_),window.removeEventListener("resize",_)}},[O]);const[G,V]=hi(_=>{const K=P().filter(Y=>!Y.disabled),z=K.find(Y=>Y.ref.current===document.activeElement),H=mi(K,_,z);H&&setTimeout(()=>H.ref.current.focus())}),ae=s.useCallback((_,K,z)=>{const H=!M.current&&!z;(w.value!==void 0&&w.value===K||H)&&(N(_),H&&(M.current=!0))},[w.value]),ie=s.useCallback(()=>x==null?void 0:x.focus(),[x]),ce=s.useCallback((_,K,z)=>{const H=!M.current&&!z;(w.value!==void 0&&w.value===K||H)&&T(_)},[w.value]),le=n==="popper"?En:Zs,J=le===En?{side:i,sideOffset:l,align:d,alignOffset:h,arrowPadding:f,collisionBoundary:g,collisionPadding:b,sticky:p,hideWhenDetached:m,avoidCollisions:y}:{};return u.jsx(Xs,{scope:r,content:x,viewport:E,onViewportChange:S,itemRefCallback:ae,selectedItem:$,onItemLeave:ie,itemTextRefCallback:ce,focusSelectedItem:R,selectedItemText:D,position:n,isPositioned:j,searchRef:G,children:u.jsx(rh,{as:Ot,allowPinchZoom:!0,children:u.jsx(ls,{asChild:!0,trapped:w.open,onMountAutoFocus:_=>{_.preventDefault()},onUnmountAutoFocus:oe(o,_=>{var K;(K=w.trigger)==null||K.focus({preventScroll:!0}),_.preventDefault()}),children:u.jsx(is,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:c,onFocusOutside:_=>_.preventDefault(),onDismiss:()=>w.onOpenChange(!1),children:u.jsx(le,{role:"listbox",id:w.contentId,"data-state":w.open?"open":"closed",dir:w.dir,onContextMenu:_=>_.preventDefault(),...v,...J,onPlaced:()=>I(!0),ref:C,style:{display:"flex",flexDirection:"column",outline:"none",...v.style},onKeyDown:oe(v.onKeyDown,_=>{const K=_.ctrlKey||_.altKey||_.metaKey;if(_.key==="Tab"&&_.preventDefault(),!K&&_.key.length===1&&V(_.key),["ArrowUp","ArrowDown","Home","End"].includes(_.key)){let H=P().filter(Y=>!Y.disabled).map(Y=>Y.ref.current);if(["ArrowUp","End"].includes(_.key)&&(H=H.slice().reverse()),["ArrowUp","ArrowDown"].includes(_.key)){const Y=_.target,X=H.indexOf(Y);H=H.slice(X+1)}setTimeout(()=>F(H)),_.preventDefault()}})})})})})})});qs.displayName=uh;var fh="SelectItemAlignedPosition",Zs=s.forwardRef((e,t)=>{const{__scopeSelect:r,onPlaced:n,...o}=e,a=Ye(Qe,r),c=Xe(Qe,r),[i,l]=s.useState(null),[d,h]=s.useState(null),f=se(t,C=>h(C)),g=_r(r),b=s.useRef(!1),p=s.useRef(!0),{viewport:m,selectedItem:y,selectedItemText:v,focusSelectedItem:w}=c,x=s.useCallback(()=>{if(a.trigger&&a.valueNode&&i&&d&&m&&y&&v){const C=a.trigger.getBoundingClientRect(),$=d.getBoundingClientRect(),N=a.valueNode.getBoundingClientRect(),D=v.getBoundingClientRect();if(a.dir!=="rtl"){const Y=D.left-$.left,X=N.left-Y,Q=C.left-X,de=C.width+Q,ue=Math.max(de,$.width),Ce=window.innerWidth-Re,Pe=hr(X,[Re,Ce-ue]);i.style.minWidth=de+"px",i.style.left=Pe+"px"}else{const Y=$.right-D.right,X=window.innerWidth-N.right-Y,Q=window.innerWidth-C.right-X,de=C.width+Q,ue=Math.max(de,$.width),Ce=window.innerWidth-Re,Pe=hr(X,[Re,Ce-ue]);i.style.minWidth=de+"px",i.style.right=Pe+"px"}const T=g(),P=window.innerHeight-Re*2,j=m.scrollHeight,I=window.getComputedStyle(d),M=parseInt(I.borderTopWidth,10),F=parseInt(I.paddingTop,10),R=parseInt(I.borderBottomWidth,10),O=parseInt(I.paddingBottom,10),A=M+F+j+O+R,G=Math.min(y.offsetHeight*5,A),V=window.getComputedStyle(m),ae=parseInt(V.paddingTop,10),ie=parseInt(V.paddingBottom,10),ce=C.top+C.height/2-Re,le=P-ce,J=y.offsetHeight/2,_=y.offsetTop+J,K=M+F+_,z=A-K;if(K<=ce){const Y=y===T[T.length-1].ref.current;i.style.bottom="0px";const X=d.clientHeight-m.offsetTop-m.offsetHeight,Q=Math.max(le,J+(Y?ie:0)+X+R),de=K+Q;i.style.height=de+"px"}else{const Y=y===T[0].ref.current;i.style.top="0px";const Q=Math.max(ce,M+m.offsetTop+(Y?ae:0)+J)+z;i.style.height=Q+"px",m.scrollTop=K-ce+m.offsetTop}i.style.margin=`${Re}px 0`,i.style.minHeight=G+"px",i.style.maxHeight=P+"px",n==null||n(),requestAnimationFrame(()=>b.current=!0)}},[g,a.trigger,a.valueNode,i,d,m,y,v,a.dir,n]);ge(()=>x(),[x]);const[k,E]=s.useState();ge(()=>{d&&E(window.getComputedStyle(d).zIndex)},[d]);const S=s.useCallback(C=>{C&&p.current===!0&&(x(),w==null||w(),p.current=!1)},[x,w]);return u.jsx(mh,{scope:r,contentWrapper:i,shouldExpandOnScrollRef:b,onScrollButtonChange:S,children:u.jsx("div",{ref:l,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:k},children:u.jsx(ne.div,{...o,ref:f,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});Zs.displayName=fh;var hh="SelectPopperPosition",En=s.forwardRef((e,t)=>{const{__scopeSelect:r,align:n="start",collisionPadding:o=Re,...a}=e,c=Ir(r);return u.jsx(yf,{...c,...a,ref:t,align:n,collisionPadding:o,style:{boxSizing:"border-box",...a.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});En.displayName=hh;var[mh,Fo]=At(Qe,{}),Sn="SelectViewport",Js=s.forwardRef((e,t)=>{const{__scopeSelect:r,nonce:n,...o}=e,a=Xe(Sn,r),c=Fo(Sn,r),i=se(t,a.onViewportChange),l=s.useRef(0);return u.jsxs(u.Fragment,{children:[u.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:n}),u.jsx(jr.Slot,{scope:r,children:u.jsx(ne.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:i,style:{position:"relative",flex:1,overflow:"auto",...o.style},onScroll:oe(o.onScroll,d=>{const h=d.currentTarget,{contentWrapper:f,shouldExpandOnScrollRef:g}=c;if(g!=null&&g.current&&f){const b=Math.abs(l.current-h.scrollTop);if(b>0){const p=window.innerHeight-Re*2,m=parseFloat(f.style.minHeight),y=parseFloat(f.style.height),v=Math.max(m,y);if(v<p){const w=v+b,x=Math.min(p,w),k=w-x;f.style.height=x+"px",f.style.bottom==="0px"&&(h.scrollTop=k>0?k:0,f.style.justifyContent="flex-end")}}}l.current=h.scrollTop})})})]})});Js.displayName=Sn;var Qs="SelectGroup",[gh,ph]=At(Qs),bh=s.forwardRef((e,t)=>{const{__scopeSelect:r,...n}=e,o=so();return u.jsx(gh,{scope:r,id:o,children:u.jsx(ne.div,{role:"group","aria-labelledby":o,...n,ref:t})})});bh.displayName=Qs;var ei="SelectLabel",ti=s.forwardRef((e,t)=>{const{__scopeSelect:r,...n}=e,o=ph(ei,r);return u.jsx(ne.div,{id:o.id,...n,ref:t})});ti.displayName=ei;var yr="SelectItem",[yh,ri]=At(yr),ni=s.forwardRef((e,t)=>{const{__scopeSelect:r,value:n,disabled:o=!1,textValue:a,...c}=e,i=Ye(yr,r),l=Xe(yr,r),d=i.value===n,[h,f]=s.useState(a??""),[g,b]=s.useState(!1),p=se(t,v=>{var w;return(w=l.itemRefCallback)==null?void 0:w.call(l,v,n,o)}),m=so(),y=()=>{o||(i.onValueChange(n),i.onOpenChange(!1))};if(n==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return u.jsx(yh,{scope:r,value:n,disabled:o,textId:m,isSelected:d,onItemTextChange:s.useCallback(v=>{f(w=>w||((v==null?void 0:v.textContent)??"").trim())},[]),children:u.jsx(jr.ItemSlot,{scope:r,value:n,disabled:o,textValue:h,children:u.jsx(ne.div,{role:"option","aria-labelledby":m,"data-highlighted":g?"":void 0,"aria-selected":d&&g,"data-state":d?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...c,ref:p,onFocus:oe(c.onFocus,()=>b(!0)),onBlur:oe(c.onBlur,()=>b(!1)),onPointerUp:oe(c.onPointerUp,y),onPointerMove:oe(c.onPointerMove,v=>{var w;o?(w=l.onItemLeave)==null||w.call(l):v.currentTarget.focus({preventScroll:!0})}),onPointerLeave:oe(c.onPointerLeave,v=>{var w;v.currentTarget===document.activeElement&&((w=l.onItemLeave)==null||w.call(l))}),onKeyDown:oe(c.onKeyDown,v=>{var x;((x=l.searchRef)==null?void 0:x.current)!==""&&v.key===" "||(oh.includes(v.key)&&y(),v.key===" "&&v.preventDefault())})})})})});ni.displayName=yr;var Tt="SelectItemText",oi=s.forwardRef((e,t)=>{const{__scopeSelect:r,className:n,style:o,...a}=e,c=Ye(Tt,r),i=Xe(Tt,r),l=ri(Tt,r),d=ch(Tt,r),[h,f]=s.useState(null),g=se(t,v=>f(v),l.onItemTextChange,v=>{var w;return(w=i.itemTextRefCallback)==null?void 0:w.call(i,v,l.value,l.disabled)}),b=h==null?void 0:h.textContent,p=s.useMemo(()=>u.jsx("option",{value:l.value,disabled:l.disabled,children:b},l.value),[l.disabled,l.value,b]),{onNativeOptionAdd:m,onNativeOptionRemove:y}=d;return ge(()=>(m(p),()=>y(p)),[m,y,p]),u.jsxs(u.Fragment,{children:[u.jsx(ne.span,{id:l.textId,...a,ref:g}),l.isSelected&&c.valueNode&&!c.valueNodeHasChildren?Ae.createPortal(a.children,c.valueNode):null]})});oi.displayName=Tt;var ai="SelectItemIndicator",si=s.forwardRef((e,t)=>{const{__scopeSelect:r,...n}=e;return ri(ai,r).isSelected?u.jsx(ne.span,{"aria-hidden":!0,...n,ref:t}):null});si.displayName=ai;var $n="SelectScrollUpButton",ii=s.forwardRef((e,t)=>{const r=Xe($n,e.__scopeSelect),n=Fo($n,e.__scopeSelect),[o,a]=s.useState(!1),c=se(t,n.onScrollButtonChange);return ge(()=>{if(r.viewport&&r.isPositioned){let i=function(){const d=l.scrollTop>0;a(d)};const l=r.viewport;return i(),l.addEventListener("scroll",i),()=>l.removeEventListener("scroll",i)}},[r.viewport,r.isPositioned]),o?u.jsx(li,{...e,ref:c,onAutoScroll:()=>{const{viewport:i,selectedItem:l}=r;i&&l&&(i.scrollTop=i.scrollTop-l.offsetHeight)}}):null});ii.displayName=$n;var An="SelectScrollDownButton",ci=s.forwardRef((e,t)=>{const r=Xe(An,e.__scopeSelect),n=Fo(An,e.__scopeSelect),[o,a]=s.useState(!1),c=se(t,n.onScrollButtonChange);return ge(()=>{if(r.viewport&&r.isPositioned){let i=function(){const d=l.scrollHeight-l.clientHeight,h=Math.ceil(l.scrollTop)<d;a(h)};const l=r.viewport;return i(),l.addEventListener("scroll",i),()=>l.removeEventListener("scroll",i)}},[r.viewport,r.isPositioned]),o?u.jsx(li,{...e,ref:c,onAutoScroll:()=>{const{viewport:i,selectedItem:l}=r;i&&l&&(i.scrollTop=i.scrollTop+l.offsetHeight)}}):null});ci.displayName=An;var li=s.forwardRef((e,t)=>{const{__scopeSelect:r,onAutoScroll:n,...o}=e,a=Xe("SelectScrollButton",r),c=s.useRef(null),i=_r(r),l=s.useCallback(()=>{c.current!==null&&(window.clearInterval(c.current),c.current=null)},[]);return s.useEffect(()=>()=>l(),[l]),ge(()=>{var h;const d=i().find(f=>f.ref.current===document.activeElement);(h=d==null?void 0:d.ref.current)==null||h.scrollIntoView({block:"nearest"})},[i]),u.jsx(ne.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:oe(o.onPointerDown,()=>{c.current===null&&(c.current=window.setInterval(n,50))}),onPointerMove:oe(o.onPointerMove,()=>{var d;(d=a.onItemLeave)==null||d.call(a),c.current===null&&(c.current=window.setInterval(n,50))}),onPointerLeave:oe(o.onPointerLeave,()=>{l()})})}),vh="SelectSeparator",di=s.forwardRef((e,t)=>{const{__scopeSelect:r,...n}=e;return u.jsx(ne.div,{"aria-hidden":!0,...n,ref:t})});di.displayName=vh;var Pn="SelectArrow",wh=s.forwardRef((e,t)=>{const{__scopeSelect:r,...n}=e,o=Ir(r),a=Ye(Pn,r),c=Xe(Pn,r);return a.open&&c.position==="popper"?u.jsx(vf,{...o,...n,ref:t}):null});wh.displayName=Pn;function ui(e){return e===""||e===void 0}var fi=s.forwardRef((e,t)=>{const{value:r,...n}=e,o=s.useRef(null),a=se(t,o),c=Ts(r);return s.useEffect(()=>{const i=o.current,l=window.HTMLSelectElement.prototype,h=Object.getOwnPropertyDescriptor(l,"value").set;if(c!==r&&h){const f=new Event("change",{bubbles:!0});h.call(i,r),i.dispatchEvent(f)}},[c,r]),u.jsx(Ed,{asChild:!0,children:u.jsx("select",{...n,ref:a,defaultValue:r})})});fi.displayName="BubbleSelect";function hi(e){const t=Te(e),r=s.useRef(""),n=s.useRef(0),o=s.useCallback(c=>{const i=r.current+c;t(i),function l(d){r.current=d,window.clearTimeout(n.current),d!==""&&(n.current=window.setTimeout(()=>l(""),1e3))}(i)},[t]),a=s.useCallback(()=>{r.current="",window.clearTimeout(n.current)},[]);return s.useEffect(()=>()=>window.clearTimeout(n.current),[]),[r,o,a]}function mi(e,t,r){const o=t.length>1&&Array.from(t).every(d=>d===t[0])?t[0]:t,a=r?e.indexOf(r):-1;let c=xh(e,Math.max(a,0));o.length===1&&(c=c.filter(d=>d!==r));const l=c.find(d=>d.textValue.toLowerCase().startsWith(o.toLowerCase()));return l!==r?l:void 0}function xh(e,t){return e.map((r,n)=>e[(t+n)%e.length])}var kh=Ws,gi=zs,Ch=Us,Eh=Gs,Sh=Ks,pi=Ys,$h=Js,bi=ti,yi=ni,Ah=oi,Ph=si,vi=ii,wi=ci,xi=di;const Rn=kh,Nn=Ch,vr=s.forwardRef(({className:e,children:t,...r},n)=>u.jsxs(gi,{ref:n,className:L("flex h-10 w-full items-center justify-between  rounded-md border border-[var(--border)] bg-[var(--background)] px-3 py-2 text-sm placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...r,children:[t,u.jsx(Eh,{asChild:!0,children:u.jsx(Wt,{className:"h-4 w-4 opacity-50"})})]}));vr.displayName=gi.displayName;const ki=s.forwardRef(({className:e,...t},r)=>u.jsx(vi,{ref:r,className:L("flex cursor-default items-center justify-center py-1",e),...t,children:u.jsx(Id,{className:"h-4 w-4"})}));ki.displayName=vi.displayName;const Ci=s.forwardRef(({className:e,...t},r)=>u.jsx(wi,{ref:r,className:L("flex cursor-default items-center justify-center py-1",e),...t,children:u.jsx(Wt,{className:"h-4 w-4"})}));Ci.displayName=wi.displayName;const wr=s.forwardRef(({className:e,children:t,position:r="popper",...n},o)=>u.jsx(Sh,{children:u.jsxs(pi,{ref:o,className:L("relative z-50  max-h-96 min-w-[8rem] overflow-hidden rounded-md border border-[var(--border)] bg-[var(--background)] shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...n,children:[u.jsx(ki,{}),u.jsx($h,{className:L("p-1",r==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),u.jsx(Ci,{})]})}));wr.displayName=pi.displayName;const Rh=s.forwardRef(({className:e,...t},r)=>u.jsx(bi,{ref:r,className:L("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t}));Rh.displayName=bi.displayName;const vt=s.forwardRef(({className:e,children:t,...r},n)=>u.jsxs(yi,{ref:n,className:L("relative flex w-full bg-[var(--background)]  border-[var(--border)] cursor-default select-none items-start justify-start rounded-sm py-1.5 pl-8 pr-2 text-sm data-[disabled]:pointer-events-none data-[disabled]:opacity-50 ",e),...r,children:[u.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:u.jsx(Ph,{children:u.jsx(Nr,{className:"h-4 w-4"})})}),u.jsx(Ah,{children:t})]}));vt.displayName=yi.displayName;const Nh=s.forwardRef(({className:e,...t},r)=>u.jsx(xi,{ref:r,className:L("-mx-1 my-1 h-px bg-muted",e),...t}));Nh.displayName=xi.displayName;function Fe(e,t,{checkForDefaultPrevented:r=!0}={}){return function(o){if(e==null||e(o),r===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Fh(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function Ei(...e){return t=>e.forEach(r=>Fh(r,t))}function nt(...e){return s.useCallback(Ei(...e),e)}function Si(e,t=[]){let r=[];function n(a,c){const i=s.createContext(c),l=r.length;r=[...r,c];function d(f){const{scope:g,children:b,...p}=f,m=(g==null?void 0:g[e][l])||i,y=s.useMemo(()=>p,Object.values(p));return u.jsx(m.Provider,{value:y,children:b})}function h(f,g){const b=(g==null?void 0:g[e][l])||i,p=s.useContext(b);if(p)return p;if(c!==void 0)return c;throw new Error(`\`${f}\` must be used within \`${a}\``)}return d.displayName=a+"Provider",[d,h]}const o=()=>{const a=r.map(c=>s.createContext(c));return function(i){const l=(i==null?void 0:i[e])||a;return s.useMemo(()=>({[`__scope${e}`]:{...i,[e]:l}}),[i,l])}};return o.scopeName=e,[n,Dh(o,...t)]}function Dh(...e){const t=e[0];if(e.length===1)return t;const r=()=>{const n=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(a){const c=n.reduce((i,{useScope:l,scopeName:d})=>{const f=l(a)[`__scope${d}`];return{...i,...f}},{});return s.useMemo(()=>({[`__scope${t.scopeName}`]:c}),[c])}};return r.scopeName=t.scopeName,r}var $i=s.forwardRef((e,t)=>{const{children:r,...n}=e,o=s.Children.toArray(r),a=o.find(Th);if(a){const c=a.props.children,i=o.map(l=>l===a?s.Children.count(c)>1?s.Children.only(null):s.isValidElement(c)?c.props.children:null:l);return u.jsx(Fn,{...n,ref:t,children:s.isValidElement(c)?s.cloneElement(c,void 0,i):null})}return u.jsx(Fn,{...n,ref:t,children:r})});$i.displayName="Slot";var Fn=s.forwardRef((e,t)=>{const{children:r,...n}=e;if(s.isValidElement(r)){const o=Oh(r);return s.cloneElement(r,{...Mh(n,r.props),ref:t?Ei(t,o):o})}return s.Children.count(r)>1?s.Children.only(null):null});Fn.displayName="SlotClone";var Ai=({children:e})=>u.jsx(u.Fragment,{children:e});function Th(e){return s.isValidElement(e)&&e.type===Ai}function Mh(e,t){const r={...t};for(const n in t){const o=e[n],a=t[n];/^on[A-Z]/.test(n)?o&&a?r[n]=(...i)=>{a(...i),o(...i)}:o&&(r[n]=o):n==="style"?r[n]={...o,...a}:n==="className"&&(r[n]=[o,a].filter(Boolean).join(" "))}return{...e,...r}}function Oh(e){var n,o;let t=(n=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:n.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,r=t&&"isReactWarning"in t&&t.isReactWarning,r?e.props.ref:e.props.ref||e.ref)}var jh=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],Pt=jh.reduce((e,t)=>{const r=s.forwardRef((n,o)=>{const{asChild:a,...c}=n,i=a?$i:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),u.jsx(i,{...c,ref:o})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function _h(e,t){e&&Ae.flushSync(()=>e.dispatchEvent(t))}function Rt(e){const t=s.useRef(e);return s.useEffect(()=>{t.current=e}),s.useMemo(()=>(...r)=>{var n;return(n=t.current)==null?void 0:n.call(t,...r)},[])}function Ih(e,t=globalThis==null?void 0:globalThis.document){const r=Rt(e);s.useEffect(()=>{const n=o=>{o.key==="Escape"&&r(o)};return t.addEventListener("keydown",n,{capture:!0}),()=>t.removeEventListener("keydown",n,{capture:!0})},[r,t])}var Lh="DismissableLayer",Dn="dismissableLayer.update",Bh="dismissableLayer.pointerDownOutside",Wh="dismissableLayer.focusOutside",ka,Pi=s.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Ri=s.forwardRef((e,t)=>{const{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:n,onPointerDownOutside:o,onFocusOutside:a,onInteractOutside:c,onDismiss:i,...l}=e,d=s.useContext(Pi),[h,f]=s.useState(null),g=(h==null?void 0:h.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,b]=s.useState({}),p=nt(t,C=>f(C)),m=Array.from(d.layers),[y]=[...d.layersWithOutsidePointerEventsDisabled].slice(-1),v=m.indexOf(y),w=h?m.indexOf(h):-1,x=d.layersWithOutsidePointerEventsDisabled.size>0,k=w>=v,E=Hh(C=>{const $=C.target,N=[...d.branches].some(D=>D.contains($));!k||N||(o==null||o(C),c==null||c(C),C.defaultPrevented||i==null||i())},g),S=Uh(C=>{const $=C.target;[...d.branches].some(D=>D.contains($))||(a==null||a(C),c==null||c(C),C.defaultPrevented||i==null||i())},g);return Ih(C=>{w===d.layers.size-1&&(n==null||n(C),!C.defaultPrevented&&i&&(C.preventDefault(),i()))},g),s.useEffect(()=>{if(h)return r&&(d.layersWithOutsidePointerEventsDisabled.size===0&&(ka=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),d.layersWithOutsidePointerEventsDisabled.add(h)),d.layers.add(h),Ca(),()=>{r&&d.layersWithOutsidePointerEventsDisabled.size===1&&(g.body.style.pointerEvents=ka)}},[h,g,r,d]),s.useEffect(()=>()=>{h&&(d.layers.delete(h),d.layersWithOutsidePointerEventsDisabled.delete(h),Ca())},[h,d]),s.useEffect(()=>{const C=()=>b({});return document.addEventListener(Dn,C),()=>document.removeEventListener(Dn,C)},[]),u.jsx(Pt.div,{...l,ref:p,style:{pointerEvents:x?k?"auto":"none":void 0,...e.style},onFocusCapture:Fe(e.onFocusCapture,S.onFocusCapture),onBlurCapture:Fe(e.onBlurCapture,S.onBlurCapture),onPointerDownCapture:Fe(e.onPointerDownCapture,E.onPointerDownCapture)})});Ri.displayName=Lh;var Vh="DismissableLayerBranch",zh=s.forwardRef((e,t)=>{const r=s.useContext(Pi),n=s.useRef(null),o=nt(t,n);return s.useEffect(()=>{const a=n.current;if(a)return r.branches.add(a),()=>{r.branches.delete(a)}},[r.branches]),u.jsx(Pt.div,{...e,ref:o})});zh.displayName=Vh;function Hh(e,t=globalThis==null?void 0:globalThis.document){const r=Rt(e),n=s.useRef(!1),o=s.useRef(()=>{});return s.useEffect(()=>{const a=i=>{if(i.target&&!n.current){let l=function(){Ni(Bh,r,d,{discrete:!0})};const d={originalEvent:i};i.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=l,t.addEventListener("click",o.current,{once:!0})):l()}else t.removeEventListener("click",o.current);n.current=!1},c=window.setTimeout(()=>{t.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(c),t.removeEventListener("pointerdown",a),t.removeEventListener("click",o.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}function Uh(e,t=globalThis==null?void 0:globalThis.document){const r=Rt(e),n=s.useRef(!1);return s.useEffect(()=>{const o=a=>{a.target&&!n.current&&Ni(Wh,r,{originalEvent:a},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}function Ca(){const e=new CustomEvent(Dn);document.dispatchEvent(e)}function Ni(e,t,r,{discrete:n}){const o=r.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),n?_h(o,a):o.dispatchEvent(a)}var kt=globalThis!=null&&globalThis.document?s.useLayoutEffect:()=>{},Gh=Bt.useId||(()=>{}),Kh=0;function Yh(e){const[t,r]=s.useState(Gh());return kt(()=>{e||r(n=>n??String(Kh++))},[e]),e||(t?`radix-${t}`:"")}var Xh="Arrow",Fi=s.forwardRef((e,t)=>{const{children:r,width:n=10,height:o=5,...a}=e;return u.jsx(Pt.svg,{...a,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:u.jsx("polygon",{points:"0,0 30,0 15,10"})})});Fi.displayName=Xh;var qh=Fi;function Zh(e){const[t,r]=s.useState(void 0);return kt(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});const n=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const a=o[0];let c,i;if("borderBoxSize"in a){const l=a.borderBoxSize,d=Array.isArray(l)?l[0]:l;c=d.inlineSize,i=d.blockSize}else c=e.offsetWidth,i=e.offsetHeight;r({width:c,height:i})});return n.observe(e,{box:"border-box"}),()=>n.unobserve(e)}else r(void 0)},[e]),t}var Do="Popper",[Di,Ti]=Si(Do),[Jh,Mi]=Di(Do),Oi=e=>{const{__scopePopper:t,children:r}=e,[n,o]=s.useState(null);return u.jsx(Jh,{scope:t,anchor:n,onAnchorChange:o,children:r})};Oi.displayName=Do;var ji="PopperAnchor",_i=s.forwardRef((e,t)=>{const{__scopePopper:r,virtualRef:n,...o}=e,a=Mi(ji,r),c=s.useRef(null),i=nt(t,c);return s.useEffect(()=>{a.onAnchorChange((n==null?void 0:n.current)||c.current)}),n?null:u.jsx(Pt.div,{...o,ref:i})});_i.displayName=ji;var To="PopperContent",[Qh,em]=Di(To),Ii=s.forwardRef((e,t)=>{var _,K,z,H,Y,X;const{__scopePopper:r,side:n="bottom",sideOffset:o=0,align:a="center",alignOffset:c=0,arrowPadding:i=0,avoidCollisions:l=!0,collisionBoundary:d=[],collisionPadding:h=0,sticky:f="partial",hideWhenDetached:g=!1,updatePositionStrategy:b="optimized",onPlaced:p,...m}=e,y=Mi(To,r),[v,w]=s.useState(null),x=nt(t,Q=>w(Q)),[k,E]=s.useState(null),S=Zh(k),C=(S==null?void 0:S.width)??0,$=(S==null?void 0:S.height)??0,N=n+(a!=="center"?"-"+a:""),D=typeof h=="number"?h:{top:0,right:0,bottom:0,left:0,...h},T=Array.isArray(d)?d:[d],P=T.length>0,j={padding:D,boundary:T.filter(rm),altBoundary:P},{refs:I,floatingStyles:M,placement:F,isPositioned:R,middlewareData:O}=ko({strategy:"fixed",placement:N,whileElementsMounted:(...Q)=>go(...Q,{animationFrame:b==="always"}),elements:{reference:y.anchor},middleware:[uo({mainAxis:o+$,alignmentAxis:c}),l&&po({mainAxis:!0,crossAxis:!1,limiter:f==="partial"?wo():void 0,...j}),l&&bo({...j}),yo({...j,apply:({elements:Q,rects:de,availableWidth:ue,availableHeight:Ce})=>{const{width:Pe,height:Dt}=de.reference,ve=Q.floating.style;ve.setProperty("--radix-popper-available-width",`${ue}px`),ve.setProperty("--radix-popper-available-height",`${Ce}px`),ve.setProperty("--radix-popper-anchor-width",`${Pe}px`),ve.setProperty("--radix-popper-anchor-height",`${Dt}px`)}}),k&&xo({element:k,padding:i}),nm({arrowWidth:C,arrowHeight:$}),g&&vo({strategy:"referenceHidden",...j})]}),[A,G]=Wi(F),V=Rt(p);kt(()=>{R&&(V==null||V())},[R,V]);const ae=(_=O.arrow)==null?void 0:_.x,ie=(K=O.arrow)==null?void 0:K.y,ce=((z=O.arrow)==null?void 0:z.centerOffset)!==0,[le,J]=s.useState();return kt(()=>{v&&J(window.getComputedStyle(v).zIndex)},[v]),u.jsx("div",{ref:I.setFloating,"data-radix-popper-content-wrapper":"",style:{...M,transform:R?M.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:le,"--radix-popper-transform-origin":[(H=O.transformOrigin)==null?void 0:H.x,(Y=O.transformOrigin)==null?void 0:Y.y].join(" "),...((X=O.hide)==null?void 0:X.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:u.jsx(Qh,{scope:r,placedSide:A,onArrowChange:E,arrowX:ae,arrowY:ie,shouldHideArrow:ce,children:u.jsx(Pt.div,{"data-side":A,"data-align":G,...m,ref:x,style:{...m.style,animation:R?void 0:"none"}})})})});Ii.displayName=To;var Li="PopperArrow",tm={top:"bottom",right:"left",bottom:"top",left:"right"},Bi=s.forwardRef(function(t,r){const{__scopePopper:n,...o}=t,a=em(Li,n),c=tm[a.placedSide];return u.jsx("span",{ref:a.onArrowChange,style:{position:"absolute",left:a.arrowX,top:a.arrowY,[c]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[a.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[a.placedSide],visibility:a.shouldHideArrow?"hidden":void 0},children:u.jsx(qh,{...o,ref:r,style:{...o.style,display:"block"}})})});Bi.displayName=Li;function rm(e){return e!==null}var nm=e=>({name:"transformOrigin",options:e,fn(t){var y,v,w;const{placement:r,rects:n,middlewareData:o}=t,c=((y=o.arrow)==null?void 0:y.centerOffset)!==0,i=c?0:e.arrowWidth,l=c?0:e.arrowHeight,[d,h]=Wi(r),f={start:"0%",center:"50%",end:"100%"}[h],g=(((v=o.arrow)==null?void 0:v.x)??0)+i/2,b=(((w=o.arrow)==null?void 0:w.y)??0)+l/2;let p="",m="";return d==="bottom"?(p=c?f:`${g}px`,m=`${-l}px`):d==="top"?(p=c?f:`${g}px`,m=`${n.floating.height+l}px`):d==="right"?(p=`${-l}px`,m=c?f:`${b}px`):d==="left"&&(p=`${n.floating.width+l}px`,m=c?f:`${b}px`),{data:{x:p,y:m}}}});function Wi(e){const[t,r="center"]=e.split("-");return[t,r]}var om=Oi,am=_i,sm=Ii,im=Bi;function cm(e,t){return s.useReducer((r,n)=>t[r][n]??r,e)}var Vi=e=>{const{present:t,children:r}=e,n=lm(t),o=typeof r=="function"?r({present:n.isPresent}):s.Children.only(r),a=nt(n.ref,dm(o));return typeof r=="function"||n.isPresent?s.cloneElement(o,{ref:a}):null};Vi.displayName="Presence";function lm(e){const[t,r]=s.useState(),n=s.useRef({}),o=s.useRef(e),a=s.useRef("none"),c=e?"mounted":"unmounted",[i,l]=cm(c,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return s.useEffect(()=>{const d=tr(n.current);a.current=i==="mounted"?d:"none"},[i]),kt(()=>{const d=n.current,h=o.current;if(h!==e){const g=a.current,b=tr(d);e?l("MOUNT"):b==="none"||(d==null?void 0:d.display)==="none"?l("UNMOUNT"):l(h&&g!==b?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),kt(()=>{if(t){const d=f=>{const b=tr(n.current).includes(f.animationName);f.target===t&&b&&Ae.flushSync(()=>l("ANIMATION_END"))},h=f=>{f.target===t&&(a.current=tr(n.current))};return t.addEventListener("animationstart",h),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{t.removeEventListener("animationstart",h),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(i),ref:s.useCallback(d=>{d&&(n.current=getComputedStyle(d)),r(d)},[])}}function tr(e){return(e==null?void 0:e.animationName)||"none"}function dm(e){var n,o;let t=(n=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:n.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,r=t&&"isReactWarning"in t&&t.isReactWarning,r?e.props.ref:e.props.ref||e.ref)}function um({prop:e,defaultProp:t,onChange:r=()=>{}}){const[n,o]=fm({defaultProp:t,onChange:r}),a=e!==void 0,c=a?e:n,i=Rt(r),l=s.useCallback(d=>{if(a){const f=typeof d=="function"?d(e):d;f!==e&&i(f)}else o(d)},[a,e,o,i]);return[c,l]}function fm({defaultProp:e,onChange:t}){const r=s.useState(e),[n]=r,o=s.useRef(n),a=Rt(t);return s.useEffect(()=>{o.current!==n&&(a(n),o.current=n)},[n,o,a]),r}var[Lr,nw]=Si("Tooltip",[Ti]),Br=Ti(),zi="TooltipProvider",hm=700,Tn="tooltip.open",[mm,Mo]=Lr(zi),Hi=e=>{const{__scopeTooltip:t,delayDuration:r=hm,skipDelayDuration:n=300,disableHoverableContent:o=!1,children:a}=e,[c,i]=s.useState(!0),l=s.useRef(!1),d=s.useRef(0);return s.useEffect(()=>{const h=d.current;return()=>window.clearTimeout(h)},[]),u.jsx(mm,{scope:t,isOpenDelayed:c,delayDuration:r,onOpen:s.useCallback(()=>{window.clearTimeout(d.current),i(!1)},[]),onClose:s.useCallback(()=>{window.clearTimeout(d.current),d.current=window.setTimeout(()=>i(!0),n)},[n]),isPointerInTransitRef:l,onPointerInTransitChange:s.useCallback(h=>{l.current=h},[]),disableHoverableContent:o,children:a})};Hi.displayName=zi;var Wr="Tooltip",[gm,Vr]=Lr(Wr),Ui=e=>{const{__scopeTooltip:t,children:r,open:n,defaultOpen:o=!1,onOpenChange:a,disableHoverableContent:c,delayDuration:i}=e,l=Mo(Wr,e.__scopeTooltip),d=Br(t),[h,f]=s.useState(null),g=Yh(),b=s.useRef(0),p=c??l.disableHoverableContent,m=i??l.delayDuration,y=s.useRef(!1),[v=!1,w]=um({prop:n,defaultProp:o,onChange:C=>{C?(l.onOpen(),document.dispatchEvent(new CustomEvent(Tn))):l.onClose(),a==null||a(C)}}),x=s.useMemo(()=>v?y.current?"delayed-open":"instant-open":"closed",[v]),k=s.useCallback(()=>{window.clearTimeout(b.current),y.current=!1,w(!0)},[w]),E=s.useCallback(()=>{window.clearTimeout(b.current),w(!1)},[w]),S=s.useCallback(()=>{window.clearTimeout(b.current),b.current=window.setTimeout(()=>{y.current=!0,w(!0)},m)},[m,w]);return s.useEffect(()=>()=>window.clearTimeout(b.current),[]),u.jsx(om,{...d,children:u.jsx(gm,{scope:t,contentId:g,open:v,stateAttribute:x,trigger:h,onTriggerChange:f,onTriggerEnter:s.useCallback(()=>{l.isOpenDelayed?S():k()},[l.isOpenDelayed,S,k]),onTriggerLeave:s.useCallback(()=>{p?E():window.clearTimeout(b.current)},[E,p]),onOpen:k,onClose:E,disableHoverableContent:p,children:r})})};Ui.displayName=Wr;var Mn="TooltipTrigger",Gi=s.forwardRef((e,t)=>{const{__scopeTooltip:r,...n}=e,o=Vr(Mn,r),a=Mo(Mn,r),c=Br(r),i=s.useRef(null),l=nt(t,i,o.onTriggerChange),d=s.useRef(!1),h=s.useRef(!1),f=s.useCallback(()=>d.current=!1,[]);return s.useEffect(()=>()=>document.removeEventListener("pointerup",f),[f]),u.jsx(am,{asChild:!0,...c,children:u.jsx(Pt.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...n,ref:l,onPointerMove:Fe(e.onPointerMove,g=>{g.pointerType!=="touch"&&!h.current&&!a.isPointerInTransitRef.current&&(o.onTriggerEnter(),h.current=!0)}),onPointerLeave:Fe(e.onPointerLeave,()=>{o.onTriggerLeave(),h.current=!1}),onPointerDown:Fe(e.onPointerDown,()=>{d.current=!0,document.addEventListener("pointerup",f,{once:!0})}),onFocus:Fe(e.onFocus,()=>{d.current||o.onOpen()}),onBlur:Fe(e.onBlur,o.onClose),onClick:Fe(e.onClick,o.onClose)})})});Gi.displayName=Mn;var pm="TooltipPortal",[ow,bm]=Lr(pm,{forceMount:void 0}),Ct="TooltipContent",Ki=s.forwardRef((e,t)=>{const r=bm(Ct,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...a}=e,c=Vr(Ct,e.__scopeTooltip);return u.jsx(Vi,{present:n||c.open,children:c.disableHoverableContent?u.jsx(Yi,{side:o,...a,ref:t}):u.jsx(ym,{side:o,...a,ref:t})})}),ym=s.forwardRef((e,t)=>{const r=Vr(Ct,e.__scopeTooltip),n=Mo(Ct,e.__scopeTooltip),o=s.useRef(null),a=nt(t,o),[c,i]=s.useState(null),{trigger:l,onClose:d}=r,h=o.current,{onPointerInTransitChange:f}=n,g=s.useCallback(()=>{i(null),f(!1)},[f]),b=s.useCallback((p,m)=>{const y=p.currentTarget,v={x:p.clientX,y:p.clientY},w=km(v,y.getBoundingClientRect()),x=Cm(v,w),k=Em(m.getBoundingClientRect()),E=$m([...x,...k]);i(E),f(!0)},[f]);return s.useEffect(()=>()=>g(),[g]),s.useEffect(()=>{if(l&&h){const p=y=>b(y,h),m=y=>b(y,l);return l.addEventListener("pointerleave",p),h.addEventListener("pointerleave",m),()=>{l.removeEventListener("pointerleave",p),h.removeEventListener("pointerleave",m)}}},[l,h,b,g]),s.useEffect(()=>{if(c){const p=m=>{const y=m.target,v={x:m.clientX,y:m.clientY},w=(l==null?void 0:l.contains(y))||(h==null?void 0:h.contains(y)),x=!Sm(v,c);w?g():x&&(g(),d())};return document.addEventListener("pointermove",p),()=>document.removeEventListener("pointermove",p)}},[l,h,c,d,g]),u.jsx(Yi,{...e,ref:a})}),[vm,wm]=Lr(Wr,{isInside:!1}),Yi=s.forwardRef((e,t)=>{const{__scopeTooltip:r,children:n,"aria-label":o,onEscapeKeyDown:a,onPointerDownOutside:c,...i}=e,l=Vr(Ct,r),d=Br(r),{onClose:h}=l;return s.useEffect(()=>(document.addEventListener(Tn,h),()=>document.removeEventListener(Tn,h)),[h]),s.useEffect(()=>{if(l.trigger){const f=g=>{const b=g.target;b!=null&&b.contains(l.trigger)&&h()};return window.addEventListener("scroll",f,{capture:!0}),()=>window.removeEventListener("scroll",f,{capture:!0})}},[l.trigger,h]),u.jsx(Ri,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:c,onFocusOutside:f=>f.preventDefault(),onDismiss:h,children:u.jsxs(sm,{"data-state":l.stateAttribute,...d,...i,ref:t,style:{...i.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[u.jsx(Ai,{children:n}),u.jsx(vm,{scope:r,isInside:!0,children:u.jsx(Sd,{id:l.contentId,role:"tooltip",children:o||n})})]})})});Ki.displayName=Ct;var Xi="TooltipArrow",xm=s.forwardRef((e,t)=>{const{__scopeTooltip:r,...n}=e,o=Br(r);return wm(Xi,r).isInside?null:u.jsx(im,{...o,...n,ref:t})});xm.displayName=Xi;function km(e,t){const r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),a=Math.abs(t.left-e.x);switch(Math.min(r,n,o,a)){case a:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw new Error("unreachable")}}function Cm(e,t,r=5){const n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r});break}return n}function Em(e){const{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}function Sm(e,t){const{x:r,y:n}=e;let o=!1;for(let a=0,c=t.length-1;a<t.length;c=a++){const i=t[a].x,l=t[a].y,d=t[c].x,h=t[c].y;l>n!=h>n&&r<(d-i)*(n-l)/(h-l)+i&&(o=!o)}return o}function $m(e){const t=e.slice();return t.sort((r,n)=>r.x<n.x?-1:r.x>n.x?1:r.y<n.y?-1:r.y>n.y?1:0),Am(t)}function Am(e){if(e.length<=1)return e.slice();const t=[];for(let n=0;n<e.length;n++){const o=e[n];for(;t.length>=2;){const a=t[t.length-1],c=t[t.length-2];if((a.x-c.x)*(o.y-c.y)>=(a.y-c.y)*(o.x-c.x))t.pop();else break}t.push(o)}t.pop();const r=[];for(let n=e.length-1;n>=0;n--){const o=e[n];for(;r.length>=2;){const a=r[r.length-1],c=r[r.length-2];if((a.x-c.x)*(o.y-c.y)>=(a.y-c.y)*(o.x-c.x))r.pop();else break}r.push(o)}return r.pop(),t.length===1&&r.length===1&&t[0].x===r[0].x&&t[0].y===r[0].y?t:t.concat(r)}var Pm=Hi,Rm=Ui,Nm=Gi,qi=Ki;const Fm=Pm,On=Rm,jn=Nm,xr=s.forwardRef(({className:e,sideOffset:t=4,...r},n)=>u.jsx(qi,{ref:n,sideOffset:t,className:L("z-50 overflow-hidden rounded-md border border-[#ffffff36] bg-muted px-3 py-1.5 text-sm text-muted-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r}));xr.displayName=qi.displayName;const Dm=s.createContext(void 0),Tm={setTheme:e=>{},themes:[]},Mm=()=>{var e;return(e=s.useContext(Dm))!==null&&e!==void 0?e:Tm};function Om({children:e,className:t,gradientSize:r=200,gradientColor:n="border",gradientOpacity:o=.8}){const a=s.useRef(null),c=Mt(-r),i=Mt(-r),l=s.useCallback(f=>{if(a.current){const{left:g,top:b}=a.current.getBoundingClientRect(),p=f.clientX,m=f.clientY;c.set(p-g),i.set(m-b)}},[c,i]),d=s.useCallback(f=>{f.relatedTarget||(document.removeEventListener("mousemove",l),c.set(-r),i.set(-r))},[l,c,r,i]),h=s.useCallback(()=>{document.addEventListener("mousemove",l),c.set(-r),i.set(-r)},[l,c,r,i]);return s.useEffect(()=>(document.addEventListener("mousemove",l),document.addEventListener("mouseout",d),document.addEventListener("mouseenter",h),()=>{document.removeEventListener("mousemove",l),document.removeEventListener("mouseout",d),document.removeEventListener("mouseenter",h)}),[h,l,d]),s.useEffect(()=>{c.set(-r),i.set(-r)},[r,c,i]),u.jsxs("div",{"data-aos":"fade-up",ref:a,className:L("group relative flex size-full overflow-hidden rounded-xl border border-border bg-card text-black dark:bg-neutral-900 dark:text-white",t),children:[u.jsx("div",{className:"relative z-10 w-full",children:e}),u.jsx(te.div,{className:"pointer-events-none absolute -inset-px rounded-xl opacity-0 transition-opacity duration-300 group-hover:opacity-100",style:{background:Fd`
            radial-gradient(${r}px circle at ${c}px ${i}px, ${n}, transparent 100%)
          `,opacity:o}})]})}const jm=({style:e,gradientType:t,gradient:r,isAnimated:n=!1,animationSpeed:o=1})=>{const a=r.colors[0],c=n?{...e,backgroundSize:"400% 400%",animation:`gradientShift ${4/o}s ease infinite`}:e,i=t==="text"?{...c,color:"transparent",backgroundClip:"text",WebkitBackgroundClip:"text"}:c;return u.jsxs("header",{className:"relative w-full",children:[u.jsx("div",{className:"absolute inset-0 opacity-50 blur-[42px]",style:{background:`radial-gradient(circle, ${a} 0%, transparent 30%)`,transform:"translate(-25%, -25%) scale(1.5)"}}),u.jsx(te.div,{className:"relative left-0 right-0 m-auto mt-6 flex h-48 w-48 items-center justify-center overflow-hidden rounded-full",style:t==="background"?c:{},transition:{duration:.3},children:t==="text"?u.jsx(te.span,{className:"overflow-hidden text-nowrap p-2 text-2xl font-bold",style:i,initial:{opacity:0},animate:{opacity:1},transition:{duration:.3},children:r.name}):u.jsx(te.span,{className:"text-2xl font-bold text-white",initial:{opacity:0},animate:{opacity:1},transition:{duration:.3}})})]})},_m=({name:e,isFavorite:t,onFavoriteToggle:r,onExport:n})=>u.jsxs("div",{className:"flex w-full items-center justify-between",children:[u.jsx("h3",{className:"text-lg font-semibold text-primary",children:e}),u.jsxs("div",{className:"flex items-center gap-0",children:[n&&u.jsxs(On,{children:[u.jsx(jn,{asChild:!0,children:u.jsx(te.button,{className:"rounded-full p-2 transition-colors hover:bg-muted",onClick:n,whileHover:{scale:1.1},whileTap:{scale:.9},children:u.jsx(ns,{className:"h-4 w-4 text-gray-400 hover:text-primary"})})}),u.jsx(xr,{children:u.jsx("p",{children:"Export gradient"})})]}),u.jsxs(On,{children:[u.jsx(jn,{asChild:!0,children:u.jsx(te.button,{className:"rounded-full p-2 transition-colors hover:bg-muted",onClick:()=>r(e),whileHover:{scale:1.1},whileTap:{scale:.9},children:u.jsx($d,{className:L("h-5 w-5",t?"fill-current text-red-400":"text-gray-400 hover:text-red-400")})})}),u.jsx(xr,{children:u.jsx("p",{children:t?"Remove from favorites":"Add to favorites"})})]})]})]}),Im=({colors:e,getColorInFormat:t,copyToClipboard:r})=>u.jsx("div",{className:"flex flex-wrap gap-2",children:e.map((n,o)=>u.jsxs(On,{children:[u.jsx(jn,{asChild:!0,children:u.jsx(te.div,{className:"hoverd h-6 w-6 cursor-pointer rounded-full border border-border",style:{backgroundColor:n},onClick:()=>r(t(n),"colors"),whileHover:{scale:1.2},whileTap:{scale:.9}})}),u.jsx(xr,{children:u.jsxs("p",{className:"text-muted-foreground",children:["Click to copy: ",t(n)]})})]},o))});function mt(e,t,{checkForDefaultPrevented:r=!0}={}){return function(o){if(e==null||e(o),r===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Lm(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function Zi(...e){return t=>e.forEach(r=>Lm(r,t))}function et(...e){return s.useCallback(Zi(...e),e)}function Bm(e,t=[]){let r=[];function n(a,c){const i=s.createContext(c),l=r.length;r=[...r,c];const d=f=>{var v;const{scope:g,children:b,...p}=f,m=((v=g==null?void 0:g[e])==null?void 0:v[l])||i,y=s.useMemo(()=>p,Object.values(p));return u.jsx(m.Provider,{value:y,children:b})};d.displayName=a+"Provider";function h(f,g){var m;const b=((m=g==null?void 0:g[e])==null?void 0:m[l])||i,p=s.useContext(b);if(p)return p;if(c!==void 0)return c;throw new Error(`\`${f}\` must be used within \`${a}\``)}return[d,h]}const o=()=>{const a=r.map(c=>s.createContext(c));return function(i){const l=(i==null?void 0:i[e])||a;return s.useMemo(()=>({[`__scope${e}`]:{...i,[e]:l}}),[i,l])}};return o.scopeName=e,[n,Wm(o,...t)]}function Wm(...e){const t=e[0];if(e.length===1)return t;const r=()=>{const n=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(a){const c=n.reduce((i,{useScope:l,scopeName:d})=>{const f=l(a)[`__scope${d}`];return{...i,...f}},{});return s.useMemo(()=>({[`__scope${t.scopeName}`]:c}),[c])}};return r.scopeName=t.scopeName,r}function Ji(e){const t=s.useRef(e);return s.useEffect(()=>{t.current=e}),s.useMemo(()=>(...r)=>{var n;return(n=t.current)==null?void 0:n.call(t,...r)},[])}function Vm({prop:e,defaultProp:t,onChange:r=()=>{}}){const[n,o]=zm({defaultProp:t,onChange:r}),a=e!==void 0,c=a?e:n,i=Ji(r),l=s.useCallback(d=>{if(a){const f=typeof d=="function"?d(e):d;f!==e&&i(f)}else o(d)},[a,e,o,i]);return[c,l]}function zm({defaultProp:e,onChange:t}){const r=s.useState(e),[n]=r,o=s.useRef(n),a=Ji(t);return s.useEffect(()=>{o.current!==n&&(a(n),o.current=n)},[n,o,a]),r}var Hm=s.createContext(void 0);function Um(e){const t=s.useContext(Hm);return e||t||"ltr"}var Gm=globalThis!=null&&globalThis.document?s.useLayoutEffect:()=>{};function Km(e){const[t,r]=s.useState(void 0);return Gm(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});const n=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const a=o[0];let c,i;if("borderBoxSize"in a){const l=a.borderBoxSize,d=Array.isArray(l)?l[0]:l;c=d.inlineSize,i=d.blockSize}else c=e.offsetWidth,i=e.offsetHeight;r({width:c,height:i})});return n.observe(e,{box:"border-box"}),()=>n.unobserve(e)}else r(void 0)},[e]),t}var kr=s.forwardRef((e,t)=>{const{children:r,...n}=e,o=s.Children.toArray(r),a=o.find(Xm);if(a){const c=a.props.children,i=o.map(l=>l===a?s.Children.count(c)>1?s.Children.only(null):s.isValidElement(c)?c.props.children:null:l);return u.jsx(_n,{...n,ref:t,children:s.isValidElement(c)?s.cloneElement(c,void 0,i):null})}return u.jsx(_n,{...n,ref:t,children:r})});kr.displayName="Slot";var _n=s.forwardRef((e,t)=>{const{children:r,...n}=e;if(s.isValidElement(r)){const o=Zm(r);return s.cloneElement(r,{...qm(n,r.props),ref:t?Zi(t,o):o})}return s.Children.count(r)>1?s.Children.only(null):null});_n.displayName="SlotClone";var Ym=({children:e})=>u.jsx(u.Fragment,{children:e});function Xm(e){return s.isValidElement(e)&&e.type===Ym}function qm(e,t){const r={...t};for(const n in t){const o=e[n],a=t[n];/^on[A-Z]/.test(n)?o&&a?r[n]=(...i)=>{a(...i),o(...i)}:o&&(r[n]=o):n==="style"?r[n]={...o,...a}:n==="className"&&(r[n]=[o,a].filter(Boolean).join(" "))}return{...e,...r}}function Zm(e){var n,o;let t=(n=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:n.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,r=t&&"isReactWarning"in t&&t.isReactWarning,r?e.props.ref:e.props.ref||e.ref)}var Jm=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],zr=Jm.reduce((e,t)=>{const r=s.forwardRef((n,o)=>{const{asChild:a,...c}=n,i=a?kr:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),u.jsx(i,{...c,ref:o})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function Qm(e,t=[]){let r=[];function n(a,c){const i=s.createContext(c),l=r.length;r=[...r,c];function d(f){const{scope:g,children:b,...p}=f,m=(g==null?void 0:g[e][l])||i,y=s.useMemo(()=>p,Object.values(p));return u.jsx(m.Provider,{value:y,children:b})}function h(f,g){const b=(g==null?void 0:g[e][l])||i,p=s.useContext(b);if(p)return p;if(c!==void 0)return c;throw new Error(`\`${f}\` must be used within \`${a}\``)}return d.displayName=a+"Provider",[d,h]}const o=()=>{const a=r.map(c=>s.createContext(c));return function(i){const l=(i==null?void 0:i[e])||a;return s.useMemo(()=>({[`__scope${e}`]:{...i,[e]:l}}),[i,l])}};return o.scopeName=e,[n,eg(o,...t)]}function eg(...e){const t=e[0];if(e.length===1)return t;const r=()=>{const n=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(a){const c=n.reduce((i,{useScope:l,scopeName:d})=>{const f=l(a)[`__scope${d}`];return{...i,...f}},{});return s.useMemo(()=>({[`__scope${t.scopeName}`]:c}),[c])}};return r.scopeName=t.scopeName,r}function tg(e){const t=e+"CollectionProvider",[r,n]=Qm(t),[o,a]=r(t,{collectionRef:{current:null},itemMap:new Map}),c=b=>{const{scope:p,children:m}=b,y=W.useRef(null),v=W.useRef(new Map).current;return u.jsx(o,{scope:p,itemMap:v,collectionRef:y,children:m})};c.displayName=t;const i=e+"CollectionSlot",l=W.forwardRef((b,p)=>{const{scope:m,children:y}=b,v=a(i,m),w=et(p,v.collectionRef);return u.jsx(kr,{ref:w,children:y})});l.displayName=i;const d=e+"CollectionItemSlot",h="data-radix-collection-item",f=W.forwardRef((b,p)=>{const{scope:m,children:y,...v}=b,w=W.useRef(null),x=et(p,w),k=a(d,m);return W.useEffect(()=>(k.itemMap.set(w,{ref:w,...v}),()=>void k.itemMap.delete(w))),u.jsx(kr,{[h]:"",ref:x,children:y})});f.displayName=d;function g(b){const p=a(e+"CollectionConsumer",b);return W.useCallback(()=>{const y=p.collectionRef.current;if(!y)return[];const v=Array.from(y.querySelectorAll(`[${h}]`));return Array.from(p.itemMap.values()).sort((k,E)=>v.indexOf(k.ref.current)-v.indexOf(E.ref.current))},[p.collectionRef,p.itemMap])}return[{Provider:c,Slot:l,ItemSlot:f},g,n]}var Qi=["PageUp","PageDown"],ec=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],tc={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},Nt="Slider",[In,rg,ng]=tg(Nt),[rc,aw]=Bm(Nt,[ng]),[og,Hr]=rc(Nt),nc=s.forwardRef((e,t)=>{const{name:r,min:n=0,max:o=100,step:a=1,orientation:c="horizontal",disabled:i=!1,minStepsBetweenThumbs:l=0,defaultValue:d=[n],value:h,onValueChange:f=()=>{},onValueCommit:g=()=>{},inverted:b=!1,form:p,...m}=e,y=s.useRef(new Set),v=s.useRef(0),x=c==="horizontal"?ag:sg,[k=[],E]=Vm({prop:h,defaultProp:d,onChange:T=>{var j;(j=[...y.current][v.current])==null||j.focus(),f(T)}}),S=s.useRef(k);function C(T){const P=ug(k,T);D(T,P)}function $(T){D(T,v.current)}function N(){const T=S.current[v.current];k[v.current]!==T&&g(k)}function D(T,P,{commit:j}={commit:!1}){const I=gg(a),M=pg(Math.round((T-n)/a)*a+n,I),F=hr(M,[n,o]);E((R=[])=>{const O=lg(R,F,P);if(mg(O,l*a)){v.current=O.indexOf(F);const A=String(O)!==String(R);return A&&j&&g(O),A?O:R}else return R})}return u.jsx(og,{scope:e.__scopeSlider,name:r,disabled:i,min:n,max:o,valueIndexToChangeRef:v,thumbs:y.current,values:k,orientation:c,form:p,children:u.jsx(In.Provider,{scope:e.__scopeSlider,children:u.jsx(In.Slot,{scope:e.__scopeSlider,children:u.jsx(x,{"aria-disabled":i,"data-disabled":i?"":void 0,...m,ref:t,onPointerDown:mt(m.onPointerDown,()=>{i||(S.current=k)}),min:n,max:o,inverted:b,onSlideStart:i?void 0:C,onSlideMove:i?void 0:$,onSlideEnd:i?void 0:N,onHomeKeyDown:()=>!i&&D(n,0,{commit:!0}),onEndKeyDown:()=>!i&&D(o,k.length-1,{commit:!0}),onStepKeyDown:({event:T,direction:P})=>{if(!i){const M=Qi.includes(T.key)||T.shiftKey&&ec.includes(T.key)?10:1,F=v.current,R=k[F],O=a*M*P;D(R+O,F,{commit:!0})}}})})})})});nc.displayName=Nt;var[oc,ac]=rc(Nt,{startEdge:"left",endEdge:"right",size:"width",direction:1}),ag=s.forwardRef((e,t)=>{const{min:r,max:n,dir:o,inverted:a,onSlideStart:c,onSlideMove:i,onSlideEnd:l,onStepKeyDown:d,...h}=e,[f,g]=s.useState(null),b=et(t,x=>g(x)),p=s.useRef(),m=Um(o),y=m==="ltr",v=y&&!a||!y&&a;function w(x){const k=p.current||f.getBoundingClientRect(),E=[0,k.width],C=Oo(E,v?[r,n]:[n,r]);return p.current=k,C(x-k.left)}return u.jsx(oc,{scope:e.__scopeSlider,startEdge:v?"left":"right",endEdge:v?"right":"left",direction:v?1:-1,size:"width",children:u.jsx(sc,{dir:m,"data-orientation":"horizontal",...h,ref:b,style:{...h.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:x=>{const k=w(x.clientX);c==null||c(k)},onSlideMove:x=>{const k=w(x.clientX);i==null||i(k)},onSlideEnd:()=>{p.current=void 0,l==null||l()},onStepKeyDown:x=>{const E=tc[v?"from-left":"from-right"].includes(x.key);d==null||d({event:x,direction:E?-1:1})}})})}),sg=s.forwardRef((e,t)=>{const{min:r,max:n,inverted:o,onSlideStart:a,onSlideMove:c,onSlideEnd:i,onStepKeyDown:l,...d}=e,h=s.useRef(null),f=et(t,h),g=s.useRef(),b=!o;function p(m){const y=g.current||h.current.getBoundingClientRect(),v=[0,y.height],x=Oo(v,b?[n,r]:[r,n]);return g.current=y,x(m-y.top)}return u.jsx(oc,{scope:e.__scopeSlider,startEdge:b?"bottom":"top",endEdge:b?"top":"bottom",size:"height",direction:b?1:-1,children:u.jsx(sc,{"data-orientation":"vertical",...d,ref:f,style:{...d.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:m=>{const y=p(m.clientY);a==null||a(y)},onSlideMove:m=>{const y=p(m.clientY);c==null||c(y)},onSlideEnd:()=>{g.current=void 0,i==null||i()},onStepKeyDown:m=>{const v=tc[b?"from-bottom":"from-top"].includes(m.key);l==null||l({event:m,direction:v?-1:1})}})})}),sc=s.forwardRef((e,t)=>{const{__scopeSlider:r,onSlideStart:n,onSlideMove:o,onSlideEnd:a,onHomeKeyDown:c,onEndKeyDown:i,onStepKeyDown:l,...d}=e,h=Hr(Nt,r);return u.jsx(zr.span,{...d,ref:t,onKeyDown:mt(e.onKeyDown,f=>{f.key==="Home"?(c(f),f.preventDefault()):f.key==="End"?(i(f),f.preventDefault()):Qi.concat(ec).includes(f.key)&&(l(f),f.preventDefault())}),onPointerDown:mt(e.onPointerDown,f=>{const g=f.target;g.setPointerCapture(f.pointerId),f.preventDefault(),h.thumbs.has(g)?g.focus():n(f)}),onPointerMove:mt(e.onPointerMove,f=>{f.target.hasPointerCapture(f.pointerId)&&o(f)}),onPointerUp:mt(e.onPointerUp,f=>{const g=f.target;g.hasPointerCapture(f.pointerId)&&(g.releasePointerCapture(f.pointerId),a(f))})})}),ic="SliderTrack",cc=s.forwardRef((e,t)=>{const{__scopeSlider:r,...n}=e,o=Hr(ic,r);return u.jsx(zr.span,{"data-disabled":o.disabled?"":void 0,"data-orientation":o.orientation,...n,ref:t})});cc.displayName=ic;var Ln="SliderRange",lc=s.forwardRef((e,t)=>{const{__scopeSlider:r,...n}=e,o=Hr(Ln,r),a=ac(Ln,r),c=s.useRef(null),i=et(t,c),l=o.values.length,d=o.values.map(g=>uc(g,o.min,o.max)),h=l>1?Math.min(...d):0,f=100-Math.max(...d);return u.jsx(zr.span,{"data-orientation":o.orientation,"data-disabled":o.disabled?"":void 0,...n,ref:i,style:{...e.style,[a.startEdge]:h+"%",[a.endEdge]:f+"%"}})});lc.displayName=Ln;var Bn="SliderThumb",dc=s.forwardRef((e,t)=>{const r=rg(e.__scopeSlider),[n,o]=s.useState(null),a=et(t,i=>o(i)),c=s.useMemo(()=>n?r().findIndex(i=>i.ref.current===n):-1,[r,n]);return u.jsx(ig,{...e,ref:a,index:c})}),ig=s.forwardRef((e,t)=>{const{__scopeSlider:r,index:n,name:o,...a}=e,c=Hr(Bn,r),i=ac(Bn,r),[l,d]=s.useState(null),h=et(t,w=>d(w)),f=l?c.form||!!l.closest("form"):!0,g=Km(l),b=c.values[n],p=b===void 0?0:uc(b,c.min,c.max),m=dg(n,c.values.length),y=g==null?void 0:g[i.size],v=y?fg(y,p,i.direction):0;return s.useEffect(()=>{if(l)return c.thumbs.add(l),()=>{c.thumbs.delete(l)}},[l,c.thumbs]),u.jsxs("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[i.startEdge]:`calc(${p}% + ${v}px)`},children:[u.jsx(In.ItemSlot,{scope:e.__scopeSlider,children:u.jsx(zr.span,{role:"slider","aria-label":e["aria-label"]||m,"aria-valuemin":c.min,"aria-valuenow":b,"aria-valuemax":c.max,"aria-orientation":c.orientation,"data-orientation":c.orientation,"data-disabled":c.disabled?"":void 0,tabIndex:c.disabled?void 0:0,...a,ref:h,style:b===void 0?{display:"none"}:e.style,onFocus:mt(e.onFocus,()=>{c.valueIndexToChangeRef.current=n})})}),f&&u.jsx(cg,{name:o??(c.name?c.name+(c.values.length>1?"[]":""):void 0),form:c.form,value:b},n)]})});dc.displayName=Bn;var cg=e=>{const{value:t,...r}=e,n=s.useRef(null),o=Ts(t);return s.useEffect(()=>{const a=n.current,c=window.HTMLInputElement.prototype,l=Object.getOwnPropertyDescriptor(c,"value").set;if(o!==t&&l){const d=new Event("input",{bubbles:!0});l.call(a,t),a.dispatchEvent(d)}},[o,t]),u.jsx("input",{style:{display:"none"},...r,ref:n,defaultValue:t})};function lg(e=[],t,r){const n=[...e];return n[r]=t,n.sort((o,a)=>o-a)}function uc(e,t,r){const a=100/(r-t)*(e-t);return hr(a,[0,100])}function dg(e,t){return t>2?`Value ${e+1} of ${t}`:t===2?["Minimum","Maximum"][e]:void 0}function ug(e,t){if(e.length===1)return 0;const r=e.map(o=>Math.abs(o-t)),n=Math.min(...r);return r.indexOf(n)}function fg(e,t,r){const n=e/2,a=Oo([0,50],[0,n]);return(n-a(t)*r)*r}function hg(e){return e.slice(0,-1).map((t,r)=>e[r+1]-t)}function mg(e,t){if(t>0){const r=hg(e);return Math.min(...r)>=t}return!0}function Oo(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];const n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}function gg(e){return(String(e).split(".")[1]||"").length}function pg(e,t){const r=Math.pow(10,t);return Math.round(e*r)/r}var fc=nc,bg=cc,yg=lc,vg=dc;const Cr=s.forwardRef(({className:e,...t},r)=>u.jsxs(fc,{ref:r,className:L("relative flex w-full touch-none select-none items-center",e),...t,children:[u.jsx(bg,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:u.jsx(yg,{className:"absolute h-full bg-gray-700"})}),u.jsx(vg,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]}));Cr.displayName=fc.displayName;var hc={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},Ea=W.createContext&&W.createContext(hc),ze=function(){return ze=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++){t=arguments[r];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o])}return e},ze.apply(this,arguments)},wg=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function mc(e){return e&&e.map(function(t,r){return W.createElement(t.tag,ze({key:r},t.attr),mc(t.child))})}function Ie(e){return function(t){return W.createElement(xg,ze({attr:ze({},e.attr)},t),mc(e.child))}}function xg(e){var t=function(r){var n=e.attr,o=e.size,a=e.title,c=wg(e,["attr","size","title"]),i=o||r.size||"1em",l;return r.className&&(l=r.className),e.className&&(l=(l?l+" ":"")+e.className),W.createElement("svg",ze({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,n,c,{className:l,style:ze(ze({color:e.color||r.color},r.style),e.style),height:i,width:i,xmlns:"http://www.w3.org/2000/svg"}),a&&W.createElement("title",null,a),e.children)};return Ea!==void 0?W.createElement(Ea.Consumer,null,function(r){return t(r)}):t(hc)}function kg(e){return Ie({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M125.7 160H176c17.7 0 32 14.3 32 32s-14.3 32-32 32H48c-17.7 0-32-14.3-32-32V64c0-17.7 14.3-32 32-32s32 14.3 32 32v51.2L97.6 97.6c87.5-87.5 229.3-87.5 316.8 0s87.5 229.3 0 316.8s-229.3 87.5-316.8 0c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0c62.5 62.5 163.8 62.5 226.3 0s62.5-163.8 0-226.3s-163.8-62.5-226.3 0L125.7 160z"}}]})(e)}const Cg=({angle:e,setAngle:t})=>u.jsxs("div",{className:"flex w-full items-center space-x-2",children:[u.jsx(Cr,{value:[e],onValueChange:r=>t(r[0]),max:360,step:1,className:"flex-grow"}),u.jsxs("span",{className:"text-sm font-medium text-primary",children:[e,"°"]}),u.jsx(kg,{className:"flex h-full cursor-pointer items-center justify-center text-primary",onClick:()=>t(90)})]});function U(e,t,{checkForDefaultPrevented:r=!0}={}){return function(o){if(e==null||e(o),r===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Ht(e,t=[]){let r=[];function n(a,c){const i=s.createContext(c),l=r.length;r=[...r,c];function d(f){const{scope:g,children:b,...p}=f,m=(g==null?void 0:g[e][l])||i,y=s.useMemo(()=>p,Object.values(p));return s.createElement(m.Provider,{value:y},b)}function h(f,g){const b=(g==null?void 0:g[e][l])||i,p=s.useContext(b);if(p)return p;if(c!==void 0)return c;throw new Error(`\`${f}\` must be used within \`${a}\``)}return d.displayName=a+"Provider",[d,h]}const o=()=>{const a=r.map(c=>s.createContext(c));return function(i){const l=(i==null?void 0:i[e])||a;return s.useMemo(()=>({[`__scope${e}`]:{...i,[e]:l}}),[i,l])}};return o.scopeName=e,[n,Eg(o,...t)]}function Eg(...e){const t=e[0];if(e.length===1)return t;const r=()=>{const n=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(a){const c=n.reduce((i,{useScope:l,scopeName:d})=>{const f=l(a)[`__scope${d}`];return{...i,...f}},{});return s.useMemo(()=>({[`__scope${t.scopeName}`]:c}),[c])}};return r.scopeName=t.scopeName,r}function $e(e){const t=s.useRef(e);return s.useEffect(()=>{t.current=e}),s.useMemo(()=>(...r)=>{var n;return(n=t.current)===null||n===void 0?void 0:n.call(t,...r)},[])}function gc({prop:e,defaultProp:t,onChange:r=()=>{}}){const[n,o]=Sg({defaultProp:t,onChange:r}),a=e!==void 0,c=a?e:n,i=$e(r),l=s.useCallback(d=>{if(a){const f=typeof d=="function"?d(e):d;f!==e&&i(f)}else o(d)},[a,e,o,i]);return[c,l]}function Sg({defaultProp:e,onChange:t}){const r=s.useState(e),[n]=r,o=s.useRef(n),a=$e(t);return s.useEffect(()=>{o.current!==n&&(a(n),o.current=n)},[n,o,a]),r}const $g=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],be=$g.reduce((e,t)=>{const r=s.forwardRef((n,o)=>{const{asChild:a,...c}=n,i=a?fr:t;return s.useEffect(()=>{window[Symbol.for("radix-ui")]=!0},[]),s.createElement(i,B({},c,{ref:o}))});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function pc(e,t){e&&Ae.flushSync(()=>e.dispatchEvent(t))}function bc(e){const t=e+"CollectionProvider",[r,n]=Ht(t),[o,a]=r(t,{collectionRef:{current:null},itemMap:new Map}),c=b=>{const{scope:p,children:m}=b,y=W.useRef(null),v=W.useRef(new Map).current;return W.createElement(o,{scope:p,itemMap:v,collectionRef:y},m)},i=e+"CollectionSlot",l=W.forwardRef((b,p)=>{const{scope:m,children:y}=b,v=a(i,m),w=me(p,v.collectionRef);return W.createElement(fr,{ref:w},y)}),d=e+"CollectionItemSlot",h="data-radix-collection-item",f=W.forwardRef((b,p)=>{const{scope:m,children:y,...v}=b,w=W.useRef(null),x=me(p,w),k=a(d,m);return W.useEffect(()=>(k.itemMap.set(w,{ref:w,...v}),()=>void k.itemMap.delete(w))),W.createElement(fr,{[h]:"",ref:x},y)});function g(b){const p=a(e+"CollectionConsumer",b);return W.useCallback(()=>{const y=p.collectionRef.current;if(!y)return[];const v=Array.from(y.querySelectorAll(`[${h}]`));return Array.from(p.itemMap.values()).sort((k,E)=>v.indexOf(k.ref.current)-v.indexOf(E.ref.current))},[p.collectionRef,p.itemMap])}return[{Provider:c,Slot:l,ItemSlot:f},g,n]}const Ag=s.createContext(void 0);function yc(e){const t=s.useContext(Ag);return e||t||"ltr"}function Pg(e,t=globalThis==null?void 0:globalThis.document){const r=$e(e);s.useEffect(()=>{const n=o=>{o.key==="Escape"&&r(o)};return t.addEventListener("keydown",n),()=>t.removeEventListener("keydown",n)},[r,t])}const Wn="dismissableLayer.update",Rg="dismissableLayer.pointerDownOutside",Ng="dismissableLayer.focusOutside";let Sa;const Fg=s.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Dg=s.forwardRef((e,t)=>{var r;const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:o,onPointerDownOutside:a,onFocusOutside:c,onInteractOutside:i,onDismiss:l,...d}=e,h=s.useContext(Fg),[f,g]=s.useState(null),b=(r=f==null?void 0:f.ownerDocument)!==null&&r!==void 0?r:globalThis==null?void 0:globalThis.document,[,p]=s.useState({}),m=me(t,$=>g($)),y=Array.from(h.layers),[v]=[...h.layersWithOutsidePointerEventsDisabled].slice(-1),w=y.indexOf(v),x=f?y.indexOf(f):-1,k=h.layersWithOutsidePointerEventsDisabled.size>0,E=x>=w,S=Tg($=>{const N=$.target,D=[...h.branches].some(T=>T.contains(N));!E||D||(a==null||a($),i==null||i($),$.defaultPrevented||l==null||l())},b),C=Mg($=>{const N=$.target;[...h.branches].some(T=>T.contains(N))||(c==null||c($),i==null||i($),$.defaultPrevented||l==null||l())},b);return Pg($=>{x===h.layers.size-1&&(o==null||o($),!$.defaultPrevented&&l&&($.preventDefault(),l()))},b),s.useEffect(()=>{if(f)return n&&(h.layersWithOutsidePointerEventsDisabled.size===0&&(Sa=b.body.style.pointerEvents,b.body.style.pointerEvents="none"),h.layersWithOutsidePointerEventsDisabled.add(f)),h.layers.add(f),$a(),()=>{n&&h.layersWithOutsidePointerEventsDisabled.size===1&&(b.body.style.pointerEvents=Sa)}},[f,b,n,h]),s.useEffect(()=>()=>{f&&(h.layers.delete(f),h.layersWithOutsidePointerEventsDisabled.delete(f),$a())},[f,h]),s.useEffect(()=>{const $=()=>p({});return document.addEventListener(Wn,$),()=>document.removeEventListener(Wn,$)},[]),s.createElement(be.div,B({},d,{ref:m,style:{pointerEvents:k?E?"auto":"none":void 0,...e.style},onFocusCapture:U(e.onFocusCapture,C.onFocusCapture),onBlurCapture:U(e.onBlurCapture,C.onBlurCapture),onPointerDownCapture:U(e.onPointerDownCapture,S.onPointerDownCapture)}))});function Tg(e,t=globalThis==null?void 0:globalThis.document){const r=$e(e),n=s.useRef(!1),o=s.useRef(()=>{});return s.useEffect(()=>{const a=i=>{if(i.target&&!n.current){let d=function(){vc(Rg,r,l,{discrete:!0})};const l={originalEvent:i};i.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=d,t.addEventListener("click",o.current,{once:!0})):d()}else t.removeEventListener("click",o.current);n.current=!1},c=window.setTimeout(()=>{t.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(c),t.removeEventListener("pointerdown",a),t.removeEventListener("click",o.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}function Mg(e,t=globalThis==null?void 0:globalThis.document){const r=$e(e),n=s.useRef(!1);return s.useEffect(()=>{const o=a=>{a.target&&!n.current&&vc(Ng,r,{originalEvent:a},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}function $a(){const e=new CustomEvent(Wn);document.dispatchEvent(e)}function vc(e,t,r,{discrete:n}){const o=r.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),n?pc(o,a):o.dispatchEvent(a)}let an=0;function Og(){s.useEffect(()=>{var e,t;const r=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",(e=r[0])!==null&&e!==void 0?e:Aa()),document.body.insertAdjacentElement("beforeend",(t=r[1])!==null&&t!==void 0?t:Aa()),an++,()=>{an===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(n=>n.remove()),an--}},[])}function Aa(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}const sn="focusScope.autoFocusOnMount",cn="focusScope.autoFocusOnUnmount",Pa={bubbles:!1,cancelable:!0},jg=s.forwardRef((e,t)=>{const{loop:r=!1,trapped:n=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...c}=e,[i,l]=s.useState(null),d=$e(o),h=$e(a),f=s.useRef(null),g=me(t,m=>l(m)),b=s.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;s.useEffect(()=>{if(n){let m=function(x){if(b.paused||!i)return;const k=x.target;i.contains(k)?f.current=k:We(f.current,{select:!0})},y=function(x){if(b.paused||!i)return;const k=x.relatedTarget;k!==null&&(i.contains(k)||We(f.current,{select:!0}))},v=function(x){if(document.activeElement===document.body)for(const E of x)E.removedNodes.length>0&&We(i)};document.addEventListener("focusin",m),document.addEventListener("focusout",y);const w=new MutationObserver(v);return i&&w.observe(i,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",m),document.removeEventListener("focusout",y),w.disconnect()}}},[n,i,b.paused]),s.useEffect(()=>{if(i){Na.add(b);const m=document.activeElement;if(!i.contains(m)){const v=new CustomEvent(sn,Pa);i.addEventListener(sn,d),i.dispatchEvent(v),v.defaultPrevented||(_g(Vg(wc(i)),{select:!0}),document.activeElement===m&&We(i))}return()=>{i.removeEventListener(sn,d),setTimeout(()=>{const v=new CustomEvent(cn,Pa);i.addEventListener(cn,h),i.dispatchEvent(v),v.defaultPrevented||We(m??document.body,{select:!0}),i.removeEventListener(cn,h),Na.remove(b)},0)}}},[i,d,h,b]);const p=s.useCallback(m=>{if(!r&&!n||b.paused)return;const y=m.key==="Tab"&&!m.altKey&&!m.ctrlKey&&!m.metaKey,v=document.activeElement;if(y&&v){const w=m.currentTarget,[x,k]=Ig(w);x&&k?!m.shiftKey&&v===k?(m.preventDefault(),r&&We(x,{select:!0})):m.shiftKey&&v===x&&(m.preventDefault(),r&&We(k,{select:!0})):v===w&&m.preventDefault()}},[r,n,b.paused]);return s.createElement(be.div,B({tabIndex:-1},c,{ref:g,onKeyDown:p}))});function _g(e,{select:t=!1}={}){const r=document.activeElement;for(const n of e)if(We(n,{select:t}),document.activeElement!==r)return}function Ig(e){const t=wc(e),r=Ra(t,e),n=Ra(t.reverse(),e);return[r,n]}function wc(e){const t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:n=>{const o=n.tagName==="INPUT"&&n.type==="hidden";return n.disabled||n.hidden||o?NodeFilter.FILTER_SKIP:n.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function Ra(e,t){for(const r of e)if(!Lg(r,{upTo:t}))return r}function Lg(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Bg(e){return e instanceof HTMLInputElement&&"select"in e}function We(e,{select:t=!1}={}){if(e&&e.focus){const r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&Bg(e)&&t&&e.select()}}const Na=Wg();function Wg(){let e=[];return{add(t){const r=e[0];t!==r&&(r==null||r.pause()),e=Fa(e,t),e.unshift(t)},remove(t){var r;e=Fa(e,t),(r=e[0])===null||r===void 0||r.resume()}}}function Fa(e,t){const r=[...e],n=r.indexOf(t);return n!==-1&&r.splice(n,1),r}function Vg(e){return e.filter(t=>t.tagName!=="A")}const Et=globalThis!=null&&globalThis.document?s.useLayoutEffect:()=>{},zg=Bt.useId||(()=>{});let Hg=0;function Vn(e){const[t,r]=s.useState(zg());return Et(()=>{e||r(n=>n??String(Hg++))},[e]),e||(t?`radix-${t}`:"")}function Ug(e){const[t,r]=s.useState(void 0);return Et(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});const n=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const a=o[0];let c,i;if("borderBoxSize"in a){const l=a.borderBoxSize,d=Array.isArray(l)?l[0]:l;c=d.inlineSize,i=d.blockSize}else c=e.offsetWidth,i=e.offsetHeight;r({width:c,height:i})});return n.observe(e,{box:"border-box"}),()=>n.unobserve(e)}else r(void 0)},[e]),t}const xc="Popper",[kc,Cc]=Ht(xc),[Gg,Ec]=kc(xc),Kg=e=>{const{__scopePopper:t,children:r}=e,[n,o]=s.useState(null);return s.createElement(Gg,{scope:t,anchor:n,onAnchorChange:o},r)},Yg="PopperAnchor",Xg=s.forwardRef((e,t)=>{const{__scopePopper:r,virtualRef:n,...o}=e,a=Ec(Yg,r),c=s.useRef(null),i=me(t,c);return s.useEffect(()=>{a.onAnchorChange((n==null?void 0:n.current)||c.current)}),n?null:s.createElement(be.div,B({},o,{ref:i}))}),Sc="PopperContent",[qg,sw]=kc(Sc),Zg=s.forwardRef((e,t)=>{var r,n,o,a,c,i,l,d;const{__scopePopper:h,side:f="bottom",sideOffset:g=0,align:b="center",alignOffset:p=0,arrowPadding:m=0,avoidCollisions:y=!0,collisionBoundary:v=[],collisionPadding:w=0,sticky:x="partial",hideWhenDetached:k=!1,updatePositionStrategy:E="optimized",onPlaced:S,...C}=e,$=Ec(Sc,h),[N,D]=s.useState(null),T=me(t,ue=>D(ue)),[P,j]=s.useState(null),I=Ug(P),M=(r=I==null?void 0:I.width)!==null&&r!==void 0?r:0,F=(n=I==null?void 0:I.height)!==null&&n!==void 0?n:0,R=f+(b!=="center"?"-"+b:""),O=typeof w=="number"?w:{top:0,right:0,bottom:0,left:0,...w},A=Array.isArray(v)?v:[v],G=A.length>0,V={padding:O,boundary:A.filter(Jg),altBoundary:G},{refs:ae,floatingStyles:ie,placement:ce,isPositioned:le,middlewareData:J}=ko({strategy:"fixed",placement:R,whileElementsMounted:(...ue)=>go(...ue,{animationFrame:E==="always"}),elements:{reference:$.anchor},middleware:[uo({mainAxis:g+F,alignmentAxis:p}),y&&po({mainAxis:!0,crossAxis:!1,limiter:x==="partial"?wo():void 0,...V}),y&&bo({...V}),yo({...V,apply:({elements:ue,rects:Ce,availableWidth:Pe,availableHeight:Dt})=>{const{width:ve,height:bd}=Ce.reference,Xt=ue.floating.style;Xt.setProperty("--radix-popper-available-width",`${Pe}px`),Xt.setProperty("--radix-popper-available-height",`${Dt}px`),Xt.setProperty("--radix-popper-anchor-width",`${ve}px`),Xt.setProperty("--radix-popper-anchor-height",`${bd}px`)}}),P&&xo({element:P,padding:m}),Qg({arrowWidth:M,arrowHeight:F}),k&&vo({strategy:"referenceHidden",...V})]}),[_,K]=$c(ce),z=$e(S);Et(()=>{le&&(z==null||z())},[le,z]);const H=(o=J.arrow)===null||o===void 0?void 0:o.x,Y=(a=J.arrow)===null||a===void 0?void 0:a.y,X=((c=J.arrow)===null||c===void 0?void 0:c.centerOffset)!==0,[Q,de]=s.useState();return Et(()=>{N&&de(window.getComputedStyle(N).zIndex)},[N]),s.createElement("div",{ref:ae.setFloating,"data-radix-popper-content-wrapper":"",style:{...ie,transform:le?ie.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Q,"--radix-popper-transform-origin":[(i=J.transformOrigin)===null||i===void 0?void 0:i.x,(l=J.transformOrigin)===null||l===void 0?void 0:l.y].join(" ")},dir:e.dir},s.createElement(qg,{scope:h,placedSide:_,onArrowChange:j,arrowX:H,arrowY:Y,shouldHideArrow:X},s.createElement(be.div,B({"data-side":_,"data-align":K},C,{ref:T,style:{...C.style,animation:le?void 0:"none",opacity:(d=J.hide)!==null&&d!==void 0&&d.referenceHidden?0:void 0}}))))});function Jg(e){return e!==null}const Qg=e=>({name:"transformOrigin",options:e,fn(t){var r,n,o,a,c;const{placement:i,rects:l,middlewareData:d}=t,f=((r=d.arrow)===null||r===void 0?void 0:r.centerOffset)!==0,g=f?0:e.arrowWidth,b=f?0:e.arrowHeight,[p,m]=$c(i),y={start:"0%",center:"50%",end:"100%"}[m],v=((n=(o=d.arrow)===null||o===void 0?void 0:o.x)!==null&&n!==void 0?n:0)+g/2,w=((a=(c=d.arrow)===null||c===void 0?void 0:c.y)!==null&&a!==void 0?a:0)+b/2;let x="",k="";return p==="bottom"?(x=f?y:`${v}px`,k=`${-b}px`):p==="top"?(x=f?y:`${v}px`,k=`${l.floating.height+b}px`):p==="right"?(x=`${-b}px`,k=f?y:`${w}px`):p==="left"&&(x=`${l.floating.width+b}px`,k=f?y:`${w}px`),{data:{x,y:k}}}});function $c(e){const[t,r="center"]=e.split("-");return[t,r]}const ep=Kg,tp=Xg,rp=Zg,np=s.forwardRef((e,t)=>{var r;const{container:n=globalThis==null||(r=globalThis.document)===null||r===void 0?void 0:r.body,...o}=e;return n?oo.createPortal(s.createElement(be.div,B({},o,{ref:t})),n):null});function op(e,t){return s.useReducer((r,n)=>{const o=t[r][n];return o??r},e)}const Ut=e=>{const{present:t,children:r}=e,n=ap(t),o=typeof r=="function"?r({present:n.isPresent}):s.Children.only(r),a=me(n.ref,o.ref);return typeof r=="function"||n.isPresent?s.cloneElement(o,{ref:a}):null};Ut.displayName="Presence";function ap(e){const[t,r]=s.useState(),n=s.useRef({}),o=s.useRef(e),a=s.useRef("none"),c=e?"mounted":"unmounted",[i,l]=op(c,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return s.useEffect(()=>{const d=rr(n.current);a.current=i==="mounted"?d:"none"},[i]),Et(()=>{const d=n.current,h=o.current;if(h!==e){const g=a.current,b=rr(d);e?l("MOUNT"):b==="none"||(d==null?void 0:d.display)==="none"?l("UNMOUNT"):l(h&&g!==b?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),Et(()=>{if(t){const d=f=>{const b=rr(n.current).includes(f.animationName);f.target===t&&b&&Ae.flushSync(()=>l("ANIMATION_END"))},h=f=>{f.target===t&&(a.current=rr(n.current))};return t.addEventListener("animationstart",h),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{t.removeEventListener("animationstart",h),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(i),ref:s.useCallback(d=>{d&&(n.current=getComputedStyle(d)),r(d)},[])}}function rr(e){return(e==null?void 0:e.animationName)||"none"}const ln="rovingFocusGroup.onEntryFocus",sp={bubbles:!1,cancelable:!0},jo="RovingFocusGroup",[zn,Ac,ip]=bc(jo),[cp,Pc]=Ht(jo,[ip]),[lp,dp]=cp(jo),up=s.forwardRef((e,t)=>s.createElement(zn.Provider,{scope:e.__scopeRovingFocusGroup},s.createElement(zn.Slot,{scope:e.__scopeRovingFocusGroup},s.createElement(fp,B({},e,{ref:t}))))),fp=s.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:r,orientation:n,loop:o=!1,dir:a,currentTabStopId:c,defaultCurrentTabStopId:i,onCurrentTabStopIdChange:l,onEntryFocus:d,...h}=e,f=s.useRef(null),g=me(t,f),b=yc(a),[p=null,m]=gc({prop:c,defaultProp:i,onChange:l}),[y,v]=s.useState(!1),w=$e(d),x=Ac(r),k=s.useRef(!1),[E,S]=s.useState(0);return s.useEffect(()=>{const C=f.current;if(C)return C.addEventListener(ln,w),()=>C.removeEventListener(ln,w)},[w]),s.createElement(lp,{scope:r,orientation:n,dir:b,loop:o,currentTabStopId:p,onItemFocus:s.useCallback(C=>m(C),[m]),onItemShiftTab:s.useCallback(()=>v(!0),[]),onFocusableItemAdd:s.useCallback(()=>S(C=>C+1),[]),onFocusableItemRemove:s.useCallback(()=>S(C=>C-1),[])},s.createElement(be.div,B({tabIndex:y||E===0?-1:0,"data-orientation":n},h,{ref:g,style:{outline:"none",...e.style},onMouseDown:U(e.onMouseDown,()=>{k.current=!0}),onFocus:U(e.onFocus,C=>{const $=!k.current;if(C.target===C.currentTarget&&$&&!y){const N=new CustomEvent(ln,sp);if(C.currentTarget.dispatchEvent(N),!N.defaultPrevented){const D=x().filter(M=>M.focusable),T=D.find(M=>M.active),P=D.find(M=>M.id===p),I=[T,P,...D].filter(Boolean).map(M=>M.ref.current);Rc(I)}}k.current=!1}),onBlur:U(e.onBlur,()=>v(!1))})))}),hp="RovingFocusGroupItem",mp=s.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:r,focusable:n=!0,active:o=!1,tabStopId:a,...c}=e,i=Vn(),l=a||i,d=dp(hp,r),h=d.currentTabStopId===l,f=Ac(r),{onFocusableItemAdd:g,onFocusableItemRemove:b}=d;return s.useEffect(()=>{if(n)return g(),()=>b()},[n,g,b]),s.createElement(zn.ItemSlot,{scope:r,id:l,focusable:n,active:o},s.createElement(be.span,B({tabIndex:h?0:-1,"data-orientation":d.orientation},c,{ref:t,onMouseDown:U(e.onMouseDown,p=>{n?d.onItemFocus(l):p.preventDefault()}),onFocus:U(e.onFocus,()=>d.onItemFocus(l)),onKeyDown:U(e.onKeyDown,p=>{if(p.key==="Tab"&&p.shiftKey){d.onItemShiftTab();return}if(p.target!==p.currentTarget)return;const m=bp(p,d.orientation,d.dir);if(m!==void 0){p.preventDefault();let v=f().filter(w=>w.focusable).map(w=>w.ref.current);if(m==="last")v.reverse();else if(m==="prev"||m==="next"){m==="prev"&&v.reverse();const w=v.indexOf(p.currentTarget);v=d.loop?yp(v,w+1):v.slice(w+1)}setTimeout(()=>Rc(v))}})})))}),gp={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function pp(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function bp(e,t,r){const n=pp(e.key,r);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(n))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(n)))return gp[n]}function Rc(e){const t=document.activeElement;for(const r of e)if(r===t||(r.focus(),document.activeElement!==t))return}function yp(e,t){return e.map((r,n)=>e[(t+n)%e.length])}const vp=up,wp=mp;var Nc=Po(),dn=function(){},Ur=s.forwardRef(function(e,t){var r=s.useRef(null),n=s.useState({onScrollCapture:dn,onWheelCapture:dn,onTouchMoveCapture:dn}),o=n[0],a=n[1],c=e.forwardProps,i=e.children,l=e.className,d=e.removeScrollBar,h=e.enabled,f=e.shards,g=e.sideCar,b=e.noIsolation,p=e.inert,m=e.allowPinchZoom,y=e.as,v=y===void 0?"div":y,w=Tr(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as"]),x=g,k=Ao([r,t]),E=Z(Z({},w),o);return s.createElement(s.Fragment,null,h&&s.createElement(x,{sideCar:Nc,removeScrollBar:d,shards:f,noIsolation:b,inert:p,setCallbacks:a,allowPinchZoom:!!m,lockRef:r}),c?s.cloneElement(s.Children.only(i),Z(Z({},E),{ref:k})):s.createElement(v,Z({},E,{className:l,ref:k}),i))});Ur.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Ur.classNames={fullWidth:bt,zeroRight:pt};var Hn=!1;if(typeof window<"u")try{var nr=Object.defineProperty({},"passive",{get:function(){return Hn=!0,!0}});window.addEventListener("test",nr,nr),window.removeEventListener("test",nr,nr)}catch{Hn=!1}var dt=Hn?{passive:!1}:!1,xp=function(e){return e.tagName==="TEXTAREA"},Fc=function(e,t){var r=window.getComputedStyle(e);return r[t]!=="hidden"&&!(r.overflowY===r.overflowX&&!xp(e)&&r[t]==="visible")},kp=function(e){return Fc(e,"overflowY")},Cp=function(e){return Fc(e,"overflowX")},Da=function(e,t){var r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var n=Dc(e,r);if(n){var o=Tc(e,r),a=o[1],c=o[2];if(a>c)return!0}r=r.parentNode}while(r&&r!==document.body);return!1},Ep=function(e){var t=e.scrollTop,r=e.scrollHeight,n=e.clientHeight;return[t,r,n]},Sp=function(e){var t=e.scrollLeft,r=e.scrollWidth,n=e.clientWidth;return[t,r,n]},Dc=function(e,t){return e==="v"?kp(t):Cp(t)},Tc=function(e,t){return e==="v"?Ep(t):Sp(t)},$p=function(e,t){return e==="h"&&t==="rtl"?-1:1},Ap=function(e,t,r,n,o){var a=$p(e,window.getComputedStyle(t).direction),c=a*n,i=r.target,l=t.contains(i),d=!1,h=c>0,f=0,g=0;do{var b=Tc(e,i),p=b[0],m=b[1],y=b[2],v=m-y-a*p;(p||v)&&Dc(e,i)&&(f+=v,g+=p),i=i.parentNode}while(!l&&i!==document.body||l&&(t.contains(i)||t===i));return(h&&(o&&f===0||!o&&c>f)||!h&&(o&&g===0||!o&&-c>g))&&(d=!0),d},or=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Ta=function(e){return[e.deltaX,e.deltaY]},Ma=function(e){return e&&"current"in e?e.current:e},Pp=function(e,t){return e[0]===t[0]&&e[1]===t[1]},Rp=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},Np=0,ut=[];function Fp(e){var t=s.useRef([]),r=s.useRef([0,0]),n=s.useRef(),o=s.useState(Np++)[0],a=s.useState(function(){return Or()})[0],c=s.useRef(e);s.useEffect(function(){c.current=e},[e]),s.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var m=$o([e.lockRef.current],(e.shards||[]).map(Ma),!0).filter(Boolean);return m.forEach(function(y){return y.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),m.forEach(function(y){return y.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var i=s.useCallback(function(m,y){if("touches"in m&&m.touches.length===2)return!c.current.allowPinchZoom;var v=or(m),w=r.current,x="deltaX"in m?m.deltaX:w[0]-v[0],k="deltaY"in m?m.deltaY:w[1]-v[1],E,S=m.target,C=Math.abs(x)>Math.abs(k)?"h":"v";if("touches"in m&&C==="h"&&S.type==="range")return!1;var $=Da(C,S);if(!$)return!0;if($?E=C:(E=C==="v"?"h":"v",$=Da(C,S)),!$)return!1;if(!n.current&&"changedTouches"in m&&(x||k)&&(n.current=E),!E)return!0;var N=n.current||E;return Ap(N,y,m,N==="h"?x:k,!0)},[]),l=s.useCallback(function(m){var y=m;if(!(!ut.length||ut[ut.length-1]!==a)){var v="deltaY"in y?Ta(y):or(y),w=t.current.filter(function(E){return E.name===y.type&&E.target===y.target&&Pp(E.delta,v)})[0];if(w&&w.should){y.cancelable&&y.preventDefault();return}if(!w){var x=(c.current.shards||[]).map(Ma).filter(Boolean).filter(function(E){return E.contains(y.target)}),k=x.length>0?i(y,x[0]):!c.current.noIsolation;k&&y.cancelable&&y.preventDefault()}}},[]),d=s.useCallback(function(m,y,v,w){var x={name:m,delta:y,target:v,should:w};t.current.push(x),setTimeout(function(){t.current=t.current.filter(function(k){return k!==x})},1)},[]),h=s.useCallback(function(m){r.current=or(m),n.current=void 0},[]),f=s.useCallback(function(m){d(m.type,Ta(m),m.target,i(m,e.lockRef.current))},[]),g=s.useCallback(function(m){d(m.type,or(m),m.target,i(m,e.lockRef.current))},[]);s.useEffect(function(){return ut.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:g}),document.addEventListener("wheel",l,dt),document.addEventListener("touchmove",l,dt),document.addEventListener("touchstart",h,dt),function(){ut=ut.filter(function(m){return m!==a}),document.removeEventListener("wheel",l,dt),document.removeEventListener("touchmove",l,dt),document.removeEventListener("touchstart",h,dt)}},[]);var b=e.removeScrollBar,p=e.inert;return s.createElement(s.Fragment,null,p?s.createElement(a,{styles:Rp(o)}):null,b?s.createElement(No,{gapMode:"margin"}):null)}const Dp=Ro(Nc,Fp);var Mc=s.forwardRef(function(e,t){return s.createElement(Ur,Z({},e,{ref:t,sideCar:Dp}))});Mc.classNames=Ur.classNames;const Tp=Mc,Un=["Enter"," "],Mp=["ArrowDown","PageUp","Home"],Oc=["ArrowUp","PageDown","End"],Op=[...Mp,...Oc],jp={ltr:[...Un,"ArrowRight"],rtl:[...Un,"ArrowLeft"]},_p={ltr:["ArrowLeft"],rtl:["ArrowRight"]},Gr="Menu",[It,Ip,Lp]=bc(Gr),[ot,jc]=Ht(Gr,[Lp,Cc,Pc]),_o=Cc(),_c=Pc(),[Bp,at]=ot(Gr),[Wp,Gt]=ot(Gr),Vp=e=>{const{__scopeMenu:t,open:r=!1,children:n,dir:o,onOpenChange:a,modal:c=!0}=e,i=_o(t),[l,d]=s.useState(null),h=s.useRef(!1),f=$e(a),g=yc(o);return s.useEffect(()=>{const b=()=>{h.current=!0,document.addEventListener("pointerdown",p,{capture:!0,once:!0}),document.addEventListener("pointermove",p,{capture:!0,once:!0})},p=()=>h.current=!1;return document.addEventListener("keydown",b,{capture:!0}),()=>{document.removeEventListener("keydown",b,{capture:!0}),document.removeEventListener("pointerdown",p,{capture:!0}),document.removeEventListener("pointermove",p,{capture:!0})}},[]),s.createElement(ep,i,s.createElement(Bp,{scope:t,open:r,onOpenChange:f,content:l,onContentChange:d},s.createElement(Wp,{scope:t,onClose:s.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:h,dir:g,modal:c},n)))},Ic=s.forwardRef((e,t)=>{const{__scopeMenu:r,...n}=e,o=_o(r);return s.createElement(tp,B({},o,n,{ref:t}))}),Lc="MenuPortal",[zp,Bc]=ot(Lc,{forceMount:void 0}),Hp=e=>{const{__scopeMenu:t,forceMount:r,children:n,container:o}=e,a=at(Lc,t);return s.createElement(zp,{scope:t,forceMount:r},s.createElement(Ut,{present:r||a.open},s.createElement(np,{asChild:!0,container:o},n)))},xe="MenuContent",[Up,Io]=ot(xe),Gp=s.forwardRef((e,t)=>{const r=Bc(xe,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,a=at(xe,e.__scopeMenu),c=Gt(xe,e.__scopeMenu);return s.createElement(It.Provider,{scope:e.__scopeMenu},s.createElement(Ut,{present:n||a.open},s.createElement(It.Slot,{scope:e.__scopeMenu},c.modal?s.createElement(Kp,B({},o,{ref:t})):s.createElement(Yp,B({},o,{ref:t})))))}),Kp=s.forwardRef((e,t)=>{const r=at(xe,e.__scopeMenu),n=s.useRef(null),o=me(t,n);return s.useEffect(()=>{const a=n.current;if(a)return So(a)},[]),s.createElement(Lo,B({},e,{ref:o,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:U(e.onFocusOutside,a=>a.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)}))}),Yp=s.forwardRef((e,t)=>{const r=at(xe,e.__scopeMenu);return s.createElement(Lo,B({},e,{ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)}))}),Lo=s.forwardRef((e,t)=>{const{__scopeMenu:r,loop:n=!1,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:c,disableOutsidePointerEvents:i,onEntryFocus:l,onEscapeKeyDown:d,onPointerDownOutside:h,onFocusOutside:f,onInteractOutside:g,onDismiss:b,disableOutsideScroll:p,...m}=e,y=at(xe,r),v=Gt(xe,r),w=_o(r),x=_c(r),k=Ip(r),[E,S]=s.useState(null),C=s.useRef(null),$=me(t,C,y.onContentChange),N=s.useRef(0),D=s.useRef(""),T=s.useRef(0),P=s.useRef(null),j=s.useRef("right"),I=s.useRef(0),M=p?Tp:s.Fragment,F=p?{as:fr,allowPinchZoom:!0}:void 0,R=A=>{var G,V;const ae=D.current+A,ie=k().filter(z=>!z.disabled),ce=document.activeElement,le=(G=ie.find(z=>z.ref.current===ce))===null||G===void 0?void 0:G.textValue,J=ie.map(z=>z.textValue),_=db(J,ae,le),K=(V=ie.find(z=>z.textValue===_))===null||V===void 0?void 0:V.ref.current;(function z(H){D.current=H,window.clearTimeout(N.current),H!==""&&(N.current=window.setTimeout(()=>z(""),1e3))})(ae),K&&setTimeout(()=>K.focus())};s.useEffect(()=>()=>window.clearTimeout(N.current),[]),Og();const O=s.useCallback(A=>{var G,V;return j.current===((G=P.current)===null||G===void 0?void 0:G.side)&&fb(A,(V=P.current)===null||V===void 0?void 0:V.area)},[]);return s.createElement(Up,{scope:r,searchRef:D,onItemEnter:s.useCallback(A=>{O(A)&&A.preventDefault()},[O]),onItemLeave:s.useCallback(A=>{var G;O(A)||((G=C.current)===null||G===void 0||G.focus(),S(null))},[O]),onTriggerLeave:s.useCallback(A=>{O(A)&&A.preventDefault()},[O]),pointerGraceTimerRef:T,onPointerGraceIntentChange:s.useCallback(A=>{P.current=A},[])},s.createElement(M,F,s.createElement(jg,{asChild:!0,trapped:o,onMountAutoFocus:U(a,A=>{var G;A.preventDefault(),(G=C.current)===null||G===void 0||G.focus()}),onUnmountAutoFocus:c},s.createElement(Dg,{asChild:!0,disableOutsidePointerEvents:i,onEscapeKeyDown:d,onPointerDownOutside:h,onFocusOutside:f,onInteractOutside:g,onDismiss:b},s.createElement(vp,B({asChild:!0},x,{dir:v.dir,orientation:"vertical",loop:n,currentTabStopId:E,onCurrentTabStopIdChange:S,onEntryFocus:U(l,A=>{v.isUsingKeyboardRef.current||A.preventDefault()})}),s.createElement(rp,B({role:"menu","aria-orientation":"vertical","data-state":Uc(y.open),"data-radix-menu-content":"",dir:v.dir},w,m,{ref:$,style:{outline:"none",...m.style},onKeyDown:U(m.onKeyDown,A=>{const V=A.target.closest("[data-radix-menu-content]")===A.currentTarget,ae=A.ctrlKey||A.altKey||A.metaKey,ie=A.key.length===1;V&&(A.key==="Tab"&&A.preventDefault(),!ae&&ie&&R(A.key));const ce=C.current;if(A.target!==ce||!Op.includes(A.key))return;A.preventDefault();const J=k().filter(_=>!_.disabled).map(_=>_.ref.current);Oc.includes(A.key)&&J.reverse(),cb(J)}),onBlur:U(e.onBlur,A=>{A.currentTarget.contains(A.target)||(window.clearTimeout(N.current),D.current="")}),onPointerMove:U(e.onPointerMove,Lt(A=>{const G=A.target,V=I.current!==A.clientX;if(A.currentTarget.contains(G)&&V){const ae=A.clientX>I.current?"right":"left";j.current=ae,I.current=A.clientX}}))})))))))}),Xp=s.forwardRef((e,t)=>{const{__scopeMenu:r,...n}=e;return s.createElement(be.div,B({},n,{ref:t}))}),Gn="MenuItem",Oa="menu.itemSelect",Bo=s.forwardRef((e,t)=>{const{disabled:r=!1,onSelect:n,...o}=e,a=s.useRef(null),c=Gt(Gn,e.__scopeMenu),i=Io(Gn,e.__scopeMenu),l=me(t,a),d=s.useRef(!1),h=()=>{const f=a.current;if(!r&&f){const g=new CustomEvent(Oa,{bubbles:!0,cancelable:!0});f.addEventListener(Oa,b=>n==null?void 0:n(b),{once:!0}),pc(f,g),g.defaultPrevented?d.current=!1:c.onClose()}};return s.createElement(Wc,B({},o,{ref:l,disabled:r,onClick:U(e.onClick,h),onPointerDown:f=>{var g;(g=e.onPointerDown)===null||g===void 0||g.call(e,f),d.current=!0},onPointerUp:U(e.onPointerUp,f=>{var g;d.current||(g=f.currentTarget)===null||g===void 0||g.click()}),onKeyDown:U(e.onKeyDown,f=>{const g=i.searchRef.current!=="";r||g&&f.key===" "||Un.includes(f.key)&&(f.currentTarget.click(),f.preventDefault())})}))}),Wc=s.forwardRef((e,t)=>{const{__scopeMenu:r,disabled:n=!1,textValue:o,...a}=e,c=Io(Gn,r),i=_c(r),l=s.useRef(null),d=me(t,l),[h,f]=s.useState(!1),[g,b]=s.useState("");return s.useEffect(()=>{const p=l.current;if(p){var m;b(((m=p.textContent)!==null&&m!==void 0?m:"").trim())}},[a.children]),s.createElement(It.ItemSlot,{scope:r,disabled:n,textValue:o??g},s.createElement(wp,B({asChild:!0},i,{focusable:!n}),s.createElement(be.div,B({role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":n||void 0,"data-disabled":n?"":void 0},a,{ref:d,onPointerMove:U(e.onPointerMove,Lt(p=>{n?c.onItemLeave(p):(c.onItemEnter(p),p.defaultPrevented||p.currentTarget.focus())})),onPointerLeave:U(e.onPointerLeave,Lt(p=>c.onItemLeave(p))),onFocus:U(e.onFocus,()=>f(!0)),onBlur:U(e.onBlur,()=>f(!1))}))))}),qp=s.forwardRef((e,t)=>{const{checked:r=!1,onCheckedChange:n,...o}=e;return s.createElement(zc,{scope:e.__scopeMenu,checked:r},s.createElement(Bo,B({role:"menuitemcheckbox","aria-checked":Er(r)?"mixed":r},o,{ref:t,"data-state":Wo(r),onSelect:U(o.onSelect,()=>n==null?void 0:n(Er(r)?!0:!r),{checkForDefaultPrevented:!1})})))}),Zp="MenuRadioGroup",[iw,Jp]=ot(Zp,{value:void 0,onValueChange:()=>{}}),Qp="MenuRadioItem",eb=s.forwardRef((e,t)=>{const{value:r,...n}=e,o=Jp(Qp,e.__scopeMenu),a=r===o.value;return s.createElement(zc,{scope:e.__scopeMenu,checked:a},s.createElement(Bo,B({role:"menuitemradio","aria-checked":a},n,{ref:t,"data-state":Wo(a),onSelect:U(n.onSelect,()=>{var c;return(c=o.onValueChange)===null||c===void 0?void 0:c.call(o,r)},{checkForDefaultPrevented:!1})})))}),Vc="MenuItemIndicator",[zc,tb]=ot(Vc,{checked:!1}),rb=s.forwardRef((e,t)=>{const{__scopeMenu:r,forceMount:n,...o}=e,a=tb(Vc,r);return s.createElement(Ut,{present:n||Er(a.checked)||a.checked===!0},s.createElement(be.span,B({},o,{ref:t,"data-state":Wo(a.checked)})))}),nb=s.forwardRef((e,t)=>{const{__scopeMenu:r,...n}=e;return s.createElement(be.div,B({role:"separator","aria-orientation":"horizontal"},n,{ref:t}))}),ob="MenuSub",[cw,Hc]=ot(ob),ar="MenuSubTrigger",ab=s.forwardRef((e,t)=>{const r=at(ar,e.__scopeMenu),n=Gt(ar,e.__scopeMenu),o=Hc(ar,e.__scopeMenu),a=Io(ar,e.__scopeMenu),c=s.useRef(null),{pointerGraceTimerRef:i,onPointerGraceIntentChange:l}=a,d={__scopeMenu:e.__scopeMenu},h=s.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return s.useEffect(()=>h,[h]),s.useEffect(()=>{const f=i.current;return()=>{window.clearTimeout(f),l(null)}},[i,l]),s.createElement(Ic,B({asChild:!0},d),s.createElement(Wc,B({id:o.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":o.contentId,"data-state":Uc(r.open)},e,{ref:es(t,o.onTriggerChange),onClick:f=>{var g;(g=e.onClick)===null||g===void 0||g.call(e,f),!(e.disabled||f.defaultPrevented)&&(f.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:U(e.onPointerMove,Lt(f=>{a.onItemEnter(f),!f.defaultPrevented&&!e.disabled&&!r.open&&!c.current&&(a.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{r.onOpenChange(!0),h()},100))})),onPointerLeave:U(e.onPointerLeave,Lt(f=>{var g;h();const b=(g=r.content)===null||g===void 0?void 0:g.getBoundingClientRect();if(b){var p;const m=(p=r.content)===null||p===void 0?void 0:p.dataset.side,y=m==="right",v=y?-5:5,w=b[y?"left":"right"],x=b[y?"right":"left"];a.onPointerGraceIntentChange({area:[{x:f.clientX+v,y:f.clientY},{x:w,y:b.top},{x,y:b.top},{x,y:b.bottom},{x:w,y:b.bottom}],side:m}),window.clearTimeout(i.current),i.current=window.setTimeout(()=>a.onPointerGraceIntentChange(null),300)}else{if(a.onTriggerLeave(f),f.defaultPrevented)return;a.onPointerGraceIntentChange(null)}})),onKeyDown:U(e.onKeyDown,f=>{const g=a.searchRef.current!=="";if(!(e.disabled||g&&f.key===" ")&&jp[n.dir].includes(f.key)){var b;r.onOpenChange(!0),(b=r.content)===null||b===void 0||b.focus(),f.preventDefault()}})})))}),sb="MenuSubContent",ib=s.forwardRef((e,t)=>{const r=Bc(xe,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,a=at(xe,e.__scopeMenu),c=Gt(xe,e.__scopeMenu),i=Hc(sb,e.__scopeMenu),l=s.useRef(null),d=me(t,l);return s.createElement(It.Provider,{scope:e.__scopeMenu},s.createElement(Ut,{present:n||a.open},s.createElement(It.Slot,{scope:e.__scopeMenu},s.createElement(Lo,B({id:i.contentId,"aria-labelledby":i.triggerId},o,{ref:d,align:"start",side:c.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:h=>{var f;c.isUsingKeyboardRef.current&&((f=l.current)===null||f===void 0||f.focus()),h.preventDefault()},onCloseAutoFocus:h=>h.preventDefault(),onFocusOutside:U(e.onFocusOutside,h=>{h.target!==i.trigger&&a.onOpenChange(!1)}),onEscapeKeyDown:U(e.onEscapeKeyDown,h=>{c.onClose(),h.preventDefault()}),onKeyDown:U(e.onKeyDown,h=>{const f=h.currentTarget.contains(h.target),g=_p[c.dir].includes(h.key);if(f&&g){var b;a.onOpenChange(!1),(b=i.trigger)===null||b===void 0||b.focus(),h.preventDefault()}})})))))});function Uc(e){return e?"open":"closed"}function Er(e){return e==="indeterminate"}function Wo(e){return Er(e)?"indeterminate":e?"checked":"unchecked"}function cb(e){const t=document.activeElement;for(const r of e)if(r===t||(r.focus(),document.activeElement!==t))return}function lb(e,t){return e.map((r,n)=>e[(t+n)%e.length])}function db(e,t,r){const o=t.length>1&&Array.from(t).every(d=>d===t[0])?t[0]:t,a=r?e.indexOf(r):-1;let c=lb(e,Math.max(a,0));o.length===1&&(c=c.filter(d=>d!==r));const l=c.find(d=>d.toLowerCase().startsWith(o.toLowerCase()));return l!==r?l:void 0}function ub(e,t){const{x:r,y:n}=e;let o=!1;for(let a=0,c=t.length-1;a<t.length;c=a++){const i=t[a].x,l=t[a].y,d=t[c].x,h=t[c].y;l>n!=h>n&&r<(d-i)*(n-l)/(h-l)+i&&(o=!o)}return o}function fb(e,t){if(!t)return!1;const r={x:e.clientX,y:e.clientY};return ub(r,t)}function Lt(e){return t=>t.pointerType==="mouse"?e(t):void 0}const hb=Vp,mb=Ic,gb=Hp,pb=Gp,bb=Xp,yb=Bo,vb=qp,wb=eb,xb=rb,kb=nb,Cb=ab,Eb=ib,Gc="DropdownMenu",[Sb,lw]=Ht(Gc,[jc]),ye=jc(),[$b,Kc]=Sb(Gc),Ab=e=>{const{__scopeDropdownMenu:t,children:r,dir:n,open:o,defaultOpen:a,onOpenChange:c,modal:i=!0}=e,l=ye(t),d=s.useRef(null),[h=!1,f]=gc({prop:o,defaultProp:a,onChange:c});return s.createElement($b,{scope:t,triggerId:Vn(),triggerRef:d,contentId:Vn(),open:h,onOpenChange:f,onOpenToggle:s.useCallback(()=>f(g=>!g),[f]),modal:i},s.createElement(hb,B({},l,{open:h,onOpenChange:f,dir:n,modal:i}),r))},Pb="DropdownMenuTrigger",Rb=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:r,disabled:n=!1,...o}=e,a=Kc(Pb,r),c=ye(r);return s.createElement(mb,B({asChild:!0},c),s.createElement(be.button,B({type:"button",id:a.triggerId,"aria-haspopup":"menu","aria-expanded":a.open,"aria-controls":a.open?a.contentId:void 0,"data-state":a.open?"open":"closed","data-disabled":n?"":void 0,disabled:n},o,{ref:es(t,a.triggerRef),onPointerDown:U(e.onPointerDown,i=>{!n&&i.button===0&&i.ctrlKey===!1&&(a.onOpenToggle(),a.open||i.preventDefault())}),onKeyDown:U(e.onKeyDown,i=>{n||(["Enter"," "].includes(i.key)&&a.onOpenToggle(),i.key==="ArrowDown"&&a.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(i.key)&&i.preventDefault())})})))}),Nb=e=>{const{__scopeDropdownMenu:t,...r}=e,n=ye(t);return s.createElement(gb,B({},n,r))},Fb="DropdownMenuContent",Db=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:r,...n}=e,o=Kc(Fb,r),a=ye(r),c=s.useRef(!1);return s.createElement(pb,B({id:o.contentId,"aria-labelledby":o.triggerId},a,n,{ref:t,onCloseAutoFocus:U(e.onCloseAutoFocus,i=>{var l;c.current||(l=o.triggerRef.current)===null||l===void 0||l.focus(),c.current=!1,i.preventDefault()}),onInteractOutside:U(e.onInteractOutside,i=>{const l=i.detail.originalEvent,d=l.button===0&&l.ctrlKey===!0,h=l.button===2||d;(!o.modal||h)&&(c.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}}))}),Tb=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:r,...n}=e,o=ye(r);return s.createElement(bb,B({},o,n,{ref:t}))}),Mb=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:r,...n}=e,o=ye(r);return s.createElement(yb,B({},o,n,{ref:t}))}),Ob=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:r,...n}=e,o=ye(r);return s.createElement(vb,B({},o,n,{ref:t}))}),jb=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:r,...n}=e,o=ye(r);return s.createElement(wb,B({},o,n,{ref:t}))}),_b=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:r,...n}=e,o=ye(r);return s.createElement(xb,B({},o,n,{ref:t}))}),Ib=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:r,...n}=e,o=ye(r);return s.createElement(kb,B({},o,n,{ref:t}))}),Lb=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:r,...n}=e,o=ye(r);return s.createElement(Cb,B({},o,n,{ref:t}))}),Bb=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:r,...n}=e,o=ye(r);return s.createElement(Eb,B({},o,n,{ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}}))}),Wb=Ab,Vb=Rb,zb=Nb,Yc=Db,Xc=Tb,qc=Mb,Zc=Ob,Jc=jb,Qc=_b,el=Ib,tl=Lb,rl=Bb,Kr=Wb,Yr=Vb,Hb=s.forwardRef(({className:e,inset:t,children:r,...n},o)=>u.jsxs(tl,{ref:o,className:L("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none","focus:bg-[var(--card)] focus:text-[var(--card-foreground)] data-[state=open]:bg-[var(--card)]",t&&"pl-8",e),...n,children:[r,u.jsx(rs,{className:"ml-auto h-4 w-4"})]}));Hb.displayName=tl.displayName;const Ub=s.forwardRef(({className:e,...t},r)=>u.jsx(rl,{ref:r,className:L("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-[var(--muted)] p-1 shadow-lg","border-[var(--border)] text-[var(--muted-foreground)]","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t}));Ub.displayName=rl.displayName;const Kt=s.forwardRef(({className:e,sideOffset:t=4,...r},n)=>u.jsx(zb,{children:u.jsx(Yc,{ref:n,sideOffset:t,className:L("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-[var(--card)] p-1 shadow-md","border-[var(--border)] text-[var(--card-foreground)]","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})}));Kt.displayName=Yc.displayName;const Yt=s.forwardRef(({className:e,inset:t,...r},n)=>u.jsx(qc,{ref:n,className:L("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors","focus:bg-[var(--muted)] focus:text-[var(--muted-foreground)]","data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[highlighted]:bg-[var(--accent)] data-[highlighted]:text-[var(--accent-foreground)]",t&&"pl-8",e),...r}));Yt.displayName=qc.displayName;const Gb=s.forwardRef(({className:e,children:t,checked:r,...n},o)=>u.jsxs(Zc,{ref:o,className:L("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors","focus:bg-[var(--muted)] focus:text-[var(--muted-foreground)]","data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[highlighted]:bg-[var(--accent)] data-[highlighted]:text-[var(--accent-foreground)]",e),checked:r,...n,children:[u.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:u.jsx(Qc,{children:u.jsx(Nr,{className:"h-4 w-4"})})}),t]}));Gb.displayName=Zc.displayName;const Kb=s.forwardRef(({className:e,children:t,...r},n)=>u.jsxs(Jc,{ref:n,className:L("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors","focus:bg-[var(--muted)] focus:text-[var(--muted-foreground)]","data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[highlighted]:bg-[var(--accent)] data-[highlighted]:text-[var(--accent-foreground)]",e),...r,children:[u.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:u.jsx(Qc,{children:u.jsx(Ld,{className:"h-2 w-2 fill-current"})})}),t]}));Kb.displayName=Jc.displayName;const Yb=s.forwardRef(({className:e,inset:t,...r},n)=>u.jsx(Xc,{ref:n,className:L("px-2 py-1.5 text-sm font-semibold","text-[var(--muted-foreground)]",t&&"pl-8",e),...r}));Yb.displayName=Xc.displayName;const Xb=s.forwardRef(({className:e,...t},r)=>u.jsx(el,{ref:r,className:L("-mx-1 my-1 h-px bg-[var(--muted)]",e),...t}));Xb.displayName=el.displayName;const qb=({selectedColorFormat:e,setSelectedColorFormat:t,colorFormats:r})=>u.jsxs(Kr,{children:[u.jsx(Yr,{className:"nofocus inline-flex",children:u.jsxs("div",{className:"flex w-24 items-center justify-between rounded-md border border-border bg-secondary p-2 text-sm text-primary",children:[u.jsx("span",{children:e}),u.jsx(Wt,{className:"h-4 w-4"})]})}),u.jsx(Kt,{align:"end",className:"w-24 rounded-md border border-border bg-secondary p-1",children:r.map(n=>u.jsx(Yt,{onSelect:()=>{t(n)},className:"hover:bg-primary/10 cursor-pointer rounded px-2 py-1.5 text-sm text-primary",children:n},n))})]}),Zb=({gradientType:e,setGradientType:t})=>u.jsxs(Kr,{children:[u.jsx(Yr,{className:"inline-flex",children:u.jsxs("div",{className:"flex w-32 items-center justify-between rounded-md border border-border bg-secondary p-2 text-sm text-primary",children:[u.jsx("span",{children:e.charAt(0).toUpperCase()+e.slice(1)}),u.jsx(Wt,{className:"h-4 w-4"})]})}),u.jsx(Kt,{align:"end",className:"w-32 rounded-md border border-border bg-secondary p-1",children:["background","text"].map(r=>u.jsx(Yt,{onSelect:()=>t(r),className:"hover:bg-primary/10 cursor-pointer rounded px-2 py-1.5 text-sm text-primary",children:r.charAt(0).toUpperCase()+r.slice(1)},r))})]}),Jb=({selectedColorFormat:e,setSelectedColorFormat:t,colorFormats:r,gradientType:n,setGradientType:o})=>u.jsxs("div",{className:"flex gap-1",children:[u.jsx(qb,{selectedColorFormat:e,setSelectedColorFormat:t,colorFormats:r}),u.jsx("div",{}),u.jsx(Zb,{gradientType:n,setGradientType:o})]}),Qb=({code:e,copiedStates:t,activeTab:r,copyToClipboard:n})=>u.jsxs("div",{className:"relative mt-2 max-w-full",children:[u.jsx("pre",{className:"truncate w-full max-w-full overflow-hidden whitespace-nowrap rounded-md border border-border bg-secondary p-2 pr-10 text-xs text-muted-foreground",children:e}),u.jsx("button",{className:"absolute right-0 top-0 flex h-full w-[30px] items-center justify-center border border-border bg-secondary p-0 text-primary",onClick:()=>n(e,r),children:u.jsx(ts,{children:t[r]?u.jsx(te.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},transition:{duration:.2},children:u.jsx(Nr,{className:"text-success h-4 w-4"})}):u.jsx(te.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},transition:{duration:.2},children:u.jsx(Ad,{className:"h-4 w-4"})})})})]}),ey=()=>u.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 24 24",fill:"currentColor",children:u.jsx("path",{d:"M12.001,4.8c-3.2,0-5.2,1.6-6,4.8c1.2-1.6,2.6-2.2,4.2-1.8c0.913,0.228,1.565,0.89,2.288,1.624 C13.666,10.618,15.027,12,18.001,12c3.2,0,5.2-1.6,6-4.8c-1.2,1.6-2.6,2.2-4.2,1.8c-0.913-0.228-1.565-0.89-2.288-1.624 C16.337,6.182,14.976,4.8,12.001,4.8z M6.001,12c-3.2,0-5.2,1.6-6,4.8c1.2-1.6,2.6-2.2,4.2-1.8c0.913,0.228,1.565,0.89,2.288,1.624 C7.666,17.818,9.027,19.2,12.001,19.2c3.2,0,5.2-1.6,6-4.8c-1.2,1.6-2.6,2.2-4.2,1.8c-0.913-0.228-1.565-0.89-2.288-1.624 C10.337,13.382,8.976,12,6.001,12z"})}),ty=()=>u.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 24 24",fill:"currentColor",children:u.jsx("path",{d:"M1.5 0h21l-1.91 21.563L11.977 24l-8.565-2.438L1.5 0zm17.09 4.413L5.41 4.413l.213 2.622h10.125l-.255 2.716h-6.64l.24 2.573h6.182l-.366 3.523-2.91.804-2.956-.81-.188-2.11h-2.61l.29 3.855L12 19.288l5.373-1.53L18.59 4.414z"})}),ry=()=>u.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 24 24",fill:"currentColor",children:u.jsx("path",{d:"M12,0C5.4,0,0,5.4,0,12s5.4,12,12,12s12-5.4,12-12S18.6,0,12,0z M10.6,16.2c-0.3,1.1-0.7,2.1-1.5,2.9 c-0.5,0.5-1.1,0.9-1.8,0.9c-0.7,0-1.1-0.4-1.1-1.1c0-1.1,0.6-2.1,1.4-2.8c0.8-0.7,1.8-1.1,2.9-1.1c0.1,0,0.1,0,0.2,0 C10.7,15.4,10.7,15.8,10.6,16.2z M16.6,14.4c-0.3,0-0.6,0.1-0.9,0.1c-1.3,0.2-1.5,0.4-1.7,0.9c-0.1,0.3-0.1,0.6-0.1,0.9 c0,1.1,0.4,1.8,1.1,2.2c0.4,0.2,0.9,0.3,1.4,0.3c1.1,0,2.1-0.4,2.8-1.1c0.7-0.7,1.1-1.7,1.1-2.8c0-0.7-0.2-1.3-0.6-1.8 C18.2,12.7,17.4,12.4,16.6,14.4z"})}),ny=()=>u.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 24 24",fill:"currentColor",children:u.jsx("path",{d:"M6.1 21.98C4.2 21.81 2.78 20.79 2.06 19.62c-.72-1.17-.72-2.06 0-3.23.72-1.17 2.14-2.19 4.04-2.36.1-.01.2-.01.3-.01 1.5 0 2.9.6 3.9 1.6 1 1 1.6 2.4 1.6 3.9 0 .1 0 .2-.01.3-.17 1.9-1.19 3.32-2.36 4.04-1.17.72-2.06.72-3.23 0-.72-.44-1.3-1.02-1.74-1.74-.44-.72-.72-1.5-.72-2.32 0-.1 0-.2.01-.3.17-1.9 1.19-3.32 2.36-4.04 1.17-.72 2.06-.72 3.23 0 1.17.72 2.19 2.14 2.36 4.04.01.1.01.2.01.3 0 .82-.28 1.6-.72 2.32-.44.72-1.02 1.3-1.74 1.74z"})}),oy=()=>u.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 24 24",fill:"currentColor",children:u.jsx("path",{d:"M5,3H7L9,7L11,3H13L10,9L13,15H11L9,11L7,15H5L8,9L5,3M14,3H16V15H14V3M18,3H20V15H18V3Z"})}),ay=()=>u.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 24 24",fill:"currentColor",children:u.jsx("path",{d:"M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M7.07,18.28C7.5,17.38 8.12,16.19 8.91,14.79C9.71,13.39 10.58,11.88 11.5,10.28C12.42,11.88 13.29,13.39 14.09,14.79C14.88,16.19 15.5,17.38 15.93,18.28C14.75,19.13 13.42,19.64 12,19.64C10.58,19.64 9.25,19.13 7.07,18.28M5.08,16.15C5.69,16.15 6.22,15.39 6.7,14.02C7.18,12.65 7.59,10.81 7.93,8.64C8.27,10.81 8.68,12.65 9.16,14.02C9.64,15.39 10.17,16.15 10.78,16.15C11.39,16.15 11.92,15.39 12.4,14.02C12.88,12.65 13.29,10.81 13.63,8.64C13.97,10.81 14.38,12.65 14.86,14.02C15.34,15.39 15.87,16.15 16.48,16.15C17.09,16.15 17.62,15.39 18.1,14.02C18.58,12.65 18.99,10.81 19.33,8.64C19.36,8.47 19.38,8.31 19.4,8.15C19.91,9.68 20.2,11.31 20.2,13C20.2,14.69 19.91,16.32 19.4,17.85C18.99,15.68 18.58,13.84 18.1,12.47C17.62,11.1 17.09,10.34 16.48,10.34C15.87,10.34 15.34,11.1 14.86,12.47C14.38,13.84 13.97,15.68 13.63,17.85C13.29,15.68 12.88,13.84 12.4,12.47C11.92,11.1 11.39,10.34 10.78,10.34C10.17,10.34 9.64,11.1 9.16,12.47C8.68,13.84 8.27,15.68 7.93,17.85C7.59,15.68 7.18,13.84 6.7,12.47C6.22,11.1 5.69,10.34 5.08,10.34C4.47,10.34 3.94,11.1 3.46,12.47C2.98,13.84 2.57,15.68 2.23,17.85C1.72,16.32 1.43,14.69 1.43,13C1.43,11.31 1.72,9.68 2.23,8.15C2.57,10.32 2.98,12.16 3.46,13.53C3.94,14.9 4.47,15.66 5.08,15.66Z"})}),sy=()=>u.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 24 24",fill:"currentColor",children:u.jsx("path",{d:"M5,3H7V5H5V10A2,2 0 0,1 3,12A2,2 0 0,1 5,14V19H7V21H5C3.93,20.73 3,20.1 3,19V15A2,2 0 0,0 1,13H0V11H1A2,2 0 0,0 3,9V5C3,3.9 3.9,3 5,3M19,3A2,2 0 0,1 21,5V9A2,2 0 0,0 23,11H24V13H23A2,2 0 0,0 21,15V19A2,2 0 0,1 19,21H17V19H19V14A2,2 0 0,1 21,12A2,2 0 0,1 19,10V5H17V3H19Z"})}),iy=({getCode:e,copiedStates:t,copyToClipboard:r})=>{var c;const[n,o]=s.useState("tailwind"),a=[{key:"tailwind",label:"Tailwind CSS",icon:ey,color:"#06B6D4"},{key:"css",label:"CSS",icon:ty,color:"#1572B6"},{key:"sass",label:"SASS/SCSS",icon:ry,color:"#CF649A"},{key:"bootstrap",label:"Bootstrap",icon:ny,color:"#7952B3"},{key:"xml",label:"Android XML",icon:oy,color:"#A4C639"},{key:"svg",label:"SVG",icon:ay,color:"#FFB13B"},{key:"json",label:"JSON",icon:sy,color:"#000000"}];return u.jsxs("div",{className:"space-y-3 max-w-full",children:[u.jsxs(Kr,{children:[u.jsx(Yr,{className:"nofocus relative w-full",children:u.jsxs("div",{className:"flex items-center justify-between rounded-md border border-border bg-secondary p-2 text-sm text-primary",children:[u.jsxs("div",{className:"flex items-center gap-2",children:[(()=>{var l;const i=(l=a.find(d=>d.key===n))==null?void 0:l.icon;return i?u.jsx(i,{}):null})(),u.jsx("span",{children:((c=a.find(i=>i.key===n))==null?void 0:c.label)||n})]}),u.jsx(Wt,{className:"h-4 w-4"})]})}),u.jsx(Kt,{align:"end",className:"w-48 rounded-md border border-border bg-secondary p-1",children:a.map(i=>u.jsx(Yt,{onSelect:()=>{o(i.key)},className:"hover:bg-primary/10 cursor-pointer rounded px-2 py-1.5 text-sm text-primary",children:u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx(i.icon,{}),u.jsx("span",{children:i.label})]})},i.key))})]}),u.jsx(Qb,{code:e(n),copiedStates:t,activeTab:n,copyToClipboard:(i,l)=>{r(i,l)}})]})},cy=({checked:e,onChange:t,label:r="Animate Gradient",className:n=""})=>u.jsxs("div",{className:`flex items-center gap-2 ${n}`,children:[u.jsx(te.button,{className:`relative flex h-5 w-5 items-center justify-center rounded border-2 transition-colors ${e?"border-blue-500 bg-blue-500":"border-gray-300 bg-transparent hover:border-blue-400"}`,onClick:()=>t(!e),whileHover:{scale:1.05},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:17},children:u.jsx(te.div,{initial:!1,animate:{scale:e?1:0,opacity:e?1:0},transition:{type:"spring",stiffness:400,damping:17},children:u.jsx(Nr,{className:"h-3 w-3 text-white"})})}),u.jsxs(te.div,{className:"flex items-center gap-1 text-sm text-muted-foreground",whileHover:{scale:1.02},children:[u.jsx(Kd,{className:"h-3 w-3"}),u.jsx("span",{children:r})]})]}),ly=({speed:e,onChange:t,disabled:r=!1,className:n=""})=>{const o=[{value:.5,label:"Slow"},{value:1,label:"Normal"},{value:2,label:"Fast"},{value:3,label:"Very Fast"}];return u.jsxs("div",{className:`flex items-center gap-2 ${n}`,children:[u.jsx(Hd,{className:"h-4 w-4 text-muted-foreground"}),u.jsx("div",{className:"flex items-center gap-1",children:o.map(a=>u.jsx(te.button,{className:`px-2 py-1 text-xs rounded transition-colors ${e===a.value?"bg-blue-500 text-white":"bg-secondary text-muted-foreground hover:bg-primary/10"} ${r?"opacity-50 cursor-not-allowed":"cursor-pointer"}`,onClick:()=>!r&&t(a.value),whileHover:r?{}:{scale:1.05},whileTap:r?{}:{scale:.95},disabled:r,children:a.label},a.value))})]})},dy=({gradient:e,getColorInFormat:t,copyToClipboard:r,selectedColorFormat:n,setSelectedColorFormat:o,colorFormats:a,gradientType:c,setGradientType:i,angle:l,setAngle:d,getCode:h,copiedStates:f,isAnimated:g,setIsAnimated:b,animationSpeed:p,setAnimationSpeed:m})=>u.jsxs("div",{className:"flex flex-col items-start space-y-4 max-w-full",children:[u.jsxs("div",{className:"flex w-full items-center justify-between gap-2",children:[u.jsx(Im,{colors:e.colors,getColorInFormat:t,copyToClipboard:(y,v)=>{r(y,v)}}),u.jsx(Jb,{selectedColorFormat:n,setSelectedColorFormat:o,colorFormats:a,gradientType:c,setGradientType:i})]}),u.jsx(Cg,{angle:l,setAngle:y=>{d(y)}}),u.jsxs("div",{className:"flex flex-col gap-3",children:[u.jsx(cy,{checked:g,onChange:b,label:"Animate Gradient"}),u.jsx(ly,{speed:p,onChange:m,disabled:!g})]}),u.jsx(iy,{getCode:h,copiedStates:f,copyToClipboard:r})]}),wt={hexToRGB:e=>{const t=parseInt(e.slice(1,3),16),r=parseInt(e.slice(3,5),16),n=parseInt(e.slice(5,7),16);return{r:t,g:r,b:n}},rgbToHSL:(e,t,r)=>{e/=255,t/=255,r/=255;const n=Math.max(e,t,r),o=Math.min(e,t,r);let a=0,c=0;const i=(n+o)/2;if(n!==o){const l=n-o;switch(c=i>.5?l/(2-n-o):l/(n+o),n){case e:a=((t-r)/l+(t<r?6:0))*60;break;case t:a=((r-e)/l+2)*60;break;case r:a=((e-t)/l+4)*60;break}}return{h:Math.round(a),s:Math.round(c*100),l:Math.round(i*100)}},getUniqueColors:e=>{const t=new Set;return e.forEach(r=>{r.colorsname&&Array.isArray(r.colorsname)&&r.colorsname.forEach(n=>{n&&typeof n=="string"&&t.add(n.toLowerCase().trim())})}),Array.from(t).sort()},getColorCategories:e=>{const t={"🔴 Red":[],"🩷 Pink":[],"🟠 Orange":[],"🟡 Yellow":[],"🟢 Green":[],"🔵 Blue":[],"🟣 Purple":[],"🟤 Brown":[],"⚫ Black":[],"⚪ White":[],"🔘 Gray":[],"🎨 Other":[]};return e.forEach(r=>{const n=r.toLowerCase();n.includes("red")?t["🔴 Red"].push(r):n.includes("pink")?t["🩷 Pink"].push(r):n.includes("orange")||n.includes("peach")?t["🟠 Orange"].push(r):n.includes("yellow")?t["🟡 Yellow"].push(r):n.includes("green")||n.includes("olive")||n.includes("teal")||n.includes("cyan")?t["🟢 Green"].push(r):n.includes("blue")||n.includes("indigo")?t["🔵 Blue"].push(r):n.includes("purple")||n.includes("violet")||n.includes("magenta")?t["🟣 Purple"].push(r):n.includes("brown")||n.includes("beige")?t["🟤 Brown"].push(r):n.includes("black")?t["⚫ Black"].push(r):n.includes("white")?t["⚪ White"].push(r):n.includes("gray")||n.includes("grey")?t["🔘 Gray"].push(r):t["🎨 Other"].push(r)}),Object.keys(t).forEach(r=>{t[r].length===0&&delete t[r]}),t},getBasicColors:e=>{const t=wt.getUniqueColors(e),r=["red","pink","orange","yellow","green","blue","purple","brown","black","white","gray","grey","teal","cyan"];return t.filter(n=>{const o=n.toLowerCase();return r.some(a=>o.includes(a))})},getBasicColorCategories:e=>{const t=e.filter(r=>{const n=r.toLowerCase();return["red","pink","orange","yellow","green","blue","purple","brown","black","white","gray","grey","teal","cyan"].some(a=>n.includes(a))});return wt.getColorCategories(t)},getSimplifiedColors:e=>{const t=new Set;return e.forEach(r=>{r.colorsname&&Array.isArray(r.colorsname)&&r.colorsname.forEach(n=>{const o=n.toLowerCase();o.includes("red")?t.add("Red"):o.includes("pink")?t.add("Pink"):o.includes("orange")?t.add("Orange"):o.includes("yellow")?t.add("Yellow"):o.includes("green")?t.add("Green"):o.includes("blue")?t.add("Blue"):o.includes("purple")||o.includes("violet")?t.add("Purple"):o.includes("brown")||o.includes("beige")?t.add("Brown"):o.includes("black")?t.add("Black"):o.includes("white")?t.add("White"):o.includes("gray")||o.includes("grey")?t.add("Gray"):o.includes("teal")?t.add("Teal"):o.includes("cyan")&&t.add("Cyan")})}),Array.from(t).sort()},getSimplifiedColorCategories:e=>{const t={"🔴 Red":[],"🩷 Pink":[],"🟠 Orange":[],"🟡 Yellow":[],"🟢 Green":[],"🔵 Blue":[],"🟣 Purple":[],"🟤 Brown":[],"⚫ Black":[],"⚪ White":[],"🔘 Gray":[],"🩵 Teal":[],"🩵 Cyan":[]};return e.forEach(r=>{switch(r){case"Red":t["🔴 Red"].push(r);break;case"Pink":t["🩷 Pink"].push(r);break;case"Orange":t["🟠 Orange"].push(r);break;case"Yellow":t["🟡 Yellow"].push(r);break;case"Green":t["🟢 Green"].push(r);break;case"Blue":t["🔵 Blue"].push(r);break;case"Purple":t["🟣 Purple"].push(r);break;case"Brown":t["🟤 Brown"].push(r);break;case"Black":t["⚫ Black"].push(r);break;case"White":t["⚪ White"].push(r);break;case"Gray":t["🔘 Gray"].push(r);break;case"Teal":t["🩵 Teal"].push(r);break;case"Cyan":t["🩵 Cyan"].push(r);break}}),Object.keys(t).forEach(r=>{t[r].length===0&&delete t[r]}),t}},uy=()=>{const[e,t]=s.useState("HEX"),[r,n]=s.useState("background");return{selectedColorFormat:e,setSelectedColorFormat:c=>{t(c)},getColorInFormat:c=>{switch(e){case"RGB":{const{r:i,g:l,b:d}=wt.hexToRGB(c);return`rgb(${i}, ${l}, ${d})`}case"HSL":{const{r:i,g:l,b:d}=wt.hexToRGB(c),{h,s:f,l:g}=wt.rgbToHSL(i,l,d);return`hsl(${h}, ${f}%, ${g}%)`}default:return c.toUpperCase()}},gradientType:r,setGradientType:n}},fy=()=>{const[e,t]=s.useState({tailwind:!1,css:!1,sass:!1,bootstrap:!1,xml:!1,svg:!1,json:!1,colors:!1}),r=(n,o)=>{navigator.clipboard.writeText(n),t(a=>({...a,[o]:!0})),yn({title:"Copied to clipboard ✅",description:`The ${o} code has been copied to your clipboard.`})};return s.useEffect(()=>{const n=setTimeout(()=>{t({tailwind:!1,css:!1,sass:!1,bootstrap:!1,xml:!1,svg:!1,json:!1,colors:!1})},2e3);return()=>clearTimeout(n)},[e]),{copiedStates:e,copyToClipboard:r}};function He(e,t,{checkForDefaultPrevented:r=!0}={}){return function(o){if(e==null||e(o),r===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function ja(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function nl(...e){return t=>{let r=!1;const n=e.map(o=>{const a=ja(o,t);return!r&&typeof a=="function"&&(r=!0),a});if(r)return()=>{for(let o=0;o<n.length;o++){const a=n[o];typeof a=="function"?a():ja(e[o],null)}}}}function st(...e){return s.useCallback(nl(...e),e)}function hy(e,t){const r=s.createContext(t),n=a=>{const{children:c,...i}=a,l=s.useMemo(()=>i,Object.values(i));return u.jsx(r.Provider,{value:l,children:c})};n.displayName=e+"Provider";function o(a){const c=s.useContext(r);if(c)return c;if(t!==void 0)return t;throw new Error(`\`${a}\` must be used within \`${e}\``)}return[n,o]}function my(e,t=[]){let r=[];function n(a,c){const i=s.createContext(c),l=r.length;r=[...r,c];const d=f=>{var v;const{scope:g,children:b,...p}=f,m=((v=g==null?void 0:g[e])==null?void 0:v[l])||i,y=s.useMemo(()=>p,Object.values(p));return u.jsx(m.Provider,{value:y,children:b})};d.displayName=a+"Provider";function h(f,g){var m;const b=((m=g==null?void 0:g[e])==null?void 0:m[l])||i,p=s.useContext(b);if(p)return p;if(c!==void 0)return c;throw new Error(`\`${f}\` must be used within \`${a}\``)}return[d,h]}const o=()=>{const a=r.map(c=>s.createContext(c));return function(i){const l=(i==null?void 0:i[e])||a;return s.useMemo(()=>({[`__scope${e}`]:{...i,[e]:l}}),[i,l])}};return o.scopeName=e,[n,gy(o,...t)]}function gy(...e){const t=e[0];if(e.length===1)return t;const r=()=>{const n=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(a){const c=n.reduce((i,{useScope:l,scopeName:d})=>{const f=l(a)[`__scope${d}`];return{...i,...f}},{});return s.useMemo(()=>({[`__scope${t.scopeName}`]:c}),[c])}};return r.scopeName=t.scopeName,r}var Sr=globalThis!=null&&globalThis.document?s.useLayoutEffect:()=>{},py=Bt.useId||(()=>{}),by=0;function un(e){const[t,r]=s.useState(py());return Sr(()=>{e||r(n=>n??String(by++))},[e]),e||(t?`radix-${t}`:"")}function tt(e){const t=s.useRef(e);return s.useEffect(()=>{t.current=e}),s.useMemo(()=>(...r)=>{var n;return(n=t.current)==null?void 0:n.call(t,...r)},[])}function yy({prop:e,defaultProp:t,onChange:r=()=>{}}){const[n,o]=vy({defaultProp:t,onChange:r}),a=e!==void 0,c=a?e:n,i=tt(r),l=s.useCallback(d=>{if(a){const f=typeof d=="function"?d(e):d;f!==e&&i(f)}else o(d)},[a,e,o,i]);return[c,l]}function vy({defaultProp:e,onChange:t}){const r=s.useState(e),[n]=r,o=s.useRef(n),a=tt(t);return s.useEffect(()=>{o.current!==n&&(a(n),o.current=n)},[n,o,a]),r}var Vo=s.forwardRef((e,t)=>{const{children:r,...n}=e,o=s.Children.toArray(r),a=o.find(xy);if(a){const c=a.props.children,i=o.map(l=>l===a?s.Children.count(c)>1?s.Children.only(null):s.isValidElement(c)?c.props.children:null:l);return u.jsx(Kn,{...n,ref:t,children:s.isValidElement(c)?s.cloneElement(c,void 0,i):null})}return u.jsx(Kn,{...n,ref:t,children:r})});Vo.displayName="Slot";var Kn=s.forwardRef((e,t)=>{const{children:r,...n}=e;if(s.isValidElement(r)){const o=Cy(r);return s.cloneElement(r,{...ky(n,r.props),ref:t?nl(t,o):o})}return s.Children.count(r)>1?s.Children.only(null):null});Kn.displayName="SlotClone";var wy=({children:e})=>u.jsx(u.Fragment,{children:e});function xy(e){return s.isValidElement(e)&&e.type===wy}function ky(e,t){const r={...t};for(const n in t){const o=e[n],a=t[n];/^on[A-Z]/.test(n)?o&&a?r[n]=(...i)=>{a(...i),o(...i)}:o&&(r[n]=o):n==="style"?r[n]={...o,...a}:n==="className"&&(r[n]=[o,a].filter(Boolean).join(" "))}return{...e,...r}}function Cy(e){var n,o;let t=(n=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:n.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,r=t&&"isReactWarning"in t&&t.isReactWarning,r?e.props.ref:e.props.ref||e.ref)}var Ey=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],Le=Ey.reduce((e,t)=>{const r=s.forwardRef((n,o)=>{const{asChild:a,...c}=n,i=a?Vo:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),u.jsx(i,{...c,ref:o})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function Sy(e,t){e&&Ae.flushSync(()=>e.dispatchEvent(t))}function $y(e,t=globalThis==null?void 0:globalThis.document){const r=tt(e);s.useEffect(()=>{const n=o=>{o.key==="Escape"&&r(o)};return t.addEventListener("keydown",n,{capture:!0}),()=>t.removeEventListener("keydown",n,{capture:!0})},[r,t])}var Ay="DismissableLayer",Yn="dismissableLayer.update",Py="dismissableLayer.pointerDownOutside",Ry="dismissableLayer.focusOutside",_a,ol=s.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),al=s.forwardRef((e,t)=>{const{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:n,onPointerDownOutside:o,onFocusOutside:a,onInteractOutside:c,onDismiss:i,...l}=e,d=s.useContext(ol),[h,f]=s.useState(null),g=(h==null?void 0:h.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,b]=s.useState({}),p=st(t,C=>f(C)),m=Array.from(d.layers),[y]=[...d.layersWithOutsidePointerEventsDisabled].slice(-1),v=m.indexOf(y),w=h?m.indexOf(h):-1,x=d.layersWithOutsidePointerEventsDisabled.size>0,k=w>=v,E=Dy(C=>{const $=C.target,N=[...d.branches].some(D=>D.contains($));!k||N||(o==null||o(C),c==null||c(C),C.defaultPrevented||i==null||i())},g),S=Ty(C=>{const $=C.target;[...d.branches].some(D=>D.contains($))||(a==null||a(C),c==null||c(C),C.defaultPrevented||i==null||i())},g);return $y(C=>{w===d.layers.size-1&&(n==null||n(C),!C.defaultPrevented&&i&&(C.preventDefault(),i()))},g),s.useEffect(()=>{if(h)return r&&(d.layersWithOutsidePointerEventsDisabled.size===0&&(_a=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),d.layersWithOutsidePointerEventsDisabled.add(h)),d.layers.add(h),Ia(),()=>{r&&d.layersWithOutsidePointerEventsDisabled.size===1&&(g.body.style.pointerEvents=_a)}},[h,g,r,d]),s.useEffect(()=>()=>{h&&(d.layers.delete(h),d.layersWithOutsidePointerEventsDisabled.delete(h),Ia())},[h,d]),s.useEffect(()=>{const C=()=>b({});return document.addEventListener(Yn,C),()=>document.removeEventListener(Yn,C)},[]),u.jsx(Le.div,{...l,ref:p,style:{pointerEvents:x?k?"auto":"none":void 0,...e.style},onFocusCapture:He(e.onFocusCapture,S.onFocusCapture),onBlurCapture:He(e.onBlurCapture,S.onBlurCapture),onPointerDownCapture:He(e.onPointerDownCapture,E.onPointerDownCapture)})});al.displayName=Ay;var Ny="DismissableLayerBranch",Fy=s.forwardRef((e,t)=>{const r=s.useContext(ol),n=s.useRef(null),o=st(t,n);return s.useEffect(()=>{const a=n.current;if(a)return r.branches.add(a),()=>{r.branches.delete(a)}},[r.branches]),u.jsx(Le.div,{...e,ref:o})});Fy.displayName=Ny;function Dy(e,t=globalThis==null?void 0:globalThis.document){const r=tt(e),n=s.useRef(!1),o=s.useRef(()=>{});return s.useEffect(()=>{const a=i=>{if(i.target&&!n.current){let l=function(){sl(Py,r,d,{discrete:!0})};const d={originalEvent:i};i.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=l,t.addEventListener("click",o.current,{once:!0})):l()}else t.removeEventListener("click",o.current);n.current=!1},c=window.setTimeout(()=>{t.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(c),t.removeEventListener("pointerdown",a),t.removeEventListener("click",o.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}function Ty(e,t=globalThis==null?void 0:globalThis.document){const r=tt(e),n=s.useRef(!1);return s.useEffect(()=>{const o=a=>{a.target&&!n.current&&sl(Ry,r,{originalEvent:a},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}function Ia(){const e=new CustomEvent(Yn);document.dispatchEvent(e)}function sl(e,t,r,{discrete:n}){const o=r.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),n?Sy(o,a):o.dispatchEvent(a)}var fn="focusScope.autoFocusOnMount",hn="focusScope.autoFocusOnUnmount",La={bubbles:!1,cancelable:!0},My="FocusScope",il=s.forwardRef((e,t)=>{const{loop:r=!1,trapped:n=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...c}=e,[i,l]=s.useState(null),d=tt(o),h=tt(a),f=s.useRef(null),g=st(t,m=>l(m)),b=s.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;s.useEffect(()=>{if(n){let m=function(x){if(b.paused||!i)return;const k=x.target;i.contains(k)?f.current=k:Ve(f.current,{select:!0})},y=function(x){if(b.paused||!i)return;const k=x.relatedTarget;k!==null&&(i.contains(k)||Ve(f.current,{select:!0}))},v=function(x){if(document.activeElement===document.body)for(const E of x)E.removedNodes.length>0&&Ve(i)};document.addEventListener("focusin",m),document.addEventListener("focusout",y);const w=new MutationObserver(v);return i&&w.observe(i,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",m),document.removeEventListener("focusout",y),w.disconnect()}}},[n,i,b.paused]),s.useEffect(()=>{if(i){Wa.add(b);const m=document.activeElement;if(!i.contains(m)){const v=new CustomEvent(fn,La);i.addEventListener(fn,d),i.dispatchEvent(v),v.defaultPrevented||(Oy(By(cl(i)),{select:!0}),document.activeElement===m&&Ve(i))}return()=>{i.removeEventListener(fn,d),setTimeout(()=>{const v=new CustomEvent(hn,La);i.addEventListener(hn,h),i.dispatchEvent(v),v.defaultPrevented||Ve(m??document.body,{select:!0}),i.removeEventListener(hn,h),Wa.remove(b)},0)}}},[i,d,h,b]);const p=s.useCallback(m=>{if(!r&&!n||b.paused)return;const y=m.key==="Tab"&&!m.altKey&&!m.ctrlKey&&!m.metaKey,v=document.activeElement;if(y&&v){const w=m.currentTarget,[x,k]=jy(w);x&&k?!m.shiftKey&&v===k?(m.preventDefault(),r&&Ve(x,{select:!0})):m.shiftKey&&v===x&&(m.preventDefault(),r&&Ve(k,{select:!0})):v===w&&m.preventDefault()}},[r,n,b.paused]);return u.jsx(Le.div,{tabIndex:-1,...c,ref:g,onKeyDown:p})});il.displayName=My;function Oy(e,{select:t=!1}={}){const r=document.activeElement;for(const n of e)if(Ve(n,{select:t}),document.activeElement!==r)return}function jy(e){const t=cl(e),r=Ba(t,e),n=Ba(t.reverse(),e);return[r,n]}function cl(e){const t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:n=>{const o=n.tagName==="INPUT"&&n.type==="hidden";return n.disabled||n.hidden||o?NodeFilter.FILTER_SKIP:n.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function Ba(e,t){for(const r of e)if(!_y(r,{upTo:t}))return r}function _y(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Iy(e){return e instanceof HTMLInputElement&&"select"in e}function Ve(e,{select:t=!1}={}){if(e&&e.focus){const r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&Iy(e)&&t&&e.select()}}var Wa=Ly();function Ly(){let e=[];return{add(t){const r=e[0];t!==r&&(r==null||r.pause()),e=Va(e,t),e.unshift(t)},remove(t){var r;e=Va(e,t),(r=e[0])==null||r.resume()}}}function Va(e,t){const r=[...e],n=r.indexOf(t);return n!==-1&&r.splice(n,1),r}function By(e){return e.filter(t=>t.tagName!=="A")}var Wy="Portal",ll=s.forwardRef((e,t)=>{var i;const{container:r,...n}=e,[o,a]=s.useState(!1);Sr(()=>a(!0),[]);const c=r||o&&((i=globalThis==null?void 0:globalThis.document)==null?void 0:i.body);return c?oo.createPortal(u.jsx(Le.div,{...n,ref:t}),c):null});ll.displayName=Wy;function Vy(e,t){return s.useReducer((r,n)=>t[r][n]??r,e)}var Xr=e=>{const{present:t,children:r}=e,n=zy(t),o=typeof r=="function"?r({present:n.isPresent}):s.Children.only(r),a=st(n.ref,Hy(o));return typeof r=="function"||n.isPresent?s.cloneElement(o,{ref:a}):null};Xr.displayName="Presence";function zy(e){const[t,r]=s.useState(),n=s.useRef({}),o=s.useRef(e),a=s.useRef("none"),c=e?"mounted":"unmounted",[i,l]=Vy(c,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return s.useEffect(()=>{const d=sr(n.current);a.current=i==="mounted"?d:"none"},[i]),Sr(()=>{const d=n.current,h=o.current;if(h!==e){const g=a.current,b=sr(d);e?l("MOUNT"):b==="none"||(d==null?void 0:d.display)==="none"?l("UNMOUNT"):l(h&&g!==b?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),Sr(()=>{if(t){let d;const h=t.ownerDocument.defaultView??window,f=b=>{const m=sr(n.current).includes(b.animationName);if(b.target===t&&m&&(l("ANIMATION_END"),!o.current)){const y=t.style.animationFillMode;t.style.animationFillMode="forwards",d=h.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=y)})}},g=b=>{b.target===t&&(a.current=sr(n.current))};return t.addEventListener("animationstart",g),t.addEventListener("animationcancel",f),t.addEventListener("animationend",f),()=>{h.clearTimeout(d),t.removeEventListener("animationstart",g),t.removeEventListener("animationcancel",f),t.removeEventListener("animationend",f)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(i),ref:s.useCallback(d=>{d&&(n.current=getComputedStyle(d)),r(d)},[])}}function sr(e){return(e==null?void 0:e.animationName)||"none"}function Hy(e){var n,o;let t=(n=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:n.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,r=t&&"isReactWarning"in t&&t.isReactWarning,r?e.props.ref:e.props.ref||e.ref)}var mn=0;function Uy(){s.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??za()),document.body.insertAdjacentElement("beforeend",e[1]??za()),mn++,()=>{mn===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),mn--}},[])}function za(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var dl=Po(),gn=function(){},qr=s.forwardRef(function(e,t){var r=s.useRef(null),n=s.useState({onScrollCapture:gn,onWheelCapture:gn,onTouchMoveCapture:gn}),o=n[0],a=n[1],c=e.forwardProps,i=e.children,l=e.className,d=e.removeScrollBar,h=e.enabled,f=e.shards,g=e.sideCar,b=e.noIsolation,p=e.inert,m=e.allowPinchZoom,y=e.as,v=y===void 0?"div":y,w=e.gapMode,x=Tr(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),k=g,E=Ao([r,t]),S=Z(Z({},x),o);return s.createElement(s.Fragment,null,h&&s.createElement(k,{sideCar:dl,removeScrollBar:d,shards:f,noIsolation:b,inert:p,setCallbacks:a,allowPinchZoom:!!m,lockRef:r,gapMode:w}),c?s.cloneElement(s.Children.only(i),Z(Z({},S),{ref:E})):s.createElement(v,Z({},S,{className:l,ref:E}),i))});qr.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};qr.classNames={fullWidth:bt,zeroRight:pt};var Xn=!1;if(typeof window<"u")try{var ir=Object.defineProperty({},"passive",{get:function(){return Xn=!0,!0}});window.addEventListener("test",ir,ir),window.removeEventListener("test",ir,ir)}catch{Xn=!1}var ft=Xn?{passive:!1}:!1,Gy=function(e){return e.tagName==="TEXTAREA"},ul=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return r[t]!=="hidden"&&!(r.overflowY===r.overflowX&&!Gy(e)&&r[t]==="visible")},Ky=function(e){return ul(e,"overflowY")},Yy=function(e){return ul(e,"overflowX")},Ha=function(e,t){var r=t.ownerDocument,n=t;do{typeof ShadowRoot<"u"&&n instanceof ShadowRoot&&(n=n.host);var o=fl(e,n);if(o){var a=hl(e,n),c=a[1],i=a[2];if(c>i)return!0}n=n.parentNode}while(n&&n!==r.body);return!1},Xy=function(e){var t=e.scrollTop,r=e.scrollHeight,n=e.clientHeight;return[t,r,n]},qy=function(e){var t=e.scrollLeft,r=e.scrollWidth,n=e.clientWidth;return[t,r,n]},fl=function(e,t){return e==="v"?Ky(t):Yy(t)},hl=function(e,t){return e==="v"?Xy(t):qy(t)},Zy=function(e,t){return e==="h"&&t==="rtl"?-1:1},Jy=function(e,t,r,n,o){var a=Zy(e,window.getComputedStyle(t).direction),c=a*n,i=r.target,l=t.contains(i),d=!1,h=c>0,f=0,g=0;do{var b=hl(e,i),p=b[0],m=b[1],y=b[2],v=m-y-a*p;(p||v)&&fl(e,i)&&(f+=v,g+=p),i instanceof ShadowRoot?i=i.host:i=i.parentNode}while(!l&&i!==document.body||l&&(t.contains(i)||t===i));return(h&&(o&&Math.abs(f)<1||!o&&c>f)||!h&&(o&&Math.abs(g)<1||!o&&-c>g))&&(d=!0),d},cr=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Ua=function(e){return[e.deltaX,e.deltaY]},Ga=function(e){return e&&"current"in e?e.current:e},Qy=function(e,t){return e[0]===t[0]&&e[1]===t[1]},ev=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},tv=0,ht=[];function rv(e){var t=s.useRef([]),r=s.useRef([0,0]),n=s.useRef(),o=s.useState(tv++)[0],a=s.useState(Or)[0],c=s.useRef(e);s.useEffect(function(){c.current=e},[e]),s.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var m=$o([e.lockRef.current],(e.shards||[]).map(Ga),!0).filter(Boolean);return m.forEach(function(y){return y.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),m.forEach(function(y){return y.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var i=s.useCallback(function(m,y){if("touches"in m&&m.touches.length===2||m.type==="wheel"&&m.ctrlKey)return!c.current.allowPinchZoom;var v=cr(m),w=r.current,x="deltaX"in m?m.deltaX:w[0]-v[0],k="deltaY"in m?m.deltaY:w[1]-v[1],E,S=m.target,C=Math.abs(x)>Math.abs(k)?"h":"v";if("touches"in m&&C==="h"&&S.type==="range")return!1;var $=Ha(C,S);if(!$)return!0;if($?E=C:(E=C==="v"?"h":"v",$=Ha(C,S)),!$)return!1;if(!n.current&&"changedTouches"in m&&(x||k)&&(n.current=E),!E)return!0;var N=n.current||E;return Jy(N,y,m,N==="h"?x:k,!0)},[]),l=s.useCallback(function(m){var y=m;if(!(!ht.length||ht[ht.length-1]!==a)){var v="deltaY"in y?Ua(y):cr(y),w=t.current.filter(function(E){return E.name===y.type&&(E.target===y.target||y.target===E.shadowParent)&&Qy(E.delta,v)})[0];if(w&&w.should){y.cancelable&&y.preventDefault();return}if(!w){var x=(c.current.shards||[]).map(Ga).filter(Boolean).filter(function(E){return E.contains(y.target)}),k=x.length>0?i(y,x[0]):!c.current.noIsolation;k&&y.cancelable&&y.preventDefault()}}},[]),d=s.useCallback(function(m,y,v,w){var x={name:m,delta:y,target:v,should:w,shadowParent:nv(v)};t.current.push(x),setTimeout(function(){t.current=t.current.filter(function(k){return k!==x})},1)},[]),h=s.useCallback(function(m){r.current=cr(m),n.current=void 0},[]),f=s.useCallback(function(m){d(m.type,Ua(m),m.target,i(m,e.lockRef.current))},[]),g=s.useCallback(function(m){d(m.type,cr(m),m.target,i(m,e.lockRef.current))},[]);s.useEffect(function(){return ht.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:g}),document.addEventListener("wheel",l,ft),document.addEventListener("touchmove",l,ft),document.addEventListener("touchstart",h,ft),function(){ht=ht.filter(function(m){return m!==a}),document.removeEventListener("wheel",l,ft),document.removeEventListener("touchmove",l,ft),document.removeEventListener("touchstart",h,ft)}},[]);var b=e.removeScrollBar,p=e.inert;return s.createElement(s.Fragment,null,p?s.createElement(a,{styles:ev(o)}):null,b?s.createElement(No,{gapMode:e.gapMode}):null)}function nv(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const ov=Ro(dl,rv);var ml=s.forwardRef(function(e,t){return s.createElement(qr,Z({},e,{ref:t,sideCar:ov}))});ml.classNames=qr.classNames;const av=ml;var zo="Dialog",[gl,dw]=my(zo),[sv,ke]=gl(zo),pl=e=>{const{__scopeDialog:t,children:r,open:n,defaultOpen:o,onOpenChange:a,modal:c=!0}=e,i=s.useRef(null),l=s.useRef(null),[d=!1,h]=yy({prop:n,defaultProp:o,onChange:a});return u.jsx(sv,{scope:t,triggerRef:i,contentRef:l,contentId:un(),titleId:un(),descriptionId:un(),open:d,onOpenChange:h,onOpenToggle:s.useCallback(()=>h(f=>!f),[h]),modal:c,children:r})};pl.displayName=zo;var bl="DialogTrigger",iv=s.forwardRef((e,t)=>{const{__scopeDialog:r,...n}=e,o=ke(bl,r),a=st(t,o.triggerRef);return u.jsx(Le.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":Go(o.open),...n,ref:a,onClick:He(e.onClick,o.onOpenToggle)})});iv.displayName=bl;var Ho="DialogPortal",[cv,yl]=gl(Ho,{forceMount:void 0}),vl=e=>{const{__scopeDialog:t,forceMount:r,children:n,container:o}=e,a=ke(Ho,t);return u.jsx(cv,{scope:t,forceMount:r,children:s.Children.map(n,c=>u.jsx(Xr,{present:r||a.open,children:u.jsx(ll,{asChild:!0,container:o,children:c})}))})};vl.displayName=Ho;var $r="DialogOverlay",wl=s.forwardRef((e,t)=>{const r=yl($r,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=ke($r,e.__scopeDialog);return a.modal?u.jsx(Xr,{present:n||a.open,children:u.jsx(lv,{...o,ref:t})}):null});wl.displayName=$r;var lv=s.forwardRef((e,t)=>{const{__scopeDialog:r,...n}=e,o=ke($r,r);return u.jsx(av,{as:Vo,allowPinchZoom:!0,shards:[o.contentRef],children:u.jsx(Le.div,{"data-state":Go(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),rt="DialogContent",xl=s.forwardRef((e,t)=>{const r=yl(rt,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=ke(rt,e.__scopeDialog);return u.jsx(Xr,{present:n||a.open,children:a.modal?u.jsx(dv,{...o,ref:t}):u.jsx(uv,{...o,ref:t})})});xl.displayName=rt;var dv=s.forwardRef((e,t)=>{const r=ke(rt,e.__scopeDialog),n=s.useRef(null),o=st(t,r.contentRef,n);return s.useEffect(()=>{const a=n.current;if(a)return So(a)},[]),u.jsx(kl,{...e,ref:o,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:He(e.onCloseAutoFocus,a=>{var c;a.preventDefault(),(c=r.triggerRef.current)==null||c.focus()}),onPointerDownOutside:He(e.onPointerDownOutside,a=>{const c=a.detail.originalEvent,i=c.button===0&&c.ctrlKey===!0;(c.button===2||i)&&a.preventDefault()}),onFocusOutside:He(e.onFocusOutside,a=>a.preventDefault())})}),uv=s.forwardRef((e,t)=>{const r=ke(rt,e.__scopeDialog),n=s.useRef(!1),o=s.useRef(!1);return u.jsx(kl,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{var c,i;(c=e.onCloseAutoFocus)==null||c.call(e,a),a.defaultPrevented||(n.current||(i=r.triggerRef.current)==null||i.focus(),a.preventDefault()),n.current=!1,o.current=!1},onInteractOutside:a=>{var l,d;(l=e.onInteractOutside)==null||l.call(e,a),a.defaultPrevented||(n.current=!0,a.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const c=a.target;((d=r.triggerRef.current)==null?void 0:d.contains(c))&&a.preventDefault(),a.detail.originalEvent.type==="focusin"&&o.current&&a.preventDefault()}})}),kl=s.forwardRef((e,t)=>{const{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:o,onCloseAutoFocus:a,...c}=e,i=ke(rt,r),l=s.useRef(null),d=st(t,l);return Uy(),u.jsxs(u.Fragment,{children:[u.jsx(il,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:o,onUnmountAutoFocus:a,children:u.jsx(al,{role:"dialog",id:i.contentId,"aria-describedby":i.descriptionId,"aria-labelledby":i.titleId,"data-state":Go(i.open),...c,ref:d,onDismiss:()=>i.onOpenChange(!1)})}),u.jsxs(u.Fragment,{children:[u.jsx(fv,{titleId:i.titleId}),u.jsx(mv,{contentRef:l,descriptionId:i.descriptionId})]})]})}),Uo="DialogTitle",Cl=s.forwardRef((e,t)=>{const{__scopeDialog:r,...n}=e,o=ke(Uo,r);return u.jsx(Le.h2,{id:o.titleId,...n,ref:t})});Cl.displayName=Uo;var El="DialogDescription",Sl=s.forwardRef((e,t)=>{const{__scopeDialog:r,...n}=e,o=ke(El,r);return u.jsx(Le.p,{id:o.descriptionId,...n,ref:t})});Sl.displayName=El;var $l="DialogClose",Al=s.forwardRef((e,t)=>{const{__scopeDialog:r,...n}=e,o=ke($l,r);return u.jsx(Le.button,{type:"button",...n,ref:t,onClick:He(e.onClick,()=>o.onOpenChange(!1))})});Al.displayName=$l;function Go(e){return e?"open":"closed"}var Pl="DialogTitleWarning",[uw,Rl]=hy(Pl,{contentName:rt,titleName:Uo,docsSlug:"dialog"}),fv=({titleId:e})=>{const t=Rl(Pl),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return s.useEffect(()=>{e&&(document.getElementById(e)||console.error(r))},[r,e]),null},hv="DialogDescriptionWarning",mv=({contentRef:e,descriptionId:t})=>{const n=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Rl(hv).contentName}}.`;return s.useEffect(()=>{var a;const o=(a=e.current)==null?void 0:a.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(n))},[n,e,t]),null},gv=pl,pv=vl,Nl=wl,Fl=xl,Dl=Cl,Tl=Sl,bv=Al;const yv=gv,vv=pv,Ml=s.forwardRef(({className:e,...t},r)=>u.jsx(Nl,{ref:r,className:L("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));Ml.displayName=Nl.displayName;const Ol=s.forwardRef(({className:e,children:t,...r},n)=>u.jsxs(vv,{children:[u.jsx(Ml,{}),u.jsxs(Fl,{ref:n,className:L("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-[var(--background)] border-[var(--border)] p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...r,children:[t,u.jsxs(bv,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[u.jsx(Pd,{className:"h-4 w-4"}),u.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));Ol.displayName=Fl.displayName;const jl=({className:e,...t})=>u.jsx("div",{className:L("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});jl.displayName="DialogHeader";const wv=s.forwardRef(({className:e,...t},r)=>u.jsx(Dl,{ref:r,className:L("text-lg font-semibold leading-none tracking-tight",e),...t}));wv.displayName=Dl.displayName;const xv=s.forwardRef(({className:e,...t},r)=>u.jsx(Tl,{ref:r,className:L("text-sm text-muted-foreground",e),...t}));xv.displayName=Tl.displayName;function kv(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function Cv(...e){return t=>e.forEach(r=>kv(r,t))}var _l=s.forwardRef((e,t)=>{const{children:r,...n}=e,o=s.Children.toArray(r),a=o.find(Sv);if(a){const c=a.props.children,i=o.map(l=>l===a?s.Children.count(c)>1?s.Children.only(null):s.isValidElement(c)?c.props.children:null:l);return u.jsx(qn,{...n,ref:t,children:s.isValidElement(c)?s.cloneElement(c,void 0,i):null})}return u.jsx(qn,{...n,ref:t,children:r})});_l.displayName="Slot";var qn=s.forwardRef((e,t)=>{const{children:r,...n}=e;if(s.isValidElement(r)){const o=Av(r);return s.cloneElement(r,{...$v(n,r.props),ref:t?Cv(t,o):o})}return s.Children.count(r)>1?s.Children.only(null):null});qn.displayName="SlotClone";var Ev=({children:e})=>u.jsx(u.Fragment,{children:e});function Sv(e){return s.isValidElement(e)&&e.type===Ev}function $v(e,t){const r={...t};for(const n in t){const o=e[n],a=t[n];/^on[A-Z]/.test(n)?o&&a?r[n]=(...i)=>{a(...i),o(...i)}:o&&(r[n]=o):n==="style"?r[n]={...o,...a}:n==="className"&&(r[n]=[o,a].filter(Boolean).join(" "))}return{...e,...r}}function Av(e){var n,o;let t=(n=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:n.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,r=t&&"isReactWarning"in t&&t.isReactWarning,r?e.props.ref:e.props.ref||e.ref)}var Pv=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],Rv=Pv.reduce((e,t)=>{const r=s.forwardRef((n,o)=>{const{asChild:a,...c}=n,i=a?_l:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),u.jsx(i,{...c,ref:o})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),Nv="Label",Il=s.forwardRef((e,t)=>u.jsx(Rv.label,{...e,ref:t,onMouseDown:r=>{var o;r.target.closest("button, input, select, textarea")||((o=e.onMouseDown)==null||o.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));Il.displayName=Nv;var Ll=Il;const Fv=Rd("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),qe=s.forwardRef(({className:e,...t},r)=>u.jsx(Ll,{ref:r,className:L(Fv(),e),...t}));qe.displayName=Ll.displayName;const q={generateSVG:(e,t)=>{const{colors:r,angle:n,name:o}=e,{width:a,height:c}=t,i=n*Math.PI/180,l=Math.round(50+50*Math.cos(i+Math.PI/2)),d=Math.round(50+50*Math.sin(i+Math.PI/2)),h=Math.round(50+50*Math.cos(i-Math.PI/2)),f=Math.round(50+50*Math.sin(i-Math.PI/2)),g=r.map((b,p)=>`<stop offset="${r.length===1?0:p/(r.length-1)*100}%" stop-color="${b}" />`).join(`
    `);return`<?xml version="1.0" encoding="UTF-8"?>
<svg width="${a}" height="${c}" viewBox="0 0 ${a} ${c}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="${l}%" y1="${d}%" x2="${h}%" y2="${f}%">
      ${g}
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#gradient)" />
  <text x="50%" y="95%" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="rgba(255,255,255,0.8)">${o}</text>
</svg>`},convertSVGToFormat:async(e,t)=>new Promise((r,n)=>{const o=document.createElement("canvas"),a=o.getContext("2d");if(!a){n(new Error("Canvas context not available"));return}o.width=t.width,o.height=t.height;const c=new Image,i=new Blob([e],{type:"image/svg+xml;charset=utf-8"}),l=URL.createObjectURL(i);c.onload=()=>{a.drawImage(c,0,0,t.width,t.height);let d="image/png",h;switch(t.format){case"png":d="image/png";break;case"jpg":d="image/jpeg",h=t.quality||.9;break;case"webp":d="image/webp",h=t.quality||.9;break}const f=o.toDataURL(d,h);URL.revokeObjectURL(l),r(f)},c.onerror=()=>{URL.revokeObjectURL(l),n(new Error("Failed to load SVG"))},c.src=l}),downloadFile:(e,t,r)=>{const n=document.createElement("a");n.download=`${t}.${r}`,n.href=e,document.body.appendChild(n),n.click(),document.body.removeChild(n)},exportGradient:async(e,t)=>{try{const r=e.name.toLowerCase().replace(/\s+/g,"-");switch(t.format){case"svg":const n=q.generateSVG(e,t);await q.copyToClipboard(n);break;case"png":case"jpg":case"webp":const o=q.generateSVG(e,t),a=await q.convertSVGToFormat(o,t);q.downloadFile(a,r,t.format);break;case"css":const c=q.generateCSS(e,t);await q.copyToClipboard(c);break;case"json":const i=q.generateJSON(e,t);await q.copyToClipboard(i);break;case"android":const l=q.generateAndroidXML(e,t);await q.copyToClipboard(l);break;case"ios":const d=q.generateiOSSwift(e,t);await q.copyToClipboard(d);break;case"less":const h=q.generateLess(e,t);await q.copyToClipboard(h);break;case"scss":const f=q.generateSCSS(e,t);await q.copyToClipboard(f);break;case"figma":const g=q.generateFigma(e,t);await q.copyToClipboard(g);break;case"sketch":const b=q.generateSketch(e,t);await q.copyToClipboard(b);break;case"mesh":const p=q.generateMeshGradient(e,t),m=new Blob([p],{type:"image/svg+xml;charset=utf-8"}),y=URL.createObjectURL(m);q.downloadFile(y,`${r}-mesh`,"svg"),URL.revokeObjectURL(y);break;default:throw new Error(`Unsupported format: ${t.format}`)}}catch(r){throw console.error("Export failed:",r),r}},getSizePresets:()=>[{name:"Small",width:400,height:300},{name:"Medium",width:800,height:600},{name:"Large",width:1200,height:900},{name:"HD",width:1920,height:1080},{name:"Square Small",width:400,height:400},{name:"Square Medium",width:800,height:800},{name:"Square Large",width:1200,height:1200}],getFormatOptions:()=>[{value:"png",label:"PNG",description:"High quality, transparent background support"},{value:"jpg",label:"JPG",description:"Smaller file size, good for photos"},{value:"webp",label:"WebP",description:"Modern format, excellent compression"},{value:"mesh",label:"Mesh Gradient",description:"SVG mesh gradient"}],getCopyableFormats:()=>[{value:"svg",label:"SVG",description:"Vector format, scalable"},{value:"json",label:"JSON",description:"Data format for design tools"},{value:"android",label:"Android XML",description:"Android gradient drawable"},{value:"css",label:"CSS",description:"CSS gradient code"},{value:"ios",label:"iOS Swift",description:"Swift CAGradientLayer code"},{value:"less",label:"Less",description:"Less preprocessor code"},{value:"scss",label:"SCSS",description:"SCSS preprocessor code"},{value:"figma",label:"Figma",description:"Figma plugin compatible"},{value:"sketch",label:"Sketch",description:"Sketch plugin compatible"}],generateCSS:(e,t)=>{const{colors:r,angle:n=45}=e,o=t.angle||n,a=r.join(", ");return`background: linear-gradient(${o}deg, ${a});`},generateJSON:(e,t)=>{const r={name:e.name,colors:e.colors,angle:t.angle||45,type:"linear-gradient",format:"css",metadata:{exportedAt:new Date().toISOString(),tool:"GradientsCSS",version:"1.0.0"}};return JSON.stringify(r,null,2)},generateAndroidXML:(e,t)=>{const{colors:r,name:n}=e,o=t.angle||45,a=r.map((c,i)=>`    <item android:offset="${r.length===1?0:i/(r.length-1)}" android:color="${c}" />`).join(`
`);return`<?xml version="1.0" encoding="utf-8"?>
<!-- ${n} gradient -->
<shape xmlns:android="http://schemas.android.com/apk/res/android">
  <gradient
    android:type="linear"
    android:angle="${o}"
    android:startColor="${r[0]}"
    android:endColor="${r[r.length-1]}">
${a}
  </gradient>
</shape>`},generateiOSSwift:(e,t)=>{const{colors:r,name:n}=e,a=(t.angle||45)*Math.PI/180,c=.5+.5*Math.cos(a+Math.PI),i=.5+.5*Math.sin(a+Math.PI),l=.5+.5*Math.cos(a),d=.5+.5*Math.sin(a),h=r.map(g=>`UIColor(hex: "${g}").cgColor`).join(", "),f=r.map((g,b)=>r.length===1?"0.0":(b/(r.length-1)).toFixed(2)).join(", ");return`// ${n} gradient
let ${n.toLowerCase().replace(/\s+/g,"")}Gradient = CAGradientLayer()
${n.toLowerCase().replace(/\s+/g,"")}Gradient.colors = [${h}]
${n.toLowerCase().replace(/\s+/g,"")}Gradient.locations = [${f}]
${n.toLowerCase().replace(/\s+/g,"")}Gradient.startPoint = CGPoint(x: ${c.toFixed(2)}, y: ${i.toFixed(2)})
${n.toLowerCase().replace(/\s+/g,"")}Gradient.endPoint = CGPoint(x: ${l.toFixed(2)}, y: ${d.toFixed(2)})
${n.toLowerCase().replace(/\s+/g,"")}Gradient.frame = view.bounds
view.layer.insertSublayer(${n.toLowerCase().replace(/\s+/g,"")}Gradient, at: 0)`},generateLess:(e,t)=>{const{colors:r,name:n}=e,o=t.angle||45,a=r.join(", "),c=n.toLowerCase().replace(/\s+/g,"-");return`.${c}-gradient() {
  background: linear-gradient(${o}deg, ${a});
}

// Usage: .${c}-gradient();`},generateSCSS:(e,t)=>{const{colors:r,name:n}=e,o=t.angle||45,a=r.join(", "),c=n.toLowerCase().replace(/\s+/g,"-");return`@mixin ${c}-gradient($angle: ${o}deg) {
  background: linear-gradient($angle, ${a});
}

// Usage: @include ${c}-gradient();`},generateFigma:(e,t)=>{const{colors:r,name:n}=e,o=t.angle||45,a=r.map((i,l)=>({color:i,position:r.length===1?0:l/(r.length-1)})),c={name:n,type:"GRADIENT_LINEAR",gradientStops:a,gradientTransform:[[Math.cos(o*Math.PI/180),Math.sin(o*Math.PI/180),0],[-Math.sin(o*Math.PI/180),Math.cos(o*Math.PI/180),0]]};return JSON.stringify(c,null,2)},generateSketch:(e,t)=>{const{colors:r}=e,n={_class:"gradient",elipseLength:0,from:"{0.5, 0}",gradientType:0,to:"{0.5, 1}",stops:r.map((o,a)=>({_class:"gradientStop",color:{_class:"color",alpha:1,blue:parseInt(o.slice(5,7),16)/255,green:parseInt(o.slice(3,5),16)/255,red:parseInt(o.slice(1,3),16)/255},position:r.length===1?0:a/(r.length-1)}))};return JSON.stringify(n,null,2)},generateMeshGradient:(e,t)=>{const{colors:r,name:n}=e,{width:o,height:a,meshComplexity:c=5}=t,i=[];for(let h=0;h<c;h++){const f=(Math.random()*.8+.1)*100,g=(Math.random()*.8+.1)*100,b=r[Math.floor(Math.random()*r.length)],p=Math.random()*30+20;i.push({id:`mesh-${h}`,x:f,y:g,color:b,radius:p})}const l=i.map(h=>`
    <radialGradient id="${h.id}" cx="${h.x}%" cy="${h.y}%" r="${h.radius}%">
      <stop offset="0%" stop-color="${h.color}" stop-opacity="0.8"/>
      <stop offset="100%" stop-color="${h.color}" stop-opacity="0"/>
    </radialGradient>`).join(""),d=i.map(h=>`
    <circle cx="${h.x}%" cy="${h.y}%" r="${h.radius}%" fill="url(#${h.id})"/>`).join("");return`<?xml version="1.0" encoding="UTF-8"?>
<svg width="${o}" height="${a}" viewBox="0 0 ${o} ${a}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    ${l}
  </defs>
  <rect width="100%" height="100%" fill="${r[0]}"/>
  ${d}
  <text x="50%" y="95%" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="rgba(255,255,255,0.8)">${n}</text>
</svg>`},copyToClipboard:async e=>{try{await navigator.clipboard.writeText(e)}catch{const r=document.createElement("textarea");r.value=e,document.body.appendChild(r),r.select(),document.execCommand("copy"),document.body.removeChild(r)}}};function Dv({isOpen:e,onClose:t,gradientData:r}){const[n,o]=s.useState("png"),[a,c]=s.useState("Medium"),[i,l]=s.useState(800),[d,h]=s.useState(600),[f,g]=s.useState([90]),[b]=s.useState([45]),[p,m]=s.useState([5]),[y,v]=s.useState(!1),w=q.getSizePresets(),x=q.getFormatOptions(),k=()=>a==="Custom"?{width:i,height:d}:w.find($=>$.name===a)||{width:800,height:600},E=async()=>{v(!0);try{const C=k(),$={format:n,width:C.width,height:C.height,quality:n==="jpg"||n==="webp"?f[0]/100:void 0,angle:b[0],meshComplexity:p[0]};await q.exportGradient(r,$),yn({title:"Export Successful",description:`${r.name} exported as ${n.toUpperCase()}`}),t()}catch{yn({title:"Export Failed",description:"There was an error exporting your gradient. Please try again.",variant:"destructive"})}finally{v(!1)}},S=C=>{switch(C){case"svg":return u.jsx(Qo,{className:"h-4 w-4"});case"png":case"jpg":case"webp":return u.jsx(Jo,{className:"h-4 w-4"});case"json":return u.jsx(Vd,{className:"h-4 w-4"});case"css":case"less":case"scss":return u.jsx(Bd,{className:"h-4 w-4"});case"android":return u.jsx(Xd,{className:"h-4 w-4"});case"ios":return u.jsx(Gd,{className:"h-4 w-4"});case"figma":case"sketch":return u.jsx(Ud,{className:"h-4 w-4"});case"mesh":return u.jsx(Qo,{className:"h-4 w-4"});default:return u.jsx(Jo,{className:"h-4 w-4"})}};return u.jsx(yv,{open:e,onOpenChange:t,children:u.jsxs(Ol,{className:"sm:max-w-md",children:[u.jsx(jl,{}),u.jsxs("div",{className:"space-y-6",children:[u.jsxs("div",{className:"space-y-2",children:[u.jsx(qe,{children:"Format"}),u.jsxs(Rn,{value:n,onValueChange:C=>o(C),children:[u.jsx(vr,{children:u.jsx(Nn,{})}),u.jsx(wr,{children:x.map(C=>u.jsx(vt,{value:C.value,children:u.jsxs("div",{className:"flex items-center gap-1  ",children:[S(C.value),u.jsxs("div",{className:"flex items-center gap-3",children:[u.jsx("div",{className:"font-medium",children:C.label}),u.jsx("div",{className:"text-xs text-muted-foreground",children:C.description})]})]})},C.value))})]})]}),u.jsx("div",{className:"space-y-3",children:u.jsxs("div",{className:"",children:[u.jsx("div",{className:"relative overflow-hidden rounded-lg border-4 border-[var(--border)] shadow-lg bg-[var(--card)] transition-transform duration-300 ",children:u.jsxs("div",{className:"relative",children:[u.jsx("div",{className:`w-full ${a==="Large"||a==="4K"?"h-24":a==="Small"?"h-32":a==="Custom"?i>d?"h-24":"h-32":"h-28"}`,style:{background:`linear-gradient(${r.angle||45}deg, ${r.colors.join(", ")})`}}),u.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:u.jsx("div",{className:"rounded-lg bg-black/30 px-4 py-2 text-sm font-semibold text-white backdrop-blur-sm border border-white/20",children:r.name})}),u.jsx("div",{className:"absolute top-3 right-3",children:u.jsx("div",{className:"rounded-full bg-white/95 px-3 py-1 text-xs font-bold text-gray-800 shadow-sm border border-gray-200 flex items-center gap-1",children:n.toUpperCase()})}),n!=="mesh"&&u.jsx("div",{className:"absolute top-3 left-3",children:u.jsxs("div",{className:"rounded-full bg-green-500/90 px-2 py-1 text-xs font-medium text-white shadow-sm",children:[f[0],"% Quality"]})}),u.jsx("div",{className:"absolute bottom-3 left-3",children:u.jsx("div",{className:"rounded-lg bg-black/30 px-3 py-1 text-xs font-medium text-white backdrop-blur-sm border border-white/20",children:a==="Custom"?`${i}×${d}px`:a==="Small"?"400×400px":a==="Medium"?"800×600px":a==="Large"?"1920×1080px":a==="4K"?"3840×2160px":a})}),n==="mesh"&&u.jsxs(u.Fragment,{children:[u.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-transparent via-white/10 to-transparent pointer-events-none"}),u.jsx("div",{className:"absolute bottom-3 right-3",children:u.jsx("div",{className:"rounded-full bg-purple-500/90 px-2 py-1 text-xs font-medium text-white shadow-sm",children:"Mesh"})})]})]})}),u.jsx("div",{className:"absolute inset-0 rounded-xl bg-gradient-to-br from-transparent to-black/5 pointer-events-none"})]})}),!["css","ios","less","scss","figma","sketch","json"].includes(n)&&u.jsxs("div",{className:"space-y-2",children:[u.jsx(qe,{children:"Size"}),u.jsxs(Rn,{value:a,onValueChange:c,children:[u.jsx(vr,{children:u.jsx(Nn,{})}),u.jsxs(wr,{children:[w.map(C=>u.jsxs(vt,{value:C.name,children:[C.name," (",C.width," × ",C.height,")"]},C.name)),u.jsx(vt,{value:"Custom",children:"Custom Size"})]})]})]}),a==="Custom"&&!["css","ios","less","scss","figma","sketch","json"].includes(n)&&u.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[u.jsxs("div",{className:"space-y-2",children:[u.jsx(qe,{children:"Width"}),u.jsx("input",{type:"number",value:i,onChange:C=>l(Number(C.target.value)),className:"w-full rounded-md border border-border bg-background px-3 py-2 text-sm",min:"100",max:"4000"})]}),u.jsxs("div",{className:"space-y-2",children:[u.jsx(qe,{children:"Height"}),u.jsx("input",{type:"number",value:d,onChange:C=>h(Number(C.target.value)),className:"w-full rounded-md border border-border bg-background px-3 py-2 text-sm",min:"100",max:"4000"})]})]}),(n==="jpg"||n==="webp")&&u.jsxs("div",{className:"space-y-2",children:[u.jsxs(qe,{children:["Quality: ",f[0],"%"]}),u.jsx(Cr,{value:f,onValueChange:g,max:100,min:10,step:5,className:"w-full"})]}),n==="mesh"&&u.jsxs("div",{className:"space-y-2",children:[u.jsxs(qe,{children:["Mesh Complexity: ",p[0]]}),u.jsx(Cr,{value:p,onValueChange:m,max:10,min:1,step:1,className:"w-full"})]}),u.jsx("div",{className:"rounded-lg bg-muted p-3 text-sm",children:u.jsxs("div",{className:"text-muted-foreground",children:["Format: ",n.toUpperCase(),!["css","ios","less","scss","figma","sketch"].includes(n)&&` • Size: ${k().width} × ${k().height}px`,(n==="jpg"||n==="webp")&&` • Quality: ${f[0]}%`,!["json","figma","sketch"].includes(n)&&` • Angle: ${b[0]}°`,n==="mesh"&&` • Complexity: ${p[0]}`]})})]}),u.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[u.jsx(Ze,{variant:"outline",onClick:t,children:"Cancel"}),u.jsx(Ze,{onClick:E,disabled:y,children:y?u.jsxs(u.Fragment,{children:[u.jsx("div",{className:"mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"}),"Exporting..."]}):u.jsxs(u.Fragment,{children:[u.jsx(ns,{className:"mr-2 h-4 w-4"}),"Export"]})})]})]})})}const Ne={generateAnimatedCSS:e=>{const{colors:t,angle:r,isAnimated:n,animationSpeed:o,gradientType:a="background"}=e,c=`linear-gradient(${r}deg, ${t.join(", ")})`;if(!n)return a==="text"?`color: transparent;
background-image: ${c};
-webkit-background-clip: text;
background-clip: text;`:`background: ${c};`;const i=`${4/o}s`,l=`gradientAnimation${Math.random().toString(36).substr(2,9)}`;return a==="text"?`
color: transparent;
background-image: ${c};
background-size: 400% 400%;
-webkit-background-clip: text;
background-clip: text;
animation: ${l} ${i} ease infinite;

@keyframes ${l} {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 100% 100%; }
  75% { background-position: 0% 100%; }
  100% { background-position: 0% 50%; }
}`:`
background: ${c};
background-size: 400% 400%;
animation: ${l} ${i} ease infinite;

@keyframes ${l} {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 100% 100%; }
  75% { background-position: 0% 100%; }
  100% { background-position: 0% 50%; }
}`},generateAnimatedTailwind:e=>{const{colors:t,angle:r,isAnimated:n,animationSpeed:o,gradientType:a="background"}=e,i=(g=>{const b=(g%360+360)%360;return b>=315||b<45?"to-r":b>=45&&b<135?"to-br":b>=135&&b<225?"to-b":b>=225&&b<315?"to-bl":"to-r"})(r),l=t.length>2;if(!n)return a==="text"?l?`text-transparent bg-clip-text bg-gradient-${i} from-[${t[0]}] via-[${t[1]}] to-[${t[t.length-1]}]`:`text-transparent bg-clip-text bg-gradient-${i} from-[${t[0]}] to-[${t[t.length-1]}]`:l?`bg-gradient-${i} from-[${t[0]}] via-[${t[1]}] to-[${t[t.length-1]}]`:`bg-gradient-${i} from-[${t[0]}] to-[${t[t.length-1]}]`;const h=(g=>g<=.5?"animate-gradient-slow":g<=1?"animate-gradient-normal":g<=2?"animate-gradient-fast":"animate-gradient-very-fast")(o);return`${a==="text"?"text-transparent bg-clip-text":""} bg-gradient-${i} from-[${t[0]}] ${l?`via-[${t[1]}] `:""}to-[${t[t.length-1]}] bg-[length:400%_400%] ${h}`},generateAnimatedSass:e=>{const{colors:t,angle:r,isAnimated:n,animationSpeed:o,gradientType:a="background"}=e,c=`linear-gradient(${r}deg, ${t.join(", ")})`;if(!n)return a==="text"?`// Text gradient using SASS/SCSS
$gradient-colors: (${t.map(l=>`"${l}"`).join(", ")});
$gradient-angle: ${r}deg;

color: transparent;
background-image: ${c};
-webkit-background-clip: text;
background-clip: text;`:`// Background gradient using SASS/SCSS
$gradient-colors: (${t.map(l=>`"${l}"`).join(", ")});
$gradient-angle: ${r}deg;

background: ${c};`;const i=`${4/o}s`;return a==="text"?`// Animated text gradient using SASS/SCSS
$gradient-colors: (${t.map(l=>`"${l}"`).join(", ")});
$animation-duration: ${i};
$gradient-angle: ${r}deg;

color: transparent;
background-image: ${c};
background-size: 400% 400%;
-webkit-background-clip: text;
background-clip: text;
animation: gradientShift $animation-duration ease infinite;

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 100% 100%; }
  75% { background-position: 0% 100%; }
  100% { background-position: 0% 50%; }
}`:`// Animated background gradient using SASS/SCSS
$gradient-colors: (${t.map(l=>`"${l}"`).join(", ")});
$animation-duration: ${i};
$gradient-angle: ${r}deg;

background: ${c};
background-size: 400% 400%;
animation: gradientShift $animation-duration ease infinite;

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 100% 100%; }
  75% { background-position: 0% 100%; }
  100% { background-position: 0% 50%; }
}`},generateAnimatedBootstrap:e=>{const{colors:t,angle:r,isAnimated:n,animationSpeed:o,gradientType:a="background"}=e,c=`linear-gradient(${r}deg, ${t.join(", ")})`;if(!n)return a==="text"?`/* Bootstrap Text Gradient */
.text-gradient {
  color: transparent;
  background-image: ${c};
  -webkit-background-clip: text;
  background-clip: text;
}`:`/* Bootstrap Background Gradient */
.bg-gradient {
  background: ${c};
}`;const i=`${4/o}s`;return a==="text"?`/* Bootstrap Animated Text Gradient */
.animated-text-gradient {
  color: transparent;
  background-image: ${c};
  background-size: 400% 400%;
  -webkit-background-clip: text;
  background-clip: text;
  animation: gradient-animation ${i} ease infinite;
}

@keyframes gradient-animation {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 100% 100%; }
  75% { background-position: 0% 100%; }
  100% { background-position: 0% 50%; }
}`:`/* Bootstrap Animated Background Gradient */
.animated-bg-gradient {
  background: ${c};
  background-size: 400% 400%;
  animation: gradient-animation ${i} ease infinite;
}

@keyframes gradient-animation {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 100% 100%; }
  75% { background-position: 0% 100%; }
  100% { background-position: 0% 50%; }
}`},generateAnimatedSVG:e=>{const{colors:t,isAnimated:r,animationSpeed:n}=e,o=t.map((c,i)=>`<stop offset="${i/(t.length-1)*100}%" stop-color="${c}"/>`).join(`
    `);if(!r)return`
<svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      ${o}
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#gradient)"/>
</svg>
      `;const a=`${4/n}s`;return`
<svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="animatedGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      ${o}
      <animateTransform
        attributeName="gradientTransform"
        type="rotate"
        values="0 200 200;360 200 200"
        dur="${a}"
        repeatCount="indefinite"/>
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#animatedGradient)"/>
</svg>
    `},generateAnimatedJSON:e=>{const{colors:t,angle:r,isAnimated:n,animationSpeed:o,gradientType:a="background"}=e;return JSON.stringify({type:"linear-gradient",gradientType:a,angle:r,colors:t,animated:n,animationSpeed:o,animationDuration:n?`${4/o}s`:null,css:Ne.generateAnimatedCSS(e),tailwind:Ne.generateAnimatedTailwind(e)},null,2)},generateAnimatedXML:e=>{const{colors:t,angle:r,isAnimated:n}=e,o=(r+90)%360;return n?`
<!-- Animated gradient for Android (requires custom implementation) -->
<shape xmlns:android="http://schemas.android.com/apk/res/android">
    <gradient
        android:type="linear"
        android:angle="${o}"
        android:startColor="${t[0]}"
        android:centerColor="${t[1]||t[0]}"
        android:endColor="${t[t.length-1]}" />
</shape>

<!-- Note: For animation, use ValueAnimator in your Java/Kotlin code -->
    `:`
<shape xmlns:android="http://schemas.android.com/apk/res/android">
    <gradient
        android:type="linear"
        android:angle="${o}"
        android:startColor="${t[0]}"
        android:endColor="${t[t.length-1]}" />
</shape>
      `}};function Tv({gradient:e,isFavorite:t,onFavoriteToggle:r}){const[n,o]=s.useState(90),[a,c]=s.useState(!1),[i,l]=s.useState(!1),[d,h]=s.useState(1),{selectedColorFormat:f,setSelectedColorFormat:g,getColorInFormat:b,gradientType:p,setGradientType:m}=uy(),{copiedStates:y,copyToClipboard:v}=fy(),w=["HEX","RGB","HSL"],x={name:e.name,colors:e.colors,angle:n,type:p},k=()=>{c(!0)},E=()=>{const N=e.colors.map(T=>b(T)),D=`linear-gradient(${n}deg, ${N.join(", ")})`;return p==="background"?i?{backgroundImage:D,backgroundSize:"400% 400%",animation:`gradientShift ${4/d}s ease infinite`}:{backgroundImage:D}:i?{color:"transparent",backgroundImage:D,backgroundSize:"400% 400%",WebkitBackgroundClip:"text",backgroundClip:"text",animation:`gradientShift ${4/d}s ease infinite`}:{color:"transparent",backgroundImage:D,WebkitBackgroundClip:"text",backgroundClip:"text"}},S=N=>{const D=e.colors.map(P=>b(P)),T={colors:D,angle:n,isAnimated:i,animationSpeed:d,gradientType:p};switch(N){case"tailwind":return Ne.generateAnimatedTailwind(T);case"css":return Ne.generateAnimatedCSS(T);case"sass":return Ne.generateAnimatedSass(T);case"bootstrap":return Ne.generateAnimatedBootstrap(T);case"xml":return Ne.generateAnimatedXML(T);case"svg":return Ne.generateAnimatedSVG(T);case"json":return Ne.generateAnimatedJSON(T);default:const P=`linear-gradient(${n}deg, ${D.join(", ")})`;return p==="background"?`background-image: ${P};`:`color: transparent;
background-image: ${P};
-webkit-background-clip: text;
background-clip: text;`}},C=()=>{r(e.name)},{theme:$}=Mm();return u.jsxs(Om,{gradientColor:$==="dark"?"#262626":"#282828",className:"overflow-hidden transition-all duration-300",children:[u.jsx(ts,{mode:"wait",children:u.jsx(te.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3},children:u.jsx(jm,{style:E(),gradientType:p,gradient:e,isAnimated:i,animationSpeed:d})},p)}),u.jsxs("footer",{className:"flex flex-col items-start space-y-4 p-4 w-full max-w-full",children:[u.jsx("div",{className:"flex w-full items-center justify-between",children:u.jsx(_m,{name:e.name,isFavorite:t,onFavoriteToggle:C,onExport:k})}),u.jsx(dy,{gradient:e,getColorInFormat:b,copyToClipboard:v,selectedColorFormat:f,setSelectedColorFormat:g,colorFormats:w,gradientType:p,setGradientType:m,angle:n,setAngle:o,getCode:S,copiedStates:y,isAnimated:i,setIsAnimated:l,animationSpeed:d,setAnimationSpeed:h})]}),u.jsx(Dv,{isOpen:a,onClose:()=>c(!1),gradientData:x})]})}function Ee({className:e,...t}){return u.jsx("div",{className:L("animate-pulse rounded-md bg-muted",e),...t})}const Bl=s.forwardRef(({className:e,...t},r)=>u.jsx("div",{ref:r,className:L("rounded-lg bg-[var(--card-background)]",e),...t}));Bl.displayName="Card";const Mv=s.forwardRef(({className:e,...t},r)=>u.jsx("div",{ref:r,className:L("flex flex-row items-center justify-between space-y-1.5 p-6",e),...t}));Mv.displayName="CardHeader";const Ov=s.forwardRef(({className:e,...t},r)=>u.jsx("h3",{ref:r,className:L("text-base font-semibold leading-none tracking-tight text-[var(--headline)]",e),...t}));Ov.displayName="CardTitle";const jv=s.forwardRef(({className:e,...t},r)=>u.jsx("p",{ref:r,className:L("text-sm text-[var(--paragraph)]",e),...t}));jv.displayName="CardDescription";const Wl=s.forwardRef(({className:e,...t},r)=>u.jsx("div",{ref:r,className:L("p-6 pt-0",e),...t}));Wl.displayName="CardContent";const Vl=s.forwardRef(({className:e,...t},r)=>u.jsx("div",{ref:r,className:L("flex items-start gap-2 p-6 pt-0",e),...t}));Vl.displayName="CardFooter";function De(e,t,{checkForDefaultPrevented:r=!0}={}){return function(o){if(e==null||e(o),r===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Ko(e,t=[]){let r=[];function n(a,c){const i=s.createContext(c),l=r.length;r=[...r,c];function d(f){const{scope:g,children:b,...p}=f,m=(g==null?void 0:g[e][l])||i,y=s.useMemo(()=>p,Object.values(p));return u.jsx(m.Provider,{value:y,children:b})}function h(f,g){const b=(g==null?void 0:g[e][l])||i,p=s.useContext(b);if(p)return p;if(c!==void 0)return c;throw new Error(`\`${f}\` must be used within \`${a}\``)}return d.displayName=a+"Provider",[d,h]}const o=()=>{const a=r.map(c=>s.createContext(c));return function(i){const l=(i==null?void 0:i[e])||a;return s.useMemo(()=>({[`__scope${e}`]:{...i,[e]:l}}),[i,l])}};return o.scopeName=e,[n,_v(o,...t)]}function _v(...e){const t=e[0];if(e.length===1)return t;const r=()=>{const n=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(a){const c=n.reduce((i,{useScope:l,scopeName:d})=>{const f=l(a)[`__scope${d}`];return{...i,...f}},{});return s.useMemo(()=>({[`__scope${t.scopeName}`]:c}),[c])}};return r.scopeName=t.scopeName,r}function Iv(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function zl(...e){return t=>e.forEach(r=>Iv(r,t))}function Ar(...e){return s.useCallback(zl(...e),e)}var Pr=s.forwardRef((e,t)=>{const{children:r,...n}=e,o=s.Children.toArray(r),a=o.find(Bv);if(a){const c=a.props.children,i=o.map(l=>l===a?s.Children.count(c)>1?s.Children.only(null):s.isValidElement(c)?c.props.children:null:l);return u.jsx(Zn,{...n,ref:t,children:s.isValidElement(c)?s.cloneElement(c,void 0,i):null})}return u.jsx(Zn,{...n,ref:t,children:r})});Pr.displayName="Slot";var Zn=s.forwardRef((e,t)=>{const{children:r,...n}=e;if(s.isValidElement(r)){const o=Vv(r);return s.cloneElement(r,{...Wv(n,r.props),ref:t?zl(t,o):o})}return s.Children.count(r)>1?s.Children.only(null):null});Zn.displayName="SlotClone";var Lv=({children:e})=>u.jsx(u.Fragment,{children:e});function Bv(e){return s.isValidElement(e)&&e.type===Lv}function Wv(e,t){const r={...t};for(const n in t){const o=e[n],a=t[n];/^on[A-Z]/.test(n)?o&&a?r[n]=(...i)=>{a(...i),o(...i)}:o&&(r[n]=o):n==="style"?r[n]={...o,...a}:n==="className"&&(r[n]=[o,a].filter(Boolean).join(" "))}return{...e,...r}}function Vv(e){var n,o;let t=(n=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:n.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,r=t&&"isReactWarning"in t&&t.isReactWarning,r?e.props.ref:e.props.ref||e.ref)}function zv(e){const t=e+"CollectionProvider",[r,n]=Ko(t),[o,a]=r(t,{collectionRef:{current:null},itemMap:new Map}),c=b=>{const{scope:p,children:m}=b,y=W.useRef(null),v=W.useRef(new Map).current;return u.jsx(o,{scope:p,itemMap:v,collectionRef:y,children:m})};c.displayName=t;const i=e+"CollectionSlot",l=W.forwardRef((b,p)=>{const{scope:m,children:y}=b,v=a(i,m),w=Ar(p,v.collectionRef);return u.jsx(Pr,{ref:w,children:y})});l.displayName=i;const d=e+"CollectionItemSlot",h="data-radix-collection-item",f=W.forwardRef((b,p)=>{const{scope:m,children:y,...v}=b,w=W.useRef(null),x=Ar(p,w),k=a(d,m);return W.useEffect(()=>(k.itemMap.set(w,{ref:w,...v}),()=>void k.itemMap.delete(w))),u.jsx(Pr,{[h]:"",ref:x,children:y})});f.displayName=d;function g(b){const p=a(e+"CollectionConsumer",b);return W.useCallback(()=>{const y=p.collectionRef.current;if(!y)return[];const v=Array.from(y.querySelectorAll(`[${h}]`));return Array.from(p.itemMap.values()).sort((k,E)=>v.indexOf(k.ref.current)-v.indexOf(E.ref.current))},[p.collectionRef,p.itemMap])}return[{Provider:c,Slot:l,ItemSlot:f},g,n]}var Jn=globalThis!=null&&globalThis.document?s.useLayoutEffect:()=>{},Hv=Bt.useId||(()=>{}),Uv=0;function Hl(e){const[t,r]=s.useState(Hv());return Jn(()=>{e||r(n=>n??String(Uv++))},[e]),e||(t?`radix-${t}`:"")}var Gv=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],Ft=Gv.reduce((e,t)=>{const r=s.forwardRef((n,o)=>{const{asChild:a,...c}=n,i=a?Pr:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),u.jsx(i,{...c,ref:o})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function Yo(e){const t=s.useRef(e);return s.useEffect(()=>{t.current=e}),s.useMemo(()=>(...r)=>{var n;return(n=t.current)==null?void 0:n.call(t,...r)},[])}function Ul({prop:e,defaultProp:t,onChange:r=()=>{}}){const[n,o]=Kv({defaultProp:t,onChange:r}),a=e!==void 0,c=a?e:n,i=Yo(r),l=s.useCallback(d=>{if(a){const f=typeof d=="function"?d(e):d;f!==e&&i(f)}else o(d)},[a,e,o,i]);return[c,l]}function Kv({defaultProp:e,onChange:t}){const r=s.useState(e),[n]=r,o=s.useRef(n),a=Yo(t);return s.useEffect(()=>{o.current!==n&&(a(n),o.current=n)},[n,o,a]),r}var Yv=s.createContext(void 0);function Gl(e){const t=s.useContext(Yv);return e||t||"ltr"}var pn="rovingFocusGroup.onEntryFocus",Xv={bubbles:!1,cancelable:!0},Zr="RovingFocusGroup",[Qn,Kl,qv]=zv(Zr),[Zv,Yl]=Ko(Zr,[qv]),[Jv,Qv]=Zv(Zr),Xl=s.forwardRef((e,t)=>u.jsx(Qn.Provider,{scope:e.__scopeRovingFocusGroup,children:u.jsx(Qn.Slot,{scope:e.__scopeRovingFocusGroup,children:u.jsx(e0,{...e,ref:t})})}));Xl.displayName=Zr;var e0=s.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:r,orientation:n,loop:o=!1,dir:a,currentTabStopId:c,defaultCurrentTabStopId:i,onCurrentTabStopIdChange:l,onEntryFocus:d,preventScrollOnEntryFocus:h=!1,...f}=e,g=s.useRef(null),b=Ar(t,g),p=Gl(a),[m=null,y]=Ul({prop:c,defaultProp:i,onChange:l}),[v,w]=s.useState(!1),x=Yo(d),k=Kl(r),E=s.useRef(!1),[S,C]=s.useState(0);return s.useEffect(()=>{const $=g.current;if($)return $.addEventListener(pn,x),()=>$.removeEventListener(pn,x)},[x]),u.jsx(Jv,{scope:r,orientation:n,dir:p,loop:o,currentTabStopId:m,onItemFocus:s.useCallback($=>y($),[y]),onItemShiftTab:s.useCallback(()=>w(!0),[]),onFocusableItemAdd:s.useCallback(()=>C($=>$+1),[]),onFocusableItemRemove:s.useCallback(()=>C($=>$-1),[]),children:u.jsx(Ft.div,{tabIndex:v||S===0?-1:0,"data-orientation":n,...f,ref:b,style:{outline:"none",...e.style},onMouseDown:De(e.onMouseDown,()=>{E.current=!0}),onFocus:De(e.onFocus,$=>{const N=!E.current;if($.target===$.currentTarget&&N&&!v){const D=new CustomEvent(pn,Xv);if($.currentTarget.dispatchEvent(D),!D.defaultPrevented){const T=k().filter(F=>F.focusable),P=T.find(F=>F.active),j=T.find(F=>F.id===m),M=[P,j,...T].filter(Boolean).map(F=>F.ref.current);Jl(M,h)}}E.current=!1}),onBlur:De(e.onBlur,()=>w(!1))})})}),ql="RovingFocusGroupItem",Zl=s.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:r,focusable:n=!0,active:o=!1,tabStopId:a,...c}=e,i=Hl(),l=a||i,d=Qv(ql,r),h=d.currentTabStopId===l,f=Kl(r),{onFocusableItemAdd:g,onFocusableItemRemove:b}=d;return s.useEffect(()=>{if(n)return g(),()=>b()},[n,g,b]),u.jsx(Qn.ItemSlot,{scope:r,id:l,focusable:n,active:o,children:u.jsx(Ft.span,{tabIndex:h?0:-1,"data-orientation":d.orientation,...c,ref:t,onMouseDown:De(e.onMouseDown,p=>{n?d.onItemFocus(l):p.preventDefault()}),onFocus:De(e.onFocus,()=>d.onItemFocus(l)),onKeyDown:De(e.onKeyDown,p=>{if(p.key==="Tab"&&p.shiftKey){d.onItemShiftTab();return}if(p.target!==p.currentTarget)return;const m=n0(p,d.orientation,d.dir);if(m!==void 0){if(p.metaKey||p.ctrlKey||p.altKey||p.shiftKey)return;p.preventDefault();let v=f().filter(w=>w.focusable).map(w=>w.ref.current);if(m==="last")v.reverse();else if(m==="prev"||m==="next"){m==="prev"&&v.reverse();const w=v.indexOf(p.currentTarget);v=d.loop?o0(v,w+1):v.slice(w+1)}setTimeout(()=>Jl(v))}})})})});Zl.displayName=ql;var t0={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function r0(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function n0(e,t,r){const n=r0(e.key,r);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(n))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(n)))return t0[n]}function Jl(e,t=!1){const r=document.activeElement;for(const n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}function o0(e,t){return e.map((r,n)=>e[(t+n)%e.length])}var a0=Xl,s0=Zl;function i0(e,t){return s.useReducer((r,n)=>t[r][n]??r,e)}var Ql=e=>{const{present:t,children:r}=e,n=c0(t),o=typeof r=="function"?r({present:n.isPresent}):s.Children.only(r),a=Ar(n.ref,l0(o));return typeof r=="function"||n.isPresent?s.cloneElement(o,{ref:a}):null};Ql.displayName="Presence";function c0(e){const[t,r]=s.useState(),n=s.useRef({}),o=s.useRef(e),a=s.useRef("none"),c=e?"mounted":"unmounted",[i,l]=i0(c,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return s.useEffect(()=>{const d=lr(n.current);a.current=i==="mounted"?d:"none"},[i]),Jn(()=>{const d=n.current,h=o.current;if(h!==e){const g=a.current,b=lr(d);e?l("MOUNT"):b==="none"||(d==null?void 0:d.display)==="none"?l("UNMOUNT"):l(h&&g!==b?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),Jn(()=>{if(t){const d=f=>{const b=lr(n.current).includes(f.animationName);f.target===t&&b&&Ae.flushSync(()=>l("ANIMATION_END"))},h=f=>{f.target===t&&(a.current=lr(n.current))};return t.addEventListener("animationstart",h),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{t.removeEventListener("animationstart",h),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(i),ref:s.useCallback(d=>{d&&(n.current=getComputedStyle(d)),r(d)},[])}}function lr(e){return(e==null?void 0:e.animationName)||"none"}function l0(e){var n,o;let t=(n=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:n.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,r=t&&"isReactWarning"in t&&t.isReactWarning,r?e.props.ref:e.props.ref||e.ref)}var Xo="Tabs",[d0,fw]=Ko(Xo,[Yl]),ed=Yl(),[u0,qo]=d0(Xo),td=s.forwardRef((e,t)=>{const{__scopeTabs:r,value:n,onValueChange:o,defaultValue:a,orientation:c="horizontal",dir:i,activationMode:l="automatic",...d}=e,h=Gl(i),[f,g]=Ul({prop:n,onChange:o,defaultProp:a});return u.jsx(u0,{scope:r,baseId:Hl(),value:f,onValueChange:g,orientation:c,dir:h,activationMode:l,children:u.jsx(Ft.div,{dir:h,"data-orientation":c,...d,ref:t})})});td.displayName=Xo;var rd="TabsList",nd=s.forwardRef((e,t)=>{const{__scopeTabs:r,loop:n=!0,...o}=e,a=qo(rd,r),c=ed(r);return u.jsx(a0,{asChild:!0,...c,orientation:a.orientation,dir:a.dir,loop:n,children:u.jsx(Ft.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:t})})});nd.displayName=rd;var od="TabsTrigger",ad=s.forwardRef((e,t)=>{const{__scopeTabs:r,value:n,disabled:o=!1,...a}=e,c=qo(od,r),i=ed(r),l=cd(c.baseId,n),d=ld(c.baseId,n),h=n===c.value;return u.jsx(s0,{asChild:!0,...i,focusable:!o,active:h,children:u.jsx(Ft.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":d,"data-state":h?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:l,...a,ref:t,onMouseDown:De(e.onMouseDown,f=>{!o&&f.button===0&&f.ctrlKey===!1?c.onValueChange(n):f.preventDefault()}),onKeyDown:De(e.onKeyDown,f=>{[" ","Enter"].includes(f.key)&&c.onValueChange(n)}),onFocus:De(e.onFocus,()=>{const f=c.activationMode!=="manual";!h&&!o&&f&&c.onValueChange(n)})})})});ad.displayName=od;var sd="TabsContent",id=s.forwardRef((e,t)=>{const{__scopeTabs:r,value:n,forceMount:o,children:a,...c}=e,i=qo(sd,r),l=cd(i.baseId,n),d=ld(i.baseId,n),h=n===i.value,f=s.useRef(h);return s.useEffect(()=>{const g=requestAnimationFrame(()=>f.current=!1);return()=>cancelAnimationFrame(g)},[]),u.jsx(Ql,{present:o||h,children:({present:g})=>u.jsx(Ft.div,{"data-state":h?"active":"inactive","data-orientation":i.orientation,role:"tabpanel","aria-labelledby":l,hidden:!g,id:d,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:f.current?"0s":void 0},children:g&&a})})});id.displayName=sd;function cd(e,t){return`${e}-trigger-${t}`}function ld(e,t){return`${e}-content-${t}`}var f0=td,dd=nd,ud=ad,fd=id;const h0=f0,hd=s.forwardRef(({className:e,...t},r)=>u.jsx(dd,{ref:r,className:L("inline-flex h-10 items-center justify-center rounded-md p-1 text-[var(--paragraph)]",e),...t}));hd.displayName=dd.displayName;const eo=s.forwardRef(({className:e,...t},r)=>u.jsx(ud,{ref:r,className:L("px- inline-flex items-center justify-center whitespace-nowrap rounded-sm p-2 py-1.5 text-sm font-medium text-[var(--paragraph)] ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-[var(--card-background)] data-[state=active]:text-[var(--headline)] data-[state=active]:shadow-sm",e),...t}));eo.displayName=ud.displayName;const to=s.forwardRef(({className:e,...t},r)=>u.jsx(fd,{ref:r,className:L("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));to.displayName=fd.displayName;function m0(){return u.jsxs(Bl,{className:"overflow-hidden shadow-md",children:[u.jsx(Wl,{className:"p-0",children:u.jsx(Ee,{className:"h-48 w-full"})}),u.jsxs(Vl,{className:"flex flex-col items-start space-y-4 p-4",children:[u.jsxs("div",{className:"flex w-full items-center justify-between",children:[u.jsx(Ee,{className:"h-6 w-32"}),u.jsx(Ee,{className:"h-8 w-8 rounded-full"})]}),u.jsx("div",{className:"flex w-full flex-wrap gap-2",children:[...Array(5)].map((e,t)=>u.jsx(Ee,{className:"h-6 w-6 rounded-full"},t))}),u.jsx(Ee,{className:"h-8 w-full"}),u.jsxs(h0,{defaultValue:"tailwind",className:"w-full",children:[u.jsxs(hd,{className:"grid w-full grid-cols-2",children:[u.jsx(eo,{value:"tailwind",children:u.jsx(Ee,{className:"h-4 w-16"})}),u.jsx(eo,{value:"css",children:u.jsx(Ee,{className:"h-4 w-8"})})]}),u.jsx(to,{value:"tailwind",className:"mt-2",children:u.jsx(Ee,{className:"h-8 w-full"})}),u.jsx(to,{value:"css",className:"mt-2",children:u.jsx(Ee,{className:"h-8 w-full"})})]})]})]})}function g0({gradients:e,favorites:t,toggleFavorite:r,isLoading:n}){return u.jsx("div",{className:"grid grid-cols-1 gap-6 pt-6 sm:grid-cols-2 lg:grid-cols-3",children:n?Array.from({length:6}).map((o,a)=>u.jsx(te.div,{layout:!0,initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},transition:{duration:.3},children:u.jsx(m0,{})},`skeleton-${a}`)):e.length===0?u.jsxs(te.div,{layout:!0,initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},transition:{duration:.3},className:"col-span-full flex items-center justify-center gap-1 pt-16 text-center text-muted-foreground",children:[u.jsx(zd,{}),u.jsx("span",{children:"No gradients found"})]},"no-gradients"):e.map(o=>u.jsx("div",{children:u.jsx(Tv,{gradient:o,isFavorite:t.includes(o.name),onFavoriteToggle:()=>r(o.name)})},o.name))})}function p0({currentPage:e,totalPages:t,onPageChange:r}){return u.jsxs(te.div,{className:"flex items-center justify-center gap-4 pt-16 max-md:py-4",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5,delay:.3},children:[u.jsx(Ze,{className:"h-max w-max rounded-md border-border bg-card p-2 text-primary",onClick:()=>r("prev"),disabled:e===1,children:u.jsx(_d,{className:"h-4 w-4 text-primary"})}),u.jsxs(te.div,{className:"text-sm font-semibold text-muted-foreground",initial:{opacity:0},animate:{opacity:1},transition:{duration:.3,delay:.1},children:[e," / ",t]}),u.jsx(Ze,{className:"h-max w-max rounded-md border-border bg-card p-2 text-primary",onClick:()=>r("next"),disabled:e===t,children:u.jsx(rs,{className:"h-4 w-4"})})]})}const b0="https://alshaer.vercel.app/";function y0(){return u.jsxs("footer",{className:`container mx-auto flex h-16 items-center justify-between gap-2 px-4 py-4
      max-md:flex-col max-md:h-auto max-md:gap-2 max-md:text-center max-md:items-center max-md:justify-center`,children:[u.jsxs("p",{className:"flex items-center text-sm text-primary",children:["Developed by"," ",u.jsxs("a",{target:"_blank",rel:"noopener noreferrer",className:"flex items-center ps-1 font-medium text-[var(--link)] hover:text-[var(--link-hover)]",href:b0,children:["Baraa",u.jsx(Wd,{className:"ms-1 h-3 w-3"})]})]}),u.jsxs("p",{className:"text-xs text-muted-foreground",children:["© ",new Date().getFullYear()," GradientsCSS. All rights reserved."]})]})}const v0=({children:e,className:t,shimmerWidth:r=100})=>u.jsx("p",{style:{"--shiny-width":`${r}px`},className:L("mx-auto max-w-md text-neutral-600/70 dark:text-neutral-400/70","animate-shiny-text bg-clip-text bg-no-repeat [background-position:0_0] [background-size:var(--shiny-width)_100%] [transition:background-position_1s_cubic-bezier(.6,.6,0,1)_infinite]","bg-gradient-to-r from-transparent via-black/80 via-50% to-transparent dark:via-white/80",t),children:e}),w0=()=>u.jsxs("div",{className:"space-y-6",children:[u.jsx("a",{href:"https://github.com/balshaer/gradients-css",target:"_blank",rel:"noopener noreferrer",children:u.jsx("div",{className:"flex w-full items-center justify-center",children:u.jsx("div",{className:L("group rounded-full border border-border bg-card text-base text-[var(--muted)] transition-all ease-in hover:cursor-pointer dark:border-white/5 dark:bg-neutral-900 dark:hover:bg-neutral-800"),children:u.jsxs(v0,{className:"inline-flex items-center justify-center px-4 py-1 text-primary transition ease-out hover:duration-300 max-md:text-xs",children:[u.jsx("span",{children:"✨ Contribute to The Project"}),u.jsx(jd,{className:"ml-1 size-3 transition-transform duration-300 ease-in-out group-hover:translate-x-0.5"})]})})})}),u.jsxs("h1",{className:"pt-6 text-center text-3xl font-medium text-primary dark:text-gray-50 sm:text-6xl",children:["Collection of modern,",u.jsxs("span",{className:"relative ps-1",children:[u.jsx("span",{className:"relative",children:"Gradients"}),u.jsx("img",{className:"absolute h-[150px] left-[190px] w-[120px] bottom-0 top-[45px] right-0 -rotate-45 max-[923px]:hidden",src:"/app/assets/arrow.svg",alt:"icon"})]})]}),u.jsx("p",{className:"mx-auto max-w-[600px] text-center text-muted-foreground",children:"Discover beautiful CSS gradients for your next project. Copy, export, and use them anywhere."})]}),md="http://www.w3.org/2000/svg";class x0{constructor(t){this.seed=t}next(){return this.seed?(2**31-1&(this.seed=Math.imul(48271,this.seed)))/2**31:Math.random()}}function we(e,t,r,n,o){return{type:"path",ops:Rr(e,t,r,n,o)}}function gd(e,t,r){const n=(e||[]).length;if(n>2){const o=[];for(let a=0;a<n-1;a++)o.push(...Rr(e[a][0],e[a][1],e[a+1][0],e[a+1][1],r));return t&&o.push(...Rr(e[n-1][0],e[n-1][1],e[0][0],e[0][1],r)),{type:"path",ops:o}}return n===2?we(e[0][0],e[0][1],e[1][0],e[1][1],r):{type:"path",ops:[]}}function k0(e,t,r,n,o){return function(a,c){return gd(a,!0,c)}([[e,t],[e+r,t],[e+r,t+n],[e,t+n]],o)}function Ka(e,t,r,n,o){return function(a,c,i,l){const[d,h]=qa(l.increment,a,c,l.rx,l.ry,1,l.increment*ro(.1,ro(.4,1,i),i),i);let f=Xa(d,null,i);if(!i.disableMultiStroke){const[g]=qa(l.increment,a,c,l.rx,l.ry,1.5,0,i),b=Xa(g,null,i);f=f.concat(b)}return{estimatedPoints:h,opset:{type:"path",ops:f}}}(e,t,o,function(a,c,i){const l=Math.sqrt(2*Math.PI*Math.sqrt((Math.pow(a/2,2)+Math.pow(c/2,2))/2)),d=Math.max(i.curveStepCount,i.curveStepCount/Math.sqrt(200)*l),h=2*Math.PI/d;let f=Math.abs(a/2),g=Math.abs(c/2);const b=1-i.curveFitting;return f+=ee(f*b,i),g+=ee(g*b,i),{increment:h,rx:f,ry:g}}(r,n,o)).opset}function pd(e){return e.randomizer||(e.randomizer=new x0(e.seed||0)),e.randomizer.next()}function ro(e,t,r,n=1){return r.roughness*n*(pd(r)*(t-e)+e)}function ee(e,t,r=1){return ro(-e,e,t,r)}function Rr(e,t,r,n,o,a=!1){const c=a?o.disableMultiStrokeFill:o.disableMultiStroke,i=Ya(e,t,r,n,o,!0,!1);if(c)return i;const l=Ya(e,t,r,n,o,!0,!0);return i.concat(l)}function Ya(e,t,r,n,o,a,c){const i=Math.pow(e-r,2)+Math.pow(t-n,2),l=Math.sqrt(i);let d=1;d=l<200?1:l>500?.4:-.0016668*l+1.233334;let h=o.maxRandomnessOffset||0;h*h*100>i&&(h=l/10);const f=h/2,g=.2+.2*pd(o);let b=o.bowing*o.maxRandomnessOffset*(n-t)/200,p=o.bowing*o.maxRandomnessOffset*(e-r)/200;b=ee(b,o,d),p=ee(p,o,d);const m=[],y=()=>ee(f,o,d),v=()=>ee(h,o,d);return a&&(c?m.push({op:"move",data:[e+y(),t+y()]}):m.push({op:"move",data:[e+ee(h,o,d),t+ee(h,o,d)]})),c?m.push({op:"bcurveTo",data:[b+e+(r-e)*g+y(),p+t+(n-t)*g+y(),b+e+2*(r-e)*g+y(),p+t+2*(n-t)*g+y(),r+y(),n+y()]}):m.push({op:"bcurveTo",data:[b+e+(r-e)*g+v(),p+t+(n-t)*g+v(),b+e+2*(r-e)*g+v(),p+t+2*(n-t)*g+v(),r+v(),n+v()]}),m}function Xa(e,t,r){const n=e.length,o=[];if(n>3){const a=[],c=1-r.curveTightness;o.push({op:"move",data:[e[1][0],e[1][1]]});for(let i=1;i+2<n;i++){const l=e[i];a[0]=[l[0],l[1]],a[1]=[l[0]+(c*e[i+1][0]-c*e[i-1][0])/6,l[1]+(c*e[i+1][1]-c*e[i-1][1])/6],a[2]=[e[i+1][0]+(c*e[i][0]-c*e[i+2][0])/6,e[i+1][1]+(c*e[i][1]-c*e[i+2][1])/6],a[3]=[e[i+1][0],e[i+1][1]],o.push({op:"bcurveTo",data:[a[1][0],a[1][1],a[2][0],a[2][1],a[3][0],a[3][1]]})}if(t&&t.length===2){const i=r.maxRandomnessOffset;o.push({op:"lineTo",data:[t[0]+ee(i,r),t[1]+ee(i,r)]})}}else n===3?(o.push({op:"move",data:[e[1][0],e[1][1]]}),o.push({op:"bcurveTo",data:[e[1][0],e[1][1],e[2][0],e[2][1],e[2][0],e[2][1]]})):n===2&&o.push(...Rr(e[0][0],e[0][1],e[1][0],e[1][1],r));return o}function qa(e,t,r,n,o,a,c,i){const l=[],d=[],h=ee(.5,i)-Math.PI/2;d.push([ee(a,i)+t+.9*n*Math.cos(h-e),ee(a,i)+r+.9*o*Math.sin(h-e)]);for(let f=h;f<2*Math.PI+h-.01;f+=e){const g=[ee(a,i)+t+n*Math.cos(f),ee(a,i)+r+o*Math.sin(f)];l.push(g),d.push(g)}return d.push([ee(a,i)+t+n*Math.cos(h+2*Math.PI+.5*c),ee(a,i)+r+o*Math.sin(h+2*Math.PI+.5*c)]),d.push([ee(a,i)+t+.98*n*Math.cos(h+c),ee(a,i)+r+.98*o*Math.sin(h+c)]),d.push([ee(a,i)+t+.9*n*Math.cos(h+.5*c),ee(a,i)+r+.9*o*Math.sin(h+.5*c)]),[d,l]}function bn(e,t){return{maxRandomnessOffset:2,roughness:e==="highlight"?3:1.5,bowing:1,stroke:"#000",strokeWidth:1.5,curveTightness:0,curveFitting:.95,curveStepCount:9,fillStyle:"hachure",fillWeight:-1,hachureAngle:-41,hachureGap:-1,dashOffset:-1,dashGap:-1,zigzagOffset:-1,combineNestedSvgPaths:!1,disableMultiStroke:e!=="double",disableMultiStrokeFill:!1,seed:t}}function C0(e,t,r,n,o,a){const c=[];let i=r.strokeWidth||2;const l=function(b){const p=b.padding;if(p||p===0){if(typeof p=="number")return[p,p,p,p];if(Array.isArray(p)){const m=p;if(m.length)switch(m.length){case 4:return[...m];case 1:return[m[0],m[0],m[0],m[0]];case 2:return[...m,...m];case 3:return[...m,m[1]];default:return[m[0],m[1],m[2],m[3]]}}}return[5,5,5,5]}(r),d=r.animate===void 0||!!r.animate,h=r.iterations||2,f=r.rtl?1:0,g=bn("single",a);switch(r.type){case"underline":{const b=t.y+t.h+l[2];for(let p=f;p<h+f;p++)p%2?c.push(we(t.x+t.w,b,t.x,b,g)):c.push(we(t.x,b,t.x+t.w,b,g));break}case"strike-through":{const b=t.y+t.h/2;for(let p=f;p<h+f;p++)p%2?c.push(we(t.x+t.w,b,t.x,b,g)):c.push(we(t.x,b,t.x+t.w,b,g));break}case"box":{const b=t.x-l[3],p=t.y-l[0],m=t.w+(l[1]+l[3]),y=t.h+(l[0]+l[2]);for(let v=0;v<h;v++)c.push(k0(b,p,m,y,g));break}case"bracket":{const b=Array.isArray(r.brackets)?r.brackets:r.brackets?[r.brackets]:["right"],p=t.x-2*l[3],m=t.x+t.w+2*l[1],y=t.y-2*l[0],v=t.y+t.h+2*l[2];for(const w of b){let x;switch(w){case"bottom":x=[[p,t.y+t.h],[p,v],[m,v],[m,t.y+t.h]];break;case"top":x=[[p,t.y],[p,y],[m,y],[m,t.y]];break;case"left":x=[[t.x,y],[p,y],[p,v],[t.x,v]];break;case"right":x=[[t.x+t.w,y],[m,y],[m,v],[t.x+t.w,v]]}x&&c.push(gd(x,!1,g))}break}case"crossed-off":{const b=t.x,p=t.y,m=b+t.w,y=p+t.h;for(let v=f;v<h+f;v++)v%2?c.push(we(m,y,b,p,g)):c.push(we(b,p,m,y,g));for(let v=f;v<h+f;v++)v%2?c.push(we(b,y,m,p,g)):c.push(we(m,p,b,y,g));break}case"circle":{const b=bn("double",a),p=t.w+(l[1]+l[3]),m=t.h+(l[0]+l[2]),y=t.x-l[3]+p/2,v=t.y-l[0]+m/2,w=Math.floor(h/2),x=h-2*w;for(let k=0;k<w;k++)c.push(Ka(y,v,p,m,b));for(let k=0;k<x;k++)c.push(Ka(y,v,p,m,g));break}case"highlight":{const b=bn("highlight",a);i=.95*t.h;const p=t.y+t.h/2;for(let m=f;m<h+f;m++)m%2?c.push(we(t.x+t.w,p,t.x,p,b)):c.push(we(t.x,p,t.x+t.w,p,b));break}}if(c.length){const b=function(w){const x=[];for(const k of w){let E="";for(const S of k.ops){const C=S.data;switch(S.op){case"move":E.trim()&&x.push(E.trim()),E=`M${C[0]} ${C[1]} `;break;case"bcurveTo":E+=`C${C[0]} ${C[1]}, ${C[2]} ${C[3]}, ${C[4]} ${C[5]} `;break;case"lineTo":E+=`L${C[0]} ${C[1]} `}}E.trim()&&x.push(E.trim())}return x}(c),p=[],m=[];let y=0;const v=(w,x,k)=>w.setAttribute(x,k);for(const w of b){const x=document.createElementNS(md,"path");if(v(x,"d",w),v(x,"fill","none"),v(x,"stroke",r.color||"currentColor"),v(x,"stroke-width",""+i),d){const k=x.getTotalLength();p.push(k),y+=k}e.appendChild(x),m.push(x)}if(d){let w=0;for(let x=0;x<m.length;x++){const k=m[x],E=p[x],S=y?o*(E/y):0,C=n+w,$=k.style;$.strokeDashoffset=""+E,$.strokeDasharray=""+E,$.animation=`rough-notation-dash ${S}ms ease-out ${C}ms forwards`,w+=S}}}}class E0{constructor(t,r){this._state="unattached",this._resizing=!1,this._seed=Math.floor(Math.random()*2**31),this._lastSizes=[],this._animationDelay=0,this._resizeListener=()=>{this._resizing||(this._resizing=!0,setTimeout(()=>{this._resizing=!1,this._state==="showing"&&this.haveRectsChanged()&&this.show()},400))},this._e=t,this._config=JSON.parse(JSON.stringify(r)),this.attach()}get animate(){return this._config.animate}set animate(t){this._config.animate=t}get animationDuration(){return this._config.animationDuration}set animationDuration(t){this._config.animationDuration=t}get iterations(){return this._config.iterations}set iterations(t){this._config.iterations=t}get color(){return this._config.color}set color(t){this._config.color!==t&&(this._config.color=t,this.refresh())}get strokeWidth(){return this._config.strokeWidth}set strokeWidth(t){this._config.strokeWidth!==t&&(this._config.strokeWidth=t,this.refresh())}get padding(){return this._config.padding}set padding(t){this._config.padding!==t&&(this._config.padding=t,this.refresh())}attach(){if(this._state==="unattached"&&this._e.parentElement){(function(){if(!window.__rno_kf_s){const o=window.__rno_kf_s=document.createElement("style");o.textContent="@keyframes rough-notation-dash { to { stroke-dashoffset: 0; } }",document.head.appendChild(o)}})();const t=this._svg=document.createElementNS(md,"svg");t.setAttribute("class","rough-annotation");const r=t.style;r.position="absolute",r.top="0",r.left="0",r.overflow="visible",r.pointerEvents="none",r.width="100px",r.height="100px";const n=this._config.type==="highlight";if(this._e.insertAdjacentElement(n?"beforebegin":"afterend",t),this._state="not-showing",n){const o=window.getComputedStyle(this._e).position;(!o||o==="static")&&(this._e.style.position="relative")}this.attachListeners()}}detachListeners(){window.removeEventListener("resize",this._resizeListener),this._ro&&this._ro.unobserve(this._e)}attachListeners(){this.detachListeners(),window.addEventListener("resize",this._resizeListener,{passive:!0}),!this._ro&&"ResizeObserver"in window&&(this._ro=new window.ResizeObserver(t=>{for(const r of t)r.contentRect&&this._resizeListener()})),this._ro&&this._ro.observe(this._e)}haveRectsChanged(){if(this._lastSizes.length){const t=this.rects();if(t.length!==this._lastSizes.length)return!0;for(let r=0;r<t.length;r++)if(!this.isSameRect(t[r],this._lastSizes[r]))return!0}return!1}isSameRect(t,r){const n=(o,a)=>Math.round(o)===Math.round(a);return n(t.x,r.x)&&n(t.y,r.y)&&n(t.w,r.w)&&n(t.h,r.h)}isShowing(){return this._state!=="not-showing"}refresh(){this.isShowing()&&!this.pendingRefresh&&(this.pendingRefresh=Promise.resolve().then(()=>{this.isShowing()&&this.show(),delete this.pendingRefresh}))}show(){switch(this._state){case"unattached":break;case"showing":this.hide(),this._svg&&this.render(this._svg,!0);break;case"not-showing":this.attach(),this._svg&&this.render(this._svg,!1)}}hide(){if(this._svg)for(;this._svg.lastChild;)this._svg.removeChild(this._svg.lastChild);this._state="not-showing"}remove(){this._svg&&this._svg.parentElement&&this._svg.parentElement.removeChild(this._svg),this._svg=void 0,this._state="unattached",this.detachListeners()}render(t,r){let n=this._config;r&&(n=JSON.parse(JSON.stringify(this._config)),n.animate=!1);const o=this.rects();let a=0;o.forEach(l=>a+=l.w);const c=n.animationDuration||800;let i=0;for(let l=0;l<o.length;l++){const d=c*(o[l].w/a);C0(t,o[l],n,i+this._animationDelay,d,this._seed),i+=d}this._lastSizes=o,this._state="showing"}rects(){const t=[];if(this._svg)if(this._config.multiline){const r=this._e.getClientRects();for(let n=0;n<r.length;n++)t.push(this.svgRect(this._svg,r[n]))}else t.push(this.svgRect(this._svg,this._e.getBoundingClientRect()));return t}svgRect(t,r){const n=t.getBoundingClientRect(),o=r;return{x:(o.x||o.left)-(n.x||n.left),y:(o.y||o.top)-(n.y||n.top),w:o.width,h:o.height}}}function S0(e,t){return new E0(e,t)}/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var no=function(){return no=Object.assign||function(t){for(var r,n=1,o=arguments.length;n<o;n++){r=arguments[n];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(t[a]=r[a])}return t},no.apply(this,arguments)};function $0(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}var A0=s.createContext(null),P0=s.createContext(null),R0=function(e,t){var r=s.useContext(A0),n=s.useContext(P0),o=s.useRef({annotation:e,context:r,dispatch:n,order:t});s.useEffect(function(){var a=o.current,c=a.annotation,i=a.context,l=a.dispatch,d=a.order;if(i&&l)return l({type:"ADD",payload:{annotation:c,order:d}})},[])},N0=function(e){var t=e.animate,r=t===void 0?!0:t,n=e.animationDelay,o=n===void 0?0:n,a=e.animationDuration,c=a===void 0?800:a,i=e.brackets,l=e.children,d=e.color,h=e.customElement,f=h===void 0?"span":h,g=e.getAnnotationObject,b=e.iterations,p=b===void 0?2:b,m=e.multiline,y=m===void 0?!1:m,v=e.order,w=e.padding,x=w===void 0?5:w,k=e.show,E=k===void 0?!1:k,S=e.strokeWidth,C=S===void 0?1:S,$=e.type,N=$===void 0?"underline":$,D=$0(e,["animate","animationDelay","animationDuration","brackets","children","color","customElement","getAnnotationObject","iterations","multiline","order","padding","show","strokeWidth","type"]),T=s.useRef(null),P=s.useRef(),j=s.useRef({playing:!1,timeout:null}),I=s.useRef({animate:r,animationDuration:c,brackets:i,color:d,getAnnotationObject:g,iterations:p,multiline:y,padding:x,strokeWidth:C,type:N}),M=s.useCallback(function(){j.current.timeout||(j.current.timeout=window.setTimeout(function(){var O,A;j.current.playing=!0,(A=(O=P.current)===null||O===void 0?void 0:O.show)===null||A===void 0||A.call(O),window.setTimeout(function(){j.current.playing=!1,j.current.timeout=null},c)},o))},[o,c]),F=s.useCallback(function(){var O,A;(A=(O=P.current)===null||O===void 0?void 0:O.hide)===null||A===void 0||A.call(O),j.current.playing=!1,j.current.timeout&&(clearTimeout(j.current.timeout),j.current.timeout=null)},[]),R=s.useCallback(function(){return P.current},[P]);return R0({getAnnotation:R,show:M,hide:F},typeof v=="string"?parseInt(v):v),s.useEffect(function(){var O=I.current,A=O.getAnnotationObject;return P.current=S0(T.current,O),A&&A(P.current),function(){var G,V;(V=(G=P.current)===null||G===void 0?void 0:G.remove)===null||V===void 0||V.call(G)}},[]),s.useEffect(function(){E?M():F()},[P,E,o,j,c,M,F]),s.useEffect(function(){P.current&&(P.current.animate=r,P.current.animationDuration=c,P.current.color=d,P.current.strokeWidth=C,P.current.padding=x)},[P,r,c,d,C,x]),W.createElement(f,no({ref:T},D),l)};function F0(e){return Ie({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8z"}}]})(e)}function D0(e){return Ie({tag:"svg",attr:{viewBox:"0 0 496 512"},child:[{tag:"path",attr:{d:"M336.5 160C322 70.7 287.8 8 248 8s-74 62.7-88.5 152h177zM152 256c0 22.2 1.2 43.5 3.3 64h185.3c2.1-20.5 3.3-41.8 3.3-64s-1.2-43.5-3.3-64H155.3c-2.1 20.5-3.3 41.8-3.3 64zm324.7-96c-28.6-67.9-86.5-120.4-158-141.6 24.4 33.8 41.2 84.7 50 141.6h108zM177.2 18.4C105.8 39.6 47.8 92.1 19.3 160h108c8.7-56.9 25.5-107.8 49.9-141.6zM487.4 192H372.7c2.1 21 3.3 42.5 3.3 64s-1.2 43-3.3 64h114.6c5.5-20.5 8.6-41.8 8.6-64s-3.1-43.5-8.5-64zM120 256c0-21.5 1.2-43 3.3-64H8.6C3.2 212.5 0 233.8 0 256s3.2 43.5 8.6 64h114.6c-2-21-3.2-42.5-3.2-64zm39.5 96c14.5 89.3 48.7 152 88.5 152s74-62.7 88.5-152h-177zm159.3 141.6c71.4-21.2 129.4-73.7 158-141.6h-108c-8.8 56.9-25.6 107.8-50 141.6zM19.3 352c28.6 67.9 86.5 120.4 158 141.6-24.4-33.8-41.2-84.7-50-141.6h-108z"}}]})(e)}function T0(e){return Ie({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M464 448H48c-26.51 0-48-21.49-48-48V112c0-26.51 21.49-48 48-48h416c26.51 0 48 21.49 48 48v288c0 26.51-21.49 48-48 48zM112 120c-30.928 0-56 25.072-56 56s25.072 56 56 56 56-25.072 56-56-25.072-56-56-56zM64 384h384V272l-87.515-87.515c-4.686-4.686-12.284-4.686-16.971 0L208 320l-55.515-55.515c-4.686-4.686-12.284-4.686-16.971 0L64 336v48z"}}]})(e)}function M0(e){return Ie({tag:"svg",attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M255.03 261.65c6.25 6.25 16.38 6.25 22.63 0l11.31-11.31c6.25-6.25 6.25-16.38 0-22.63L253.25 192l35.71-35.72c6.25-6.25 6.25-16.38 0-22.63l-11.31-11.31c-6.25-6.25-16.38-6.25-22.63 0l-58.34 58.34c-6.25 6.25-6.25 16.38 0 22.63l58.35 58.34zm96.01-11.3l11.31 11.31c6.25 6.25 16.38 6.25 22.63 0l58.34-58.34c6.25-6.25 6.25-16.38 0-22.63l-58.34-58.34c-6.25-6.25-16.38-6.25-22.63 0l-11.31 11.31c-6.25 6.25-6.25 16.38 0 22.63L386.75 192l-35.71 35.72c-6.25 6.25-6.25 16.38 0 22.63zM624 416H381.54c-.74 19.81-14.71 32-32.74 32H288c-18.69 0-33.02-17.47-32.77-32H16c-8.8 0-16 7.2-16 16v16c0 35.2 28.8 64 64 64h512c35.2 0 64-28.8 64-64v-16c0-8.8-7.2-16-16-16zM576 48c0-26.4-21.6-48-48-48H112C85.6 0 64 21.6 64 48v336h512V48zm-64 272H128V64h384v256z"}}]})(e)}function O0(e){return Ie({tag:"svg",attr:{viewBox:"0 0 320 512"},child:[{tag:"path",attr:{d:"M272 0H48C21.5 0 0 21.5 0 48v416c0 26.5 21.5 48 48 48h224c26.5 0 48-21.5 48-48V48c0-26.5-21.5-48-48-48zM160 480c-17.7 0-32-14.3-32-32s14.3-32 32-32 32 14.3 32 32-14.3 32-32 32zm112-108c0 6.6-5.4 12-12 12H60c-6.6 0-12-5.4-12-12V60c0-6.6 5.4-12 12-12h200c6.6 0 12 5.4 12 12v312z"}}]})(e)}function j0(e){return Ie({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M167.02 309.34c-40.12 2.58-76.53 17.86-97.19 72.3-2.35 6.21-8 9.98-14.59 9.98-11.11 0-45.46-27.67-55.25-34.35C0 439.62 37.93 512 128 512c75.86 0 128-43.77 128-120.19 0-3.11-.65-6.08-.97-9.13l-88.01-73.34zM457.89 0c-15.16 0-29.37 6.71-40.21 16.45C213.27 199.05 192 203.34 192 257.09c0 13.7 3.25 26.76 8.73 38.7l63.82 53.18c7.21 1.8 14.64 3.03 22.39 3.03 62.11 0 98.11-45.47 211.16-256.46 7.38-14.35 13.9-29.85 13.9-45.99C512 20.64 486 0 457.89 0z"}}]})(e)}function _0(e=640){const[t,r]=s.useState(()=>typeof window<"u"&&window.innerWidth<=e);return s.useEffect(()=>{const n=()=>r(window.innerWidth<=e);return window.addEventListener("resize",n),()=>window.removeEventListener("resize",n)},[e]),t}const I0={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.15,delayChildren:.25}}},L0={hidden:{opacity:0,y:40,scale:.6},visible:{opacity:1,y:0,scale:1,transition:{type:"spring",stiffness:440,damping:24}}},B0=s.memo(({icon:e,text:t,className:r,highlight:n})=>{const o=_0();return u.jsxs(te.div,{className:`relative flex items-center gap-1 rounded-full bg-primary/10 px-3 py-1 text-xs font-medium text-primary ${r||""}`,variants:L0,initial:"hidden",animate:"visible",children:[e&&u.jsx("span",{role:"img","aria-label":t,className:"mr-1 flex items-center",children:e}),n?u.jsx(N0,{type:"circle",show:!0,color:"#edf0f1",padding:o?7:14,animationDuration:750,children:u.jsx("span",{children:t})}):u.jsx("span",{children:t})]})}),W0=()=>{const e=[{icon:u.jsx(O0,{size:16,color:"#007aff"}),text:"iOS & Android"},{icon:u.jsx(j0,{size:16,color:"#e95946"}),text:"Figma & Sketch"},{icon:u.jsx(M0,{size:16,color:"#20c997"}),text:"CSS & SCSS"},{icon:u.jsx(T0,{size:16,color:"#f9ca24"}),text:"PNG & SVG"},{icon:u.jsx(D0,{size:16,color:"#845ef7"}),text:"JSON Export"},{icon:null,text:"and more soon",highlight:!0}];return u.jsx(te.div,{className:"flex flex-wrap items-center justify-center gap-2 pt-4 pb-2",variants:I0,initial:"hidden",whileInView:"visible",viewport:{once:!0,amount:.6},children:e.map((t,r)=>u.jsx(B0,{icon:t.icon,text:t.text,highlight:!!t.highlight},t.text+r))})};function V0({value:e,direction:t="up",delay:r=0,className:n,decimalPlaces:o=0}){const a=s.useRef(null),c=Mt(t==="down"?e:0),i=Dd(c,{damping:60,stiffness:100}),l=Od(a,{once:!0,margin:"0px"});return s.useEffect(()=>{l&&setTimeout(()=>{c.set(t==="down"?0:e)},r*1e3)},[c,l,r,e,t]),s.useEffect(()=>i.on("change",d=>{a.current&&(a.current.textContent=Intl.NumberFormat("en-US",{minimumFractionDigits:o,maximumFractionDigits:o}).format(Number(d.toFixed(o))))}),[i,o]),u.jsx("span",{className:L("inline-block ps-1 tabular-nums tracking-wider text-[var(--headline)] dark:text-white",n),ref:a})}const z0=s.memo(()=>u.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"orange",stroke:"orange",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round",className:"lucide lucide-star h-5 w-5","aria-label":"Star",role:"img",children:u.jsx("polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"})})),H0=s.memo(({src:e,alt:t})=>{const[r,n]=s.useState(!0);return u.jsxs("span",{className:"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",children:[r&&u.jsx(Ee,{className:"h-10 w-10"}),u.jsx("img",{alt:t,src:e,onLoad:()=>n(!1),className:`transition-opacity duration-300 ${r?"opacity-0":"opacity-100"}`})]})}),U0=[{src:"https://bundui-images.netlify.app/avatars/01.png",alt:"Avatar 1"},{src:"https://bundui-images.netlify.app/avatars/03.png",alt:"Avatar 2"},{src:"https://bundui-images.netlify.app/avatars/05.png",alt:"Avatar 3"},{src:"https://bundui-images.netlify.app/avatars/06.png",alt:"Avatar 4"}],G0=()=>u.jsx("div",{children:u.jsxs("div",{className:"mt-3 flex flex-col gap-4 lg:flex-row",children:[u.jsx("div",{className:"flex justify-center -space-x-4",children:U0.map(({src:e,alt:t})=>u.jsx(H0,{src:e,alt:t},e))}),u.jsxs("div",{className:"flex flex-col justify-center",children:[u.jsx("span",{className:"mt-1 flex justify-center gap-1 lg:justify-start",children:Array(5).fill(null).map((e,t)=>u.jsx(z0,{},t))}),u.jsxs("span",{className:"text-sm text-muted-foreground flex gap-1 items-center",children:["More than ",u.jsx(V0,{value:50}),"+ happy users"]})]})]})}),K0=()=>u.jsxs("header",{className:"relative mx-auto max-w-6xl space-y-2 pt-[20px]",children:[u.jsx(w0,{}),u.jsx(W0,{}),u.jsx("div",{className:"flex w-full items-center justify-center pb-6",children:u.jsx(G0,{})})]});function Y0(e){return Ie({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"20 6 9 17 4 12"}}]})(e)}function X0(e){return Ie({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"6 9 12 15 18 9"}}]})(e)}function q0({availableColors:e,selectedColors:t,onColorChange:r}){const[n,o]=s.useState(!1),a=h=>{const f=t.includes(h)?t.filter(g=>g!==h):[...t,h];r(f)},c=()=>{r([])},i=()=>{r(e)},l=h=>{const f=h.toLowerCase();let g="#cbd5e1";return f.includes("red")?g="#ef4444":f.includes("pink")?g="#ec4899":f.includes("orange")||f.includes("peach")?g="#f97316":f.includes("yellow")?g="#eab308":f.includes("green")||f.includes("olive")?g="#22c55e":f.includes("teal")||f.includes("cyan")?g="#14b8a6":f.includes("blue")||f.includes("indigo")?g="#3b82f6":f.includes("purple")||f.includes("violet")||f.includes("magenta")?g="#a21caf":f.includes("brown")||f.includes("beige")?g="#b45309":f.includes("gray")||f.includes("grey")?g="#6b7280":f.includes("black")?g="#000":f.includes("white")&&(g="#fff"),u.jsx(F0,{style:{color:g,border:f==="white"?"1px solid var(--border)":void 0,borderRadius:"50%",fontSize:"1.1rem"}})},d=h=>{const f=t.includes(h);return u.jsxs(Yt,{className:L("flex cursor-pointer items-center justify-between gap-2 px-3 py-2",f&&"bg-accent"),onSelect:g=>{g.preventDefault(),a(h)},children:[u.jsxs("div",{className:"flex items-center gap-2",children:[l(h),u.jsx("span",{className:"capitalize",children:h})]}),f&&u.jsx(Y0,{className:"h-4 w-4 text-primary"})]},h)};return u.jsx("div",{className:"flex flex-col gap-2",children:u.jsxs(Kr,{open:n,onOpenChange:o,children:[u.jsx(Yr,{asChild:!0,children:u.jsxs(Ze,{variant:"outline",className:"w-full justify-between text-left font-normal hover:bg-accent",children:[u.jsxs("div",{className:"flex items-center gap-2",children:[t.length>0&&t.length<=3&&u.jsx("div",{className:"flex gap-1",children:t.slice(0,3).map(h=>u.jsx("div",{className:"scale-75",children:l(h)},h))}),u.jsx("span",{children:t.length===0?`Select colors (${e.length})`:`${t.length} selected`})]}),u.jsx(X0,{className:"h-4 w-4 opacity-50"})]})}),u.jsxs(Kt,{className:"max-h-96 w-64 overflow-y-auto",children:[u.jsxs("div",{className:"flex items-center justify-between border-[var(--border)] border-b p-2",children:[u.jsx(Ze,{variant:"ghost",size:"sm",onClick:i,className:"h-auto p-2 text-xs hover:bg-accent",children:"Select All"}),u.jsx(Ze,{variant:"ghost",size:"sm",onClick:c,className:"h-auto p-2 text-xs hover:bg-accent",children:"Clear All"})]}),e.map(d)]})]})})}const Z0=[{name:"Omolon",colors:["#091E3A","#2F80ED","#2D9EE0"],colorsname:["dark blue","bright blue","light blue"],keywords:[["dark","blue","serious","deep"],["bright","blue","trust","calm"],["bright","light blue","fresh","cool"]]},{name:"Farhan",colors:["#9400D3","#4B0082"],colorsname:["dark violet","indigo"],keywords:[["dark","violet","mystery","royalty"],["dark","indigo","depth","wisdom"]]},{name:"Purple",colors:["#c84e89","#F15F79"],colorsname:["light pinkish purple","light pink"],keywords:[["light","pinkish","purple","romantic"],["bright","pink","playful","cheerful"]]},{name:"Ibtesam",colors:["#00F5A0","#00D9F5"],colorsname:["light green","light cyan"],keywords:[["bright","green","fresh","nature"],["bright","cyan","cool","refreshing"]]},{name:"Radioactive Heat",colors:["#F7941E","#72C6EF","#00A651"],colorsname:["orange","light blue","green"],keywords:[["bright","orange","energy","vibrant"],["bright","light blue","calm","refreshing"],["bright","green","nature","fresh"]]},{name:"The Sky And The Sea",colors:["#F7941E","#004E8F"],colorsname:["orange","dark blue"],keywords:[["bright","orange","vibrant","energy"],["dark","blue","depth","serious"]]},{name:"From Ice To Fire",colors:["#72C6EF","#004E8F"],colorsname:["light blue","dark blue"],keywords:[["bright","light blue","cool","refreshing"],["dark","blue","depth","serious"]]},{name:"Blue & Orange",colors:["#FD8112","#0085CA"],colorsname:["bright orange","blue"],keywords:[["bright","orange","vibrant","energy"],["bright","blue","trust","calm"]]},{name:"Purple Dream",colors:["#bf5ae0","#a811da"],colorsname:["pinkish purple","purple"],keywords:[["bright","pinkish","purple","romantic"],["dark","purple","mystery","royalty"]]},{name:"Blu",colors:["#00416A","#E4E5E6"],colorsname:["dark blue","light gray"],keywords:[["dark","blue","depth","serious"],["light","gray","neutral","calm"]]},{name:"Summer Breeze",colors:["#fbed96","#abecd6"],colorsname:["light yellow","light green"],keywords:[["bright","yellow","cheerful","sunny"],["light","green","fresh","nature"]]},{name:"Ver",colors:["#FFE000","#799F0C"],colorsname:["bright yellow","green"],keywords:[["bright","yellow","cheerful","sunny"],["bright","green","nature","fresh"]]},{name:"Ver Black",colors:["#F7F8F8","#ACBB78"],colorsname:["light gray","olive"],keywords:[["light","gray","neutral","calm"],["dark","olive","earthy","natural"]]},{name:"Combi",colors:["#00416A","#799F0C","#FFE000"],colorsname:["dark blue","green","bright yellow"],keywords:[["dark","blue","depth","serious"],["bright","green","nature","fresh"],["bright","yellow","cheerful","sunny"]]},{name:"Anwar",colors:["#334d50","#cbcaa5"],colorsname:["dark grayish teal","light beige"],keywords:[["dark","grayish","teal","calm"],["light","beige","neutral","warm"]]},{name:"Bluelagoo",colors:["#0052D4","#4364F7","#6FB1FC"],colorsname:["blue","light blue","light blue"],keywords:[["bright","blue","trust","calm"],["bright","light blue","cool","refreshing"],["bright","light blue","cool","refreshing"]]},{name:"Lunada",colors:["#5433FF","#20BDFF","#A5FECB"],colorsname:["dark blue","light blue","light green"],keywords:[["dark","blue","depth","serious"],["bright","light blue","cool","refreshing"],["bright","light green","fresh","nature"]]},{name:"Reaqua",colors:["#799F0C","#ACBB78"],colorsname:["green","olive"],keywords:[["bright","green","nature","fresh"],["dark","olive","earthy","natural"]]},{name:"Mango",colors:["#ffe259","#ffa751"],colorsname:["light yellow","peach"],keywords:[["bright","yellow","cheerful","sunny"],["bright","peach","warm","soft"]]},{name:"Bupe",colors:["#00416A","#E4E5E6"],colorsname:["dark blue","light gray"],keywords:[["dark","blue","depth","serious"],["light","gray","neutral","calm"]]},{name:"Rea",colors:["#FFE000","#799F0C"],colorsname:["bright yellow","green"],keywords:[["bright","yellow","cheerful","sunny"],["bright","green","nature","fresh"]]},{name:"Windy",colors:["#acb6e5","#86fde8"],colorsname:["light blue","light cyan"],keywords:[["light","blue","fresh","calm"],["bright","cyan","cool","refreshing"]]},{name:"Royal Blue",colors:["#536976","#292E49"],colorsname:["dark bluish gray","dark blue"],keywords:[["dark","bluish","gray","serious"],["dark","blue","depth","serious"]]},{name:"Royal Blue + Petrol",colors:["#BBD2C5","#536976","#292E49"],colorsname:["light gray","dark bluish gray","dark blue"],keywords:[["light","gray","neutral","calm"],["dark","bluish","gray","serious"],["dark","blue","depth","serious"]]},{name:"Copper",colors:["#B79891","#94716B"],colorsname:["light brown","dark brown"],keywords:[["light","brown","warm","earthy"],["dark","brown","earthy","natural"]]},{name:"Anamnisar",colors:["#9796f0","#fbc7d4"],colorsname:["light purple","light pink"],keywords:[["light","purple","romantic","soft"],["light","pink","cheerful","soft"]]},{name:"Petrol",colors:["#BBD2C5","#536976"],colorsname:["light gray","dark bluish gray"],keywords:[["light","gray","neutral","calm"],["dark","bluish","gray","serious"]]},{name:"Sky",colors:["#076585","#fff"],colorsname:["dark cyan","white"],keywords:[["dark","cyan","cool","refreshing"],["light","white","pure","clean"]]},{name:"Sel",colors:["#00467F","#A5CC82"],colorsname:["dark blue","light green"],keywords:[["dark","blue","depth","serious"],["light","green","fresh","nature"]]},{name:"Afternoon",colors:["#000C40","#607D8B"],colorsname:["dark blue","dark gray"],keywords:[["dark","blue","depth","serious"],["dark","gray","serious","calm"]]},{name:"Skyline",colors:["#1488CC","#2B32B2"],colorsname:["bright blue","dark blue"],keywords:[["bright","blue","trust","calm"],["dark","blue","depth","serious"]]},{name:"DIMIGO",colors:["#ec008c","#fc6767"],colorsname:["bright pink","light red"],keywords:[["bright","pink","romantic","cheerful"],["bright","red","energy","vibrant"]]},{name:"Purple Love",colors:["#cc2b5e","#753a88"],colorsname:["dark pink","dark purple"],keywords:[["dark","pink","romantic","soft"],["dark","purple","mystery","royalty"]]},{name:"Sexy Blue",colors:["#2193b0","#6dd5ed"],colorsname:["bright blue","light blue"],keywords:[["bright","blue","trust","calm"],["bright","light blue","cool","refreshing"]]},{name:"Blooker20",colors:["#e65c00","#F9D423"],colorsname:["bright orange","light yellow"],keywords:[["bright","orange","vibrant","energy"],["light","yellow","cheerful","sunny"]]},{name:"Sea Blue",colors:["#2b5876","#4e4376"],colorsname:["dark teal","dark grayish blue"],keywords:[["dark","teal","depth","cool"],["dark","grayish blue","serious","calm"]]},{name:"Nimvelo",colors:["#314755","#26a0da"],colorsname:["dark teal","bright blue"],keywords:[["dark","teal","depth","cool"],["bright","blue","trust","calm"]]},{name:"Hazel",colors:["#77A1D3","#79CBCA","#E684AE"],colorsname:["light blue","light cyan","light pink"],keywords:[["light","blue","cool","refreshing"],["light","cyan","fresh","clean"],["light","pink","romantic","soft"]]},{name:"Noon to Dusk",colors:["#ff6e7f","#bfe9ff"],colorsname:["light pink","light blue"],keywords:[["bright","pink","romantic","soft"],["light","blue","cool","refreshing"]]},{name:"YouTube",colors:["#e52d27","#b31217"],colorsname:["bright red","dark red"],keywords:[["bright","red","energy","vibrant"],["dark","red","serious","passionate"]]},{name:"Cool Brown",colors:["#603813","#b29f94"],colorsname:["dark brown","light brown"],keywords:[["dark","brown","earthy","natural"],["light","brown","warm","earthy"]]},{name:"Harmonic Energy",colors:["#16A085","#F4D03F"],colorsname:["dark teal","bright yellow"],keywords:[["dark","teal","depth","cool"],["bright","yellow","cheerful","sunny"]]},{name:"Playing with Reds",colors:["#D31027","#EA384D"],colorsname:["bright red","bright pink"],keywords:[["bright","red","energy","vibrant"],["bright","pink","romantic","cheerful"]]},{name:"Sunny Days",colors:["#EDE574","#E1F5C4"],colorsname:["light yellow","light green"],keywords:[["bright","yellow","cheerful","sunny"],["light","green","fresh","nature"]]},{name:"Green Beach",colors:["#02AAB0","#00CDAC"],colorsname:["bright teal","bright cyan"],keywords:[["bright","teal","fresh","cool"],["bright","cyan","refreshing","calm"]]},{name:"Intuitive Purple",colors:["#DA22FF","#9733EE"],colorsname:["bright purple","dark purple"],keywords:[["bright","purple","romantic","mystery"],["dark","purple","serious","royalty"]]},{name:"Emerald Water",colors:["#348F50","#56B4D3"],colorsname:["dark green","light blue"],keywords:[["dark","green","nature","fresh"],["light","blue","cool","refreshing"]]},{name:"Lemon Twist",colors:["#3CA55C","#B5AC49"],colorsname:["bright green","light brown"],keywords:[["bright","green","fresh","nature"],["light","brown","warm","earthy"]]},{name:"Monte Carlo",colors:["#CC95C0","#DBD4B4","#7AA1D2"],colorsname:["light pink","light brown","light blue"],keywords:[["light","pink","romantic","soft"],["light","brown","warm","earthy"],["light","blue","cool","refreshing"]]},{name:"Horizon",colors:["#003973","#E5E5BE"],colorsname:["dark blue","light gray"],keywords:[["dark","blue","depth","serious"],["light","gray","neutral","calm"]]},{name:"Rose Water",colors:["#E55D87","#5FC3E4"],colorsname:["light pink","light blue"],keywords:[["light","pink","romantic","soft"],["light","blue","cool","refreshing"]]},{name:"Frozen",colors:["#403B4A","#E7E9BB"],colorsname:["dark gray","light gray"],keywords:[["dark","gray","serious","calm"],["light","gray","neutral","calm"]]},{name:"Mango Pulp",colors:["#F09819","#EDDE5D"],colorsname:["bright orange","light yellow"],keywords:[["bright","orange","vibrant","energy"],["light","yellow","cheerful","sunny"]]},{name:"Bloody Mary",colors:["#FF512F","#DD2476"],colorsname:["bright red","bright pink"],keywords:[["bright","red","energy","vibrant"],["bright","pink","romantic","cheerful"]]},{name:"Aubergine",colors:["#AA076B","#61045F"],colorsname:["dark purple","dark magenta"],keywords:[["dark","purple","mystery","royalty"],["dark","magenta","bold","confident"]]},{name:"Aqua Marine",colors:["#1A2980","#26D0CE"],colorsname:["dark blue","light cyan"],keywords:[["dark","blue","depth","cool"],["light","cyan","refreshing","calm"]]},{name:"Sunrise",colors:["#FF512F","#F09819"],colorsname:["bright red","bright orange"],keywords:[["bright","red","energy","vibrant"],["bright","orange","warm","cheerful"]]},{name:"Purple Paradise",colors:["#1D2B64","#F8CDDA"],colorsname:["dark blue","light pink"],keywords:[["dark","blue","depth","serious"],["light","pink","romantic","soft"]]},{name:"Stripe",colors:["#1FA2FF","#12D8FA","#A6FFCB"],colorsname:["bright blue","light blue","light green"],keywords:[["bright","blue","trust","calm"],["bright","light blue","cool","refreshing"],["bright","light green","fresh","nature"]]},{name:"Sea Weed",colors:["#4CB8C4","#3CD3AD"],colorsname:["dark cyan","light teal"],keywords:[["dark","cyan","cool","refreshing"],["light","teal","fresh","nature"]]},{name:"Pinky",colors:["#DD5E89","#F7BB97"],colorsname:["light pink","light peach"],keywords:[["light","pink","romantic","soft"],["light","peach","warm","soft"]]},{name:"Cherry",colors:["#EB3349","#F45C43"],colorsname:["bright red","light red"],keywords:[["bright","red","energy","vibrant"],["light","red","warm","soft"]]},{name:"Mojito",colors:["#1D976C","#93F9B9"],colorsname:["dark green","light cyan"],keywords:[["dark","green","nature","fresh"],["bright","cyan","cool","refreshing"]]},{name:"Juicy Orange",colors:["#FF8008","#FFC837"],colorsname:["bright orange","light yellow"],keywords:[["bright","orange","vibrant","energy"],["light","yellow","cheerful","sunny"]]},{name:"Mirage",colors:["#16222A","#3A6073"],colorsname:["dark gray","dark blue"],keywords:[["dark","gray","serious","calm"],["dark","blue","depth","cool"]]},{name:"Steel Gray",colors:["#1F1C2C","#928DAB"],colorsname:["dark gray","light gray"],keywords:[["dark","gray","serious","calm"],["light","gray","neutral","calm"]]},{name:"Kashmir",colors:["#614385","#516395"],colorsname:["dark purple","dark blue"],keywords:[["dark","purple","mystery","royalty"],["dark","blue","depth","serious"]]},{name:"Electric Violet",colors:["#4776E6","#8E54E9"],colorsname:["bright blue","bright purple"],keywords:[["bright","blue","trust","cool"],["bright","purple","romantic","mystery"]]},{name:"Venice Blue",colors:["#085078","#85D8CE"],colorsname:["dark cyan","light cyan"],keywords:[["dark","cyan","cool","refreshing"],["light","cyan","fresh","calm"]]},{name:"Bora Bora",colors:["#2BC0E4","#EAECC6"],colorsname:["bright cyan","light gray"],keywords:[["bright","cyan","fresh","cool"],["light","gray","neutral","calm"]]},{name:"Moss",colors:["#134E5E","#71B280"],colorsname:["dark teal","light green"],keywords:[["dark","teal","depth","cool"],["light","green","fresh","nature"]]},{name:"Shroom Haze",colors:["#5C258D","#4389A2"],colorsname:["dark purple","dark blue"],keywords:[["dark","purple","mystery","royalty"],["dark","blue","depth","cool"]]},{name:"Mystic",colors:["#757F9A","#D7DDE8"],colorsname:["dark gray","light purple"],keywords:[["dark","gray","mystical","serious"],["light","purple","romantic","soft"]]},{name:"Midnight City",colors:["#232526","#414345"],colorsname:["dark gray","dark gray"],keywords:[["dark","gray","serious","calm"],["dark","gray","serious","calm"]]},{name:"Sea Blizz",colors:["#1CD8D2","#93EDC7"],colorsname:["bright cyan","light green"],keywords:[["bright","cyan","fresh","cool"],["light","green","fresh","nature"]]},{name:"Opa",colors:["#3D7EAA","#FFE47A"],colorsname:["dark blue","light yellow"],keywords:[["dark","blue","depth","cool"],["light","yellow","cheerful","sunny"]]},{name:"Titanium",colors:["#283048","#859398"],colorsname:["dark gray","light gray"],keywords:[["dark","gray","serious","calm"],["light","gray","neutral","calm"]]},{name:"Mantle",colors:["#24C6DC","#514A9D"],colorsname:["bright cyan","dark purple"],keywords:[["bright","cyan","fresh","cool"],["dark","purple","mystery","royalty"]]},{name:"Dracula",colors:["#DC2424","#4A569D"],colorsname:["bright red","dark blue"],keywords:[["bright","red","energy","vibrant"],["dark","blue","depth","serious"]]},{name:"Peach",colors:["#ED4264","#FFEDBC"],colorsname:["light pink","light yellow"],keywords:[["light","pink","romantic","soft"],["light","yellow","cheerful","sunny"]]},{name:"Moonrise",colors:["#DAE2F8","#D6A4A4"],colorsname:["light blue","light pink"],keywords:[["light","blue","cool","refreshing"],["light","pink","romantic","soft"]]},{name:"Clouds",colors:["#ECE9E6","#FFFFFF"],colorsname:["light gray","white"],keywords:[["light","gray","neutral","calm"],["light","white","pure","clean"]]},{name:"Stellar",colors:["#7474BF","#348AC7"],colorsname:["dark blue","bright blue"],keywords:[["dark","blue","depth","serious"],["bright","blue","trust","calm"]]},{name:"Bourbon",colors:["#EC6F66","#F3A183"],colorsname:["light red","light brown"],keywords:[["light","red","warm","soft"],["light","brown","warm","earthy"]]},{name:"Calm Darya",colors:["#5f2c82","#49a09d"],colorsname:["dark purple","dark teal"],keywords:[["dark","purple","mystery","royalty"],["dark","teal","depth","cool"]]},{name:"Influenza",colors:["#C04848","#480048"],colorsname:["dark red","dark magenta"],keywords:[["dark","red","energy","vibrant"],["dark","magenta","bold","confident"]]},{name:"Shrimpy",colors:["#e43a15","#e65245"],colorsname:["bright red","light red"],keywords:[["bright","red","energy","vibrant"],["light","red","warm","soft"]]},{name:"Army",colors:["#414d0b","#727a17"],colorsname:["dark olive","dark green"],keywords:[["dark","olive","earthy","natural"],["dark","green","nature","fresh"]]},{name:"Miaka",colors:["#FC354C","#0ABFBC"],colorsname:["bright red","light cyan"],keywords:[["bright","red","energy","vibrant"],["light","cyan","cool","refreshing"]]},{name:"Pinot Noir",colors:["#4b6cb7","#182848"],colorsname:["dark blue","dark blue"],keywords:[["dark","blue","depth","serious"],["dark","blue","depth","serious"]]},{name:"Day Tripper",colors:["#f857a6","#ff5858"],colorsname:["bright pink","bright red"],keywords:[["bright","pink","romantic","soft"],["bright","red","energy","vibrant"]]},{name:"Namn",colors:["#a73737","#7a2828"],colorsname:["dark red","dark red"],keywords:[["dark","red","energy","vibrant"],["dark","red","energy","vibrant"]]},{name:"Blurry Beach",colors:["#d53369","#cbad6d"],colorsname:["light pink","light brown"],keywords:[["light","pink","romantic","soft"],["light","brown","warm","earthy"]]},{name:"Vasily",colors:["#e9d362","#333333"],colorsname:["light yellow","dark gray"],keywords:[["light","yellow","cheerful","sunny"],["dark","gray","serious","calm"]]},{name:"A Lost Memory",colors:["#DE6262","#FFB88C"],colorsname:["bright red","light peach"],keywords:[["bright","red","energy","vibrant"],["light","peach","warm","soft"]]},{name:"Petrichor",colors:["#666600","#999966"],colorsname:["dark olive","light olive"],keywords:[["dark","olive","earthy","natural"],["light","olive","fresh","nature"]]},{name:"Jonquil",colors:["#FFEEEE","#DDEFBB"],colorsname:["light yellow","light green"],keywords:[["light","yellow","cheerful","sunny"],["light","green","fresh","nature"]]},{name:"Sirius Tamed",colors:["#EFEFBB","#D4D3DD"],colorsname:["light yellow","light gray"],keywords:[["light","yellow","cheerful","sunny"],["light","gray","neutral","calm"]]},{name:"Kyoto",colors:["#c21500","#ffc500"],colorsname:["bright red","bright yellow"],keywords:[["bright","red","energy","vibrant"],["bright","yellow","cheerful","sunny"]]},{name:"Misty Meadow",colors:["#215f00","#e4e4d9"],colorsname:["dark green","light gray"],keywords:[["dark","green","nature","fresh"],["light","gray","neutral","calm"]]},{name:"Aqualicious",colors:["#50C9C3","#96DEDA"],colorsname:["bright teal","light cyan"],keywords:[["bright","teal","fresh","cool"],["light","cyan","refreshing","calm"]]},{name:"Moor",colors:["#616161","#9bc5c3"],colorsname:["dark gray","light cyan"],keywords:[["dark","gray","serious","calm"],["light","cyan","cool","refreshing"]]},{name:"Almost",colors:["#ddd6f3","#faaca8"],colorsname:["light purple","light peach"],keywords:[["light","purple","romantic","soft"],["light","peach","warm","soft"]]},{name:"Forever Lost",colors:["#5D4157","#A8CABA"],colorsname:["dark gray","light gray"],keywords:[["dark","gray","serious","calm"],["light","gray","neutral","calm"]]},{name:"Winter",colors:["#E6DADA","#274046"],colorsname:["light gray","dark teal"],keywords:[["light","gray","neutral","calm"],["dark","teal","depth","cool"]]},{name:"Nelson",colors:["#f2709c","#ff9472"],colorsname:["bright pink","light orange"],keywords:[["bright","pink","romantic","soft"],["light","orange","cheerful","warm"]]},{name:"Autumn",colors:["#DAD299","#B0DAB9"],colorsname:["light yellow","light green"],keywords:[["light","yellow","cheerful","sunny"],["light","green","fresh","nature"]]},{name:"Candy",colors:["#D3959B","#BFE6BA"],colorsname:["light pink","light green"],keywords:[["light","pink","romantic","soft"],["light","green","fresh","nature"]]},{name:"Reef",colors:["#00d2ff","#3a7bd5"],colorsname:["bright cyan","dark blue"],keywords:[["bright","cyan","fresh","cool"],["dark","blue","depth","serious"]]},{name:"The Strain",colors:["#870000","#190A05"],colorsname:["dark red","black"],keywords:[["dark","red","energy","vibrant"],["dark","black","depth","serious"]]},{name:"Dirty Fog",colors:["#B993D6","#8CA6DB"],colorsname:["light purple","light blue"],keywords:[["light","purple","romantic","soft"],["light","blue","cool","refreshing"]]},{name:"Earthly",colors:["#649173","#DBD5A4"],colorsname:["dark green","light beige"],keywords:[["dark","green","nature","fresh"],["light","beige","neutral","warm"]]},{name:"Virgin",colors:["#C9FFBF","#FFAFBD"],colorsname:["light green","light pink"],keywords:[["light","green","fresh","nature"],["light","pink","romantic","soft"]]},{name:"Ash",colors:["#606c88","#3f4c6b"],colorsname:["dark gray","dark blue"],keywords:[["dark","gray","serious","calm"],["dark","blue","depth","cool"]]},{name:"Cherryblossoms",colors:["#FBD3E9","#BB377D"],colorsname:["light pink","dark pink"],keywords:[["light","pink","romantic","soft"],["dark","pink","bold","vibrant"]]},{name:"Parklife",colors:["#ADD100","#7B920A"],colorsname:["light green","dark green"],keywords:[["light","green","fresh","nature"],["dark","green","nature","fresh"]]},{name:"Dance To Forget",colors:["#FF4E50","#F9D423"],colorsname:["bright red","light yellow"],keywords:[["bright","red","energy","vibrant"],["light","yellow","cheerful","sunny"]]},{name:"Starfall",colors:["#F0C27B","#4B1248"],colorsname:["light brown","dark purple"],keywords:[["light","brown","warm","earthy"],["dark","purple","mystery","royalty"]]},{name:"Red Mist",colors:["#000000","#e74c3c"],colorsname:["black","bright red"],keywords:[["dark","black","depth","serious"],["bright","red","energy","vibrant"]]},{name:"Teal Love",colors:["#AAFFA9","#11FFBD"],colorsname:["light teal","light cyan"],keywords:[["light","teal","fresh","cool"],["light","cyan","refreshing","calm"]]},{name:"Neon Life",colors:["#B3FFAB","#12FFF7"],colorsname:["bright green","bright cyan"],keywords:[["bright","green","fresh","nature"],["bright","cyan","refreshing","cool"]]},{name:"Man of Steel",colors:["#780206","#061161"],colorsname:["dark red","dark blue"],keywords:[["dark","red","energy","vibrant"],["dark","blue","depth","cool"]]},{name:"Amethyst",colors:["#9D50BB","#6E48AA"],colorsname:["bright purple","dark purple"],keywords:[["bright","purple","romantic","mystery"],["dark","purple","serious","royalty"]]},{name:"Cheer Up Emo Kid",colors:["#556270","#FF6B6B"],colorsname:["dark gray","bright red"],keywords:[["dark","gray","serious","calm"],["bright","red","energy","vibrant"]]},{name:"Shore",colors:["#70e1f5","#ffd194"],colorsname:["light blue","light yellow"],keywords:[["light","blue","cool","refreshing"],["light","yellow","cheerful","sunny"]]},{name:"Facebook Messenger",colors:["#00c6ff","#0072ff"],colorsname:["bright blue","bright blue"],keywords:[["bright","blue","trust","cool"],["bright","blue","trust","cool"]]},{name:"SoundCloud",colors:["#fe8c00","#f83600"],colorsname:["bright orange","bright red"],keywords:[["bright","orange","vibrant","energy"],["bright","red","energy","vibrant"]]},{name:"Behongo",colors:["#52c234","#061700"],colorsname:["bright green","dark green"],keywords:[["bright","green","fresh","nature"],["dark","green","earthy","natural"]]},{name:"ServQuick",colors:["#485563","#29323c"],colorsname:["dark gray","dark gray"],keywords:[["dark","gray","serious","calm"],["dark","gray","serious","calm"]]},{name:"Friday",colors:["#83a4d4","#b6fbff"],colorsname:["light blue","light cyan"],keywords:[["light","blue","cool","refreshing"],["light","cyan","fresh","calm"]]},{name:"Martini",colors:["#FDFC47","#24FE41"],colorsname:["bright yellow","bright green"],keywords:[["bright","yellow","cheerful","sunny"],["bright","green","fresh","nature"]]},{name:"Metallic Toad",colors:["#abbaab","#ffffff"],colorsname:["light gray","white"],keywords:[["light","gray","neutral","calm"],["light","white","pure","clean"]]},{name:"Between The Clouds",colors:["#73C8A9","#373B44"],colorsname:["light teal","dark gray"],keywords:[["light","teal","fresh","cool"],["dark","gray","serious","calm"]]},{name:"Crazy Orange I",colors:["#D38312","#A83279"],colorsname:["bright orange","dark pink"],keywords:[["bright","orange","vibrant","energy"],["dark","pink","romantic","soft"]]},{name:"Hersheys",colors:["#1e130c","#9a8478"],colorsname:["dark brown","light brown"],keywords:[["dark","brown","earthy","natural"],["light","brown","warm","earthy"]]},{name:"Talking To Mice Elf",colors:["#948E99","#2E1437"],colorsname:["dark gray","dark purple"],keywords:[["dark","gray","serious","calm"],["dark","purple","mystery","royalty"]]},{name:"Purple Bliss",colors:["#360033","#0b8793"],colorsname:["dark purple","dark teal"],keywords:[["dark","purple","mystery","royalty"],["dark","teal","depth","cool"]]},{name:"Predawn",colors:["#FFA17F","#00223E"],colorsname:["light orange","dark blue"],keywords:[["light","orange","warm","soft"],["dark","blue","depth","serious"]]},{name:"Endless River",colors:["#43cea2","#185a9d"],colorsname:["bright teal","dark blue"],keywords:[["bright","teal","fresh","cool"],["dark","blue","depth","serious"]]},{name:"Pastel Orange at the Sun",colors:["#ffb347","#ffcc33"],colorsname:["light orange","light yellow"],keywords:[["light","orange","warm","soft"],["light","yellow","cheerful","sunny"]]},{name:"Twitch",colors:["#6441A5","#2a0845"],colorsname:["dark purple","dark purple"],keywords:[["dark","purple","mystery","royalty"],["dark","purple","mystery","royalty"]]},{name:"Atlas",colors:["#FEAC5E","#C779D0","#4BC0C8"],colorsname:["light orange","light purple","light blue"],keywords:[["light","orange","warm","soft"],["light","purple","romantic","soft"],["light","blue","cool","refreshing"]]},{name:"Instagram",colors:["#833ab4","#fd1d1d","#fcb045"],colorsname:["dark purple","bright red","light orange"],keywords:[["dark","purple","mystery","royalty"],["bright","red","energy","vibrant"],["light","orange","warm","soft"]]},{name:"Flickr",colors:["#ff0084","#33001b"],colorsname:["bright pink","dark red"],keywords:[["bright","pink","romantic","soft"],["dark","red","energy","vibrant"]]},{name:"Vine",colors:["#00bf8f","#001510"],colorsname:["bright teal","black"],keywords:[["bright","teal","fresh","cool"],["dark","black","depth","serious"]]},{name:"Turquoise flow",colors:["#136a8a","#267871"],colorsname:["dark cyan","dark teal"],keywords:[["dark","cyan","cool","refreshing"],["dark","teal","depth","cool"]]},{name:"Portrait",colors:["#8e9eab","#eef2f3"],colorsname:["dark gray","light gray"],keywords:[["dark","gray","serious","calm"],["light","gray","neutral","calm"]]},{name:"Virgin America",colors:["#7b4397","#dc2430"],colorsname:["dark purple","bright red"],keywords:[["dark","purple","mystery","royalty"],["bright","red","energy","vibrant"]]},{name:"Koko Caramel",colors:["#D1913C","#FFD194"],colorsname:["dark brown","light beige"],keywords:[["dark","brown","earthy","natural"],["light","beige","neutral","warm"]]},{name:"Fresh Turboscent",colors:["#F1F2B5","#135058"],colorsname:["light yellow","dark teal"],keywords:[["light","yellow","cheerful","sunny"],["dark","teal","depth","cool"]]},{name:"Green to dark",colors:["#6A9113","#141517"],colorsname:["bright green","dark gray"],keywords:[["bright","green","fresh","nature"],["dark","gray","serious","calm"]]},{name:"Ukraine",colors:["#004FF9","#FFF94C"],colorsname:["bright blue","bright yellow"],keywords:[["bright","blue","trust","cool"],["bright","yellow","cheerful","sunny"]]},{name:"Curiosity blue",colors:["#525252","#3d72b4"],colorsname:["dark gray","dark blue"],keywords:[["dark","gray","serious","calm"],["dark","blue","depth","cool"]]},{name:"Dark Knight",colors:["#BA8B02","#181818"],colorsname:["gold","black"],keywords:[["gold","warm","rich","luxury"],["dark","black","depth","serious"]]},{name:"Piglet",colors:["#ee9ca7","#ffdde1"],colorsname:["light pink","light pink"],keywords:[["light","pink","romantic","soft"],["light","pink","romantic","soft"]]},{name:"Lizard",colors:["#304352","#d7d2cc"],colorsname:["dark teal","light gray"],keywords:[["dark","teal","depth","cool"],["light","gray","neutral","calm"]]},{name:"Sage Persuasion",colors:["#CCCCB2","#757519"],colorsname:["light olive","dark olive"],keywords:[["light","olive","fresh","nature"],["dark","olive","earthy","natural"]]},{name:"Between Night and Day",colors:["#2c3e50","#3498db"],colorsname:["dark gray","bright blue"],keywords:[["dark","gray","serious","calm"],["bright","blue","trust","calm"]]},{name:"Timber",colors:["#fc00ff","#00dbde"],colorsname:["bright purple","light blue"],keywords:[["bright","purple","romantic","mystery"],["light","blue","cool","refreshing"]]},{name:"Passion",colors:["#e53935","#e35d5b"],colorsname:["bright red","light red"],keywords:[["bright","red","energy","vibrant"],["light","red","warm","soft"]]},{name:"Clear Sky",colors:["#005C97","#363795"],colorsname:["dark blue","dark blue"],keywords:[["dark","blue","depth","serious"],["dark","blue","depth","serious"]]},{name:"Master Card",colors:["#f46b45","#eea849"],colorsname:["bright orange","bright yellow"],keywords:[["bright","orange","vibrant","energy"],["bright","yellow","cheerful","sunny"]]},{name:"Back To Earth",colors:["#00C9FF","#92FE9D"],colorsname:["bright cyan","light green"],keywords:[["bright","cyan","fresh","cool"],["light","green","fresh","nature"]]},{name:"Deep Purple",colors:["#673AB7","#512DA8"],colorsname:["dark purple","dark purple"],keywords:[["dark","purple","mystery","royalty"],["dark","purple","mystery","royalty"]]},{name:"Little Leaf",colors:["#76b852","#8DC26F"],colorsname:["bright green","light green"],keywords:[["bright","green","fresh","nature"],["light","green","fresh","nature"]]},{name:"Netflix",colors:["#8E0E00","#1F1C18"],colorsname:["bright red","black"],keywords:[["bright","red","energy","vibrant"],["dark","black","depth","serious"]]},{name:"Light Orange",colors:["#FFB75E","#ED8F03"],colorsname:["bright orange","dark orange"],keywords:[["bright","orange","warm","soft"],["dark","orange","warm","soft"]]},{name:"Green and Blue",colors:["#c2e59c","#64b3f4"],colorsname:["light green","light blue"],keywords:[["light","green","fresh","nature"],["light","blue","cool","refreshing"]]},{name:"Poncho",colors:["#403A3E","#BE5869"],colorsname:["dark gray","light pink"],keywords:[["dark","gray","serious","calm"],["light","pink","romantic","soft"]]},{name:"Back to the Future",colors:["#C02425","#F0CB35"],colorsname:["bright red","light yellow"],keywords:[["bright","red","energy","vibrant"],["light","yellow","cheerful","sunny"]]},{name:"Blush",colors:["#B24592","#F15F79"],colorsname:["dark pink","light pink"],keywords:[["dark","pink","romantic","soft"],["light","pink","cheerful","soft"]]},{name:"Inbox",colors:["#457fca","#5691c8"],colorsname:["bright blue","bright blue"],keywords:[["bright","blue","trust","cool"],["bright","blue","trust","cool"]]},{name:"Purplin",colors:["#6a3093","#a044ff"],colorsname:["dark purple","bright purple"],keywords:[["dark","purple","mystery","royalty"],["bright","purple","romantic","soft"]]},{name:"Pale Wood",colors:["#eacda3","#d6ae7b"],colorsname:["light beige","light brown"],keywords:[["light","beige","neutral","warm"],["light","brown","warm","earthy"]]},{name:"Haikus",colors:["#fd746c","#ff9068"],colorsname:["light pink","light orange"],keywords:[["light","pink","romantic","soft"],["light","orange","warm","soft"]]},{name:"Pizelex",colors:["#114357","#F29492"],colorsname:["dark teal","light pink"],keywords:[["dark","teal","depth","cool"],["light","pink","romantic","soft"]]},{name:"Joomla",colors:["#1e3c72","#2a5298"],colorsname:["dark blue","dark blue"],keywords:[["dark","blue","depth","serious"],["dark","blue","depth","serious"]]},{name:"Christmas",colors:["#2F7336","#AA3A38"],colorsname:["dark green","dark red"],keywords:[["dark","green","nature","fresh"],["dark","red","energy","vibrant"]]},{name:"Minnesota Vikings",colors:["#5614B0","#DBD65C"],colorsname:["dark purple","light yellow"],keywords:[["dark","purple","mystery","royalty"],["light","yellow","cheerful","sunny"]]},{name:"Miami Dolphins",colors:["#4DA0B0","#D39D38"],colorsname:["dark teal","light orange"],keywords:[["dark","teal","depth","cool"],["light","orange","warm","soft"]]},{name:"Forest",colors:["#5A3F37","#2C7744"],colorsname:["dark brown","dark green"],keywords:[["dark","brown","earthy","natural"],["dark","green","nature","fresh"]]},{name:"Nighthawk",colors:["#2980b9","#2c3e50"],colorsname:["dark blue","dark gray"],keywords:[["dark","blue","depth","cool"],["dark","gray","serious","calm"]]},{name:"Superman",colors:["#0099F7","#F11712"],colorsname:["bright blue","bright red"],keywords:[["bright","blue","trust","cool"],["bright","red","energy","vibrant"]]},{name:"Suzy",colors:["#834d9b","#d04ed6"],colorsname:["dark purple","bright pink"],keywords:[["dark","purple","mystery","royalty"],["bright","pink","romantic","soft"]]},{name:"Dark Skies",colors:["#4B79A1","#283E51"],colorsname:["dark blue","dark gray"],keywords:[["dark","blue","depth","serious"],["dark","gray","serious","calm"]]},{name:"Deep Space",colors:["#000000","#434343"],colorsname:["black","dark gray"],keywords:[["dark","black","depth","serious"],["dark","gray","serious","calm"]]},{name:"Decent",colors:["#4CA1AF","#C4E0E5"],colorsname:["dark teal","light gray"],keywords:[["dark","teal","depth","cool"],["light","gray","neutral","calm"]]},{name:"Colors Of Sky",colors:["#E0EAFC","#CFDEF3"],colorsname:["light blue","light gray"],keywords:[["light","blue","cool","refreshing"],["light","gray","neutral","calm"]]},{name:"Purple White",colors:["#BA5370","#F4E2D8"],colorsname:["dark pink","light pink"],keywords:[["dark","pink","romantic","soft"],["light","pink","cheerful","soft"]]},{name:"Ali",colors:["#ff4b1f","#1fddff"],colorsname:["bright red","bright cyan"],keywords:[["bright","red","energy","vibrant"],["bright","cyan","fresh","cool"]]},{name:"Alihossein",colors:["#f7ff00","#db36a4"],colorsname:["bright yellow","bright pink"],keywords:[["bright","yellow","cheerful","sunny"],["bright","pink","romantic","soft"]]},{name:"Shahabi",colors:["#a80077","#66ff00"],colorsname:["dark pink","bright green"],keywords:[["dark","pink","romantic","soft"],["bright","green","fresh","nature"]]},{name:"Red Ocean",colors:["#1D4350","#A43931"],colorsname:["dark blue","dark red"],keywords:[["dark","blue","depth","cool"],["dark","red","energy","vibrant"]]},{name:"Tranquil",colors:["#EECDA3","#EF629F"],colorsname:["light beige","light pink"],keywords:[["light","beige","neutral","warm"],["light","pink","romantic","soft"]]},{name:"Transfile",colors:["#16BFFD","#CB3066"],colorsname:["bright cyan","dark pink"],keywords:[["bright","cyan","fresh","cool"],["dark","pink","bold","vibrant"]]},{name:"Sylvia",colors:["#ff4b1f","#ff9068"],colorsname:["bright red","light orange"],keywords:[["bright","red","energy","vibrant"],["light","orange","warm","soft"]]},{name:"Sweet Morning",colors:["#FF5F6D","#FFC371"],colorsname:["light pink","light yellow"],keywords:[["light","pink","romantic","soft"],["light","yellow","cheerful","sunny"]]},{name:"Politics",colors:["#2196f3","#f44336"],colorsname:["bright blue","bright red"],keywords:[["bright","blue","trust","calm"],["bright","red","energy","vibrant"]]},{name:"Bright Vault",colors:["#00d2ff","#928DAB"],colorsname:["bright cyan","light gray"],keywords:[["bright","cyan","fresh","cool"],["light","gray","neutral","calm"]]},{name:"Solid Vault",colors:["#3a7bd5","#3a6073"],colorsname:["bright blue","dark blue"],keywords:[["bright","blue","trust","calm"],["dark","blue","depth","serious"]]},{name:"Sunset",colors:["#0B486B","#F56217"],colorsname:["dark blue","bright orange"],keywords:[["dark","blue","depth","cool"],["bright","orange","warm","energy"]]},{name:"Grapefruit Sunset",colors:["#e96443","#904e95"],colorsname:["bright orange","dark purple"],keywords:[["bright","orange","vibrant","energy"],["dark","purple","mystery","royalty"]]},{name:"Deep Sea Space",colors:["#2C3E50","#4CA1AF"],colorsname:["dark blue","dark teal"],keywords:[["dark","blue","depth","cool"],["dark","teal","depth","cool"]]},{name:"Dusk",colors:["#2C3E50","#FD746C"],colorsname:["dark gray","light pink"],keywords:[["dark","gray","serious","calm"],["light","pink","romantic","soft"]]},{name:"Minimal Red",colors:["#F00000","#DC281E"],colorsname:["bright red","dark red"],keywords:[["bright","red","energy","vibrant"],["dark","red","serious","bold"]]},{name:"Royal",colors:["#141E30","#243B55"],colorsname:["dark blue","dark blue"],keywords:[["dark","blue","depth","serious"],["dark","blue","depth","serious"]]},{name:"Mauve",colors:["#42275a","#734b6d"],colorsname:["dark purple","dark pink"],keywords:[["dark","purple","mystery","royalty"],["dark","pink","romantic","soft"]]},[{name:"Forest",colors:["#5A3F37","#2C7744"],colorsname:["dark brown","dark green"],keywords:[["dark","brown","earthy","natural"],["dark","green","nature","fresh"]]},{name:"Nighthawk",colors:["#2980b9","#2c3e50"],colorsname:["dark blue","dark gray"],keywords:[["dark","blue","depth","cool"],["dark","gray","serious","calm"]]},{name:"Superman",colors:["#0099F7","#F11712"],colorsname:["bright blue","bright red"],keywords:[["bright","blue","trust","cool"],["bright","red","energy","vibrant"]]},{name:"Suzy",colors:["#834d9b","#d04ed6"],colorsname:["dark purple","bright pink"],keywords:[["dark","purple","mystery","royalty"],["bright","pink","romantic","soft"]]},{name:"Dark Skies",colors:["#4B79A1","#283E51"],colorsname:["dark blue","dark gray"],keywords:[["dark","blue","depth","serious"],["dark","gray","serious","calm"]]},{name:"Deep Space",colors:["#000000","#434343"],colorsname:["black","dark gray"],keywords:[["dark","black","depth","serious"],["dark","gray","serious","calm"]]},{name:"Decent",colors:["#4CA1AF","#C4E0E5"],colorsname:["dark teal","light gray"],keywords:[["dark","teal","depth","cool"],["light","gray","neutral","calm"]]},{name:"Colors Of Sky",colors:["#E0EAFC","#CFDEF3"],colorsname:["light blue","light gray"],keywords:[["light","blue","cool","refreshing"],["light","gray","neutral","calm"]]},{name:"Purple White",colors:["#BA5370","#F4E2D8"],colorsname:["dark pink","light pink"],keywords:[["dark","pink","romantic","soft"],["light","pink","cheerful","soft"]]},{name:"Ali",colors:["#ff4b1f","#1fddff"],colorsname:["bright red","bright cyan"],keywords:[["bright","red","energy","vibrant"],["bright","cyan","fresh","cool"]]},{name:"Alihossein",colors:["#f7ff00","#db36a4"],colorsname:["bright yellow","bright pink"],keywords:[["bright","yellow","cheerful","sunny"],["bright","pink","romantic","soft"]]},{name:"Shahabi",colors:["#a80077","#66ff00"],colorsname:["dark pink","bright green"],keywords:[["dark","pink","romantic","soft"],["bright","green","fresh","nature"]]},{name:"Red Ocean",colors:["#1D4350","#A43931"],colorsname:["dark blue","dark red"],keywords:[["dark","blue","depth","cool"],["dark","red","energy","vibrant"]]},{name:"Tranquil",colors:["#EECDA3","#EF629F"],colorsname:["light beige","light pink"],keywords:[["light","beige","neutral","warm"],["light","pink","romantic","soft"]]},{name:"Transfile",colors:["#16BFFD","#CB3066"],colorsname:["bright cyan","dark pink"],keywords:[["bright","cyan","fresh","cool"],["dark","pink","bold","vibrant"]]},{name:"Sylvia",colors:["#ff4b1f","#ff9068"],colorsname:["bright red","light orange"],keywords:[["bright","red","energy","vibrant"],["light","orange","warm","soft"]]},{name:"Sweet Morning",colors:["#FF5F6D","#FFC371"],colorsname:["light pink","light yellow"],keywords:[["light","pink","romantic","soft"],["light","yellow","cheerful","sunny"]]},{name:"Politics",colors:["#2196f3","#f44336"],colorsname:["bright blue","bright red"],keywords:[["bright","blue","trust","calm"],["bright","red","energy","vibrant"]]},{name:"Bright Vault",colors:["#00d2ff","#928DAB"],colorsname:["bright cyan","light gray"],keywords:[["bright","cyan","fresh","cool"],["light","gray","neutral","calm"]]},{name:"Solid Vault",colors:["#3a7bd5","#3a6073"],colorsname:["bright blue","dark blue"],keywords:[["bright","blue","trust","calm"],["dark","blue","depth","serious"]]},{name:"Sunset",colors:["#0B486B","#F56217"],colorsname:["dark blue","bright orange"],keywords:[["dark","blue","depth","cool"],["bright","orange","warm","energy"]]},{name:"Grapefruit Sunset",colors:["#e96443","#904e95"],colorsname:["bright orange","dark purple"],keywords:[["bright","orange","vibrant","energy"],["dark","purple","mystery","royalty"]]},{name:"Deep Sea Space",colors:["#2C3E50","#4CA1AF"],colorsname:["dark blue","dark teal"],keywords:[["dark","blue","depth","cool"],["dark","teal","depth","cool"]]},{name:"Dusk",colors:["#2C3E50","#FD746C"],colorsname:["dark gray","light pink"],keywords:[["dark","gray","serious","calm"],["light","pink","romantic","soft"]]},{name:"Minimal Red",colors:["#F00000","#DC281E"],colorsname:["bright red","dark red"],keywords:[["bright","red","energy","vibrant"],["dark","red","serious","bold"]]},{name:"Royal",colors:["#141E30","#243B55"],colorsname:["dark blue","dark blue"],keywords:[["dark","blue","depth","serious"],["dark","blue","depth","serious"]]},{name:"Mauve",colors:["#42275a","#734b6d"],colorsname:["dark purple","dark pink"],keywords:[["dark","purple","mystery","royalty"],["dark","pink","romantic","soft"]]},{name:"Frost",colors:["#000428","#004e92"],colorsname:["black","dark blue"],keywords:[["dark","black","depth","serious"],["dark","blue","depth","cool"]]},{name:"Lush",colors:["#56ab2f","#a8e063"],colorsname:["bright green","light green"],keywords:[["bright","green","fresh","nature"],["light","green","fresh","nature"]]},{name:"Firewatch",colors:["#cb2d3e","#ef473a"],colorsname:["bright red","light red"],keywords:[["bright","red","energy","vibrant"],["light","red","warm","soft"]]},{name:"Sherbert",colors:["#f79d00","#64f38c"],colorsname:["bright orange","light green"],keywords:[["bright","orange","vibrant","energy"],["light","green","fresh","nature"]]},{name:"Blood Red",colors:["#f85032","#e73827"],colorsname:["bright red","dark red"],keywords:[["bright","red","energy","vibrant"],["dark","red","serious","bold"]]},{name:"Sun on the Horizon",colors:["#fceabb","#f8b500"],colorsname:["light yellow","light orange"],keywords:[["light","yellow","cheerful","sunny"],["light","orange","warm","soft"]]},{name:"IIIT Delhi",colors:["#808080","#3fada8"],colorsname:["gray","teal"],keywords:[["gray","neutral","calm"],["teal","fresh","vibrant"]]},{name:"Jupiter",colors:["#ffd89b","#19547b"],colorsname:["light yellow","dark blue"],keywords:[["light","yellow","cheerful","sunny"],["dark","blue","depth","serious"]]},{name:"50 Shades of Grey",colors:["#bdc3c7","#2c3e50"],colorsname:["light gray","dark gray"],keywords:[["light","gray","neutral","calm"],["dark","gray","serious","calm"]]},{name:"Dania",colors:["#BE93C5","#7BC6CC"],colorsname:["light pink","light teal"],keywords:[["light","pink","romantic","soft"],["light","teal","fresh","cool"]]},{name:"Limeade",colors:["#A1FFCE","#FAFFD1"],colorsname:["bright teal","light yellow"],keywords:[["bright","teal","fresh","cool"],["light","yellow","cheerful","sunny"]]},{name:"Disco",colors:["#4ECDC4","#556270"],colorsname:["bright teal","dark gray"],keywords:[["bright","teal","fresh","cool"],["dark","gray","serious","calm"]]},{name:"Love Couple",colors:["#3a6186","#89253e"],colorsname:["dark teal","dark pink"],keywords:[["dark","teal","depth","cool"],["dark","pink","romantic","soft"]]},{name:"Azure Pop",colors:["#ef32d9","#89fffd"],colorsname:["bright purple","bright cyan"],keywords:[["bright","purple","romantic","mystery"],["bright","cyan","fresh","cool"]]},{name:"Nepal",colors:["#de6161","#2657eb"],colorsname:["bright red","bright blue"],keywords:[["bright","red","energy","vibrant"],["bright","blue","trust","cool"]]},{name:"Cosmic Fusion",colors:["#ff00cc","#333399"],colorsname:["bright pink","dark blue"],keywords:[["bright","pink","romantic","soft"],["dark","blue","depth","serious"]]},{name:"Snapchat",colors:["#fffc00","#ffffff"],colorsname:["bright yellow","white"],keywords:[["bright","yellow","cheerful","sunny"],["light","white","pure","clean"]]},{name:"Ed's Sunset Gradient",colors:["#ff7e5f","#feb47b"],colorsname:["light orange","light pink"],keywords:[["light","orange","warm","soft"],["light","pink","romantic","soft"]]},{name:"Brady Brady Fun Fun",colors:["#00c3ff","#ffff1c"],colorsname:["bright cyan","bright yellow"],keywords:[["bright","cyan","fresh","cool"],["bright","yellow","cheerful","sunny"]]},{name:"Black Rosé",colors:["#f4c4f3","#fc67fa"],colorsname:["light pink","bright pink"],keywords:[["light","pink","romantic","soft"],["bright","pink","romantic","soft"]]},{name:"80's Purple",colors:["#41295a","#2F0743"],colorsname:["dark purple","dark purple"],keywords:[["dark","purple","mystery","royalty"],["dark","purple","mystery","royalty"]]},{name:"Radar",colors:["#A770EF","#CF8BF3","#FDB99B"],colorsname:["light purple","light pink","light yellow"],keywords:[["light","purple","romantic","soft"],["light","pink","cheerful","soft"],["light","yellow","cheerful","sunny"]]},{name:"Ibiza Sunset",colors:["#ee0979","#ff6a00"],colorsname:["bright pink","bright orange"],keywords:[["bright","pink","romantic","soft"],["bright","orange","vibrant","energy"]]},{name:"Dawn",colors:["#F3904F","#3B4371"],colorsname:["light orange","dark blue"],keywords:[["light","orange","warm","soft"],["dark","blue","depth","serious"]]},{name:"Mild",colors:["#67B26F","#4ca2cd"],colorsname:["bright green","light blue"],keywords:[["bright","green","fresh","nature"],["light","blue","cool","refreshing"]]},{name:"Vice City",colors:["#3494E6","#EC6EAD"],colorsname:["bright blue","light pink"],keywords:[["bright","blue","trust","cool"],["light","pink","romantic","soft"]]},{name:"Jaipur",colors:["#DBE6F6","#C5796D"],colorsname:["light blue","dark pink"],keywords:[["light","blue","cool","refreshing"],["dark","pink","romantic","soft"]]},{name:"Jodhpur",colors:["#9CECFB","#65C7F7","#0052D4"],colorsname:["light cyan","light blue","dark blue"],keywords:[["light","cyan","fresh","cool"],["light","blue","trust","calm"],["dark","blue","depth","serious"]]},{name:"Cocoaa Ice",colors:["#c0c0aa","#1cefff"],colorsname:["light gray","bright cyan"],keywords:[["light","gray","neutral","calm"],["bright","cyan","fresh","cool"]]},{name:"EasyMed",colors:["#DCE35B","#45B649"],colorsname:["light yellow","dark green"],keywords:[["light","yellow","cheerful","sunny"],["dark","green","nature","fresh"]]},{name:"Rose Colored Lenses",colors:["#E8CBC0","#636FA4"],colorsname:["light pink","dark purple"],keywords:[["light","pink","romantic","soft"],["dark","purple","mystery","royalty"]]},{name:"What lies Beyond",colors:["#F0F2F0","#000C40"],colorsname:["light gray","dark blue"],keywords:[["light","gray","neutral","calm"],["dark","blue","depth","serious"]]},{name:"Roseanna",colors:["#FFAFBD","#ffc3a0"],colorsname:["light pink","light beige"],keywords:[["light","pink","romantic","soft"],["light","beige","neutral","warm"]]},{name:"Honey Dew",colors:["#43C6AC","#F8FFAE"],colorsname:["bright teal","light yellow"],keywords:[["bright","teal","fresh","cool"],["light","yellow","cheerful","sunny"]]},{name:"Under the Lake",colors:["#093028","#237A57"],colorsname:["dark green","dark teal"],keywords:[["dark","green","nature","fresh"],["dark","teal","depth","cool"]]},{name:"The Blue Lagoon",colors:["#43C6AC","#191654"],colorsname:["bright teal","dark blue"],keywords:[["bright","teal","fresh","cool"],["dark","blue","depth","serious"]]},{name:"Can You Feel The Love Tonight",colors:["#4568DC","#B06AB3"],colorsname:["dark blue","light purple"],keywords:[["dark","blue","depth","serious"],["light","purple","romantic","soft"]]},{name:"Very Blue",colors:["#0575E6","#021B79"],colorsname:["bright blue","dark blue"],keywords:[["bright","blue","trust","cool"],["dark","blue","depth","serious"]]},{name:"Love and Liberty",colors:["#200122","#6f0000"],colorsname:["dark purple","dark red"],keywords:[["dark","purple","mystery","royalty"],["dark","red","energy","vibrant"]]},{name:"Orca",colors:["#44A08D","#093637"],colorsname:["dark teal","dark cyan"],keywords:[["dark","teal","depth","cool"],["dark","cyan","cool","refreshing"]]},{name:"Venice",colors:["#6190E8","#A7BFE8"],colorsname:["light blue","light gray"],keywords:[["light","blue","cool","refreshing"],["light","gray","neutral","calm"]]},{name:"Pacific Dream",colors:["#34e89e","#0f3443"],colorsname:["bright teal","dark teal"],keywords:[["bright","teal","fresh","cool"],["dark","teal","depth","cool"]]},{name:"Learning and Leading",colors:["#F7971E","#FFD200"],colorsname:["bright orange","bright yellow"],keywords:[["bright","orange","vibrant","energy"],["bright","yellow","cheerful","sunny"]]},{name:"Celestial",colors:["#C33764","#1D2671"],colorsname:["dark pink","dark blue"],keywords:[["dark","pink","romantic","soft"],["dark","blue","depth","serious"]]},{name:"Purplepine",colors:["#20002c","#cbb4d4"],colorsname:["dark purple","light purple"],keywords:[["dark","purple","mystery","royalty"],["light","purple","romantic","soft"]]},{name:"Sha la la",colors:["#D66D75","#E29587"],colorsname:["dark pink","light pink"],keywords:[["dark","pink","romantic","soft"],["light","pink","cheerful","soft"]]},{name:"Mini",colors:["#30E8BF","#FF8235"],colorsname:["bright teal","bright orange"],keywords:[["bright","teal","fresh","cool"],["bright","orange","vibrant","energy"]]},{name:"Maldives",colors:["#B2FEFA","#0ED2F7"],colorsname:["light cyan","bright cyan"],keywords:[["light","cyan","fresh","cool"],["bright","cyan","refreshing","calm"]]},{name:"Cinnamint",colors:["#4AC29A","#BDFFF3"],colorsname:["bright teal","light green"],keywords:[["bright","teal","fresh","cool"],["light","green","fresh","nature"]]},{name:"Html",colors:["#E44D26","#F16529"],colorsname:["bright red","orange"],keywords:[["bright","red","energy","vibrant"],["orange","warm","cheerful","soft"]]},{name:"Coal",colors:["#EB5757","#000000"],colorsname:["bright red","black"],keywords:[["bright","red","energy","vibrant"],["dark","black","depth","serious"]]},{name:"Sunkist",colors:["#F2994A","#F2C94C"],colorsname:["bright orange","light yellow"],keywords:[["bright","orange","vibrant","energy"],["light","yellow","cheerful","sunny"]]},{name:"Blue Skies",colors:["#56CCF2","#2F80ED"],colorsname:["light blue","bright blue"],keywords:[["light","blue","cool","refreshing"],["bright","blue","trust","calm"]]},{name:"Chitty Chitty Bang Bang",colors:["#007991","#78ffd6"],colorsname:["bright teal","light cyan"],keywords:[["bright","teal","fresh","cool"],["light","cyan","refreshing","calm"]]},{name:"Visions of Grandeur",colors:["#000046","#1CB5E0"],colorsname:["dark blue","bright cyan"],keywords:[["dark","blue","depth","serious"],["bright","cyan","fresh","cool"]]},{name:"Crystal Clear",colors:["#159957","#155799"],colorsname:["dark teal","dark blue"],keywords:[["dark","teal","depth","cool"],["dark","blue","depth","cool"]]},{name:"Mello",colors:["#c0392b","#8e44ad"],colorsname:["bright red","dark purple"],keywords:[["bright","red","energy","vibrant"],["dark","purple","mystery","royalty"]]},{name:"Compare Now",colors:["#EF3B36","#FFFFFF"],colorsname:["bright red","white"],keywords:[["bright","red","energy","vibrant"],["light","white","pure","clean"]]},{name:"Meridian",colors:["#283c86","#45a247"],colorsname:["dark blue","dark green"],keywords:[["dark","blue","depth","serious"],["dark","green","nature","fresh"]]},{name:"Relay",colors:["#3A1C71","#D76D77","#FFAF7B"],colorsname:["dark purple","light pink","light peach"],keywords:[["dark","purple","mystery","royalty"],["light","pink","romantic","soft"],["light","peach","warm","soft"]]},{name:"Alive",colors:["#CB356B","#BD3F32"],colorsname:["dark pink","dark red"],keywords:[["dark","pink","romantic","soft"],["dark","red","energy","vibrant"]]},{name:"Scooter",colors:["#36D1DC","#5B86E5"],colorsname:["bright teal","bright blue"],keywords:[["bright","teal","fresh","cool"],["bright","blue","trust","calm"]]},{name:"Terminal",colors:["#000000","#0f9b0f"],colorsname:["black","dark green"],keywords:[["dark","black","depth","serious"],["dark","green","nature","fresh"]]},{name:"Telegram",colors:["#1c92d2","#f2fcfe"],colorsname:["bright cyan","light cyan"],keywords:[["bright","cyan","fresh","cool"],["light","cyan","refreshing","calm"]]},{name:"Crimson Tide",colors:["#642B73","#C6426E"],colorsname:["dark purple","dark pink"],keywords:[["dark","purple","mystery","royalty"],["dark","pink","romantic","soft"]]},{name:"Socialive",colors:["#06beb6","#48b1bf"],colorsname:["bright teal","dark teal"],keywords:[["bright","teal","fresh","cool"],["dark","teal","depth","cool"]]},{name:"Subu",colors:["#0cebeb","#20e3b2","#29ffc6"],colorsname:["bright cyan","light teal"],keywords:[["bright","cyan","fresh","cool"],["light","teal","fresh","nature"]]},{name:"Broken Hearts",colors:["#d9a7c7","#fffcdc"],colorsname:["light pink","light beige"],keywords:[["light","pink","romantic","soft"],["light","beige","neutral","warm"]]},{name:"Kimoby Is The New Blue",colors:["#396afc","#2948ff"],colorsname:["bright blue","dark blue"],keywords:[["bright","blue","trust","cool"],["dark","blue","depth","serious"]]},{name:"Dull",colors:["#C9D6FF","#E2E2E2"],colorsname:["light blue","light gray"],keywords:[["light","blue","calm","soft"],["light","gray","neutral","calm"]]},{name:"Purpink",colors:["#7F00FF","#E100FF"],colorsname:["bright purple","bright pink"],keywords:[["bright","purple","romantic","mystery"],["bright","pink","romantic","soft"]]},{name:"Orange Coral",colors:["#ff9966","#ff5e62"],colorsname:["bright orange","light red"],keywords:[["bright","orange","vibrant","energy"],["light","red","warm","soft"]]},{name:"Summer",colors:["#22c1c3","#fdbb2d"],colorsname:["bright teal","light orange"],keywords:[["bright","teal","fresh","cool"],["light","orange","warm","soft"]]},{name:"King Yna",colors:["#1a2a6c","#b21f1f","#fdbb2d"],colorsname:["dark blue","dark red","light orange"],keywords:[["dark","blue","depth","serious"],["dark","red","energy","vibrant"],["light","orange","warm","soft"]]},{name:"Velvet Sun",colors:["#e1eec3","#f05053"],colorsname:["light beige","bright red"],keywords:[["light","beige","neutral","warm"],["bright","red","energy","vibrant"]]},{name:"Zinc",colors:["#ADA996","#F2F2F2","#DBDBDB","#EAEAEA"],colorsname:["light gray","light gray"],keywords:[["light","gray","neutral","calm"],["light","gray","neutral","calm"]]},{name:"Hydrogen",colors:["#667db6","#0082c8","#0082c8","#667db6"],colorsname:["dark blue","bright blue"],keywords:[["dark","blue","depth","cool"],["bright","blue","trust","calm"]]},{name:"Argon",colors:["#03001e","#7303c0","#ec38bc","#fdeff9"],colorsname:["dark purple","bright purple"],keywords:[["dark","purple","mystery","royalty"],["bright","purple","romantic","soft"]]},{name:"Lithium",colors:["#6D6027","#D3CBB8"],colorsname:["dark brown","light beige"],keywords:[["dark","brown","earthy","natural"],["light","beige","neutral","warm"]]},{name:"Digital Water",colors:["#74ebd5","#ACB6E5"],colorsname:["light cyan","light purple"],keywords:[["light","cyan","fresh","cool"],["light","purple","romantic","soft"]]},{name:"Orange Fun",colors:["#fc4a1a","#f7b733"],colorsname:["bright orange","light yellow"],keywords:[["bright","orange","vibrant","energy"],["light","yellow","cheerful","sunny"]]},{name:"Rainbow Blue",colors:["#00F260","#0575E6"],colorsname:["bright teal","bright blue"],keywords:[["bright","teal","fresh","cool"],["bright","blue","trust","calm"]]},{name:"Pink Flavour",colors:["#800080","#ffc0cb"],colorsname:["dark purple","light pink"],keywords:[["dark","purple","mystery","royalty"],["light","pink","romantic","soft"]]},{name:"Sulphur",colors:["#CAC531","#F3F9A7"],colorsname:["light olive","light yellow"],keywords:[["light","olive","fresh","nature"],["light","yellow","cheerful","sunny"]]},{name:"Selenium",colors:["#3C3B3F","#605C3C"],colorsname:["dark gray","dark green"],keywords:[["dark","gray","serious","calm"],["dark","green","nature","fresh"]]},{name:"Delicate",colors:["#D3CCE3","#E9E4F0"],colorsname:["light gray","light purple"],keywords:[["light","gray","neutral","calm"],["light","purple","romantic","soft"]]},{name:"Ohhappiness",colors:["#00b09b","#96c93d"],colorsname:["bright teal","light green"],keywords:[["bright","teal","fresh","cool"],["light","green","fresh","nature"]]},{name:"Lawrencium",colors:["#0f0c29","#302b63","#24243e"],colorsname:["dark purple","dark blue","black"],keywords:[["dark","purple","mystery","royalty"],["dark","blue","depth","serious"],["black","dark","depth","serious"]]},{name:"Relaxing red",colors:["#fffbd5","#b20a2c"],colorsname:["light yellow","dark red"],keywords:[["light","yellow","cheerful","sunny"],["dark","red","energy","vibrant"]]},{name:"Taran Tado",colors:["#23074d","#cc5333"],colorsname:["dark purple","dark red"],keywords:[["dark","purple","mystery","royalty"],["dark","red","energy","vibrant"]]},{name:"Bighead",colors:["#c94b4b","#4b134f"],colorsname:["bright red","dark purple"],keywords:[["bright","red","energy","vibrant"],["dark","purple","mystery","royalty"]]},{name:"Sublime Vivid",colors:["#FC466B","#3F5EFB"],colorsname:["bright pink","bright blue"],keywords:[["bright","pink","romantic","soft"],["bright","blue","trust","calm"]]},{name:"Sublime Light",colors:["#FC5C7D","#6A82FB"],colorsname:["light pink","light blue"],keywords:[["light","pink","romantic","soft"],["light","blue","cool","refreshing"]]},{name:"Pun Yeta",colors:["#108dc7","#ef8e38"],colorsname:["bright blue","light orange"],keywords:[["bright","blue","trust","cool"],["light","orange","warm","soft"]]},{name:"Quepal",colors:["#11998e","#38ef7d"],colorsname:["bright teal","bright green"],keywords:[["bright","teal","fresh","cool"],["bright","green","fresh","nature"]]},{name:"Sand to Blue",colors:["#3E5151","#DECBA4"],colorsname:["dark teal","light beige"],keywords:[["dark","teal","depth","cool"],["light","beige","neutral","warm"]]},{name:"Wedding Day Blues",colors:["#40E0D0","#FF8C00","#FF0080"],colorsname:["bright cyan","bright orange","bright pink"],keywords:[["bright","cyan","fresh","cool"],["bright","orange","vibrant","energy"],["bright","pink","romantic","soft"]]},{name:"Shifter",colors:["#bc4e9c","#f80759"],colorsname:["dark pink","bright pink"],keywords:[["dark","pink","romantic","soft"],["bright","pink","cheerful","soft"]]},{name:"Red Sunset",colors:["#355C7D","#6C5B7B","#C06C84"],colorsname:["dark blue","dark pink","light pink"],keywords:[["dark","blue","depth","serious"],["dark","pink","romantic","soft"],["light","pink","cheerful","soft"]]},{name:"Moon Purple",colors:["#4e54c8","#8f94fb"],colorsname:["dark blue","light purple"],keywords:[["dark","blue","depth","serious"],["light","purple","romantic","soft"]]},{name:"Pure Lust",colors:["#333333","#dd1818"],colorsname:["black","bright red"],keywords:[["dark","black","depth","serious"],["bright","red","energy","vibrant"]]},{name:"Slight Ocean View",colors:["#a8c0ff","#3f2b96"],colorsname:["light blue","dark purple"],keywords:[["light","blue","cool","refreshing"],["dark","purple","mystery","royalty"]]},{name:"eXpresso",colors:["#ad5389","#3c1053"],colorsname:["dark pink","dark purple"],keywords:[["dark","pink","romantic","soft"],["dark","purple","mystery","royalty"]]},{name:"Shifty",colors:["#636363","#a2ab58"],colorsname:["dark gray","light olive"],keywords:[["dark","gray","serious","calm"],["light","olive","fresh","nature"]]},{name:"Vanusa",colors:["#DA4453","#89216B"],colorsname:["dark pink","dark purple"],keywords:[["dark","pink","romantic","soft"],["dark","purple","mystery","royalty"]]},{name:"Evening Night",colors:["#005AA7","#FFFDE4"],colorsname:["dark blue","light beige"],keywords:[["dark","blue","depth","serious"],["light","beige","neutral","warm"]]},{name:"Magic",colors:["#59C173","#a17fe0","#5D26C1"],colorsname:["bright green","light purple","dark purple"],keywords:[["bright","green","fresh","nature"],["light","purple","romantic","soft"],["dark","purple","mystery","royalty"]]},{name:"Margo",colors:["#FFEFBA","#FFFFFF"],colorsname:["light yellow","white"],keywords:[["light","yellow","cheerful","sunny"],["light","white","pure","clean"]]},{name:"Blue Raspberry",colors:["#00B4DB","#0083B0"],colorsname:["bright cyan","dark blue"],keywords:[["bright","cyan","fresh","cool"],["dark","blue","depth","serious"]]},{name:"Citrus Peel",colors:["#FDC830","#F37335"],colorsname:["bright yellow","bright orange"],keywords:[["bright","yellow","cheerful","sunny"],["bright","orange","vibrant","energy"]]},{name:"Sin City Red",colors:["#ED213A","#93291E"],colorsname:["bright red","dark red"],keywords:[["bright","red","energy","vibrant"],["dark","red","serious","bold"]]},{name:"Rastafari",colors:["#1E9600","#FFF200","#FF0000"],colorsname:["bright green","bright yellow","bright red"],keywords:[["bright","green","fresh","nature"],["bright","yellow","cheerful","sunny"],["bright","red","energy","vibrant"]]},{name:"Summer Dog",colors:["#a8ff78","#78ffd6"],colorsname:["light green","light teal"],keywords:[["light","green","fresh","nature"],["light","teal","fresh","cool"]]},{name:"Wiretap",colors:["#8A2387","#E94057","#F27121"],colorsname:["dark pink","bright pink","bright orange"],keywords:[["dark","pink","mystery","royalty"],["bright","pink","romantic","soft"],["bright","orange","vibrant","energy"]]},{name:"Burning Orange",colors:["#FF416C","#FF4B2B"],colorsname:["bright pink","bright red"],keywords:[["bright","pink","romantic","soft"],["bright","red","energy","vibrant"]]},{name:"Ultra Voilet",colors:["#654ea3","#eaafc8"],colorsname:["dark purple","light purple"],keywords:[["dark","purple","mystery","royalty"],["light","purple","romantic","soft"]]},{name:"By Design",colors:["#009FFF","#ec2F4B"],colorsname:["bright blue","bright red"],keywords:[["bright","blue","trust","cool"],["bright","red","energy","vibrant"]]},{name:"Kyoo Tah",colors:["#544a7d","#ffd452"],colorsname:["dark blue","light yellow"],keywords:[["dark","blue","depth","serious"],["light","yellow","cheerful","sunny"]]},{name:"Kye Meh",colors:["#8360c3","#2ebf91"],colorsname:["dark purple","bright teal"],keywords:[["dark","purple","mystery","royalty"],["bright","teal","fresh","cool"]]},{name:"Kyoo Pal",colors:["#dd3e54","#6be585"],colorsname:["bright pink","bright green"],keywords:[["bright","pink","romantic","soft"],["bright","green","fresh","nature"]]},{name:"Metapolis",colors:["#659999","#f4791f"],colorsname:["dark teal","bright orange"],keywords:[["dark","teal","depth","cool"],["bright","orange","vibrant","energy"]]},{name:"Flare",colors:["#f12711","#f5af19"],colorsname:["bright red","light orange"],keywords:[["bright","red","energy","vibrant"],["light","orange","warm","soft"]]},{name:"Witching Hour",colors:["#c31432","#240b36"],colorsname:["dark red","dark purple"],keywords:[["dark","red","energy","vibrant"],["dark","purple","mystery","royalty"]]},{name:"Azur Lane",colors:["#7F7FD5","#86A8E7","#91EAE4"],colorsname:["light purple","light blue","light cyan"],keywords:[["light","purple","romantic","soft"],["light","blue","cool","refreshing"],["light","cyan","fresh","cool"]]},{name:"Neuromancer",colors:["#f953c6","#b91d73"],colorsname:["bright pink","dark pink"],keywords:[["bright","pink","romantic","soft"],["dark","pink","bold","vibrant"]]},{name:"Harvey",colors:["#1f4037","#99f2c8"],colorsname:["dark teal","light teal"],keywords:[["dark","teal","depth","cool"],["light","teal","fresh","nature"]]},{name:"Amin",colors:["#8E2DE2","#4A00E0"],colorsname:["bright purple","dark purple"],keywords:[["bright","purple","romantic","mystery"],["dark","purple","mystery","royalty"]]},{name:"Memariani",colors:["#aa4b6b","#6b6b83","#3b8d99"],colorsname:["dark pink","dark gray","dark teal"],keywords:[["dark","pink","romantic","soft"],["dark","gray","serious","calm"],["dark","teal","depth","cool"]]},{name:"Yoda",colors:["#FF0099","#493240"],colorsname:["bright pink","dark brown"],keywords:[["bright","pink","romantic","soft"],["dark","brown","earthy","natural"]]},{name:"Cool Sky",colors:["#2980B9","#6DD5FA","#FFFFFF"],colorsname:["dark blue","light blue","white"],keywords:[["dark","blue","depth","cool"],["light","blue","fresh","cool"],["light","white","pure","clean"]]},{name:"Dark Ocean",colors:["#373B44","#4286f4"],colorsname:["dark gray","bright blue"],keywords:[["dark","gray","serious","calm"],["bright","blue","trust","cool"]]},{name:"Evening Sunshine",colors:["#b92b27","#1565C0"],colorsname:["dark red","dark blue"],keywords:[["dark","red","energy","vibrant"],["dark","blue","depth","serious"]]},{name:"JShine",colors:["#12c2e9","#c471ed","#f64f59"],colorsname:["bright cyan","light purple","dark pink"],keywords:[["bright","cyan","fresh","cool"],["light","purple","romantic","soft"],["dark","pink","bold","vibrant"]]},{name:"Moonlit Asteroid",colors:["#0F2027","#203A43","#2C5364"],colorsname:["dark teal","dark gray","dark blue"],keywords:[["dark","teal","depth","cool"],["dark","gray","serious","calm"],["dark","blue","depth","cool"]]},{name:"MegaTron",colors:["#C6FFDD","#FBD786","#f7797d"],colorsname:["light cyan","light yellow","bright red"],keywords:[["light","cyan","fresh","cool"],["light","yellow","cheerful","sunny"],["bright","red","energy","vibrant"]]},{name:"Cool Blues",colors:["#2193b0","#6dd5ed"],colorsname:["bright blue","light blue"],keywords:[["bright","blue","trust","calm"],["light","blue","cool","refreshing"]]},{name:"Piggy Pink",colors:["#ee9ca7","#ffdde1"],colorsname:["light pink","light pink"],keywords:[["light","pink","romantic","soft"],["light","pink","romantic","soft"]]},{name:"Grade Grey",colors:["#bdc3c7","#2c3e50"],colorsname:["light gray","dark gray"],keywords:[["light","gray","neutral","calm"],["dark","gray","serious","calm"]]},{name:"Telko",colors:["#F36222","#5CB644","#007FC3"],colorsname:["bright orange","dark green","bright blue"],keywords:[["bright","orange","vibrant","energy"],["dark","green","nature","fresh"],["bright","blue","trust","cool"]]},{name:"Zenta",colors:["#2A2D3E","#FECB6E"],colorsname:["dark gray","light yellow"],keywords:[["dark","gray","serious","calm"],["light","yellow","cheerful","sunny"]]},{name:"Electric Peacock",colors:["#8a2be2","#0000cd","#228b22","#ccff00"],colorsname:["bright purple","dark blue","dark green","bright yellow"],keywords:[["bright","purple","romantic","mystery"],["dark","blue","depth","cool"],["dark","green","nature","fresh"],["bright","yellow","cheerful","sunny"]]},{name:"Under Blue Green",colors:["#051937","#004d7a","#008793","#00bf72","#a8eb12"],colorsname:["dark teal","dark blue","bright cyan","bright green","light yellow"],keywords:[["dark","teal","depth","cool"],["dark","blue","depth","cool"],["bright","cyan","fresh","cool"],["bright","green","fresh","nature"],["light","yellow","cheerful","sunny"]]},{name:"Lensod",colors:["#6025F5","#FF5555"],colorsname:["bright purple","bright red"],keywords:[["bright","purple","romantic","mystery"],["bright","red","energy","vibrant"]]},{name:"Newspaper",colors:["#8a2be2","#ffa500","#f8f8ff"],colorsname:["bright purple","bright orange","light gray"],keywords:[["bright","purple","romantic","mystery"],["bright","orange","vibrant","energy"],["light","gray","neutral","calm"]]},{name:"Dark Blue Gradient",colors:["#2774ae","#002E5D"],colorsname:["dark blue","dark blue"],keywords:[["dark","blue","depth","serious"],["dark","blue","depth","serious"]]},{name:"Dark Blu Two",colors:["#004680","#4484BA"],colorsname:["dark blue","light blue"],keywords:[["dark","blue","depth","serious"],["light","blue","cool","refreshing"]]},{name:"Lemon Lime",colors:["#7ec6bc","#ebe717"],colorsname:["light teal","bright yellow"],keywords:[["light","teal","fresh","cool"],["bright","yellow","cheerful","sunny"]]},{name:"Beleko",colors:["#ff1e56","#f9c942","#1e90ff"],colorsname:["bright pink","light yellow","bright blue"],keywords:[["bright","pink","romantic","soft"],["light","yellow","cheerful","sunny"],["bright","blue","trust","cool"]]},{name:"Mango Papaya",colors:["#de8a41","#2ada53"],colorsname:["bright orange","bright green"],keywords:[["bright","orange","vibrant","energy"],["bright","green","fresh","nature"]]},{name:"Unicorn Rainbow",colors:["#f7f0ac","#acf7f0","#f0acf7"],colorsname:["light yellow","light cyan","light pink"],keywords:[["light","yellow","cheerful","sunny"],["light","cyan","fresh","cool"],["light","pink","romantic","soft"]]},{name:"Flame",colors:["#ff0000","#fdcf58"],colorsname:["bright red","light orange"],keywords:[["bright","red","energy","vibrant"],["light","orange","warm","soft"]]},{name:"Blue Red",colors:["#36B1C7","#960B33"],colorsname:["bright cyan","dark red"],keywords:[["bright","cyan","fresh","cool"],["dark","red","serious","bold"]]},{name:"Twitter",colors:["#1DA1F2","#009ffc"],colorsname:["bright blue","bright blue"],keywords:[["bright","blue","trust","cool"],["bright","blue","trust","cool"]]},{name:"Blooze",colors:["#6da6be","#4b859e","#6da6be"],colorsname:["bright blue","dark blue"],keywords:[["bright","blue","trust","cool"],["dark","blue","depth","serious"]]},{name:"Blue Slate",colors:["#B5B9FF","#2B2C49"],colorsname:["light blue","dark gray"],keywords:[["light","blue","cool","refreshing"],["dark","gray","serious","calm"]]},{name:"Space Light Green",colors:["#9FA0A8","#5C7852"],colorsname:["dark gray","dark green"],keywords:[["dark","gray","serious","calm"],["dark","green","nature","fresh"]]},{name:"Flower",colors:["#DCFFBD","#CC86D1"],colorsname:["light green","light pink"],keywords:[["light","green","fresh","nature"],["light","pink","romantic","soft"]]},{name:"Elate The Euge",colors:["#8BDEDA","#43ADD0","#998EE0","#E17DC2","#EF9393"],colorsname:["light cyan","bright teal","light purple","dark pink","light red"],keywords:[["light","cyan","fresh","cool"],["bright","teal","fresh","cool"],["light","purple","romantic","soft"],["dark","pink","bold","vibrant"],["light","red","warm","soft"]]},{name:"Peach Sea",colors:["#E6AE8C","#A8CECF"],colorsname:["light orange","light teal"],keywords:[["light","orange","warm","soft"],["light","teal","fresh","cool"]]},{name:"Abbas",colors:["#00fff0","#0083fe"],colorsname:["bright cyan","bright blue"],keywords:[["bright","cyan","fresh","cool"],["bright","blue","trust","cool"]]},{name:"Winter Woods",colors:["#333333","#a2ab58","#A43931"],colorsname:["dark gray","light olive","dark red"],keywords:[["dark","gray","serious","calm"],["light","olive","fresh","nature"],["dark","red","energy","vibrant"]]},{name:"Ameena",colors:["#0c0c6d","#de512b","#98d0c1","#5bb226","#023c0d"],colorsname:["dark blue","dark red","light cyan","bright green","dark green"],keywords:[["dark","blue","depth","serious"],["dark","red","energy","vibrant"],["light","cyan","fresh","cool"],["bright","green","fresh","nature"],["dark","green","nature","fresh"]]},{name:"Emerald Sea",colors:["#05386b","#5cdb95"],colorsname:["dark blue","light green"],keywords:[["dark","blue","depth","serious"],["light","green","fresh","nature"]]},{name:"Bleem",colors:["#4284DB","#29EAC4"],colorsname:["bright blue","bright teal"],keywords:[["bright","blue","trust","cool"],["bright","teal","fresh","cool"]]},{name:"Coffee Gold",colors:["#554023","#c99846"],colorsname:["dark brown","light brown"],keywords:[["dark","brown","earthy","natural"],["light","brown","warm","earthy"]]},{name:"Compass",colors:["#516b8b","#056b3b"],colorsname:["dark teal","dark green"],keywords:[["dark","teal","depth","cool"],["dark","green","nature","fresh"]]},{name:"Andreuzza's",colors:["#D70652","#FF025E"],colorsname:["dark pink","bright pink"],keywords:[["dark","pink","romantic","soft"],["bright","pink","romantic","soft"]]},{name:"Moonwalker",colors:["#152331","#000000"],colorsname:["dark teal","black"],keywords:[["dark","teal","depth","cool"],["dark","black","depth","serious"]]},{name:"Whinehouse",colors:["#f7f7f7","#b9a0a0","#794747","#4e2020","#111111"],colorsname:["light gray","dark gray","dark red","dark gray","black"],keywords:[["light","gray","neutral","calm"],["dark","gray","serious","calm"],["dark","red","energy","vibrant"],["dark","gray","serious","calm"],["dark","black","depth","serious"]]},{name:"Hyper Blue",colors:["#59CDE9","#0A2A88"],colorsname:["bright cyan","dark blue"],keywords:[["bright","cyan","fresh","cool"],["dark","blue","depth","serious"]]},{name:"My New Gradient",colors:["#FF00FF","#00FF00"],colorsname:["bright pink","bright green"],keywords:[["bright","pink","vibrant","modern"],["bright","green","fresh","energetic"]]}],{name:"Neon Cyberpunk",colors:["#FF006E","#8338EC","#3A86FF"],colorsname:["bright pink","purple","bright blue"],keywords:[["bright","pink","neon","futuristic"],["purple","electric","modern","tech"],["bright","blue","cyber","digital"]]},{name:"Sunset Vibes",colors:["#FF9500","#FF5722","#E91E63"],colorsname:["orange","red orange","pink"],keywords:[["orange","warm","sunset","vibrant"],["red","orange","energy","bold"],["pink","romantic","evening","soft"]]},{name:"Ocean Depths",colors:["#006A6B","#0891B2","#33D4FF"],colorsname:["dark teal","blue","light blue"],keywords:[["dark","teal","ocean","deep"],["blue","water","calm","serene"],["light","blue","sky","fresh"]]},{name:"Forest Mist",colors:["#134E5E","#71B280","#A8E6CF"],colorsname:["dark green","green","light green"],keywords:[["dark","green","forest","nature"],["green","fresh","natural","organic"],["light","green","mint","peaceful"]]},{name:"Purple Haze",colors:["#667eea","#764ba2","#f093fb"],colorsname:["blue purple","purple","light pink"],keywords:[["blue","purple","dreamy","mystical"],["purple","royal","elegant","sophisticated"],["light","pink","soft","ethereal"]]},{name:"Golden Hour",colors:["#FFD700","#FFA500","#FF6347"],colorsname:["gold","orange","red orange"],keywords:[["gold","luxury","warm","precious"],["orange","sunset","energetic","vibrant"],["red","orange","fire","passionate"]]},{name:"Arctic Aurora",colors:["#00F5FF","#00CED1","#20B2AA"],colorsname:["cyan","turquoise","light sea green"],keywords:[["cyan","ice","arctic","cool"],["turquoise","aurora","magical","northern"],["light","sea","green","mystical"]]},{name:"Cosmic Dust",colors:["#2E0249","#570A57","#A91079"],colorsname:["dark purple","purple","magenta"],keywords:[["dark","purple","space","cosmic"],["purple","galaxy","mysterious","deep"],["magenta","nebula","bright","stellar"]]},{name:"Tropical Paradise",colors:["#00B4DB","#0083B0","#00A8CC"],colorsname:["light blue","blue","cyan"],keywords:[["light","blue","tropical","paradise"],["blue","ocean","vacation","relaxing"],["cyan","lagoon","crystal","clear"]]},{name:"Velvet Dreams",colors:["#8E2DE2","#4A00E0","#FF006E"],colorsname:["purple","dark purple","pink"],keywords:[["purple","velvet","luxury","rich"],["dark","purple","deep","mysterious"],["pink","dreams","romantic","soft"]]},{name:"Emerald City",colors:["#11998e","#38ef7d","#7fefb2"],colorsname:["teal","green","light green"],keywords:[["teal","emerald","precious","sophisticated"],["green","city","modern","fresh"],["light","green","nature","peaceful"]]},{name:"Fire Storm",colors:["#FF4B1F","#FF9068","#FFAD68"],colorsname:["red","orange","light orange"],keywords:[["red","fire","intense","powerful"],["orange","storm","energy","dynamic"],["light","orange","warm","glowing"]]},{name:"Midnight Blue",colors:["#191654","#43C6AC","#F8FFAE"],colorsname:["dark blue","teal","light yellow"],keywords:[["dark","blue","midnight","deep"],["teal","ocean","calm","serene"],["light","yellow","moon","gentle"]]},{name:"Rose Gold",colors:["#ED4264","#FFEDBC","#FFD700"],colorsname:["pink","cream","gold"],keywords:[["pink","rose","romantic","elegant"],["cream","soft","luxurious","smooth"],["gold","precious","valuable","rich"]]},{name:"Electric Lime",colors:["#8EE000","#32CD32","#00FF7F"],colorsname:["lime","green","spring green"],keywords:[["lime","electric","bright","energetic"],["green","nature","fresh","vibrant"],["spring","green","new","growth"]]},{name:"Lavender Fields",colors:["#E6E6FA","#DDA0DD","#9370DB"],colorsname:["lavender","plum","medium orchid"],keywords:[["lavender","fields","peaceful","calming"],["plum","soft","gentle","soothing"],["medium","orchid","floral","elegant"]]},{name:"Coral Reef",colors:["#FF7F7F","#FF6B6B","#FF5722"],colorsname:["coral","salmon","red orange"],keywords:[["coral","reef","ocean","vibrant"],["salmon","warm","tropical","lively"],["red","orange","energetic","bold"]]},{name:"Steel Blue",colors:["#4682B4","#5F9EA0","#87CEEB"],colorsname:["steel blue","cadet blue","sky blue"],keywords:[["steel","blue","strong","industrial"],["cadet","blue","professional","reliable"],["sky","blue","open","free"]]},{name:"Mint Chocolate",colors:["#98FB98","#3CB371","#2E8B57"],colorsname:["pale green","medium sea green","sea green"],keywords:[["pale","green","mint","fresh"],["medium","sea","green","natural"],["sea","green","chocolate","rich"]]},{name:"Sunset Orange",colors:["#FF8C00","#FF7F50","#FF6347"],colorsname:["dark orange","coral","tomato"],keywords:[["dark","orange","sunset","warm"],["coral","tropical","vibrant","lively"],["tomato","fresh","natural","organic"]]},{name:"Deep Ocean",colors:["#000080","#191970","#4169E1"],colorsname:["navy","midnight blue","royal blue"],keywords:[["navy","deep","ocean","mysterious"],["midnight","blue","dark","profound"],["royal","blue","majestic","elegant"]]},{name:"Cherry Blossom",colors:["#FFB6C1","#FFC0CB","#FF69B4"],colorsname:["light pink","pink","hot pink"],keywords:[["light","pink","cherry","blossom"],["pink","spring","delicate","beautiful"],["hot","pink","vibrant","bold"]]},{name:"Autumn Leaves",colors:["#FF4500","#FF6347","#FFD700"],colorsname:["orange red","tomato","gold"],keywords:[["orange","red","autumn","leaves"],["tomato","fall","seasonal","warm"],["gold","harvest","rich","abundant"]]},{name:"Glacier Ice",colors:["#F0F8FF","#E0FFFF","#B0E0E6"],colorsname:["alice blue","light cyan","powder blue"],keywords:[["alice","blue","glacier","ice"],["light","cyan","frozen","pure"],["powder","blue","arctic","clean"]]},{name:"Neon Nights",colors:["#FF1493","#00FFFF","#ADFF2F"],colorsname:["deep pink","cyan","green yellow"],keywords:[["deep","pink","neon","nights"],["cyan","electric","bright","glowing"],["green","yellow","vibrant","energetic"]]}];function J0(){const[e,t]=s.useState([]),[r,n]=s.useState(!0),[o,a]=s.useState(null);return s.useEffect(()=>{(()=>{n(!0),a(null);try{t(Z0)}catch(i){a(i instanceof Error?i.message:"Failed to load gradients")}finally{n(!1)}})()},[]),{gradients:e,isLoading:r,error:o}}function Q0(){const[e,t]=s.useState([]);return s.useEffect(()=>{const n=localStorage.getItem("gradientFavorites");n&&t(JSON.parse(n))},[]),s.useEffect(()=>{localStorage.setItem("gradientFavorites",JSON.stringify(e))},[e]),{favorites:e,toggleFavorite:n=>{t(o=>o.includes(n)?o.filter(a=>a!==n):[...o,n])}}}function ew(e,t){const[r,n]=s.useState(e);return s.useEffect(()=>{const o=setTimeout(()=>n(e),t);return()=>clearTimeout(o)},[e,t]),r}const hw=()=>{const[e,t]=s.useState(""),r=ew(e,250),[n,o]=s.useState(1),[a,c]=s.useState("all"),[i,l]=s.useState(""),[d,h]=s.useState([]),f=9,{gradients:g,isLoading:b,error:p}=J0(),{favorites:m,toggleFavorite:y}=Q0(),v=s.useMemo(()=>wt.getSimplifiedColors(g),[g]),w=s.useMemo(()=>{if(!g.length)return[];const S=r.toLowerCase();return g.filter(C=>{const $=C.name??"",N=C.colorsname??[],D=C.keywords??[],T=!r||$.toLowerCase().includes(S)||N.some(I=>(I??"").toLowerCase().includes(S))||D.some(I=>(I??[]).some(M=>(M??"").toLowerCase().includes(S))),P=a==="all"||a==="favorites"&&m.includes($),j=d.length===0||d.some(I=>N.some(M=>{const F=(M??"").toLowerCase(),R=(I??"").toLowerCase();return!!(R==="red"&&F.includes("red")||R==="pink"&&F.includes("pink")||R==="orange"&&F.includes("orange")||R==="yellow"&&F.includes("yellow")||R==="green"&&F.includes("green")||R==="blue"&&F.includes("blue")||R==="purple"&&(F.includes("purple")||F.includes("violet"))||R==="brown"&&(F.includes("brown")||F.includes("beige"))||R==="black"&&F.includes("black")||R==="white"&&F.includes("white")||R==="gray"&&(F.includes("gray")||F.includes("grey"))||R==="teal"&&F.includes("teal")||R==="cyan"&&F.includes("cyan"))}));return T&&P&&j})},[r,g,m,a,d]),x=Math.ceil(w.length/f),k=s.useMemo(()=>{const S=(n-1)*f;return w.slice(S,S+f).filter(C=>C.name!==void 0&&C.colors!==void 0&&C.name.length>0&&C.colors.length>0)},[n,w,f]),E=S=>{o(C=>S==="next"?C+1:C-1)};return u.jsx(Fm,{children:u.jsxs("div",{className:"container relative",style:{background:i},children:[u.jsxs("div",{className:"mx-auto max-w-6xl space-y-2 pt-12",children:[u.jsx(K0,{}),u.jsxs("div",{className:"flex flex-col gap-4",children:[u.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row",children:[u.jsxs("div",{className:"relative w-full",id:"input",children:[u.jsx(os,{placeholder:"Search by color name or keyword...",className:"hover:border-brand-500-secondary invalid:border-error-500 invalid:focus:border-error-500 text-placeholder peer block h-full w-full appearance-none overflow-hidden overflow-ellipsis text-nowrap rounded-md border border-border bg-input px-3 py-2 pr-[48px] text-sm outline-none focus:border-none focus:shadow-none focus:outline-none",id:"floating_outlined",type:"text",value:e,onChange:S=>{t(S.target.value),o(1)}}),u.jsx(Yd,{className:"absolute bottom-0 right-2 top-0 m-auto h-5 w-5 text-primary"})]}),u.jsx("div",{className:" ",children:u.jsx(q0,{availableColors:v,selectedColors:d,onColorChange:S=>{h(S),o(1)}})}),u.jsxs(Rn,{value:a,onValueChange:S=>{c(S),o(1)},children:[u.jsx(vr,{className:"nofocus nohover w-full border-none outline-none sm:w-[180px]",children:u.jsx(Nn,{placeholder:"Filter"})}),u.jsxs(wr,{className:"nofocus nohover border-none outline-none",children:[u.jsx(vt,{value:"all",children:"All Gradients"}),u.jsx(vt,{value:"favorites",children:"Favorites"})]})]})]}),(d.length>0||e||a!=="all")&&u.jsxs("div",{className:"text-sm text-muted-foreground",children:["Showing ",w.length," gradient",w.length!==1?"s":"",d.length>0&&u.jsxs("span",{children:[" with colors: ",d.join(", ")]})]})]}),p&&u.jsxs("div",{className:"flex flex-col items-center justify-center py-12 text-center",children:[u.jsxs("p",{className:"text-red-500 mb-4",children:["Error loading gradients: ",p]}),u.jsx("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90",children:"Retry"})]}),!p&&u.jsx(g0,{gradients:k,favorites:m,toggleFavorite:y,setBackground:l,isLoading:b}),u.jsx(p0,{currentPage:n,totalPages:x,onPageChange:E})]}),u.jsx(y0,{})]})})};export{hw as default};
